﻿
CREATE PROCEDURE [dbo].[InsertCustomDisplay_SetTeamExecutiveAsMarketingTeamLeader]

AS
BEGIN

	DECLARE @GroupTypeKey nvarchar(64) SET @GroupTypeKey = 'SetTeamExecutiveAsMarketingTeamLeader'
	
	DELETE FROM CustomPageControlDisplayRights WHERE (customPageControlDisplayRightGroupTypeKey = 'TeamExecutiveAsMarketingTeamLeader')
	DELETE FROM CustomPageControlDisplayRights WHERE (customPageControlDisplayRightGroupTypeKey = @GroupTypeKey)

	DECLARE @Insert int SET @Insert = 1
	DECLARE @Select int SET @Select = 2
	DECLARE @Update int SET @Update = 3

	DECLARE @Add int SET @Add = 1
	DECLARE @Remove int SET @Remove = 2

	DECLARE @Visible int SET @Visible = 1
	DECLARE @Enabled int SET @Enabled = 2
	DECLARE @Text int SET @Text = 3
	DECLARE @Delegate int SET @Delegate = 4
	DECLARE @Authorized int SET @Authorized = 5
	DECLARE @Administrated int SET @Administrated = 6

	DECLARE @PagePathID [varchar] (256)
	
	
	SET @PagePathID = 'ASP.usercontrols_navigation_ascx'

	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Remove,@PagePathID,@Select,'Team Executive','listItemBook',@Visible,0,NULL,NULL

	SET @PagePathID = 'ASP.customers_customersdetails_aspx_formView'

	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Team Executive','linkEdit',@Visible,NULL,NULL,'TrackResults.BES.Security.CustomerRules:HasNotTeamOrUserOrIsNotTourClosed'
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Remove,@PagePathID,@Select,'Team Executive','linkAddDoNotCall',@Visible,0,NULL,NULL

	SET @PagePathID = 'ASP.customers_tours_usercontrols_toursmaster_ascx'

	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Remove,@PagePathID,@Select,'Team Executive','linkNewTour',@Visible,0,NULL,NULL

	SET @PagePathID = 'ASP.customers_donotcallsmaster_aspx'

	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Team Executive','form1',@Authorized,0,NULL,NULL
	
	SET @PagePathID = 'ASP.customers_tours_usercontrols_toursdetails_ascx_formView'

	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Team Executive','salesNotes',@Visible,0,NULL,NULL
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Update,'Team Executive','salesNotes',@Visible,0,NULL,NULL
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Team Executive','labelPodium',@Visible,0,NULL,NULL
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Update,'Team Executive','dropDownPodium',@Enabled,0,NULL,NULL
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Team Executive','labelSalesRep',@Visible,0,NULL,NULL
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Update,'Team Executive','dropDownSalesRep',@Visible,0,NULL,NULL
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Team Executive','labelSalesCloser1',@Visible,0,NULL,NULL
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Update,'Team Executive','dropDownSalesCloser1',@Visible,0,NULL,NULL
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Team Executive','labelSalesCloser2',@Visible,0,NULL,NULL
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Update,'Team Executive','dropDownSalesCloser2',@Visible,0,NULL,NULL
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Team Executive','labelExit',@Visible,0,NULL,NULL
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Update,'Team Executive','dropDownExit',@Visible,0,NULL,NULL
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Team Executive','labelVerificationOfficer',@Visible,0,NULL,NULL
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Update,'Team Executive','dropDownVerificationOfficer',@Visible,0,NULL,NULL

	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Remove,@PagePathID,@Insert,'Team Executive','formView',@Authorized,0,NULL,NULL
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Team Executive','linkEdit',@Visible,NULL,NULL,'TrackResults.BES.Security.TourRules:IsNotRescheduled;TrackResults.BES.Security.TourRules:IsNotTourClosed'
	
	SET @PagePathID = 'ASP.customers_tours_usercontrols_tourssales_ascx'

	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Team Executive','tourSalesPanel',@Visible,0,NULL,NULL
	
	SET @PagePathID = 'ASP.customers_tours_usercontrols_cancellation_ascx'

	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Team Executive','formView',@Visible,0,NULL,NULL
	
	SET @PagePathID = 'ASP.default_aspx'
	
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Team Executive','panelCancels',@Visible,0,NULL,NULL

	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Team Executive','panelNetVolume',@Visible,0,NULL,NULL

	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Team Executive','panelCancellationRequests',@Visible,0,NULL,NULL
	
	SET @PagePathID = 'ASP.reports_default_aspx'

	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Team Executive','panelSalesReports',@Visible,0,NULL,NULL

	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Team Executive','panelCancellationRequestReports',@Visible,0,NULL,NULL
	
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Team Executive','linkUpdateSalesInformationReport',@Visible,0,NULL,NULL

	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Team Executive','linkCreatePurchasesReport',@Visible,0,NULL,NULL

	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Team Executive','linkCreateCancellationRequestReport',@Visible,0,NULL,NULL

	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Team Executive','linkUpdateCancellationRequestReport',@Visible,0,NULL,NULL

	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Team Executive','panelSystemReports',@Visible,0,NULL,NULL
	
	SET @PagePathID = 'ASP.reports_cancellationrequestdetailreport_aspx'

	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Team Executive','form1',@Authorized,0,NULL,NULL

	SET @PagePathID = 'ASP.reports_cancellationrequestefficiencyreport_aspx'

	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Team Executive','form1',@Authorized,0,NULL,NULL
	
	SET @PagePathID = 'ASP.reports_comparativesalesreport_aspx'

	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Team Executive','form1',@Authorized,0,NULL,NULL
	
	SET @PagePathID = 'ASP.reports_efficiencysalesreport_aspx'

	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Team Executive','form1',@Authorized,0,NULL,NULL

	SET @PagePathID = 'ASP.reports_lostvolumereport_aspx'

	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Team Executive','form1',@Authorized,0,NULL,NULL
	
	SET @PagePathID = 'ASP.reports_salesreport_aspx'

	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Team Executive','form1',@Authorized,0,NULL,NULL

	SET @PagePathID = 'ASP.reports_salestourreport_aspx'

	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Team Executive','form1',@Authorized,0,NULL,NULL

	SET @PagePathID = 'ASP.reports_tourreport_aspx'

	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Team Executive','linkDoNotCall',@Visible,0,NULL,NULL
	
	SET @PagePathID = 'ASP.reports_usercontrols_toursearchcriteria_ascx'

	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Team Executive','dropDownSalesCloser1',@Enabled,0,NULL,NULL
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Team Executive','dropDownSalesCloser2',@Enabled,0,NULL,NULL
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Team Executive','dropDownExit',@Enabled,0,NULL,NULL
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Team Executive','dropDownVerificationOfficer',@Enabled,0,NULL,NULL
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Team Executive','dropDownPodium',@Enabled,0,NULL,NULL
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Team Executive','dropDownSalesRep',@Enabled,0,NULL,NULL
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Team Executive','dropDownUser',@Enabled,0,NULL,NULL
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Team Executive','dropDownProduct',@Enabled,0,NULL,NULL
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Team Executive','dropDownSaleStatusTypes',@Enabled,0,NULL,NULL
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Team Executive','dropDownSaleTypes',@Enabled,0,NULL,NULL

	SET @PagePathID = 'ASP.reports_usercontrols_reportby_ascx'

	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Team Executive','menuReportByType',@Delegate,NULL,NULL,'TrackResults.Web.Security.MenuDisplay.ReportByTypes,TrackResults.Web:RemoveByReportByTypes(TrackResults.BES.Data.Types.ReportByType[] {SalesTeams,Podiums,SalesReps,SalesClosers1,Exits,SalesClosers2,VerificationOfficers,Products,SaleTypes,SaleStatusTypes,DaysInPending})'

	SET @PagePathID = 'ASP.reports_usercontrols_reporttitlesave_ascx'

	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Team Executive','dropDownCustomReportTypes',@Delegate,NULL,NULL,'TrackResults.Web.Security.DropDownDisplay.CustomReportTypes,TrackResults.Web:Remove(TrackResults.BES.Data.Types.CustomReportType System);TrackResults.Web.Security.DropDownDisplay.DropDown,TrackResults.Web:NoSelectOption'

	SET @PagePathID = 'ASP.reports_usercontrols_reportconfigurationcommands_ascx'

	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Team Executive','dropDownReportTypes',@Delegate,NULL,NULL,'TrackResults.Web.Security.DropDownDisplay.ReportTypes,TrackResults.Web:RemoveByReportTypes(TrackResults.BES.Data.Types.ReportType[] {PurchaseDetail,RevenueDetail,SalesDetail,SalesEfficiency,PenderEfficiency,LostVolume,CancellationRequestDetail,CancellationRequestEfficiency})'
	
END