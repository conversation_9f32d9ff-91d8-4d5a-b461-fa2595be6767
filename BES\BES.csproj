﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="12.0">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>9.0.30729</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{A2BF1720-80D4-4FE9-9C55-A02FE961057E}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>TrackResults.BES</RootNamespace>
    <AssemblyName>TrackResults.BES</AssemblyName>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <OldToolsVersion>3.5</OldToolsVersion>
    <UpgradeBackupLocation>
    </UpgradeBackupLocation>
    <IsWebBootstrapper>true</IsWebBootstrapper>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <PublishUrl>http://localhost/TrackResults.BES/</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Web</InstallFrom>
    <UpdateEnabled>true</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
    <TargetFrameworkProfile />
    <LangVersion>7.3</LangVersion>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
    <RunCodeAnalysis>false</RunCodeAnalysis>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="AWSSDK, Version=********, Culture=neutral, PublicKeyToken=9f476d3089b52be3, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Common\Libraries\AWSSDK.dll</HintPath>
    </Reference>
    <Reference Include="ImageResizer">
      <HintPath>..\Common\Libraries\ImageResizer.dll</HintPath>
    </Reference>
    <Reference Include="Ionic.Zip">
      <HintPath>..\Common\Libraries\Ionic.Zip.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Newtonsoft.Json, Version=*******, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Common\Libraries\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="Quartz">
      <HintPath>..\Common\Libraries\Quartz.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.Composition" />
    <Reference Include="System.configuration" />
    <Reference Include="System.Core">
      <RequiredTargetFramework>3.5</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.Data" />
    <Reference Include="System.Data.DataSetExtensions">
      <RequiredTargetFramework>3.5</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.Drawing" />
    <Reference Include="System.Numerics" />
    <Reference Include="System.Runtime.Caching" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.Transactions" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.Xml" />
    <Reference Include="WindowsBase" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="ApiServices\25\CustomerExtensions.cs" />
    <Compile Include="ApiServices\25\Data\Customer.cs" />
    <Compile Include="ApiServices\25\Data\Note.cs" />
    <Compile Include="ApiServices\25\Data\Purchase.cs" />
    <Compile Include="ApiServices\25\Data\Tour.cs" />
    <Compile Include="ApiServices\25\NoteExtensions.cs" />
    <Compile Include="ApiServices\25\PurchaseExtensions.cs" />
    <Compile Include="ApiServices\25\TourExtensions.cs" />
    <Compile Include="ApiServices\26\CustomerExtensions.cs" />
    <Compile Include="ApiServices\26\Data\Customer.cs" />
    <Compile Include="ApiServices\26\Data\Customers.Udpated.cs" />
    <Compile Include="ApiServices\26\Data\Note.cs" />
    <Compile Include="ApiServices\26\Data\Purchase.cs" />
    <Compile Include="ApiServices\26\Data\Purchase.Updated.cs" />
    <Compile Include="ApiServices\26\Data\Tour.cs" />
    <Compile Include="ApiServices\26\Data\Tour.Updated.cs" />
    <Compile Include="ApiServices\26\NoteExtensions.cs" />
    <Compile Include="ApiServices\26\PurchaseExtensions.cs" />
    <Compile Include="ApiServices\26\TourExtensions.cs" />
    <Compile Include="ApiServices\27\CustomerExtensions.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="ApiServices\27\Data\Customer.cs" />
    <Compile Include="ApiServices\27\Data\Customers.Udpated.cs" />
    <Compile Include="ApiServices\27\Data\Note.cs" />
    <Compile Include="ApiServices\27\Data\Purchase.cs" />
    <Compile Include="ApiServices\27\Data\Purchase.Updated.cs" />
    <Compile Include="ApiServices\27\Data\Tour.cs" />
    <Compile Include="ApiServices\27\Data\Tour.Updated.cs" />
    <Compile Include="ApiServices\27\Data\ToursCriteria.cs" />
    <Compile Include="ApiServices\27\Data\Types\SaleStatus.cs" />
    <Compile Include="ApiServices\27\Data\Types\SaleTypes.cs" />
    <Compile Include="ApiServices\27\Data\Types\TourStatus.cs" />
    <Compile Include="ApiServices\27\NoteExtensions.cs" />
    <Compile Include="ApiServices\27\PurchaseExtensions.cs" />
    <Compile Include="ApiServices\27\TourExtensions.cs" />
    <Compile Include="ApiServices\29\ApiConnectionExtensions.cs" />
    <Compile Include="ApiServices\29\CustomerExtensions.cs" />
    <Compile Include="ApiServices\29\Data\Customer.cs" />
    <Compile Include="ApiServices\29\Data\Customers.Updated.cs" />
    <Compile Include="ApiServices\29\Data\Note.cs" />
    <Compile Include="ApiServices\29\Data\Purchase.cs" />
    <Compile Include="ApiServices\29\Data\Purchase.Updated.cs" />
    <Compile Include="ApiServices\29\Data\Tour.cs" />
    <Compile Include="ApiServices\29\Data\Tour.Updated.cs" />
    <Compile Include="ApiServices\29\Data\ToursCriteria.cs" />
    <Compile Include="ApiServices\29\Data\Types\SaleStatus.cs" />
    <Compile Include="ApiServices\29\Data\Types\SaleTypes.cs" />
    <Compile Include="ApiServices\29\Data\Types\TourStatus.cs" />
    <Compile Include="ApiServices\29\NoteExtensions.cs" />
    <Compile Include="ApiServices\29\PurchaseExtensions.cs" />
    <Compile Include="ApiServices\29\TourExtensions.cs" />
    <Compile Include="ApiServices\28\ApiConnectionExtensions.cs" />
    <Compile Include="ApiServices\28\CustomerExtensions.cs" />
    <Compile Include="ApiServices\28\Data\Customer.cs" />
    <Compile Include="ApiServices\28\Data\Customers.Updated.cs" />
    <Compile Include="ApiServices\28\Data\Note.cs" />
    <Compile Include="ApiServices\28\Data\Purchase.cs" />
    <Compile Include="ApiServices\28\Data\Purchase.Updated.cs" />
    <Compile Include="ApiServices\28\Data\Tour.cs" />
    <Compile Include="ApiServices\28\Data\Tour.Updated.cs" />
    <Compile Include="ApiServices\28\Data\ToursCriteria.cs" />
    <Compile Include="ApiServices\28\Data\Types\SaleStatus.cs" />
    <Compile Include="ApiServices\28\Data\Types\SaleTypes.cs" />
    <Compile Include="ApiServices\28\Data\Types\TourStatus.cs" />
    <Compile Include="ApiServices\28\NoteExtensions.cs" />
    <Compile Include="ApiServices\28\PurchaseExtensions.cs" />
    <Compile Include="ApiServices\28\TourExtensions.cs" />
    <Compile Include="ApiServices\Wsdl\SchemaDocumentationAnnotation.cs" />
    <Compile Include="ApiServices\Wsdl\SchemaDocumentationAttribute.cs" />
    <Compile Include="ApiServices\Wsdl\SchemaDocumentationDisplayNameAttribute.cs" />
    <Compile Include="ApiServices\Wsdl\SchemaDocumentationDisplayNameDataContractSurrogate.cs" />
    <Compile Include="Builders\InsertBuilder.cs" />
    <Compile Include="Builders\SelectBuilder.cs" />
    <Compile Include="Builders\UpdateBuilder.cs" />
    <Compile Include="Configuration\AppSettingsConfiguration.cs" />
    <Compile Include="DataAccess\ApiConnectionsDataAccess.cs" />
    <Compile Include="DataAccess\ApiConnectionsDataMappingsDataAccess.cs" />
    <Compile Include="DataAccess\ApiConnectionsPropertyMappings.cs" />
    <Compile Include="DataAccess\ApiConnectionRequestsDataAccess.cs" />
    <Compile Include="DataAccess\ApiConnectorsDataAccess.cs" />
    <Compile Include="Builders\AuditBuilder.cs" />
    <Compile Include="DataAccess\apiHttpRequestMethodsDataAccess.cs" />
    <Compile Include="DataAccess\ApiRequestResponsesDataAccess.cs" />
    <Compile Include="DataAccess\ApiResponseActionsDataAccess.cs" />
    <Compile Include="DataAccess\ApiTriggersDataAccess.cs" />
    <Compile Include="DataAccess\Audits\AuditsCustomersDataAccess.cs" />
    <Compile Include="DataAccess\Audits\AuditsDataAccess.cs" />
    <Compile Include="DataAccess\Audits\AuditsPurchasesDataAccess.cs" />
    <Compile Include="DataAccess\Audits\AuditsToursDataAccess.cs" />
    <Compile Include="DataAccess\Audits\AuditsUsersDataAccess.cs" />
    <Compile Include="DataAccess\Audits\AuditsUsersWebAccountsDataAccess.cs" />
    <Compile Include="DataAccess\CatalogsDataAccess.cs" />
    <Compile Include="DataAccess\ConnectionStrings.cs" />
    <Compile Include="DataAccess\CustomAnalyticViewsCalculateSettingsDataAccess.cs" />
    <Compile Include="DataAccess\CustomAnalyticViewsKpisDataAccess.cs" />
    <Compile Include="DataAccess\CustomAnalyticViewsDataAccess.cs" />
    <Compile Include="DataAccess\CustomReportsCalculatedKpisDataAccess.cs" />
    <Compile Include="DataAccess\CustomReportsGoalsKpisDataAccess.cs" />
    <Compile Include="DataAccess\CustomReportsSchedulerDataAccess.cs" />
    <Compile Include="DataAccess\DashboardsDataAccess.cs" />
    <Compile Include="DataAccess\CustomReportsGoalsKpisValuesDataAccess.cs" />
    <Compile Include="DataAccess\DateTimeDataAccess.cs" />
    <Compile Include="DataAccess\DoNotCallsDataAccess.cs" />
    <Compile Include="DataAccess\DynamicClass.cs" />
    <Compile Include="DataAccess\FeaturesDataAccess.cs" />
    <Compile Include="DataAccess\CustomersDataAccess.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="DataAccess\CustomersDataAccess.InsertUpdate.cs" />
    <Compile Include="DataAccess\FeaturesFormatStringsDataAccess.cs" />
    <Compile Include="DataAccess\Global\DeploymentServersDataAccess.cs" />
    <Compile Include="DataAccess\Global\DeploymentStatusDataAccess.cs" />
    <Compile Include="DataAccess\Global\DeploymentsDataAccess.cs" />
    <Compile Include="DataAccess\ImpersonationsDataAccess.cs" />
    <Compile Include="DataAccess\ImportExport\ExportSelectBuilder.cs" />
    <Compile Include="DataAccess\LocationsDataAccess.cs" />
    <Compile Include="DataAccess\ManyPickLists\ToursGiftsManyDataAccess.cs" />
    <Compile Include="DataAccess\ManyPickLists\ManyPickListItemsDataAccess.cs" />
    <Compile Include="DataAccess\ManyPickLists\ToursManyPickList1ItemsManyDataAccess.cs" />
    <Compile Include="DataAccess\Mappings\MapToMap\GiftsLocationsMapToMapDataAccess.cs" />
    <Compile Include="DataAccess\Mappings\MapToMap\HotelsLocationsMapToMapDataAccess.cs" />
    <Compile Include="DataAccess\Mappings\MapToMap\VenuesLocationsMapToMapDataAccess.cs" />
    <Compile Include="DataAccess\Mappings\MapToMap\MapToMapDataAccess.cs" />
    <Compile Include="DataAccess\Mappings\MapToMap\TeamsLocationsMapToMapDataAccess.cs" />
    <Compile Include="DataAccess\Mappings\MapToMap\UsersTeamsMapToMapDataAccess.cs" />
    <Compile Include="DataAccess\Mappings\MapToMap\UsersLocationsMapToMapDataAccess.cs" />
    <Compile Include="DataAccess\MiddlewareErrorsDataAccess.cs" />
    <Compile Include="DataAccess\Milestones\MilestonesDataAccess.cs" />
    <Compile Include="DataAccess\Milestones\MilestonesSaleStatusTypesDataAccess.cs" />
    <Compile Include="DataAccess\Milestones\MilestonesTourStatusTypesDataAccess.cs" />
    <Compile Include="DataAccess\Notes\NotesCountsDataAccess.cs" />
    <Compile Include="DataAccess\Notes\NotesDataAccess.cs" />
    <Compile Include="DataAccess\Notes\NotesToursExitDataAccess.cs" />
    <Compile Include="DataAccess\Notes\NotesToursForm10DataAccess.cs" />
    <Compile Include="DataAccess\Notes\NotesToursForm11DataAccess.cs" />
    <Compile Include="DataAccess\Notes\NotesToursForm12DataAccess.cs" />
    <Compile Include="DataAccess\Notes\NotesToursForm13DataAccess.cs" />
    <Compile Include="DataAccess\Notes\NotesToursForm14DataAccess.cs" />
    <Compile Include="DataAccess\Notes\NotesToursForm15DataAccess.cs" />
    <Compile Include="DataAccess\Notes\NotesToursForm9DataAccess.cs" />
    <Compile Include="DataAccess\Notes\NotesToursForm8DataAccess.cs" />
    <Compile Include="DataAccess\Notes\NotesToursForm7DataAccess.cs" />
    <Compile Include="DataAccess\Notes\NotesToursForm6DataAccess.cs" />
    <Compile Include="DataAccess\Notes\NotesToursForm5DataAccess.cs" />
    <Compile Include="DataAccess\Notes\NotesToursForm4DataAccess.cs" />
    <Compile Include="DataAccess\Notes\NotesToursForm3DataAccess.cs" />
    <Compile Include="DataAccess\Notes\NotesToursForm2DataAccess.cs" />
    <Compile Include="DataAccess\Notes\NotesToursForm1DataAccess.cs" />
    <Compile Include="DataAccess\Notes\NotesToursLeadsDataAccess.cs" />
    <Compile Include="DataAccess\Notes\NotesCancellationsDataAccess.cs" />
    <Compile Include="DataAccess\Notes\NotesCustomersDataAccess.cs" />
    <Compile Include="DataAccess\Notes\NotesToursCallsDataAccess.cs" />
    <Compile Include="DataAccess\Notes\NotesToursMarketingDataAccess.cs" />
    <Compile Include="DataAccess\Notes\NotesToursSalesDataAccess.cs" />
    <Compile Include="DataAccess\PickLists\CancellationStatusTypesDataAccess.cs" />
    <Compile Include="DataAccess\PickLists\CancellationDispositionTypesDataAccess.cs" />
    <Compile Include="DataAccess\PickLists\CancellationReceivedTypesDataAccess.cs" />
    <Compile Include="DataAccess\PickLists\CancellationReasonTypesDataAccess.cs" />
    <Compile Include="DataAccess\PickLists\ContactStatusTypesDataAccess.cs" />
    <Compile Include="DataAccess\PickLists\ContactDispositionsDataAccess.cs" />
    <Compile Include="DataAccess\PickLists\LeadPickList2ItemsDataAccess.cs" />
    <Compile Include="DataAccess\PickLists\LeadPickList1ItemsDataAccess.cs" />
    <Compile Include="DataAccess\PickLists\StatesDataAccess.cs" />
    <Compile Include="DataAccess\PickLists\TourTypePickList1ItemsDataAccess.cs" />
    <Compile Include="DataAccess\PickLists\TourSourcesDataAccess.cs" />
    <Compile Include="DataAccess\PickLists\LeadTypesDataAccess.cs" />
    <Compile Include="DataAccess\PickLists\LeadSourcesDataAccess.cs" />
    <Compile Include="DataAccess\PickLists\CountriesDataAccess.cs" />
    <Compile Include="DataAccess\PickLists\SaleDispositionsDataAccess.cs" />
    <Compile Include="DataAccess\PickLists\CustomerTypesDataAccess.cs" />
    <Compile Include="DataAccess\PickLists\SubProductsDataAccess.cs" />
    <Compile Include="DataAccess\PickLists\ProductCategoriesDataAccess.cs" />
    <Compile Include="DataAccess\PickLists\RegionsDataAccess.cs" />
    <Compile Include="DataAccess\PickLists\CustomerDispositionsDataAccess.cs" />
    <Compile Include="DataAccess\PickLists\CustomerStatusTypesDataAccess.cs" />
    <Compile Include="DataAccess\PickLists\TourConcernTypesDataAccess.cs" />
    <Compile Include="DataAccess\PickLists\TeamsDataAccess.cs" />
    <Compile Include="DataAccess\PickLists\LocationsDataAccess.cs" />
    <Compile Include="DataAccess\PickLists\GiftsDataAccess.cs" />
    <Compile Include="DataAccess\PickLists\INameIDsDataAccess.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="DataAccess\PickLists\ToursManyPickList3ItemsDataAccess.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="DataAccess\PickLists\PurchasesManyPickList1ItemsDataAccess.cs" />
    <Compile Include="DataAccess\PickLists\PurchasesManyPickList2ItemsDataAccess.cs" />
    <Compile Include="DataAccess\PickLists\PurchasesPickList1ItemsDataAccess.cs" />
    <Compile Include="DataAccess\PickLists\PurchasesPickList2ItemsDataAccess.cs" />
    <Compile Include="DataAccess\PickLists\PurchasesPickList3ItemsDataAccess.cs" />
    <Compile Include="DataAccess\PickLists\TourTypesDataAccess.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="DataAccess\PickLists\UsersDataAccess.cs" />
    <Compile Include="DataAccess\PickLists\CallBackPickList1ItemsDataAccess.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="DataAccess\PickLists\CampaignsDataAccess.cs" />
    <Compile Include="DataAccess\PickLists\ChannelsDataAccess.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="DataAccess\PickLists\CustomersManyPickList1ItemsDataAccess.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="DataAccess\PickLists\CustomersManyPickList2ItemsDataAccess.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="DataAccess\PickLists\CustomersPickList1ItemsDataAccess.cs" />
    <Compile Include="DataAccess\PickLists\CustomersPickList2ItemsDataAccess.cs" />
    <Compile Include="DataAccess\PickLists\CustomersPickList3ItemsDataAccess.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="DataAccess\PickLists\CustomersPickList4ItemsDataAccess.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="DataAccess\PickLists\GuestTypesDataAccess.cs" />
    <Compile Include="DataAccess\PickLists\HotelsDataAccess.cs" />
    <Compile Include="DataAccess\PickLists\IncomeTypesDataAccess.cs" />
    <Compile Include="DataAccess\PickLists\LeadDispositionsDataAccess.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="DataAccess\PickLists\LeadStatusTypesDataAccess.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="DataAccess\PickLists\MarketingPickList1ItemsDataAccess.cs" />
    <Compile Include="DataAccess\PickLists\MarketingPickList2ItemsDataAccess.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="DataAccess\PickLists\PickListItemsDataAccess.cs" />
    <Compile Include="DataAccess\PickLists\ProductsDataAccess.cs" />
    <Compile Include="DataAccess\PickLists\SalesPickList1ItemsDataAccess.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="DataAccess\PickLists\ToursManyPickList1ItemsDataAccess.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="DataAccess\PickLists\ToursManyPickList2ItemsDataAccess.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="DataAccess\PickLists\VenuesDataAccess.cs" />
    <Compile Include="DataAccess\Reports\CancellationRequestDetailsDataAccess.cs" />
    <Compile Include="DataAccess\Reports\CancellationRequestEfficiencyDataAccess.cs" />
    <Compile Include="DataAccess\Reports\CancellationRequestReportByBaseDataAccess.cs" />
    <Compile Include="DataAccess\Reports\CancellationRequestStatusDataAccess.cs" />
    <Compile Include="DataAccess\Reports\CustomAnalyticViews\ApplicationViews\LeadsEfficiencyView.cs" />
    <Compile Include="DataAccess\Reports\CustomAnalyticViews\ApplicationViews\LostVolumeEfficiencyView.cs" />
    <Compile Include="DataAccess\Reports\CustomAnalyticViews\ApplicationViews\MarketingEfficiencyView.cs" />
    <Compile Include="DataAccess\Reports\CustomAnalyticViews\ApplicationViews\PenderEfficiencyView.cs" />
    <Compile Include="DataAccess\Reports\CustomAnalyticViews\ApplicationViews\ProposalEfficiencyView.cs" />
    <Compile Include="DataAccess\Reports\CustomAnalyticViews\ApplicationViews\PurchasesEfficiencyView.cs" />
    <Compile Include="DataAccess\Reports\CustomAnalyticViews\ApplicationViews\SalesEfficiencyView.cs" />
    <Compile Include="DataAccess\Reports\CustomAnalyticViews\ApplicationViews\TourStatusEfficiencyView.cs" />
    <Compile Include="DataAccess\Reports\CustomAnalyticViews\CustomAnalyticViewDataRowExtensions.cs" />
    <Compile Include="DataAccess\Reports\CustomAnalyticViews\CustomAnalyticViewLogicDefaultView.cs" />
    <Compile Include="DataAccess\Reports\CustomAnalyticViews\CustomAnalyticViewTourObjectContract.cs" />
    <Compile Include="DataAccess\Reports\CustomAnalyticViews\CustomAnalyticViewsKpiLogicContract.cs" />
    <Compile Include="DataAccess\Reports\CustomAnalyticViews\ExampleCustomAnalyticViewsKpiLogic.cs" />
    <Compile Include="DataAccess\Reports\CustomAnalyticViews\ReportByBaseDataAccess.cs" />
    <Compile Include="DataAccess\Reports\CustomAnalyticViews\ReportByThenByViewDataAccess.cs" />
    <Compile Include="DataAccess\Reports\CustomAnalyticViews\ExampleReportByView.cs" />
    <Compile Include="DataAccess\Reports\CustomAnalyticViews\CustomAnalyticViewLogicContract.cs" />
    <Compile Include="DataAccess\Reports\CustomAnalyticViews\ReportByViewDataAccess.cs" />
    <Compile Include="DataAccess\Reports\ReceivablesDetailDataAccess.cs" />
    <Compile Include="DataAccess\Reports\DaysManifestDataAccess.cs" />
    <Compile Include="DataAccess\Reports\LeadsRatioEfficiencyDataAccess.cs" />
    <Compile Include="DataAccess\Reports\LostVolumeDataAccess.cs" />
    <Compile Include="DataAccess\Reports\ManageNotNullColumns.cs" />
    <Compile Include="DataAccess\Reports\ManifestDataAccess.cs" />
    <Compile Include="DataAccess\Reports\LeadsDetailReportDataAccess.cs" />
    <Compile Include="DataAccess\Reports\PenderEfficiencyDataAccess.cs" />
    <Compile Include="DataAccess\Reports\RevenueReportDataAccess.cs" />
    <Compile Include="DataAccess\Reports\SalesTourReportDataAccess.cs" />
    <Compile Include="DataAccess\Reports\StatusSalesDataAccess.cs" />
    <Compile Include="DataAccess\Reports\ToursReportByBaseDataAccess.AdditionalSql.cs" />
    <Compile Include="DataAccess\Reports\ToursReportByBaseDataAccess.cs" />
    <Compile Include="DataAccess\Reports\PurchasesReportByBaseDataAccess.cs" />
    <Compile Include="DataAccess\Reports\TourStatusEfficiencyReportDataAccess.cs" />
    <Compile Include="DataAccess\ScoreFICODataAccess.cs" />
    <Compile Include="DataAccess\Secure\TeamsSecureSalesDataAccess.cs" />
    <Compile Include="DataAccess\Secure\ISecurePagedDisplayNameSearch.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="DataAccess\Secure\TeamsSecureMarketingDataAccess.cs" />
    <Compile Include="DataAccess\Secure\ToursSecureDataAccess.cs" />
    <Compile Include="DataAccess\Security\CustomCriteriaRightsDataAccess.cs" />
    <Compile Include="DataAccess\DashboardsItemsDataAccess.cs" />
    <Compile Include="DataAccess\DatabaseServerDataAccess.cs" />
    <Compile Include="DataAccess\SiteContent\SiteContentFilesDataAccess.cs" />
    <Compile Include="DataAccess\SiteContent\SiteContentFileTypesDataAccess.cs" />
    <Compile Include="DataAccess\SiteContent\SiteContentPagesDataAccess.cs" />
    <Compile Include="DataAccess\SiteContent\SiteContentPageTypesDataAccess.cs" />
    <Compile Include="DataAccess\SiteContent\SiteContentPartsDataAccess.cs" />
    <Compile Include="DataAccess\DefinedTypes\SaleStatusTypesDataAccess.cs" />
    <Compile Include="DataAccess\DefinedTypes\SaleTypesDataAccess.cs" />
    <Compile Include="DataAccess\SQLSync.cs" />
    <Compile Include="DataAccess\Types\GuestTypesDataAccess.cs" />
    <Compile Include="DataAccess\Types\IncomeTypesDataAccess.cs" />
    <Compile Include="DataAccess\Types\TourConcernTypesDataAccess.cs" />
    <Compile Include="DataAccess\VersionDataAccess.cs" />
    <Compile Include="Data\ApiConnection.cs" />
    <Compile Include="Data\ApiConnectionRequests.cs" />
    <Compile Include="Data\ApiConnector.cs" />
    <Compile Include="Data\ApiHttpRequestMethods.cs" />
    <Compile Include="Data\ApiRequestResponses.cs" />
    <Compile Include="Data\ApiResponseActions.cs" />
    <Compile Include="Data\ApiTriggers.cs" />
    <Compile Include="Data\Audit.cs" />
    <Compile Include="Data\Business\Customer.cs" />
    <Compile Include="Data\Business\Customer.Updated.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Data\Business\CustomerReference.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Data\Business\CustomerReference.Updated.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Data\Business\Deployment.cs" />
    <Compile Include="Data\Business\Impersonation.cs" />
    <Compile Include="Data\Business\Note.Updated.cs" />
    <Compile Include="Data\Business\NoteReference.Updated.cs" />
    <Compile Include="Data\Business\NotesReference.cs" />
    <Compile Include="Data\Business\SiteContent\SiteContentFile.cs" />
    <Compile Include="Data\Business\SiteContent\SiteContentFileType.cs" />
    <Compile Include="Data\Business\SiteContent\SiteContentPage.cs" />
    <Compile Include="Data\Business\SiteContent\SiteContentPageDecodedMetadata.cs" />
    <Compile Include="Data\Business\SiteContent\SiteContentPageType.cs" />
    <Compile Include="Data\Business\SiteContent\SiteContentPart.cs" />
    <Compile Include="Data\Business\User.cs" />
    <Compile Include="Data\Business\UserReference.cs" />
    <Compile Include="Data\Business\UserWebAccount.cs" />
    <Compile Include="Data\Cache\ApplicationMemorySystemCache.cs" />
    <Compile Include="Data\Cache\CustomAnalyticViewsCache.cs" />
    <Compile Include="Data\Cache\CustomReportResultsCache.cs" />
    <Compile Include="Data\Cache\DashboardsCustomReportsCache.cs" />
    <Compile Include="Data\Cache\IDNames\CancellationDispositionTypeIDNamesCache.cs" />
    <Compile Include="Data\Cache\IDNames\CancellationReasonTypeIDNamesCache.cs" />
    <Compile Include="Data\Cache\IDNames\CancellationReceivedTypeIDNamesCache.cs" />
    <Compile Include="Data\Cache\IDNames\CancellationStatusTypeIDNamesCache.cs" />
    <Compile Include="Data\Cache\IDNames\CustomReportGoalsKpiIDNamesCache.cs" />
    <Compile Include="Data\Cache\IDNames\DashboardIDNamesCache.cs" />
    <Compile Include="Data\Cache\IDNames\DashboardSizeTypeIDNamesCache.cs" />
    <Compile Include="Data\Cache\IDNames\CustomReportIDNamesCache.cs" />
    <Compile Include="Data\Cache\IDNames\DashboardTypeIDNamesCache.cs" />
    <Compile Include="Data\Cache\IDNames\ReportViewTypeIDNamesCache.cs" />
    <Compile Include="Data\Cache\IDNames\LeadPickList2ItemIDNamesCache.cs" />
    <Compile Include="Data\Cache\IDNames\LeadPickList1ItemIDNamesCache.cs" />
    <Compile Include="Data\Cache\IDNames\SaleStatusTypeCodesIDNamesCache.cs" />
    <Compile Include="Data\Cache\IDNames\SaleTypeCodesIDNamesCache.cs" />
    <Compile Include="Data\Cache\IDNames\TourStatusStateIDNamesCache.cs" />
    <Compile Include="Data\Cache\IDNames\TourTypePickList1ItemIDNamesCache.cs" />
    <Compile Include="Data\Cache\IDNames\TourSourceIDNamesCache.cs" />
    <Compile Include="Data\Cache\IDNames\LeadTypeIDNamesCache.cs" />
    <Compile Include="Data\Cache\IDNames\LeadSourceIDNamesCache.cs" />
    <Compile Include="Data\Cache\LocationsCache.cs" />
    <Compile Include="Data\Cache\NameIDs\LeadPickList2ItemNameIDsCache.cs" />
    <Compile Include="Data\Cache\NameIDs\LeadPickList1ItemNameIDsCache.cs" />
    <Compile Include="Data\Cache\NameIDs\TourTypePickList1ItemIDNameIDsCache.cs" />
    <Compile Include="Data\Cache\NameIDs\TourSourceIDNameIDsCache.cs" />
    <Compile Include="Data\Cache\NameIDs\LeadTypeNameIDsCache.cs" />
    <Compile Include="Data\Cache\NameIDs\LeadSourceNameIDsCache.cs" />
    <Compile Include="Data\Cache\ReportResultsCache.cs" />
    <Compile Include="Data\Cache\ReportViewTypesCache.cs" />
    <Compile Include="Data\Cache\Static\FeaturesBaseCache.cs" />
    <Compile Include="Data\Cache\Static\FeaturesDefaultCache.cs" />
    <Compile Include="Data\Cache\Static\FeaturesFormatStringsCache.cs" />
    <Compile Include="Data\Cache\Static\MilestoneStatesCache.cs" />
    <Compile Include="Data\Cache\Static\WeeksCache.cs" />
    <Compile Include="Data\Cache\Static\YearsCache.cs" />
    <Compile Include="Data\Catalogs.cs" />
    <Compile Include="Data\Columns\AllUsersLocationsMapNames.cs" />
    <Compile Include="Data\Columns\AspnetUserNames.cs" />
    <Compile Include="Data\Columns\CampaignsNames.cs" />
    <Compile Include="Data\Columns\CancellationsNames.cs" />
    <Compile Include="Data\Columns\CancellationsPickupsNames.cs" />
    <Compile Include="Data\Columns\CountriesNames.cs" />
    <Compile Include="Data\Columns\CustomerNames.cs" />
    <Compile Include="Data\Columns\CustomersPickList1ItemsNames.cs" />
    <Compile Include="Data\Columns\CustomersPickList2ItemsNames.cs" />
    <Compile Include="Data\Columns\DoNotCallsNames.cs" />
    <Compile Include="Data\Columns\GiftNames.cs" />
    <Compile Include="Data\Columns\GuestTypeNames.cs" />
    <Compile Include="Data\Columns\HotelsNames.cs" />
    <Compile Include="Data\Columns\IncomeTypeNames.cs" />
    <Compile Include="Data\Columns\LocationNames.cs" />
    <Compile Include="Data\Columns\MarketingPickList1ItemsNames.cs" />
    <Compile Include="Data\Columns\PurchasesNames.cs" />
    <Compile Include="Data\Columns\ToursSalesNames.cs" />
    <Compile Include="Data\Columns\SaleStatusTypesNames.cs" />
    <Compile Include="Data\Columns\SaleTypesNames.cs" />
    <Compile Include="Data\Columns\StateNames.cs" />
    <Compile Include="Data\Columns\TeamNames.cs" />
    <Compile Include="Data\Columns\TeamsLocationsMapNames.cs" />
    <Compile Include="Data\Columns\TeamsUsersMapNames.cs" />
    <Compile Include="Data\Columns\TeamTypesNames.cs" />
    <Compile Include="Data\Columns\TourConcernTypesNames.cs" />
    <Compile Include="Data\Columns\TourNames.cs" />
    <Compile Include="Data\Columns\ToursGiftsMapNames.cs" />
    <Compile Include="Data\Columns\ToursNotesNames.cs" />
    <Compile Include="Data\Columns\ToursSalesPickList1ItemsNames.cs" />
    <Compile Include="Data\Columns\ToursSalesPickList2ItemsNames.cs" />
    <Compile Include="Data\Columns\TourStatusTypeNames.cs" />
    <Compile Include="Data\Columns\UserNames.cs" />
    <Compile Include="Data\Columns\VenuesNames.cs" />
    <Compile Include="Data\Columns\WavesNames.cs" />
    <Compile Include="Data\ConexionBD.cs" />
    <Compile Include="Data\Criteria\DeploymentsCriteria.cs" />
    <Compile Include="Data\Criteria\TierOne\BasicCriteria.cs" />
    <Compile Include="Data\Criteria\TierOne\CancellationCriteria.cs" />
    <Compile Include="Data\Criteria\TierOne\CustomerCriteria.cs" />
    <Compile Include="Data\Criteria\TierOne\DateTimeCriteria.cs" />
    <Compile Include="Data\Criteria\TierOne\PurchaseCriteria.cs" />
    <Compile Include="Data\Criteria\TierOne\TierOneCriteria.cs" />
    <Compile Include="Data\Criteria\TierOne\TourCallCriteria.cs" />
    <Compile Include="Data\Criteria\TierOne\TourLeadCriteria.cs" />
    <Compile Include="Data\Criteria\TierOne\TourMarketingCriteria.cs" />
    <Compile Include="Data\Criteria\TierOne\TourSaleCriteria.cs" />
    <Compile Include="Data\Criteria\TierOne\TourTourCriteria.cs" />
    <Compile Include="Data\CustomAnalyticView.cs" />
    <Compile Include="Data\CustomAnalyticViewKpi.cs" />
    <Compile Include="Data\CustomReportCalculatedKpi.cs" />
    <Compile Include="Data\CustomReportsGoalsKpi.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Data\CustomReportsGoalsKpiValues.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Data\CustomReportsScheduler.cs" />
    <Compile Include="Data\Dashboard.cs" />
    <Compile Include="Data\DashboardsItem.cs" />
    <Compile Include="Data\Features\Dashboard.cs" />
    <Compile Include="Data\Features\IntegrationFeatures.cs" />
    <Compile Include="Data\Features\UncategorizedFeature.cs" />
    <Compile Include="Data\Global\DeploymentServer.cs" />
    <Compile Include="Data\Localization.cs" />
    <Compile Include="Data\ScoreFICO.cs" />
    <Compile Include="Data\Types\DashboardType.cs" />
    <Compile Include="Data\Feature.cs" />
    <Compile Include="Data\Features\Administration.cs" />
    <Compile Include="Data\Features\ImportExport.cs" />
    <Compile Include="Data\Features\MultipleUpdate.cs" />
    <Compile Include="Data\Features\MultipleUpdateReports\MultipleUpdateReport.cs" />
    <Compile Include="Data\Features\Report.cs" />
    <Compile Include="Data\Features\ReportViewType.cs" />
    <Compile Include="Data\Features\SaleStatusType.cs" />
    <Compile Include="Data\Features\SaleType.cs" />
    <Compile Include="Data\Features\TierOne.cs" />
    <Compile Include="Data\Business\Tour.Updated.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Data\Business\TourReference.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Data\Business\TourReference.Updated.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Data\Business\Purchase.Updated.cs" />
    <Compile Include="Data\Business\PurchaseReference.Updated.cs" />
    <Compile Include="Data\Cache\ApiConnectionsCache.cs" />
    <Compile Include="Data\Cache\ApiConnectionsDataMappingsCache.cs" />
    <Compile Include="Data\Cache\ApiConnectorsCache.cs" />
    <Compile Include="Data\Cache\Criteria\UserIDKeyWhereClauseCache.cs" />
    <Compile Include="Data\Cache\IDNames\ApiConnectionIDNamesCache.cs" />
    <Compile Include="Data\Cache\IDNames\ContactDispositionIDNamesCache.cs" />
    <Compile Include="Data\Cache\IDNames\CustomerTypeIDNamesCache.cs" />
    <Compile Include="Data\Cache\IDNames\SaleDispositionIDNamesCache.cs" />
    <Compile Include="Data\Cache\IDNames\StateAbbreviationIDNamesCache.cs" />
    <Compile Include="Data\Cache\IDNames\TourTypeIDNamesCache.cs" />
    <Compile Include="Data\Cache\IDNames\SubProductIDNamesCache.cs" />
    <Compile Include="Data\Cache\IDNames\ProductCategoryIDNamesCache.cs" />
    <Compile Include="Data\Cache\IDNames\CustomerStatusTypeIDNamesCache.cs" />
    <Compile Include="Data\Cache\IDNames\CustomerDispositionIDNamesCache.cs" />
    <Compile Include="Data\Cache\IDNames\ContactStatusTypeIDNamesCache.cs" />
    <Compile Include="Data\Cache\IDNames\RegionIDNamesCache.cs" />
    <Compile Include="Data\Cache\NameIDs\ContactStatusTypeNameIDsCache.cs" />
    <Compile Include="Data\Cache\NameIDs\ContactDispositionNameIDsCache.cs" />
    <Compile Include="Data\Cache\NameIDs\SaleDispositionIDNameIDsCache.cs" />
    <Compile Include="Data\Cache\NameIDs\TourTypeIDNameIDsCache.cs" />
    <Compile Include="Data\Cache\NameIDs\CustomerTypeNameIDsCache.cs" />
    <Compile Include="Data\Cache\NameIDs\SubProductIDNameIDsCache.cs" />
    <Compile Include="Data\Cache\NameIDs\ProductCategoryNameIDsCache.cs" />
    <Compile Include="Data\Cache\NameIDs\CustomerStatusTypeNameIDsCache.cs" />
    <Compile Include="Data\Cache\NameIDs\CustomerDispositionNameIDsCache.cs" />
    <Compile Include="Data\Cache\NameIDs\RegionNameIDsCache.cs" />
    <Compile Include="Data\Cache\Static\FeaturesCache.cs" />
    <Compile Include="Data\Cache\IDIDSetCache\PurchasesManyPickList2ItemsIDIDSetCache.cs" />
    <Compile Include="Data\Cache\IDIDSetCache\PurchasesManyPickList1ItemsIDIDSetCache.cs" />
    <Compile Include="Data\Cache\IDIDSetCache\CustomersManyPickList1ItemsIDIDSetCache.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Data\Cache\IDIDSetCache\CustomersManyPickList2ItemsIDIDSetCache.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Data\Cache\IDIDSetCache\GiftsLocationsIDIDSetCache.cs" />
    <Compile Include="Data\Cache\IDIDSetCache\HotelsLocationsIDIDSetCache.cs" />
    <Compile Include="Data\Cache\IDIDSetCache\ToursManyPickList1ItemsIDIDSetCache.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Data\Cache\IDIDSetCache\ToursManyPickList2ItemsIDIDSetCache.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Data\Cache\IDIDSetCache\ToursManyPickList3ItemsIDIDSetCache.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Data\Cache\IDIDSetCache\VenuesLocationsIDIDSetCache.cs" />
    <Compile Include="Data\Cache\IDIDSetCache\SqlIDIDSetCache.cs" />
    <Compile Include="Data\Cache\IDIDSetCache\TeamsLocationsIDIDSetCache.cs" />
    <Compile Include="Data\Cache\IDIDSetCache\UsersTeamsIDIDSetCache.cs" />
    <Compile Include="Data\Cache\IDIDSetCache\UsersLocationsIDIDSetCache.cs" />
    <Compile Include="Data\Cache\IDNames\CallBackPickList1ItemIDNamesCache.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Data\Cache\IDNames\CampaignIDNamesCache.cs" />
    <Compile Include="Data\Cache\IDNames\ChannelIDNamesCache.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Data\Cache\IDNames\PurchasesManyPickList2ItemIDNamesCache.cs" />
    <Compile Include="Data\Cache\IDNames\PurchasesManyPickList1ItemIDNamesCache.cs" />
    <Compile Include="Data\Cache\IDNames\PurchasesPickList3ItemIDNamesCache.cs" />
    <Compile Include="Data\Cache\IDNames\CustomersManyPickList1ItemIDNamesCache.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Data\Cache\IDNames\CustomersManyPickList2ItemIDNamesCache.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Data\Cache\IDNames\CustomersPickList1ItemIDNamesCache.cs" />
    <Compile Include="Data\Cache\IDNames\CustomersPickList2ItemIDNamesCache.cs" />
    <Compile Include="Data\Cache\IDNames\CustomersPickList3ItemIDNamesCache.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Data\Cache\IDNames\CustomersPickList4ItemIDNamesCache.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Data\Cache\IDNames\GiftIDNamesCache.cs" />
    <Compile Include="Data\Cache\IDNames\HotelIDNamesCache.cs" />
    <Compile Include="Data\Cache\IDNames\IIDNamesCache.cs" />
    <Compile Include="Data\Cache\IDNames\LeadDispositionIDNamesCache.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Data\Cache\IDNames\LeadStatusTypeIDNamesCache.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Data\Cache\IDNames\MarketingPickList2ItemIDNamesCache.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Data\Cache\IDNames\MiddlewareErrorTypeIDNamesCache.cs" />
    <Compile Include="Data\Cache\IDNames\ProductIDNamesCache.cs" />
    <Compile Include="Data\Cache\IDNames\SalesPickList1ItemIDNamesCache.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Data\Cache\IDNames\SiteContentFileTypesIDNamesCache.cs" />
    <Compile Include="Data\Cache\IDNames\SiteContentFileTypesRelativeUrlIDNamesCache.cs" />
    <Compile Include="Data\Cache\IDNames\SiteContentPageTypesIDNamesCache.cs" />
    <Compile Include="Data\Cache\IDNames\SiteContentPartTypesIDNamesCache.cs" />
    <Compile Include="Data\Cache\IDNames\SqlIDNamesActiveCache.cs" />
    <Compile Include="Data\Cache\IDNames\StateIDNamesCache.cs" />
    <Compile Include="Data\Cache\IDNames\TeamIDNamesCache.cs" />
    <Compile Include="Data\Cache\IDNames\ToursManyPickList1ItemIDNamesCache.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Data\Cache\IDNames\ToursManyPickList2ItemIDNamesCache.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Data\Cache\IDNames\ToursManyPickList3ItemIDNamesCache.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Data\Cache\IDNames\PurchasesPickList1ItemIDNamesCache.cs" />
    <Compile Include="Data\Cache\IDNames\PurchasesPickList2ItemIDNamesCache.cs" />
    <Compile Include="Data\Cache\IDNames\WasCourtesyTourTypeIDNamesCache.cs" />
    <Compile Include="Data\Cache\IDSetCache\ToursSalesManyPickList2ItemsIDSetCache.cs" />
    <Compile Include="Data\Cache\IDSetCache\ToursSalesManyPickList1ItemsIDSetCache.cs" />
    <Compile Include="Data\Cache\IDSetCache\CustomersManyPickList1ItemsIDSetCache.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Data\Cache\IDSetCache\CustomersManyPickList2ItemsIDSetCache.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Data\Cache\IDSetCache\GiftsLocationsIDSetCache.cs" />
    <Compile Include="Data\Cache\IDSetCache\HotelsLocationsIDSetCache.cs" />
    <Compile Include="Data\Cache\IDSetCache\ToursManyPickList1ItemsIDSetCache.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Data\Cache\IDSetCache\ToursManyPickList2ItemsIDSetCache.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Data\Cache\IDSetCache\ToursManyPickList3ItemsIDSetCache.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Data\Cache\IDSetCache\VenuesLocationsIDSetCache.cs" />
    <Compile Include="Data\Cache\IDSetCache\SqlIDSetCache.cs" />
    <Compile Include="Data\Cache\IDSetCache\TeamsLocationsIDSetCache.cs" />
    <Compile Include="Data\Cache\IDSetCache\UsersTeamsIDSetCache.cs" />
    <Compile Include="Data\Cache\IDSetCache\UsersLocationsIDSetCache.cs" />
    <Compile Include="Data\Cache\NameIDs\CallBackPickList1ItemNameIDsCache.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Data\Cache\NameIDs\ChannelNameIDsCache.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Data\Cache\NameIDs\ToursSalesManyPickList2ItemNameIDsCache.cs" />
    <Compile Include="Data\Cache\NameIDs\ToursSalesManyPickList1ItemNameIDsCache.cs" />
    <Compile Include="Data\Cache\NameIDs\ToursSalesPickList3ItemNameIDsCache.cs" />
    <Compile Include="Data\Cache\NameIDs\CustomersManyPickList1ItemNameIDsCache.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Data\Cache\NameIDs\CustomersManyPickList2ItemNameIDsCache.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Data\Cache\NameIDs\CustomersPickList1ItemNameIDsCache.cs" />
    <Compile Include="Data\Cache\NameIDs\CustomersPickList2ItemNameIDsCache.cs" />
    <Compile Include="Data\Cache\NameIDs\CustomersPickList3ItemNameIDsCache.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Data\Cache\NameIDs\CustomersPickList4ItemNameIDsCache.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Data\Cache\NameIDs\LeadDispositionNameIDsCache.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Data\Cache\NameIDs\LeadStatusTypeNameIDsCache.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Data\Cache\NameIDs\MarketingPickList2ItemNameIDsCache.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Data\Cache\NameIDs\SalesPickList1ItemNameIDsCache.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Data\Cache\NameIDs\ToursManyPickList1ItemNameIDsCache.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Data\Cache\NameIDs\ToursManyPickList2ItemNameIDsCache.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Data\Cache\NameIDs\ToursManyPickList3ItemNameIDsCache.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Data\Cache\RoleAccessibleRoleIDRoleDisplayNamesCache.cs" />
    <Compile Include="Data\Cache\RoleLocationMappingTypesCache.cs" />
    <Compile Include="Data\Cache\SiteContentFileTypesCache.cs" />
    <Compile Include="Data\Cache\SiteContentPageDecodedMetadataCache.cs" />
    <Compile Include="Data\Cache\SiteContentPageTypesCache.cs" />
    <Compile Include="Data\Cache\SiteContentPartsDraftKeysCache.cs" />
    <Compile Include="Data\Cache\SiteContentPartsKeysCache.cs" />
    <Compile Include="Data\Cache\UserIDRoleIDsCache.cs" />
    <Compile Include="Data\Cache\UserIDTeamIDsCache.cs" />
    <Compile Include="Data\Criteria\DateRangeCriteria.cs" />
    <Compile Include="Data\Criteria\SearchTermCriteria.cs" />
    <Compile Include="Data\Criteria\SearchTermTypeCriteria.cs" />
    <Compile Include="Data\Criteria\SearchTermTypesDateRangeCriteria.cs" />
    <Compile Include="Data\FeatureDictionary.cs" />
    <Compile Include="Data\Features\TierTwo.cs" />
    <Compile Include="Data\Features\TourStatusType.cs" />
    <Compile Include="Data\Features\Validation.cs" />
    <Compile Include="Data\Features\Word.cs" />
    <Compile Include="Data\MiddlewareError.cs" />
    <Compile Include="Data\OrderBys\UsersOrderBy.cs" />
    <Compile Include="Data\Business\PickListItem.cs" />
    <Compile Include="Data\Types\CachedSiteContentPartKeyType.cs" />
    <Compile Include="Data\Types\CourtesyTourReportSettingsType.cs" />
    <Compile Include="Data\Types\DashboardSizeType.cs" />
    <Compile Include="Data\Types\ReportFormat.cs" />
    <Compile Include="Data\Types\ReportViewKpiType.cs" />
    <Compile Include="Data\Types\ReportViewType.cs" />
    <Compile Include="Data\Types\CustomCriteriaRightRuleTypeID.cs" />
    <Compile Include="Data\Types\MiddlewareErrorType.cs" />
    <Compile Include="Data\Types\MilestoneState.cs" />
    <Compile Include="Data\Types\NoteType.cs" />
    <Compile Include="Data\Types\PropertyType.cs" />
    <Compile Include="Data\Types\ReportBySubType.cs" />
    <Compile Include="Data\Types\SalesLineType.cs" />
    <Compile Include="Data\Types\SiteContentModeType.cs" />
    <Compile Include="Data\Types\SiteContentPartType.cs" />
    <Compile Include="Data\Types\TierOneDateSubType.cs" />
    <Compile Include="Data\Types\TourStatusState.cs" />
    <Compile Include="Data\Types\WasCourtesyTourType.cs" />
    <Compile Include="Keys\ApplicationViewKeys.cs" />
    <Compile Include="Keys\CacheKeys.cs" />
    <Compile Include="Rules\BookingRules.cs" />
    <Compile Include="Rules\CancellationRules.cs" />
    <Compile Include="Rules\CustomerRules.cs" />
    <Compile Include="Rules\DateTimeRules.cs" />
    <Compile Include="Rules\PhoneRules.cs" />
    <Compile Include="Security\CollectionRules.cs" />
    <Compile Include="Security\Criteria\PickUsersCriteria\SecureUpdatePickUsersAllUsersCriteria.cs" />
    <Compile Include="Security\Criteria\PickUsersCriteria\SecureUpdatePickUsersMarketingSalesUsersCriteria.cs" />
    <Compile Include="Security\Criteria\PickUsersCriteria\SecureUpdatePickUsersSalesVloUsersCriteria.cs" />
    <Compile Include="Security\Criteria\PickUsersCriteria\SecureUpdatePickUsersVloUsersCriteria.cs" />
    <Compile Include="Security\Criteria\PickUsersCriteria\SecureSelectPickUsersAllUsersCriteria.cs" />
    <Compile Include="Security\Criteria\PickUsersCriteria\SecureSelectPickUsersMarketingSalesUsersCriteria.cs" />
    <Compile Include="Security\Criteria\PickUsersCriteria\SecurePickUsersCriteriaFactory.cs" />
    <Compile Include="Security\Criteria\PickUsersCriteria\SecureSelectPickUsersSalesVloUsersCriteria.cs" />
    <Compile Include="Security\Criteria\PickUsersCriteria\SecureSelectPickUsersVloUsersCriteria.cs" />
    <Compile Include="Security\Criteria\SecureTourCriteria.Leads.cs" />
    <Compile Include="Security\Criteria\SecureSelectTourCriteria.cs" />
    <Compile Include="Security\Criteria\SecureSelectTourDetailsCriteria.cs" />
    <Compile Include="Security\Criteria\SecureToursNotesCriteria.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Security\Criteria\SecureUpdateTourCriteria.cs" />
    <Compile Include="Security\CustomerRules.cs" />
    <Compile Include="Security\TourRules.cs" />
    <Compile Include="Security\ToursSaleRules.cs" />
    <Compile Include="DataAccess\ApplicationSettingsDataAccess.cs" />
    <Compile Include="DataAccess\CancellationsDataAccess.cs" />
    <Compile Include="DataAccess\CancellationsSupersededToursSalesDataAccess.cs" />
    <Compile Include="DataAccess\CancellationsPickupsDataAccess.cs" />
    <Compile Include="DataAccess\EulaAcceptancesDataAccess.cs" />
    <Compile Include="DataAccess\Global\EulasDataAccess.cs" />
    <Compile Include="DataAccess\IPSecuritiesDataAccess.cs" />
    <Compile Include="DataAccess\ToursDataAccess.InsertUpdate.cs" />
    <Compile Include="DataAccess\ImportExport\ExportDataAccess.cs" />
    <Compile Include="DataAccess\ImportExport\ExportMappingsDataAccess.cs" />
    <Compile Include="DataAccess\ImportExport\ImportMappingsDataAccess.cs" />
    <Compile Include="DataAccess\Localization\CustomLocalizationsDataAccess.cs" />
    <Compile Include="DataAccess\Localization\LocalizationsDataAccess.cs" />
    <Compile Include="DataAccess\Reports\PurchaseReportDataAccess.cs" />
    <Compile Include="DataAccess\Secure\PickUsersSecureDataAccess.cs" />
    <Compile Include="DataAccess\Secure\WavesSecureDataAccess.cs" />
    <Compile Include="DataAccess\StatesDataAccess.cs" />
    <Compile Include="DataAccess\PurchasesDataAccess.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="DataAccess\DefinedTypes\TourStatusTypesDataAccess.cs" />
    <Compile Include="Data\Business\Cancellation.cs" />
    <Compile Include="Data\Business\CancellationExtended.cs" />
    <Compile Include="Data\Business\CancellationPickup.cs" />
    <Compile Include="Data\Business\CancellationPickupExtended.cs" />
    <Compile Include="Data\Business\NoteReference.cs" />
    <Compile Include="Data\Business\Note.cs" />
    <Compile Include="Data\Business\Tour.cs" />
    <Compile Include="Data\Business\Purchase.cs" />
    <Compile Include="Data\Business\PurchaseCancellation.cs" />
    <Compile Include="Data\Business\PurchaseReference.cs" />
    <Compile Include="Data\Cache\IDNames\UserIDNamesCache.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Data\Cache\IDNames\UserIDTeamNameUserNameCache.cs" />
    <Compile Include="Data\Cache\NameIDs\CampaignNameIDsCache.cs" />
    <Compile Include="Data\Cache\NameIDs\CountryNameIDsCache.cs" />
    <Compile Include="Data\Cache\NameIDs\GiftNameIDsCache.cs" />
    <Compile Include="Data\Cache\NameIDs\GuestTypeNameIDsCache.cs" />
    <Compile Include="Data\Cache\NameIDs\HotelNameIDsCache.cs" />
    <Compile Include="Data\Cache\NameIDs\IncomeTypeNameIDsCache.cs" />
    <Compile Include="Data\Cache\IDNames\LocationIDNamesCache.cs" />
    <Compile Include="Data\Cache\NameIDs\LocationNameIDsCache.cs" />
    <Compile Include="Data\Cache\NameIDs\MarketingPickList1ItemNameIDsCache.cs" />
    <Compile Include="Data\Cache\NameIDs\ProductNameIDsCache.cs" />
    <Compile Include="Data\Cache\NameIDs\SaleStatusTypeNameIDsCache.cs" />
    <Compile Include="Data\Cache\NameIDs\SaleTypeNameIDsCache.cs" />
    <Compile Include="Data\Cache\NameIDs\SqlNameIDsCache.cs" />
    <Compile Include="Data\Cache\NameIDs\StateNameIDsCache.cs" />
    <Compile Include="Data\Cache\NameIDs\TeamNameIDsCache.cs" />
    <Compile Include="Data\Cache\NameIDs\TourConcernTypeNameIDsCache.cs" />
    <Compile Include="Data\Cache\NameIDs\ToursSalesPickList1ItemNameIDsCache.cs" />
    <Compile Include="Data\Cache\NameIDs\ToursSalesPickList2ItemNameIDsCache.cs" />
    <Compile Include="Data\Cache\IDNames\GuestTypeIDNamesCache.cs" />
    <Compile Include="Data\Cache\IDNames\IncomeTypeIDNamesCache.cs" />
    <Compile Include="Data\Cache\IDNames\MarketingPickList1ItemIDNamesCache.cs" />
    <Compile Include="Data\Cache\Static\MonthsCache.cs" />
    <Compile Include="Data\Cache\IDNames\SaleStatusTypeIDNamesCache.cs" />
    <Compile Include="Data\Cache\IDNames\SaleTypeIDNamesCache.cs" />
    <Compile Include="Data\Cache\IDNames\StateGroupTypeIDNamesCache.cs" />
    <Compile Include="Data\Cache\IDNames\TeamTypeIDNamesCache.cs" />
    <Compile Include="Data\Cache\IDNames\TourConcernTypeIDNamesCache.cs" />
    <Compile Include="Data\Cache\IDNames\TourStatusTypeIDNamesCache.cs" />
    <Compile Include="Data\Cache\NameIDs\TourStatusTypeNameIDsCache.cs" />
    <Compile Include="Data\Cache\IDNames\SqlIDNamesCache.cs" />
    <Compile Include="Data\Cache\NameIDs\UserNameIDsCache.cs" />
    <Compile Include="Data\Cache\IDNames\TourStatusTypeCodesIDNamesCache.cs" />
    <Compile Include="Data\Cache\IDNames\VenueIDNamesCache.cs" />
    <Compile Include="Data\Cache\NameIDs\VenueNameIDsCache.cs" />
    <Compile Include="Data\Cache\WavesDaysAvailabilityCache.cs" />
    <Compile Include="Data\Cache\WavesTimesAvailabilityCache.cs" />
    <Compile Include="Data\Criteria\CustomerLookupCriteria.cs" />
    <Compile Include="Data\Global\Eula.cs" />
    <Compile Include="Data\ImportExport\ExportMapping.cs" />
    <Compile Include="Data\ImportExport\ImportMapping.cs" />
    <Compile Include="Data\Security\ClassDelegate.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Data\Security\ControlDelegate.cs" />
    <Compile Include="Data\Tables\Reports\CancellationReports.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>CancellationReports.xsd</DependentUpon>
    </Compile>
    <Compile Include="Data\Tables\Waves.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Waves.xsd</DependentUpon>
    </Compile>
    <Compile Include="Data\Types\CancellationStatusType.cs" />
    <Compile Include="Data\Types\ColumnType.cs" />
    <Compile Include="Data\Types\ImportType.cs" />
    <Compile Include="Data\Types\IncomeType.cs" />
    <Compile Include="Data\Types\PickUserType.cs" />
    <Compile Include="Data\Types\ReportType.cs" />
    <Compile Include="Data\Types\StateGroupType.cs" />
    <Compile Include="Data\Types\TourConcernType.cs" />
    <Compile Include="Data\Types\TierOneDateType.cs" />
    <Compile Include="Services\ApiConnectionFileService.cs" />
    <Compile Include="Services\ApiConnectionService.cs" />
    <Compile Include="Services\ApiConnectorProviders\ApiConnectionFileApiConnectorProviders\ApiConnectionFileApiConnectorProvider.cs" />
    <Compile Include="Services\ApiConnectorProviders\ApiConnectionFileApiConnectorProviders\CharacterDelimitedApiConnectionFileApiConnectorProvider.cs" />
    <Compile Include="Services\ApiConnectorProviders\ApiConnectionFileApiConnectorProviders\CsvApiConnectionFileApiConnectorProvider.cs" />
    <Compile Include="Services\ApiConnectorProviders\ApiConnectionFileApiConnectorProviders\ExcelApiConnectionFileApiConnectorProvider.cs" />
    <Compile Include="Services\ApiConnectorProviders\ApiConnectorProvider.Business.cs" />
    <Compile Include="Services\ApiConnectorProviders\ApiConnectorProvider.cs" />
    <Compile Include="Services\ApiConnectorProviders\ConnectionObject.cs" />
    <Compile Include="Services\ApiConnectorProviders\DatabaseApiConnectorProvider.cs" />
    <Compile Include="Services\ApiConnectorProviders\IConnectionObject.cs" />
    <Compile Include="Services\ApiConnectorProviders\ApiConnectorProviderPageToken.cs" />
    <Compile Include="Services\ApiConnectorProviders\WebServiceApiConnectorProvider.cs" />
    <Compile Include="Services\ApplicationService.cs" />
    <Compile Include="Services\ApplicationSettingsService.cs" />
    <Compile Include="Services\AuditsService.cs" />
    <Compile Include="Services\BreadcrumbService.cs" />
    <Compile Include="Services\CustomAnalyticViewsService.cs" />
    <Compile Include="Services\DashboardsService.cs" />
    <Compile Include="Services\CustomReportsService.cs" />
    <Compile Include="Services\ExportMappingsService.cs" />
    <Compile Include="Services\ExtensionsServices.cs" />
    <Compile Include="Services\FeaturesService.cs" />
    <Compile Include="Services\FilesServiceProviders\AmazonS3FilesServiceProvider.cs" />
    <Compile Include="Services\FilesServiceProviders\FilesServiceProvider.cs" />
    <Compile Include="Services\FilesServiceProviders\FileSystemFilesServiceProvider.cs" />
    <Compile Include="Services\ImportMappingsService.cs" />
    <Compile Include="Services\PropertyTypesService.cs" />
    <Compile Include="Services\Internal\CriteriaRightsService.cs" />
    <Compile Include="Services\Internal\GlobalDisplayRightService.cs" />
    <Compile Include="Services\ImportExportService.cs" />
    <Compile Include="Services\MiddlewareService.cs" />
    <Compile Include="Services\MilestonesService.cs" />
    <Compile Include="Services\PropertyKeysService.cs" />
    <Compile Include="Services\ReferencesConversionService.cs" />
    <Compile Include="Services\ReferencesConversionService.FromNotes.cs" />
    <Compile Include="Services\ReferencesConversionService.FromObject.cs" />
    <Compile Include="Services\ReferencesConversionService.FromString.cs" />
    <Compile Include="Services\ReportByTypeServices.cs" />
    <Compile Include="Services\SaleStatusTypesService.cs" />
    <Compile Include="Services\SaleTypesService.cs" />
    <Compile Include="Services\ScheduleJobs\MiddlewareJob.cs" />
    <Compile Include="Services\ScheduleService.cs" />
    <Compile Include="Services\SiteContentFilesService.cs" />
    <Compile Include="Services\ApplicationMemoryContract.cs" />
    <Compile Include="Services\ApplicationMemoryService.cs" />
    <Compile Include="Services\TierOneService.cs" />
    <Compile Include="Services\TourReferencesService.cs" />
    <Compile Include="Services\TourStatusStatesService.cs" />
    <Compile Include="Services\TourStatusTypesService.cs" />
    <Compile Include="Services\UsersService.cs" />
    <Compile Include="Services\WavesService.cs" />
    <Compile Include="Rules\SaleRules.cs" />
    <Compile Include="Rules\TourRules.cs" />
    <Compile Include="Security\Criteria\SecureVenuesCriteria.cs" />
    <Compile Include="Security\Criteria\SecureLocationsCriteria.cs" />
    <Compile Include="Security\Criteria\SecureTeamsCriteria.cs" />
    <Compile Include="Security\Criteria\SecureExportCriteria.cs" />
    <Compile Include="Security\Criteria\SecureCriteria.cs" />
    <Compile Include="Security\Criteria\SecureUsersCriteria.cs" />
    <Compile Include="DataAccess\CampaignsDataAccess.cs" />
    <Compile Include="DataAccess\HotelsDataAccess.cs" />
    <Compile Include="DataAccess\GiftsDataAccess.cs" />
    <Compile Include="DataAccess\Mappings\LocationsGiftsMapDataAccess.cs" />
    <Compile Include="DataAccess\Mappings\LocationsHotelsMapDataAccess.cs" />
    <Compile Include="DataAccess\Mappings\LocationsVenuesMapDataAccess.cs" />
    <Compile Include="DataAccess\Mappings\TeamsLocationsMapDataAccess.cs" />
    <Compile Include="DataAccess\Mappings\ToursGiftsMapDataAccess.cs" />
    <Compile Include="DataAccess\Secure\HotelsSecureDataAccess.cs" />
    <Compile Include="DataAccess\Secure\VenuesSecureDataAccess.cs" />
    <Compile Include="DataAccess\Secure\GiftsSecureDataAccess.cs" />
    <Compile Include="DataAccess\Secure\LocationsSecureDataAccess.cs" />
    <Compile Include="DataAccess\Secure\TeamsSecureDataAccess.cs" />
    <Compile Include="DataAccess\Secure\UsersSecureDataAccess.cs" />
    <Compile Include="DataAccess\Security\CustomPageControlDisplayRightsDataAccess.cs" />
    <Compile Include="DataAccess\Security\PageControlDisplayRightsDataAccess.cs" />
    <Compile Include="DataAccess\TeamsDataAccess.cs" />
    <Compile Include="DataAccess\Mappings\TeamsUsersMapDataAccess.cs" />
    <Compile Include="DataAccess\Mappings\UsersLocationsMapDataAccess.cs" />
    <Compile Include="DataAccess\UsersDataAccess.cs" />
    <Compile Include="DataAccess\RolesDataAccess.cs" />
    <Compile Include="DataAccess\Reports\MarketingTourReportDataAccess.cs" />
    <Compile Include="DataAccess\Security\CriteriaRightsDataAccess.cs" />
    <Compile Include="Constants.cs" />
    <Compile Include="DataAccess\LoggingDataAccess.cs" />
    <Compile Include="DataAccess\VenuesDataAccess.cs" />
    <Compile Include="DataAccess\WebAccountDataAccess.cs" />
    <Compile Include="DataAccess\ToursDataAccess.cs" />
    <Compile Include="Data\Business\Team.cs" />
    <Compile Include="Data\Cache\IDNames\CountryIDNamesCache.cs" />
    <Compile Include="Data\Cache\CriteriaRightsCache.cs" />
    <Compile Include="Data\Cache\PageControlDisplayRightsCache.cs" />
    <Compile Include="Security\Criteria\SecureCriteriaBase.cs" />
    <Compile Include="Data\Criteria\UsersCriteria.cs" />
    <Compile Include="Data\FormatPatterns.cs" />
    <Compile Include="Data\Business\Location.cs" />
    <Compile Include="Data\Security\ControlDisplayRight.cs" />
    <Compile Include="Data\Security\ControlDisplayRightList.cs" />
    <Compile Include="Data\Security\ControlDisplayRightListHashtable.cs" />
    <Compile Include="Data\Security\CriteriaRightsHashtable.cs" />
    <Compile Include="Data\Security\ControlDisplayRightsHashtable.cs" />
    <Compile Include="Security\Criteria\SecureTourCriteria.cs" />
    <Compile Include="Data\Security\CustomPageControlDisplayRight.cs" />
    <Compile Include="Data\Security\PageControlDisplayRight.cs" />
    <Compile Include="Data\Security\PageControlDisplayRightsHashtable.cs" />
    <Compile Include="Data\Tables\Campaigns.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Campaigns.xsd</DependentUpon>
    </Compile>
    <Compile Include="Data\Tables\Gifts.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Gifts.xsd</DependentUpon>
    </Compile>
    <Compile Include="Data\Tables\Hotels.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Hotels.xsd</DependentUpon>
    </Compile>
    <Compile Include="Data\Tables\Locations.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Locations.xsd</DependentUpon>
    </Compile>
    <Compile Include="Data\Tables\Maps.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Maps.xsd</DependentUpon>
    </Compile>
    <Compile Include="Data\Tables\Products.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Products.xsd</DependentUpon>
    </Compile>
    <Compile Include="Data\Tables\Reports\LeadsReports.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>LeadsReports.xsd</DependentUpon>
    </Compile>
    <Compile Include="Data\Tables\Reports\MarketingReports.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>MarketingReports.xsd</DependentUpon>
    </Compile>
    <Compile Include="Data\Tables\Reports\SalesReports.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>SalesReports.xsd</DependentUpon>
    </Compile>
    <Compile Include="Data\Tables\Teams.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Teams.xsd</DependentUpon>
    </Compile>
    <Compile Include="Data\Tables\Users.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Users.xsd</DependentUpon>
    </Compile>
    <Compile Include="Data\Tables\Venues.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Venues.xsd</DependentUpon>
    </Compile>
    <Compile Include="Data\Types\ControlDisplayRightsTypes.cs" />
    <Compile Include="Data\Types\CalculatedSaleStatusType.cs" />
    <Compile Include="Data\Types\CustomPageControlDisplayRightRuleTypes.cs" />
    <Compile Include="Data\Types\ReportByTypes.cs" />
    <Compile Include="Data\Types\TeamTypes.cs" />
    <Compile Include="Data\Business\TeamedUser.cs" />
    <Compile Include="SecurityProfile.cs" />
    <Compile Include="Role.cs" />
    <Compile Include="DataAccess\WavesDataAccess.cs" />
    <Compile Include="DataAccess\CustomReportsDataAccess.cs" />
    <Compile Include="Data\Cache\PickLists\StatesPickListCache.cs" />
    <Compile Include="Data\Criteria\CustomersCriteria.cs" />
    <Compile Include="Data\CustomReport.cs" />
    <Compile Include="Data\Criteria\ListCriteria.cs" />
    <Compile Include="Data\Criteria\ToursCriteria.cs" />
    <Compile Include="Data\Types\CustomReportType.cs" />
    <Compile Include="Data\Types\GuestType.cs" />
    <Compile Include="Data\Types\SaleStatusTypes.cs" />
    <Compile Include="Data\Types\SaleTypes.cs" />
    <Compile Include="Data\Types\TourStatusType.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
      <DependentUpon>Settings.settings</DependentUpon>
    </Compile>
    <Compile Include="Services\Internal\PageControlDisplayRightsService.cs" />
    <Compile Include="Services\RolesService.cs" />
    <Compile Include="Services\ToursService.cs" />
    <Compile Include="_Upgrades\To28\UpgradeService.cs" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="DataAccess\PurchasesDataAccess.InsertUpdate.cs" />
    <None Include="app.config" />
    <None Include="Data\Tables\Campaigns.xsc">
      <DependentUpon>Campaigns.xsd</DependentUpon>
    </None>
    <None Include="Data\Tables\Campaigns.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>Campaigns.Designer.cs</LastGenOutput>
    </None>
    <None Include="Data\Tables\Campaigns.xss">
      <DependentUpon>Campaigns.xsd</DependentUpon>
    </None>
    <None Include="Data\Tables\Gifts.xsc">
      <DependentUpon>Gifts.xsd</DependentUpon>
    </None>
    <None Include="Data\Tables\Gifts.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>Gifts.Designer.cs</LastGenOutput>
    </None>
    <None Include="Data\Tables\Gifts.xss">
      <DependentUpon>Gifts.xsd</DependentUpon>
    </None>
    <None Include="Data\Tables\Hotels.xsc">
      <DependentUpon>Hotels.xsd</DependentUpon>
    </None>
    <None Include="Data\Tables\Hotels.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>Hotels.Designer.cs</LastGenOutput>
    </None>
    <None Include="Data\Tables\Hotels.xss">
      <DependentUpon>Hotels.xsd</DependentUpon>
    </None>
    <None Include="Data\Tables\Locations.xsc">
      <DependentUpon>Locations.xsd</DependentUpon>
    </None>
    <None Include="Data\Tables\Locations.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>Locations.Designer.cs</LastGenOutput>
    </None>
    <None Include="Data\Tables\Locations.xss">
      <DependentUpon>Locations.xsd</DependentUpon>
    </None>
    <None Include="Data\Tables\Maps.xsc">
      <DependentUpon>Maps.xsd</DependentUpon>
    </None>
    <None Include="Data\Tables\Maps.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>Maps.Designer.cs</LastGenOutput>
    </None>
    <None Include="Data\Tables\Maps.xss">
      <DependentUpon>Maps.xsd</DependentUpon>
    </None>
    <None Include="Data\Tables\Products.xsc">
      <DependentUpon>Products.xsd</DependentUpon>
    </None>
    <None Include="Data\Tables\Products.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>Products.Designer.cs</LastGenOutput>
    </None>
    <None Include="Data\Tables\Products.xss">
      <DependentUpon>Products.xsd</DependentUpon>
    </None>
    <None Include="Data\Tables\Reports\CancellationReports.xsc">
      <DependentUpon>CancellationReports.xsd</DependentUpon>
    </None>
    <None Include="Data\Tables\Reports\CancellationReports.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>CancellationReports.Designer.cs</LastGenOutput>
    </None>
    <None Include="Data\Tables\Reports\CancellationReports.xss">
      <DependentUpon>CancellationReports.xsd</DependentUpon>
    </None>
    <None Include="Data\Tables\Reports\LeadsReports.xsc">
      <DependentUpon>LeadsReports.xsd</DependentUpon>
    </None>
    <None Include="Data\Tables\Reports\LeadsReports.xsd">
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>LeadsReports.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </None>
    <None Include="Data\Tables\Reports\LeadsReports.xss">
      <DependentUpon>LeadsReports.xsd</DependentUpon>
    </None>
    <None Include="Data\Tables\Reports\MarketingReports.xsc">
      <DependentUpon>MarketingReports.xsd</DependentUpon>
    </None>
    <None Include="Data\Tables\Reports\MarketingReports.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>MarketingReports.Designer.cs</LastGenOutput>
    </None>
    <None Include="Data\Tables\Reports\MarketingReports.xss">
      <DependentUpon>MarketingReports.xsd</DependentUpon>
    </None>
    <None Include="Data\Tables\Reports\SalesReports.xsc">
      <DependentUpon>SalesReports.xsd</DependentUpon>
    </None>
    <None Include="Data\Tables\Reports\SalesReports.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>SalesReports.Designer.cs</LastGenOutput>
    </None>
    <None Include="Data\Tables\Reports\SalesReports.xss">
      <DependentUpon>SalesReports.xsd</DependentUpon>
    </None>
    <None Include="Data\Tables\Teams.xsc">
      <DependentUpon>Teams.xsd</DependentUpon>
    </None>
    <None Include="Data\Tables\Teams.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>Teams.Designer.cs</LastGenOutput>
    </None>
    <None Include="Data\Tables\Teams.xss">
      <DependentUpon>Teams.xsd</DependentUpon>
    </None>
    <None Include="Data\Tables\Users.xsc">
      <DependentUpon>Users.xsd</DependentUpon>
    </None>
    <None Include="Data\Tables\Users.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>Users.Designer.cs</LastGenOutput>
    </None>
    <None Include="Data\Tables\Users.xss">
      <DependentUpon>Users.xsd</DependentUpon>
    </None>
    <None Include="Data\Tables\Venues.xsc">
      <DependentUpon>Venues.xsd</DependentUpon>
    </None>
    <None Include="Data\Tables\Venues.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>Venues.Designer.cs</LastGenOutput>
    </None>
    <None Include="Data\Tables\Venues.xss">
      <DependentUpon>Venues.xsd</DependentUpon>
    </None>
    <None Include="Data\Tables\Waves.xsc">
      <DependentUpon>Waves.xsd</DependentUpon>
    </None>
    <None Include="Data\Tables\Waves.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>Waves.Designer.cs</LastGenOutput>
    </None>
    <None Include="Data\Tables\Waves.xss">
      <DependentUpon>Waves.xsd</DependentUpon>
    </None>
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include="Microsoft.Net.Client.3.5">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1 Client Profile</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.2.0">
      <Visible>False</Visible>
      <ProductName>.NET Framework 2.0 %28x86%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.0">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.0 %28x86%29</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Common\Common.csproj">
      <Project>{0A0866DA-2AEB-414C-AF82-B1ABDBFCB789}</Project>
      <Name>Common</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="DocumentFormat.OpenXml">
      <Version>2.10.0</Version>
    </PackageReference>
  </ItemGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <PropertyGroup>
    <PreBuildEvent>if exist "$(TargetPath).locked" del "$(TargetPath).locked"
if exist "$(TargetPath)" if not exist "$(TargetPath).locked" move "$(TargetPath)" "$(TargetPath).locked"</PreBuildEvent>
  </PropertyGroup>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>