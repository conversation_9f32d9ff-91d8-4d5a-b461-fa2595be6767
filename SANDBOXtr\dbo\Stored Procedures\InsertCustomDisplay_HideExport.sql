﻿
CREATE PROCEDURE [dbo].[InsertCustomDisplay_HideExport]

AS
BEGIN

	DECLARE @GroupTypeKey nvarchar(64) SET @GroupTypeKey = 'HideExport'

	DELETE FROM CustomPageControlDisplayRights WHERE (customPageControlDisplayRightGroupTypeKey = @GroupTypeKey)

	DECLARE @Add int SET @Add = 1
	DECLARE @Remove int SET @Remove = 2

	DECLARE @Insert int SET @Insert = 1
	DECLARE @Select int SET @Select = 2
	DECLARE @Update int SET @Update = 3

	DECLARE @Visible int SET @Visible = 1
	DECLARE @Enabled int SET @Enabled = 2
	DECLARE @Text int SET @Text = 3
	DECLARE @Delegate int SET @Delegate = 4
	DECLARE @Authorized int SET @Authorized = 5
	DECLARE @Administrated int SET @Administrated = 6

	DECLARE @PagePathID [varchar] (256)

	SET @PagePathID = 'ASP.administration_default_aspx'
	
	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'panelExport',@Visible,0,NULL,NULL

	SET @PagePathID = 'ASP.administration_importexport_tourstocsv_aspx'

	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'form1',@Authorized,0,NULL,NULL


END