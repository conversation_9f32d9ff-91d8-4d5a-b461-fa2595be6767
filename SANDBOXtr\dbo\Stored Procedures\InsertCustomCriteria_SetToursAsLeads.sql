﻿
CREATE PROCEDURE [dbo].[InsertCustomCriteria_SetToursAsLeads]

AS
BEGIN

	DECLARE @GroupType<PERSON>ey nvarchar(64) SET @GroupTypeKey = 'SetToursAsLeads'
	
	DELETE FROM CustomCriteriaRights WHERE (customCriteriaRightGroupTypeKey = @GroupTypeKey)
	
	DECLARE @Add int SET @Add = 1
	DECLARE @Remove int SET @Remove = 2

	EXECUTE InsertCustomCriteriaRights @GroupTypeKey,@Remove, 'TrackResults.BES.Security.Criteria.SecureSelectTourCriteria','Team Executive','AddWhereUsersTeamIsTeam'
	EXECUTE InsertCustomCriteriaRights @GroupTypeKey,@Add, 'TrackResults.BES.Security.Criteria.SecureSelectTourCriteria','Team Executive','AddWhereUsersTeamIsTeamLeads'
	EXECUTE InsertCustomCriteriaRights @GroupTypeKey,@Remove, 'TrackResults.BES.Security.Criteria.SecureSelectTourCriteria','Sales Team Leader','AddWhereUsersTeamIsTeamOrBooked'
	EXECUTE InsertCustomCriteriaRights @GroupTypeKey,@Add, 'TrackResults.BES.Security.Criteria.SecureSelectTourCriteria','Sales Team Leader','AddWhereUsersTeamIsTeamOrBookedLeads'
	EXECUTE InsertCustomCriteriaRights @GroupTypeKey,@Remove, 'TrackResults.BES.Security.Criteria.SecureSelectTourCriteria','Marketing Team Leader','AddWhereUsersTeamIsMarketingTeam'
	EXECUTE InsertCustomCriteriaRights @GroupTypeKey,@Add, 'TrackResults.BES.Security.Criteria.SecureSelectTourCriteria','Marketing Team Leader','AddWhereUsersTeamIsMarketingTeamLeads'
	EXECUTE InsertCustomCriteriaRights @GroupTypeKey,@Remove, 'TrackResults.BES.Security.Criteria.SecureSelectTourCriteria','Sales Dashboard','AddWhereUsersUser'
	EXECUTE InsertCustomCriteriaRights @GroupTypeKey,@Add, 'TrackResults.BES.Security.Criteria.SecureSelectTourCriteria','Sales Dashboard','AddWhereUsersUserLeads'
	EXECUTE InsertCustomCriteriaRights @GroupTypeKey,@Remove, 'TrackResults.BES.Security.Criteria.SecureSelectTourCriteria','Sales Agent','AddWhereUsersUser'
	EXECUTE InsertCustomCriteriaRights @GroupTypeKey,@Add, 'TrackResults.BES.Security.Criteria.SecureSelectTourCriteria','Sales Agent','AddWhereUsersUserLeads'
	EXECUTE InsertCustomCriteriaRights @GroupTypeKey,@Remove, 'TrackResults.BES.Security.Criteria.SecureSelectTourCriteria','Marketing Agent','AddWhereUsersUser'
	EXECUTE InsertCustomCriteriaRights @GroupTypeKey,@Add, 'TrackResults.BES.Security.Criteria.SecureSelectTourCriteria','Marketing Agent','AddWhereUsersUserLeads'

	EXECUTE InsertCustomCriteriaRights @GroupTypeKey,@Remove, 'TrackResults.BES.Security.Criteria.SecureSelectTourDetailsCriteria','Team Executive','AddWhereUsersTeamIsTeam'
	EXECUTE InsertCustomCriteriaRights @GroupTypeKey,@Add, 'TrackResults.BES.Security.Criteria.SecureSelectTourDetailsCriteria','Team Executive','AddWhereUsersTeamIsTeamLeads'
	EXECUTE InsertCustomCriteriaRights @GroupTypeKey,@Remove, 'TrackResults.BES.Security.Criteria.SecureSelectTourDetailsCriteria','Sales Team Leader','AddWhereUsersTeamIsTeamOrBooked'
	EXECUTE InsertCustomCriteriaRights @GroupTypeKey,@Add, 'TrackResults.BES.Security.Criteria.SecureSelectTourDetailsCriteria','Sales Team Leader','AddWhereUsersTeamIsTeamOrBookedLeads'
	EXECUTE InsertCustomCriteriaRights @GroupTypeKey,@Remove, 'TrackResults.BES.Security.Criteria.SecureSelectTourDetailsCriteria','Marketing Team Leader','AddWhereUsersTeamIsMarketingTeam'
	EXECUTE InsertCustomCriteriaRights @GroupTypeKey,@Add, 'TrackResults.BES.Security.Criteria.SecureSelectTourDetailsCriteria','Marketing Team Leader','AddWhereUsersTeamIsMarketingTeamLeads'
	EXECUTE InsertCustomCriteriaRights @GroupTypeKey,@Remove, 'TrackResults.BES.Security.Criteria.SecureSelectTourDetailsCriteria','Sales Dashboard','AddWhereUsersUser'
	EXECUTE InsertCustomCriteriaRights @GroupTypeKey,@Add, 'TrackResults.BES.Security.Criteria.SecureSelectTourDetailsCriteria','Sales Dashboard','AddWhereUsersUserLeads'
	EXECUTE InsertCustomCriteriaRights @GroupTypeKey,@Remove, 'TrackResults.BES.Security.Criteria.SecureSelectTourDetailsCriteria','Sales Agent','AddWhereUsersUser'
	EXECUTE InsertCustomCriteriaRights @GroupTypeKey,@Add, 'TrackResults.BES.Security.Criteria.SecureSelectTourDetailsCriteria','Sales Agent','AddWhereUsersUserLeads'
	EXECUTE InsertCustomCriteriaRights @GroupTypeKey,@Remove, 'TrackResults.BES.Security.Criteria.SecureSelectTourDetailsCriteria','Marketing Agent','AddWhereUsersUser'
	EXECUTE InsertCustomCriteriaRights @GroupTypeKey,@Add, 'TrackResults.BES.Security.Criteria.SecureSelectTourDetailsCriteria','Marketing Agent','AddWhereUsersUserLeads'

	EXECUTE InsertCustomCriteriaRights @GroupTypeKey,@Remove, 'TrackResults.BES.Security.Criteria.SecureUpdateTourCriteria','Team Executive','AddWhereUsersTeamIsTeam'
	EXECUTE InsertCustomCriteriaRights @GroupTypeKey,@Add, 'TrackResults.BES.Security.Criteria.SecureUpdateTourCriteria','Team Executive','AddWhereUsersTeamIsTeamLeads'
	EXECUTE InsertCustomCriteriaRights @GroupTypeKey,@Remove, 'TrackResults.BES.Security.Criteria.SecureUpdateTourCriteria','Sales Team Leader','AddWhereUsersTeamIsTeamOrBooked'
	EXECUTE InsertCustomCriteriaRights @GroupTypeKey,@Add, 'TrackResults.BES.Security.Criteria.SecureUpdateTourCriteria','Sales Team Leader','AddWhereUsersTeamIsTeamOrBookedLeads'
	EXECUTE InsertCustomCriteriaRights @GroupTypeKey,@Remove, 'TrackResults.BES.Security.Criteria.SecureUpdateTourCriteria','Marketing Team Leader','AddWhereUsersTeamIsMarketingTeam'
	EXECUTE InsertCustomCriteriaRights @GroupTypeKey,@Add, 'TrackResults.BES.Security.Criteria.SecureUpdateTourCriteria','Marketing Team Leader','AddWhereUsersTeamIsMarketingTeamLeads'
	EXECUTE InsertCustomCriteriaRights @GroupTypeKey,@Remove, 'TrackResults.BES.Security.Criteria.SecureUpdateTourCriteria','Sales Dashboard','AddWhereUsersUser'
	EXECUTE InsertCustomCriteriaRights @GroupTypeKey,@Add, 'TrackResults.BES.Security.Criteria.SecureUpdateTourCriteria','Sales Dashboard','AddWhereUsersUserLeads'
	EXECUTE InsertCustomCriteriaRights @GroupTypeKey,@Remove, 'TrackResults.BES.Security.Criteria.SecureUpdateTourCriteria','Sales Agent','AddWhereUsersUser'
	EXECUTE InsertCustomCriteriaRights @GroupTypeKey,@Add, 'TrackResults.BES.Security.Criteria.SecureUpdateTourCriteria','Sales Agent','AddWhereUsersUserLeads'
	EXECUTE InsertCustomCriteriaRights @GroupTypeKey,@Remove, 'TrackResults.BES.Security.Criteria.SecureUpdateTourCriteria','Marketing Agent','AddWhereUsersUser'
	EXECUTE InsertCustomCriteriaRights @GroupTypeKey,@Add, 'TrackResults.BES.Security.Criteria.SecureUpdateTourCriteria','Marketing Agent','AddWhereUsersUser'


END