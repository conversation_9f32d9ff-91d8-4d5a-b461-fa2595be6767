﻿CREATE TABLE [dbo].[Venues] (
    [venueID] INT           IDENTITY (1, 1) NOT NULL,
    [name]    NVARCHAR (64) NOT NULL,
    [code]    NVARCHAR (64) NULL,
    [active]  BIT           NOT NULL,
    CONSTRAINT [PK_Venues] PRIMARY KEY CLUSTERED ([venueID] ASC),
    CONSTRAINT [UK_Venues_name] UNIQUE NONCLUSTERED ([name] ASC)
);


GO
CREATE TRIGGER [dbo].[Venues.InsertUpdateDelete]
    ON [dbo].[Venues]
    FOR INSERT, UPDATE, DELETE
AS 
BEGIN
    SET NOCOUNT ON;

    DECLARE @Activity  NVARCHAR (8), @id INT;

    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'updated'
    END

    IF EXISTS (SELECT * FROM inserted) AND NOT EXISTS(SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'inserted'
    END

    IF EXISTS (SELECT * FROM deleted) AND NOT EXISTS(SELECT * FROM inserted)
    BEGIN
        SET @Activity = 'deleted'
    END

    

    IF (@Activity = 'deleted')
    BEGIN
        DECLARE cur CURSOR FOR SELECT venueID FROM deleted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'Venues', @id, 'venueID',
                (
                    SELECT *
                    FROM deleted i 
                    WHERE i.venueID =  @id
                    FOR JSON AUTO
                ),0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
    ELSE
    BEGIN
        DECLARE cur CURSOR
        FOR SELECT venueID
        FROM inserted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'Venues', @id, 'venueID',
                (
                    SELECT *
                    FROM inserted i 
                    WHERE i.venueID =  @id
                    FOR JSON AUTO
                ), 0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
END

GO
DISABLE TRIGGER [dbo].[Venues.InsertUpdateDelete]
    ON [dbo].[Venues];

