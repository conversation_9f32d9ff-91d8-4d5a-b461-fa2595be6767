﻿CREATE TYPE [dbo].[PurchaseType] AS TABLE (
    [purchaseID]                     INT            NULL,
    [externalPurchaseID]             NVARCHAR (128) NULL,
    [tourID]                         INT            NULL,
    [parentSupersededFromPurchaseID] INT            NULL,
    [superseded<PERSON>romPurchaseID]       INT            NULL,
    [superseding<PERSON><PERSON>Pender]          BIT            NULL,
    [saleDate]                       DATETIME       NULL,
    [saleTime]                       DATETIME       NULL,
    [productCategoryID]              INT            NULL,
    [productID]                      INT            NULL,
    [subProductID]                   INT            NULL,
    [saleAmount]                     DECIMAL (9, 2) NULL,
    [downPaymentAmount]              DECIMAL (9, 2) NULL,
    [fees1Amount]                    DECIMAL (9, 2) NULL,
    [fees2Amount]                    DECIMAL (9, 2) NULL,
    [fees3Amount]                    DECIMAL (9, 2) NULL,
    [fees4Amount]                    DECIMAL (9, 2) NULL,
    [fees5Amount]                    DECIMAL (9, 2) NULL,
    [saleTypeID]                     INT            NULL,
    [saleStatusTypeID]               INT            NULL,
    [saleDispositionID]              INT            NULL,
    [purchasesPickList1ItemID]       INT            NULL,
    [purchasesPickList2ItemID]       INT            NULL,
    [purchasesPickList3ItemID]       INT            NULL,
    [purchasesText1]                 NVARCHAR (512) NULL,
    [purchasesText2]                 NVARCHAR (512) NULL,
    [purchasesBool1]                 BIT            NULL,
    [purchasesBool2]                 BIT            NULL,
    [purchasesDate1]                 DATETIME       NULL,
    [purchasesTime1]                 DATETIME       NULL,
    [purchasesDate2]                 DATETIME       NULL,
    [purchasesTime2]                 DATETIME       NULL,
    [apiConnectionID]                INT            NULL,
    [apiExternalPurchaseID]          NVARCHAR (128) NULL,
    [apiExternalConnectionID]        NVARCHAR (128) NULL,
    [insertTimeStamp]                DATETIME       NULL,
    [updateTimeStamp]                DATETIME       NULL);

