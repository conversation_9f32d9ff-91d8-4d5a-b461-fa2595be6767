const db = require('./databaseConnections'); // Assuming you have a database connection module
const sql = require('mssql'); // Import the necessary database libraries

async function saveKPIsToDatabase(kpiData) {
    try {
        const pool = db.trConnStrPool;
        const generatedKpiIds = []; // To collect the generated KpiIds

        // Loop through the KPI data and insert each record
        for (const kpiItem of kpiData) {
            const { columnName, aggFunc, operation, condition, typeFormat } = kpiItem;

            // Define the SQL query to insert KPI data
            const insertQuery = `
          INSERT INTO Kpis (name, type, operation, condition, typeFormat, visible)
          OUTPUT INSERTED.kpiId -- Collect the generated KpiId
          VALUES (@name, @type, @operation, @condition, @typeFormat, @visible)
      `;

            // Prepare the SQL request
            const request = pool.request();
            request.input('name', sql.VarChar(250), columnName);
            request.input('type', sql.<PERSON>ar<PERSON><PERSON>(50), aggFunc);
            request.input('operation', sql.Var<PERSON>har(3000), operation);
            request.input('condition', sql.VarChar(sql.MAX), condition);
            request.input('typeFormat', sql.VarChar(50), typeFormat);
            request.input('visible', sql.Bit, 1);

            // Execute the SQL query to insert KPI data and collect the generated KpiId
            const result = await request.query(insertQuery);
            generatedKpiIds.push(result.recordset[0].kpiId); // Collect the generated KpiId
        }

        return generatedKpiIds; // Return the generated KpiIds
    } catch (error) {
        throw error;
    }
}

async function updateKPIsToDatabase(kpiData) {
    try {
        const pool = db.trConnStrPool;
        const generatedKpiIds = []; // To collect the generated KpiIds

        // Loop through the KPI data and insert each record
        for (const kpiItem of kpiData) {
            const { columnName, aggFunc, operation, condition, kpiId, typeFormat } = kpiItem;


            const query = `
              UPDATE Kpis SET
              name = ISNULL(@name, name),
              type = ISNULL(@type, type),
              operation = ISNULL(@operation, operation),
              condition = ISNULL(@condition, condition),
              typeFormat = ISNULL(@typeFormat, typeFormat)
              WHERE kpiId = @kpiId;
            `;

            const request = pool.request();
            request.input('name', sql.VarChar(250), columnName);
            request.input('type', sql.VarChar(50), aggFunc);
            request.input('operation', sql.VarChar(3000), operation);
            request.input('condition', sql.VarChar(sql.MAX), condition);
            request.input('typeFormat', sql.VarChar(50), typeFormat);
            request.input('kpiId', sql.Int, kpiId);

            await request.query(query);
        }

        return true;
    }
    catch (error) { throw error; }
}

// Function to update the ReportKPIRelationship table with KpiIds and ReportId
async function updateReportKPIRelationship(reportId, kpiIds, reportIdOld) {
    try {
        const pool = db.trConnStrPool;

            const maxPosition = `
                SELECT MAX(position) as position
                FROM ReportsKpis 
                WHERE reportId = @reportId  AND kpiid > 0
                `;

            const checkPosition = pool.request();
            checkPosition.input('reportId', sql.Int, reportId);
            const result = await checkPosition.query(maxPosition);
            var count = result.recordset[0].position + 1;

        // Loop through the kpiIds and check if each KPI exists for the report
        var pos = 1;
        for (const kpiId of kpiIds.kpiIds) {
            // Define the SQL query to check if the record already exists
            const checkQuery = `
                SELECT COUNT(*) as count 
                FROM ReportsKpis 
                WHERE reportId = @reportId AND kpiId = @kpiId
            `;

            // Prepare the SQL request for checking
            const checkRequest = pool.request();
            checkRequest.input('reportId', sql.Int, reportId);
            checkRequest.input('kpiId', sql.Int, kpiId);

            // Execute the query to check if the record exists
            const checkResult = await checkRequest.query(checkQuery);
            const exists = checkResult.recordset[0].count > 0;

            if (!exists) {
                // If the record does not exist, insert it
                const insertQuery = `
                    INSERT INTO ReportsKpis (reportId, kpiId, position)
                    VALUES (@reportId, @kpiId, @position)
                `;
                const insertRequest = pool.request();
                insertRequest.input('reportId', sql.Int, reportId);
                insertRequest.input('kpiId', sql.Int, kpiId);
                if(reportIdOld > 0) {
                    insertRequest.input('position', sql.Int, pos);
                    pos++;
                }
                else {
                    insertRequest.input('position', sql.Int, count);
                }
                
                await insertRequest.query(insertQuery);
            }
        }

        return true; // Assuming success
    } catch (error) {
        // Handle the error properly
        console.error('Error updating ReportKPI Relationship:', error);
        throw error;
    }
}


async function getGiftsFromDatabase() {
    try {
        const sqlQuery = 'SELECT * FROM Gifts';
        const result = await db.trConnStrPool.query(sqlQuery);
        return result.recordset;
    } catch (error) {
        //console.error('Error querying Gifts table:', error);
        throw error; // Rethrow the error to be handled in routes.js
    }
}

async function saveFiltersToDataBase(filterData) {
    try {
        const { reportId, filters } = filterData;
        const pool = db.trConnStrPool;
        const updateQuery = `
          UPDATE Reports
          SET filters = @filters
          WHERE reportId = @reportId
        `;
        // Prepare the SQL request
        const request = pool.request();
        request.input('reportId', sql.Int, reportId);
        request.input('filters', sql.VarChar(sql.MAX), filters);

        // Execute the SQL query
        await request.query(updateQuery);
        return reportId; // Assuming you want to return the reportId after updating
    }
    catch (error) {
        //console.error('Error updating report in the database:', error);
        throw error; // Rethrow the error to be handled elsewhere
    }
}


async function updateFiltersToDataBase(filterData) {
    try {
        const { reportId, filters } = filterData;
        const pool = db.trConnStrPool;
        const query = `
          UPDATE Reports SET filters = @filters
          WHERE reportId = @reportId;
        `;
        // Prepare the SQL request
        const request = pool.request();
        request.input('filters', sql.NVarChar(sql.MAX), filters);
        request.input('reportId', sql.Int, reportId);

        // Execute the SQL query and return the generated joinId
        await request.query(query);

        return true;
    }
    catch (error) {
        //console.error('Error saving report to the database:', error);
        throw error; // Rethrow the error to be handled elsewhere
    }
}

async function getFilters(reportId) {
    try {
        // Convert reportId to an integer if provided as a string
        reportId = parseInt(reportId);

        // Wait for the pool to connect
        await db.trConnStrPool.connect();

        // Define the SQL query to retrieve ReportName and ReportId
        let query = 'SELECT * FROM Reports';
        query += ' WHERE reportId = @reportId';

        // Create a request object
        const request = db.trConnStrPool.request();
        request.input('reportId', sql.Int, reportId);

        // Execute the query with parameters
        const result = await request.query(query);

        return result.recordset;
    } catch (error) {
        //console.error('Error retrieving ReportName and ReportId:', error);
        throw error;
    }
}

async function saveReportToDatabase(reportData) {
    try {
        // console.log(reportData);

        const { reportName, reportType, groupBy, dropDownDateRanges, dropDownYearRanges, preJoin, reportOn, filters } = reportData;

        const pool = db.trConnStrPool;

        // Define the SQL query to insert into ReportTable
        const insertQuery = `
        INSERT INTO Reports (name, groupBy, preJoin, startDate, endDate, reportOn, filters)
        OUTPUT INSERTED.reportId
        VALUES (@name, @groupBy, @preJoin, @startDate, @endDate, @reportOn, @filters)
    `;

    
        // Prepare the SQL request
        const request = pool.request();
        request.input('name', sql.VarChar(255), reportName);
        request.input('groupBy', sql.VarChar(100), groupBy);
        request.input('preJoin', sql.VarChar(100), preJoin);
        request.input('startDate', sql.Int, dropDownDateRanges);
        request.input('endDate', sql.Int, dropDownYearRanges);
        request.input('reportOn', sql.Int, reportOn);
        request.input('filters', sql.VarChar(sql.MAX), filters);

        // Execute the SQL query
        const result = await request.query(insertQuery);

        // Get the generated ReportId
        const reportId = result.recordset[0].reportId;

        return reportId;
    } catch (error) {
        console.error('Error saving report to the database:', error);
        throw error; // Rethrow the error to be handled elsewhere
    }
}

async function updateReportToDatabase(reportData) {
    try {
        const { reportName, reportType, groupBy, dateOn, dateOff, preJoin, reportId, reportOn, filters } = reportData;
        const pool = db.trConnStrPool;

        const query = `
              UPDATE Reports SET
              name = ISNULL(@name, name),
              groupBy = ISNULL(@groupBy, groupBy),
              preJoin = ISNULL(@preJoin, preJoin),
              startDate = ISNULL(@startDate, startDate),
              endDate = ISNULL(@endDate, endDate),
              reportOn = ISNULL(@reportOn, reportOn),
              filters = ISNULL(@filters, filters)
              WHERE reportId = @reportId;
            `;
        // console.log(filters);
        // Prepare the SQL request
        const request = pool.request();
        request.input('name', sql.VarChar(255), reportName);
        request.input('type', sql.VarChar(50), reportType);
        request.input('groupBy', sql.VarChar(100), groupBy);
        request.input('preJoin', sql.VarChar(50), preJoin);
        request.input('startDate', sql.Int, dateOn);
        request.input('endDate', sql.Int, dateOff);
        request.input('reportId', sql.Int, reportId);
        request.input('reportOn', sql.Int, reportOn);
        request.input('filters', sql.VarChar(sql.MAX), filters);
        
        // Execute the SQL query
        await request.query(query);

        return true;
    }
    catch (error) { throw error; }
}

async function saveJoinsToDatabase(joinData) {
    try {
        const { reportId, leftKey, rightKey, joinType, uniqueKey } = joinData;

        const pool = db.trConnStrPool;

        // Define the SQL query to insert into JoinDefinition
        const insertQuery = `
          INSERT INTO ReportsJoins (type, leftKey, rightKey,uniqueKey, reportId )
          OUTPUT INSERTED.reportJoinId
          VALUES (@type, @leftKey, @rightKey, @uniqueKey, @reportId)
      `;

        // Prepare the SQL request
        const request = pool.request();
        request.input('type', sql.VarChar(50), joinType);
        request.input('leftKey', sql.VarChar(255), leftKey);
        request.input('rightKey', sql.VarChar(255), rightKey);
        request.input('uniqueKey', sql.VarChar(255), uniqueKey);
        request.input('reportId', sql.Int, reportId);

        // Execute the SQL query and return the generated joinId
        const result = await request.query(insertQuery);

        return result.recordset[0].reportJoinId;
    } catch (error) {
        throw error;
    }
}

async function updateJoinsToDatabase(joinData) {
    try {
        const { reportId, leftKey, rightKey, joinType, uniqueKey, reportJoinId } = joinData;

        const pool = db.trConnStrPool;

        const checkQuery = `
            SELECT COUNT(*) AS count
            FROM ReportsJoins
            WHERE reportId = @reportId AND reportJoinId = @reportJoinId
        `;

        const checkRequest = pool.request();
        checkRequest.input('reportId', sql.Int, reportId); // Use appropriate data type
        checkRequest.input('reportJoinId', sql.Int, reportJoinId); // Use appropriate data type

        const checkResult = await checkRequest.query(checkQuery);
        const recordCount = checkResult.recordset[0].count;

        if (recordCount == 0) {
            await saveJoinsToDatabase(joinData);
        }
        else {
            // Define the SQL query to insert into JoinDefinition
            const query = `
                UPDATE ReportsJoins SET
                type = ISNULL(@type, type),
                leftKey = ISNULL(@leftKey, leftKey),
                rightKey = ISNULL(@rightKey, rightKey),
                uniqueKey = ISNULL(@uniqueKey, uniqueKey),
                reportId = ISNULL(@reportId, reportId)
                WHERE reportJoinId = @reportJoinId;
                `;

            // Prepare the SQL request
            const request = pool.request();
            request.input('type', sql.VarChar(50), joinType);
            request.input('leftKey', sql.VarChar(255), leftKey);
            request.input('rightKey', sql.VarChar(255), rightKey);
            request.input('uniqueKey', sql.VarChar(255), uniqueKey);
            request.input('reportId', sql.Int, reportId);
            request.input('reportJoinId', sql.Int, reportJoinId);

            // Execute the SQL query and return the generated joinId
            await request.query(query);
        }
        return true;
    } catch (error) {
        throw error;
    }
}

async function saveReportUserToDB(ReportId, UserId) {
    try {
        const pool = db.trConnStrPool;

        // Check if the combination of ReportId and UserId already exists in the ReportsUsersMap table
        const checkQuery = `
      SELECT COUNT(*) AS count
      FROM ReportsUsers
      WHERE reportId = @reportId AND userId = @userId
    `;

        const checkRequest = pool.request();
        checkRequest.input('reportId', sql.Int, ReportId); // Use appropriate data type
        checkRequest.input('userId', sql.VarChar(255), UserId); // Use appropriate data type

        const checkResult = await checkRequest.query(checkQuery);
        const recordCount = checkResult.recordset[0].count;

        if (recordCount > 0) {
            // Record already exists, no need to insert again
            return { success: true, message: 'Record already exists.' };
        }
        // Record does not exist, proceed with the insertion
        const insertQuery = `
      INSERT INTO ReportsUsers (reportId, userId)
      VALUES (@reportId, @userId)
    `;

        const insertRequest = pool.request();
        insertRequest.input('reportId', sql.Int, ReportId); // Use appropriate data type
        insertRequest.input('userId', sql.VarChar(255), UserId); // Use appropriate data type
        // Execute the insertion query
        await insertRequest.query(insertQuery);

        return { success: true, message: 'Data saved successfully.' };
    } catch (error) {
        //console.error('Error saving data into ReportsUsersMap:', error);
        return { success: false, message: 'Error saving data into ReportsUsersMap.' };
    }
}



async function unAssignReportUser(ReportId, UserId) {
    try {
        const pool = db.trConnStrPool;

        // Check if the combination of ReportId and UserId exists in the ReportsUsers table
        const checkQuery = `
            SELECT COUNT(*) AS count
            FROM ReportsUsers
            WHERE reportId = @reportId AND userId = @userId
        `;

        const checkRequest = pool.request();
        checkRequest.input('reportId', sql.Int, ReportId); // Use appropriate data type
        checkRequest.input('userId', sql.VarChar(255), UserId); // Use appropriate data type

        const checkResult = await checkRequest.query(checkQuery);
        const recordCount = checkResult.recordset[0].count;

        if (recordCount === 0) {
            // Record does not exist, no need to delete
            return { success: true, message: 'Record does not exist.' };
        }

        // Record exists, proceed with the deletion
        const deleteQuery = `
            DELETE FROM ReportsUsers
            WHERE reportId = @reportId AND userId = @userId
        `;

        const deleteRequest = pool.request();
        deleteRequest.input('reportId', sql.Int, ReportId); // Use appropriate data type
        deleteRequest.input('userId', sql.VarChar(255), UserId); // Use appropriate data type
        // Execute the deletion query
        await deleteRequest.query(deleteQuery);

        return { success: true, message: 'Data deleted successfully.' };
    } catch (error) {
        //console.error('Error deleting data from ReportsUsers:', error);
        return { success: false, message: 'Error deleting data from ReportsUsers.' };
    }
}

async function getReportsUsersMapByUserId(userId) {
    ////console.log("this is userId: " + userId);
    try {
        const pool = db.trConnStrPool; // Assuming you have a SQL Server connection pool

        // Define the SQL query to retrieve columns by UserId
        const query = `
      SELECT *
      FROM ReportsUsers
      WHERE userId = @userId
    `;

        const request = pool.request();
        request.input('userId', sql.VarChar(255), userId); // Use an appropriate data type for UserId

        const result = await request.query(query);

        return result.recordset;
    } catch (error) {
        //console.error('Error fetching columns by UserId:', error);
        throw error;
    }
}

async function getReportInfo(reportId = null) {
    try {
        // Convert reportId to an integer if provided as a string
        reportId = reportId !== null ? parseInt(reportId) : null;

        // Wait for the pool to connect
        await db.trConnStrPool.connect();

        // Define the SQL query to retrieve ReportName and ReportId
        let query = 'SELECT * FROM Reports';

        if (!isNaN(reportId)) {
            // If reportId is provided and is a valid integer, add a WHERE clause to filter by reportId
            query += ' WHERE reportId = @reportId';
        }

        // Create a request object
        const request = db.trConnStrPool.request();

        // Add the reportId as a parameter if it's provided
        if (!isNaN(reportId)) {
            request.input('reportId', sql.Int, reportId);
        }

        // Execute the query with parameters
        const result = await request.query(query);

        return result.recordset;
    } catch (error) {
        //console.error('Error retrieving ReportName and ReportId:', error);
        throw error;
    }
}

// Create a new function to retrieve a report by reportId  
async function getReportById(reportId) {
    try {
        const reports = await getReportInfo(reportId);

        // Return the first report found (there should be only one with a specific reportId)
        return reports.length > 0 ? reports[0] : null;
    } catch (error) {
        //console.error('Error retrieving report by ReportId:', error);
        throw error;
    }
}
async function getReportInfo(reportId = null) {
    try {
        // Convert reportId to an integer if provided as a string
        reportId = reportId !== null ? parseInt(reportId) : null;

        // Wait for the pool to connect
        await db.trConnStrPool.connect();

        // Define the SQL query to retrieve ReportName and ReportId
        let query = 'SELECT * FROM Reports';

        if (!isNaN(reportId)) {
            // If reportId is provided and is a valid integer, add a WHERE clause to filter by reportId
            query += ' WHERE reportId = @reportId';
        }

        // Create a request object
        const request = db.trConnStrPool.request();

        // Add the reportId as a parameter if it's provided
        if (!isNaN(reportId)) {
            request.input('reportId', sql.Int, reportId);
        }

        // Execute the query with parameters
        const result = await request.query(query);

        return result.recordset;
    } catch (error) {
        console.error('Error retrieving ReportName and ReportId:', error);
        throw error;
    }
}
// Create a new function to retrieve a saved user for report by reportId  
async function getSavedUserByReportId(reportId) {
    try {
        // Convert reportId to an integer if provided as a string
        reportId = reportId !== null ? parseInt(reportId) : null;

        // Wait for the pool to connect
        const pool = await db.trConnStrPool.connect();

        // Define the SQL query to retrieve users associated with the reportId
        let query = `
            SELECT userId
            FROM ReportsUsers
            WHERE reportId = @reportId
        `;

        // Create a request object
        const request = pool.request();

        // Add the reportId as a parameter if it's provided
        if (!isNaN(reportId)) {
            request.input('reportId', sql.Int, reportId);
        }

        // Execute the query with parameters
        const result = await request.query(query);

        // Return the array of userIds associated with the reportId
        return result.recordset.map(record => record.userId);
    } catch (error) {
        //console.error('Error retrieving users by ReportId:', error);
        throw error;
    }
}




async function fetchUserDataByUserName(UserName) {
    try {
        const pool = db.trConnStrPool;

        // Define the SQL query to retrieve user data by UserId
        const query = `
      SELECT ApplicationId, UserId, UserName, LoweredUserName
      FROM aspnet_Users
      WHERE UserName = @UserName
    `;

        const request = pool.request();
        request.input('UserName', sql.VarChar(255), UserName); // Use appropriate data type

        const result = await request.query(query);

        return result.recordset;
    } catch (error) {
        //console.error('Error fetching user data by UserId:', error);
        throw error;
    }
}


async function saveKpiVisibility(kpiId, visible) {
    try {
        const pool = db.trConnStrPool;

        // Define the SQL query to update KPI visibility
        const updateQuery = `
            UPDATE Kpis
            SET Visible = @visible
            WHERE kpiId = @kpiId
        `;

        // Create a new request instance
        const request = pool.request();
        request.input('kpiId', sql.Int, kpiId);
        request.input('visible', sql.Bit, visible);

        // Execute the query
        await request.query(updateQuery);

        return true; // Assuming success
    } catch (error) {
        throw error;
    }
}



// Function to fetch user data from the database 
async function fetchUserData() {
    try {
        // Define the SQL query to retrieve user data
        const query = `
      SELECT ApplicationId, UserId, UserName, LoweredUserName, MobileAlias, IsAnonymous, LastActivityDate
      FROM aspnet_Users
    `;

        // Execute the query
        const result = await db.trConnStrPool.query(query);

        return result.recordset;
    } catch (error) {
        throw error;
    }
}

// Function to fetch KPI data from the database based on KpiIds
async function fetchKPIsByKpiIds(reportId) {
    try {
        // Definir la consulta SQL para obtener los datos de los KPIs asociados al ReportId
        const query = `
            SELECT distinct k.*, RK.position
            FROM Kpis K
            INNER JOIN ReportsKpis RK ON K.kpiId = RK.kpiId    
            WHERE RK.ReportId = @reportId
			ORDER BY Rk.position ASC
        `;

        // Preparar la solicitud a la base de datos con el parámetro
        const request = db.trConnStrPool.request();
        request.input('reportId', sql.Int, reportId);

        // Ejecutar la consulta
        const result = await request.query(query);

        // Devolver el conjunto de resultados
        return result.recordset;
    } catch (error) {
        // Lanzar error con contexto adicional
        throw new Error(`Error fetching KPIs for reportId ${reportId}: ${error.message}`);
    }
}


async function unAssignJoinReport(JoinId) {
    try {
        const pool = db.trConnStrPool;

        
        const deleteQuery = `
            DELETE FROM ReportsJoins
            WHERE reportJoinId = @JoinId
        `;

        const deleteRequest = pool.request();
        deleteRequest.input('JoinId', sql.Int, JoinId); // Use appropriate data type
        // Execute the deletion query
        await deleteRequest.query(deleteQuery);

        return { success: true, message: 'Data deleted successfully.' };
    } catch (error) {
        //console.error('Error deleting data from ReportsUsers:', error);
        return { success: false, message: 'Error deleting data from ReportsUsers.' };
    }
} 

async function unAssignReport(ReportId) {
    if (!Number.isInteger(ReportId) || ReportId <= 0) {
        throw new Error('Invalid ReportId. Must be a positive integer.');
    }

    const pool = db.trConnStrPool;
    const transaction = pool.transaction();

    try {
        await transaction.begin();

        // Unassign related data
        await unAssignKpisReport(ReportId, transaction);
        await unAssignJoinsReport(ReportId, transaction);

        // Delete the report
        const deleteQuery = `
            DELETE FROM reports
            WHERE reportId = @reportId;
        `;
        const deleteRequest = transaction.request();
        deleteRequest.input('reportId', sql.Int, ReportId);
        await deleteRequest.query(deleteQuery);

        await transaction.commit();
        return { success: true, message: 'Report deleted successfully.' };
    } catch (error) {
        await transaction.rollback();
        console.error('Error during report deletion:', error);
        throw new Error(`Failed to delete report: ${error.message}`);
    }
}

async function unAssignKpisReport(ReportId, transaction) {
    try {
        const deleteQuery = `
            DELETE FROM ReportsKpis
            WHERE reportId = @reportId;
        `;
        const deleteRequest = transaction.request();
        deleteRequest.input('reportId', sql.Int, ReportId);
        await deleteRequest.query(deleteQuery);
    } catch (error) {
        console.error('Error deleting KPIs for report:', error);
        throw new Error(`Failed to unassign KPIs for report: ${error.message}`);
    }
}

async function unAssignJoinsReport(ReportId, transaction) {
    try {
        const deleteQuery = `
            DELETE FROM reportsjoins
            WHERE reportId = @reportId;
        `;
        const deleteRequest = transaction.request();
        deleteRequest.input('reportId', sql.Int, ReportId);
        await deleteRequest.query(deleteQuery);
    } catch (error) {
        console.error('Error deleting joins for report:', error);
        throw new Error(`Failed to unassign joins for report: ${error.message}`);
    }
}


async function unAssignKpiReport(ReportId, KpiId) {
    try {
        const pool = db.trConnStrPool;

        
        const deleteQuery = `
            DELETE FROM ReportsKpis
            WHERE reportId = @reportId AND kpiId = @kpiId
        `;

        const deleteRequest = pool.request();
        deleteRequest.input('reportId', sql.Int, ReportId); // Use appropriate data type
        deleteRequest.input('kpiId', sql.Int, KpiId); // Use appropriate data type
        // Execute the deletion query
        await deleteRequest.query(deleteQuery);

    /*    unAssignKpi(KpiId);*/

        return { success: true, message: 'Data deleted successfully.' };
    } catch (error) {
        //console.error('Error deleting data from ReportsUsers:', error);
        return { success: false, message: 'Error deleting data from ReportsUsers.' };
    }
}   


// Function to execute SQL query and retrieve ReportKPIRelationship data by reportId
async function fetchReportKPIRelationship(reportId) {
    try {
        const query = `
      SELECT DISTINCT kpiId
      FROM ReportsKpis
      WHERE reportId = @reportId
    `;
        // console.log(reportId)
        const request = db.trConnStrPool.request();
        request.input('reportId', sql.Int, reportId);

        const result = await request.query(query);

        return result.recordset;
    } catch (error) {
        throw error;
    }
}

// Function to retrieve join definitions by reportId from the database
async function getJoinDefinitionsByReportId(reportId) {
    try {
        const pool = db.trConnStrPool; // Use your database connection pool here

        // Define the SQL query to retrieve join definitions by reportId
        const query = `
      SELECT * FROM ReportsJoins
      WHERE reportId = @reportId
    `;

        // Prepare the SQL request
        const request = pool.request();
        request.input('reportId', sql.Int, reportId);

        // Execute the SQL query and get the result
        const result = await request.query(query);

        // Return the join definitions as an array of objects
        return result.recordset;
    } catch (error) {
        throw error;
    }
}

async function updateReportWithJoinIdsInDatabase(reportId, joinIds) {
    try {
        const pool = db.trConnStrPool;

        // Define the SQL query to update ReportTable with joinIds
        const updateQuery = `
      UPDATE ReportsJoins
      SET reportId = @reportId
      WHERE joinId = @joinId
    `;

        // Loop through the joinIds and update ReportTable for each one
        for (const joinId of joinIds) {
            const request = pool.request(); // Create a new request instance for each iteration
            request.input('reportId', sql.Int, reportId);
            request.input('joinId', sql.Int, joinId);
            await request.query(updateQuery);
        }

        return true; // Assuming success
    } catch (error) {
        throw error;
    }
}

// Export the async functions
module.exports = {
    saveKPIsToDatabase,
    updateKPIsToDatabase,
    updateReportKPIRelationship,
    // updateReportKPIRelationshipInDatabase,
    saveReportToDatabase,
    updateReportToDatabase,
    getGiftsFromDatabase,
    saveJoinsToDatabase,
    updateJoinsToDatabase,
    saveReportUserToDB,
    unAssignReportUser,
    getReportById,
    getSavedUserByReportId,
    fetchUserDataByUserName,
    fetchUserData,
    saveKpiVisibility,
    fetchKPIsByKpiIds,
    fetchReportKPIRelationship,
    updateReportWithJoinIdsInDatabase,
    getJoinDefinitionsByReportId,
    getReportsUsersMapByUserId,
    getReportInfo,
    updateFiltersToDataBase,
    saveFiltersToDataBase,
    getFilters,
    unAssignKpiReport,
    unAssignJoinReport,
    unAssignReport,
};