﻿<?xml version="1.0" encoding="utf-8"?>
<!-- version 9.0.2.1-->
<configuration>
	<configSections>
		<section name="loggingConfiguration" type="Microsoft.Practices.EnterpriseLibrary.Logging.Configuration.LoggingSettings, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
		<section name="exceptionHandling" type="Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ExceptionHandlingSettings, Microsoft.Practices.EnterpriseLibrary.ExceptionHandling, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
		<section name="quartz" type="System.Configuration.NameValueSectionHandler, System, Version=2.0.1.100,Culture=neutral, PublicKeyToken=b77a5c561934e089" />
	</configSections>
	<appSettings>
		<add key="ApiConnectionFilesFilesServiceProvider" value="FileSystemFilesServiceProvider" />
		<add key="ApiConnectionFilesFileSystemRelativeUrl" value="_api-connection-files" />
		<add key="ApiConnectionFilesS3BucketName" value="********************" />
		<add key="ApiConnectionFilesTemporaryStorageRelativeUrl" value="_api-connection-files-temporary" />
		<add key="aspnet:MaxHttpCollectionKeys" value="30000" />
		<add key="AWSAccessKey" value="********************" />
		<add key="AWSSecretKey" value="naefjECwdjNoNWu5HzAuWSLZhYt8yWrrHiuZLZHr" />
		<add key="CryptographyEncryptionIV" value="Jde9#$kl" />
		<add key="CryptographyEncryptionKey" value="a;ty934j@wSD4nGB8$d9wl30" />
		<add key="CryptographyEncryptionSize" value="192" />
		<add key="CryptographyFastEncryptionIV" value="Awe4@$20" />
		<add key="CryptographyFastEncryptionKey" value="a;yt934j" />
		<add key="EnableLogging" value="true" />
		<add key="EnableScheduler" value="true" />
		<add key="ExtensionsDllsDirectory" value="C:\Projects\TrackResults\Web\ExtensionDlls\" />
		<add key="GoogleAnalyticsTrackingID" value="***********-1" />
		<add key="IgnoreCertificateError" value="true" />
		<add key="MSSqlDataAccess.CommandTimeout" value="300" />
		<add key="S3ReaderRootRelativeUrl" value="s3" />
		<add key="SiteContentFilesFilesServiceProvider" value="AmazonS3FilesServiceProvider" />
		<add key="SiteContentFilesFileSystemRelativeUrl" value="_contentstorage/sitecontentfiles" />
		<add key="SiteContentFilesHostname" value="sitecontent.trackresultsdev.com" />
		<add key="SiteContentFilesRelativeUrl" value="" />
		<add key="SiteContentFilesS3BucketName" value="sitecontent.trackresultsdev.com" />
		<add key="Telerik.Web.DisableAsyncUploadHandler" value="true" />

		<!--<add key="ApiBaseUrl" value="https://kaleo-api.azurewebsites.net/" />-->
		<!--<add key="ApiBaseUrl" value="https://lifeguard-api.azurewebsites.net/"/>-->
		<!--<add key="ApiBaseUrl" value="https://crclearwater-api.azurewebsites.net/"/>-->
		<!--<add key="ApiBaseUrl" value="https://haciendadelmar-api.azurewebsites.net/"/>-->
		<!--<add key="ApiBaseUrl" value="https://teptr-api.azurewebsites.net/" />-->
		<!--<add key="ApiBaseUrl" value="https://parliamenttr-api.azurewebsites.net/" />-->
		<!--<add key="ApiBaseUrl" value="https://travelcotr-api.azurewebsites.net/" />-->
		<!--<add key="ApiBaseUrl" value="https://sandboxtr-api.azurewebsites.net/" />-->
		<add key="ApiBaseUrl" value="https://clubleisure-api.azurewebsites.net/" />
		<!--<add key="Environment" value="Production" />-->
		<add key="Environment" value="Development" />
		<!--<add key="ApiBaseUrl" value="http://localhost:3000/" />-->
		<add key="CommandTimeoutBD" value="36000" />
	</appSettings>
	<system.transactions>
		<defaultSettings timeout="01:00:00" />
	</system.transactions>
	<connectionStrings>
		<add name="TrackResults.BES.Properties.Settings.TrackResultsConnectionString" connectionString="Data Source=trackresultssqlw1.database.windows.net;Initial Catalog=sandbox-clubleisure;Integrated Security=False;User Id=tradmin; Password=TimeShare1!;Pooling=True;Max Pool Size=500;MultipleActiveResultSets=True;Connect Timeout=60;Encrypt=True;TrustServerCertificate=False;" providerName="System.Data.SqlClient" />
		<!--<add name="TrackResults.BES.Properties.Settings.TrackResultsConnectionString"
     connectionString="Data Source=tcp:developmentsqlw.database.windows.net,1433;Initial Catalog=TRAVELCOtr2;Integrated Security=False;User Id=dbdevadmin;Password=************;Pooling=True;Max Pool Size=500;MultipleActiveResultSets=True;Connect Timeout=60;Encrypt=True;TrustServerCertificate=False;"
	 providerName="System.Data.SqlClient" />-->
		<add name="TrackResults.BES.Properties.Settings.TrackResultsMasterConnectionString" connectionString="Data Source=trackresultssqlw1.database.windows.net;Initial Catalog=MASTER;Integrated Security=False;User Id=tradmin; Password=TimeShare1!;Pooling=True;MultipleActiveResultSets=True;Connect Timeout=600;Min Pool Size=5;" providerName="System.Data.SqlClient" />
		<!--<add name="TrackResults.BES.Properties.Settings.TrackResultsGenericConnectionString" connectionString="" providerName="System.Data.SqlClient" />-->
		<add name="TrackResults.BES.Properties.Settings.TrackResultsGlobalConnectionString" connectionString="Data Source=trackresultssqlw1.database.windows.net;Initial Catalog=TrackResultsGlobal;Integrated Security=False;User Id=tradmin; Password=TimeShare1!;Pooling=true;MultipleActiveResultSets=True;Connect Timeout=600;Min Pool Size=5;" providerName="System.Data.SqlClient" />
		<add name="LoggingConnectionString" connectionString="Data Source=trackresultssqlw1.database.windows.net;Initial Catalog=TrackResultsLogging;Integrated Security=False;User Id=tradmin; Password=TimeShare1!;Pooling=True;MultipleActiveResultSets=True;Connect Timeout=300;Min Pool Size=5;" providerName="System.Data.SqlClient" />

		<!--<add name="TrackResults.BES.Properties.Settings.TrackResultsConnectionString" connectionString="Data Source=tcp:developmentsqlw.database.windows.net,1433;Initial Catalog=TRAVELCOtr2;Persist Security Info=False;User ID=dbdevadmin;Password=************;MultipleActiveResultSets=True;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;" providerName="System.Data.SqlClient" />-->
	</connectionStrings>
	<!--
    For a description of web.config changes see http://go.microsoft.com/fwlink/?LinkId=235367.

    The following attributes can be set on the <httpRuntime> tag.
      <system.Web>
        <httpRuntime targetFramework="4.5.1" />
      </system.Web>
  -->
	<system.web.extensions>
		<scripting>
			<webServices>
				<jsonSerialization maxJsonLength="**********" />
			</webServices>
		</scripting>
	</system.web.extensions>
	<system.web>
		<httpRuntime maxRequestLength="**********" executionTimeout="36000" />
		<httpHandlers />
		<!--<customErrors mode="On" defaultRedirect="Errors/MaintenanceError.html" />-->
		<customErrors mode="Off" />
		<!-- C:\Windows\Microsoft.NET\Framework\v4.0.30319>aspnet_regsql.exe -E -S .\sqlexpress -ssadd -->
		<!--<sessionState timeout="2880" mode="SQLServer" sqlConnectionString="Data Source=.\sqlexpress;Integrated Security=True;Pooling=True;"/>-->
		<sessionState timeout="2880" mode="InProc" />
		<sessionPageState historySize="50" />
		<compilation debug="true" targetFramework="4.8">
			<expressionBuilders>
				<add expressionPrefix="SaleStatusTypeName" type="TrackResults.Web.Data.ExpressionBuilders.SaleStatusTypesExpressionBuilder" />
				<add expressionPrefix="SaleStatusTypeCode" type="TrackResults.Web.Data.ExpressionBuilders.SaleStatusTypesExpressionBuilder" />
				<add expressionPrefix="SaleStatusTypeCodePercent" type="TrackResults.Web.Data.ExpressionBuilders.SaleStatusTypesExpressionBuilder" />
				<add expressionPrefix="SaleStatusTypeCodeRatio" type="TrackResults.Web.Data.ExpressionBuilders.SaleStatusTypesExpressionBuilder" />
				<add expressionPrefix="SaleTypeName" type="TrackResults.Web.Data.ExpressionBuilders.SaleTypesExpressionBuilder" />
				<add expressionPrefix="SaleTypeCode" type="TrackResults.Web.Data.ExpressionBuilders.SaleTypesExpressionBuilder" />
				<add expressionPrefix="SaleTypeCodePercent" type="TrackResults.Web.Data.ExpressionBuilders.SaleTypesExpressionBuilder" />
				<add expressionPrefix="SaleTypeCodeRatio" type="TrackResults.Web.Data.ExpressionBuilders.SaleTypesExpressionBuilder" />
				<add expressionPrefix="TourStatusTypeName" type="TrackResults.Web.Data.ExpressionBuilders.TourStatusTypesExpressionBuilder" />
				<add expressionPrefix="TourStatusTypeCode" type="TrackResults.Web.Data.ExpressionBuilders.TourStatusTypesExpressionBuilder" />
				<add expressionPrefix="TourStatusTypeCodePercent" type="TrackResults.Web.Data.ExpressionBuilders.TourStatusTypesExpressionBuilder" />
				<add expressionPrefix="TourStatusTypeCodeRatio" type="TrackResults.Web.Data.ExpressionBuilders.TourStatusTypesExpressionBuilder" />
			</expressionBuilders>
			<assemblies>
				<add assembly="System.Web.DynamicData, Version=4.0.0.0, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
				<add assembly="System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=B77A5C561934E089" />
				<add assembly="System.Data.Linq, Version=4.0.0.0, Culture=neutral, PublicKeyToken=B77A5C561934E089" />
				<add assembly="System.ComponentModel.DataAnnotations, Version=4.0.0.0, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
				<add assembly="System.Web.Entity, Version=4.0.0.0, Culture=neutral, PublicKeyToken=B77A5C561934E089" />
				<add assembly="System.Data.Entity, Version=4.0.0.0, Culture=neutral, PublicKeyToken=B77A5C561934E089" />
				<add assembly="System.Xml.Linq, Version=4.0.0.0, Culture=neutral, PublicKeyToken=B77A5C561934E089" />
			</assemblies>
		</compilation>
		<machineKey validationKey="AutoGenerate,IsolateApps" decryptionKey="AutoGenerate,IsolateApps" validation="SHA1" decryption="Auto" />
		<pages enableViewStateMac="true" viewStateEncryptionMode="Auto">
			<controls>
				<add tagPrefix="uc" tagName="DateTimeCriteria" src="~/Reports/UserControls/CriteriaUserControls/DateTimeCriteriaDetails.ascx" />
				<add tagPrefix="trwc" namespace="TrackResults.Web.WebControls" assembly="TrackResults.Web" />
				<add tagPrefix="truc" tagName="Breadcrumb" src="~/UserControls/Breadcrumb.ascx" />
				<add tagPrefix="truc" tagName="CachedSiteContentPart" src="~/UserControls/CachedSiteContentPart.ascx" />
				<add tagPrefix="truc" tagName="CollapsePanel" src="~/UserControls/CollapsePanel.ascx" />
				<add tagPrefix="truc" tagName="ManyPickLists" src="~/UserControls/ManyPickLists.ascx" />
				<add tagPrefix="truc" tagName="MessageBox" src="~/UserControls/MessageBox.ascx" />
				<add tagPrefix="truc" tagName="MilestoneStatePickLists" src="~/UserControls/MilestoneStatePickLists.ascx" />
				<add tagPrefix="ha" namespace="HughAxton.WebControls" assembly="HughAxton.WebControls" />
				<add tagPrefix="telerik" namespace="Telerik.Web.UI" assembly="Telerik.Web.UI" />
				<add tagPrefix="manu" namespace="Manu.Web.UI.WebControls" assembly="ExtendedObjectDataSource" />
			</controls>
			<tagMapping>
				<add tagType="System.Web.UI.HtmlControls.HtmlGenericControl" mappedTagType="HughAxton.WebControls.ExtendedHtmlGenericControl" />
				<add tagType="System.Web.UI.HtmlControls.HtmlMeta" mappedTagType="HughAxton.WebControls.ExtendedHtmlMeta" />
				<add tagType="System.Web.UI.WebControls.BoundField" mappedTagType="TrackResults.Web.WebControls.ExtendedBoundField" />
				<add tagType="System.Web.UI.WebControls.Button" mappedTagType="HughAxton.WebControls.ExtendedButton" />
				<add tagType="System.Web.UI.WebControls.CheckBox" mappedTagType="TrackResults.Web.WebControls.ExtendedCheckBox" />
				<add tagType="System.Web.UI.WebControls.CheckBoxField" mappedTagType="TrackResults.Web.WebControls.ExtendedCheckBoxField" />
				<add tagType="System.Web.UI.WebControls.CheckBoxList" mappedTagType="HughAxton.WebControls.ExtendedCheckBoxList" />
				<add tagType="System.Web.UI.WebControls.CompareValidator" mappedTagType="HughAxton.WebControls.ExtendedCompareValidator" />
				<add tagType="System.Web.UI.WebControls.CustomValidator" mappedTagType="HughAxton.WebControls.ExtendedCustomValidator" />
				<add tagType="System.Web.UI.WebControls.DropDownList" mappedTagType="TrackResults.Web.WebControls.ModifyDropDownList" />
				<add tagType="System.Web.UI.WebControls.FormView" mappedTagType="HughAxton.WebControls.ExtendedFormView" />
				<add tagType="System.Web.UI.WebControls.HiddenField" mappedTagType="HughAxton.WebControls.ExtendedHiddenField" />
				<add tagType="System.Web.UI.WebControls.HyperLink" mappedTagType="TrackResults.Web.WebControls.ExtendedHyperLink" />
				<add tagType="System.Web.UI.WebControls.HyperLinkField" mappedTagType="TrackResults.Web.WebControls.ExtendedHyperLinkField" />
				<add tagType="System.Web.UI.WebControls.Image" mappedTagType="HughAxton.WebControls.ExtendedImage" />
				<add tagType="System.Web.UI.WebControls.Label" mappedTagType="HughAxton.WebControls.ExtendedLabel" />
				<add tagType="System.Web.UI.WebControls.LinkButton" mappedTagType="TrackResults.Web.WebControls.ExtendedLinkButton" />
				<add tagType="System.Web.UI.WebControls.ListView" mappedTagType="HughAxton.WebControls.ExtendedListView" />
				<add tagType="System.Web.UI.WebControls.Literal" mappedTagType="HughAxton.WebControls.ExtendedLiteral" />
				<add tagType="System.Web.UI.WebControls.Panel" mappedTagType="HughAxton.WebControls.ExtendedPanel" />
				<add tagType="System.Web.UI.WebControls.PlaceHolder" mappedTagType="HughAxton.WebControls.ExtendedPlaceHolder" />
				<add tagType="System.Web.UI.WebControls.RegularExpressionValidator" mappedTagType="HughAxton.WebControls.ExtendedRegularExpressionValidator" />
				<add tagType="System.Web.UI.WebControls.RequiredFieldValidator" mappedTagType="HughAxton.WebControls.ExtendedRequiredFieldValidator" />
				<add tagType="System.Web.UI.WebControls.TemplateField" mappedTagType="TrackResults.Web.WebControls.ExtendedTemplateField" />
				<add tagType="System.Web.UI.WebControls.TextBox" mappedTagType="TrackResults.Web.WebControls.ModifyTextBox" />
			</tagMapping>
		</pages>
		<globalization resourceProviderFactoryType="TrackResults.Web.Providers.Globalization.DatabaseResourceProviderFactory, TrackResults.Web" />
		<membership defaultProvider="AspNetSqlMembershipProvider">
			<providers>
				<clear />
				<add name="AspNetSqlMembershipProvider" type="System.Web.Security.SqlMembershipProvider" connectionStringName="TrackResults.BES.Properties.Settings.TrackResultsConnectionString" applicationName="TrackResults" requiresUniqueEmail="true" passwordFormat="Hashed" minRequiredPasswordLength="3" minRequiredNonalphanumericCharacters="0" enablePasswordRetrieval="false" enablePasswordReset="true" requiresQuestionAndAnswer="false" />
			</providers>
		</membership>
		<roleManager defaultProvider="ExtendedSqlRoleProvider" enabled="true" cacheRolesInCookie="true" cookieName=".ASPROLES" cookieTimeout="2880" cookiePath="/" cookieRequireSSL="false" cookieSlidingExpiration="true" cookieProtection="All">
			<providers>
				<clear />
				<add name="AspNetSqlRoleProvider" connectionStringName="TrackResults.BES.Properties.Settings.TrackResultsConnectionString" applicationName="TrackResults" type="System.Web.Security.SqlRoleProvider" />
				<add name="ExtendedSqlRoleProvider" connectionStringName="TrackResults.BES.Properties.Settings.TrackResultsConnectionString" applicationName="TrackResults" type="TrackResults.Web.Providers.ExtendedSqlRoleProvider" />
			</providers>
		</roleManager>
		<profile>
			<providers>
				<clear />
				<add name="AspNetSqlProfileProvider" connectionStringName="TrackResults.BES.Properties.Settings.TrackResultsConnectionString" applicationName="TrackResults" type="System.Web.Profile.SqlProfileProvider" />
				<add name="SecurityProfileProvider" type="TrackResults.Web.Providers.SecurityProfileProvider" />
			</providers>
			<properties>
				<add name="UserID" type="Int32" provider="SecurityProfileProvider" />
			</properties>
		</profile>
		<authentication mode="Forms">
			<forms loginUrl="~/Security/Login.aspx" timeout="30" protection="All" />
		</authentication>
		<authorization>
			<deny users="?" />
		</authorization>
	</system.web>
	<system.webServer>
		<security>
			<requestFiltering>
				<requestLimits maxAllowedContentLength="52428800" />
			</requestFiltering>
		</security>
		<modules runAllManagedModulesForAllRequests="true">
			<add name="EulaModule" type="TrackResults.Web.HttpModule.EulaModule, TrackResults.Web" preCondition="managedHandler" />
		</modules>
		<handlers>
			<add name="Telerik.Web.UI.WebResource" path="Telerik.Web.UI.WebResource.axd" verb="*" type="Telerik.Web.UI.WebResource, Telerik.Web.UI" />
			<remove name="ExtensionlessUrlHandler-Integrated-4.0" />
			<remove name="OPTIONSVerbHandler" />
			<remove name="TRACEVerbHandler" />
			<add name="ExtensionlessUrlHandler-Integrated-4.0" path="*." verb="*" type="System.Web.Handlers.TransferRequestHandler" preCondition="integratedMode,runtimeVersionv4.0" />
		</handlers>
		<httpProtocol>
			<customHeaders>
				<add name="X-Frame-Options" value="sameorigin" />
			</customHeaders>
		</httpProtocol>
	</system.webServer>
	<system.serviceModel>
		<behaviors>
			<serviceBehaviors>
				<behavior name="">
					<serviceMetadata httpGetEnabled="false" httpsGetEnabled="true" />
					<serviceDebug includeExceptionDetailInFaults="false" />
					<serviceCredentials>
						<userNameAuthentication membershipProviderName="AspNetSqlMembershipProvider" userNamePasswordValidationMode="MembershipProvider" />
					</serviceCredentials>
					<serviceAuthorization roleProviderName="AspNetSqlRoleProvider" principalPermissionMode="UseAspNetRoles" />
					<contractSecurityServiceBehavior>
						<add serviceContract="TrackResults.Web.ApiServices._25.ToursService">
							<add operationContract="GetPagedTours">
								<add role="ApiService" />
							</add>
							<add operationContract="GetPagedToursSinceLastUpdatedTime">
								<add role="ApiService" />
							</add>
							<add operationContract="GetNextPagedTours">
								<add role="ApiService" />
							</add>
							<add operationContract="GetTourByTourID">
								<add role="ApiService" />
							</add>
						</add>
						<add serviceContract="TrackResults.Web.ApiServices._26.ToursService">
							<add operationContract="GetPagedTours">
								<add role="ApiService" />
								<add role="ApiServiceReader" />
							</add>
							<add operationContract="GetPagedToursSinceLastUpdatedTime">
								<add role="ApiService" />
								<add role="ApiServiceReader" />
							</add>
							<add operationContract="GetNextPagedTours">
								<add role="ApiService" />
								<add role="ApiServiceReader" />
							</add>
							<add operationContract="GetTourByTourID">
								<add role="ApiService" />
								<add role="ApiServiceReader" />
							</add>
							<add operationContract="InsertTour">
								<add role="ApiService" />
							</add>
							<add operationContract="UpdateTour">
								<add role="ApiService" />
							</add>
						</add>
						<add serviceContract="TrackResults.Web.ApiServices._27.ToursService">
							<add operationContract="GetPagedTours">
								<add role="ApiService" />
								<add role="ApiServiceReader" />
							</add>
							<add operationContract="GetPagedToursSinceLastUpdatedTime">
								<add role="ApiService" />
								<add role="ApiServiceReader" />
							</add>
							<add operationContract="GetPagedToursSinceLastUpdatedTimeByToursCriteria">
								<add role="ApiService" />
								<add role="ApiServiceReader" />
							</add>
							<add operationContract="GetNextPagedTours">
								<add role="ApiService" />
								<add role="ApiServiceReader" />
							</add>
							<add operationContract="GetTourByTourID">
								<add role="ApiService" />
								<add role="ApiServiceReader" />
							</add>
							<add operationContract="InsertTour">
								<add role="ApiService" />
							</add>
							<add operationContract="UpdateTour">
								<add role="ApiService" />
							</add>
						</add>
						<add serviceContract="TrackResults.Web.ApiServices._28.ToursService">
							<add operationContract="GetPagedTours">
								<add role="ApiService" />
								<add role="ApiServiceReader" />
							</add>
							<add operationContract="GetPagedToursSinceLastUpdatedTime">
								<add role="ApiService" />
								<add role="ApiServiceReader" />
							</add>
							<add operationContract="GetNextPagedTours">
								<add role="ApiService" />
								<add role="ApiServiceReader" />
							</add>
							<add operationContract="GetTourByTourID">
								<add role="ApiService" />
								<add role="ApiServiceReader" />
							</add>
							<add operationContract="AddTour">
								<add role="ApiService" />
							</add>
						</add>
						<add serviceContract="TrackResults.Web.ApiServices._29.SystemService">
							<add operationContract="ApplicationMemoryServiceReset">
								<add role="ApiService" />
							</add>
							<add operationContract="ApplicationServiceResetCache">
								<add role="ApiService" />
							</add>
						</add>
						<add serviceContract="TrackResults.Web.ApiServices._29.ToursService">
							<add operationContract="GetPagedTours">
								<add role="ApiService" />
								<add role="ApiServiceReader" />
							</add>
							<add operationContract="GetPagedToursSinceLastUpdatedTime">
								<add role="ApiService" />
								<add role="ApiServiceReader" />
							</add>
							<add operationContract="GetPagedToursSinceLastUpdatedTimeByToursCriteria">
								<add role="ApiService" />
								<add role="ApiServiceReader" />
							</add>
							<add operationContract="GetNextPagedTours">
								<add role="ApiService" />
								<add role="ApiServiceReader" />
							</add>
							<add operationContract="GetTourByTourID">
								<add role="ApiService" />
								<add role="ApiServiceReader" />
							</add>
							<add operationContract="AddTour">
								<add role="ApiService" />
							</add>
							<add operationContract="CreateOrUpdateTour">
								<add role="ApiService" />
							</add>
							<add operationContract="CreateOrUpdateExternalTour">
								<add role="ApiService" />
							</add>
							<add operationContract="CreateOrUpdateCustomer">
								<add role="ApiService" />
							</add>
							<add operationContract="CreateOrUpdateExternalCustomer">
								<add role="ApiService" />
							</add>
							<add operationContract="CreateOrUpdatePurchase">
								<add role="ApiService" />
							</add>
							<add operationContract="CreateOrUpdateExternalPurchase">
								<add role="ApiService" />
							</add>
						</add>
						<add serviceContract="TrackResults.Web.ApiServices._29.ReportsService">
							<add operationContract="GetHomeReportsData">
								<add role="System Administrator" />
								<add role="ApiService" />
								<add role="ApiServiceReader" />
							</add>
						</add>
					</contractSecurityServiceBehavior>
				</behavior>
			</serviceBehaviors>
			<endpointBehaviors>
				<behavior name="">
					<flatWsdlBehavior />
				</behavior>
			</endpointBehaviors>
		</behaviors>
		<bindings>
			<basicHttpBinding>
				<binding name="" maxBufferSize="2048000" maxBufferPoolSize="524288" maxReceivedMessageSize="2048000">
					<security mode="TransportWithMessageCredential">
						<message clientCredentialType="UserName" />
						<transport clientCredentialType="None" />
					</security>
				</binding>
				<binding name="BasicHttpBinding_TrackResults29ToursService" closeTimeout="00:01:00" openTimeout="00:01:00" receiveTimeout="00:10:00" sendTimeout="00:01:00" allowCookies="false" bypassProxyOnLocal="false" hostNameComparisonMode="StrongWildcard" maxBufferSize="2048000" maxBufferPoolSize="524288" maxReceivedMessageSize="2048000" messageEncoding="Text" textEncoding="utf-8" transferMode="Buffered" useDefaultWebProxy="true">
					<readerQuotas maxDepth="32" maxStringContentLength="8192" maxArrayLength="16384" maxBytesPerRead="4096" maxNameTableCharCount="16384" />
					<security mode="TransportWithMessageCredential">
						<transport clientCredentialType="None" proxyCredentialType="None" realm="" />
						<message clientCredentialType="UserName" algorithmSuite="Default" />
					</security>
				</binding>
				<binding name="BasicHttpBinding_TimeShareWare1TrackResultsService" closeTimeout="00:01:00" openTimeout="00:01:00" receiveTimeout="00:01:00" sendTimeout="00:01:00" allowCookies="false" bypassProxyOnLocal="false" hostNameComparisonMode="StrongWildcard" maxBufferSize="2048000" maxBufferPoolSize="524288" maxReceivedMessageSize="2048000" messageEncoding="Text" textEncoding="utf-8" transferMode="Buffered" useDefaultWebProxy="true">
					<readerQuotas maxDepth="32" maxStringContentLength="8192" maxArrayLength="16384" maxBytesPerRead="4096" maxNameTableCharCount="16384" />
					<security mode="TransportWithMessageCredential">
						<transport clientCredentialType="None" proxyCredentialType="None" realm="" />
						<message clientCredentialType="UserName" algorithmSuite="Default" />
					</security>
				</binding>
			</basicHttpBinding>
		</bindings>
		<client>
			<endpoint address="https://localhost:44300/ApiServices/29/ToursService.svc" binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_TrackResults29ToursService" contract="TrackResults29ToursService.ToursService" name="BasicHttpBinding_TrackResults29ToursService" />
			<endpoint address="https://gbt.hosted.timeshareware.com/TrackResultsService/TrackResultsService.svc" binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_TimeShareWare1TrackResultsService" contract="TimeShareWare1TrackResultsService.ITrackResultsService" name="BasicHttpBinding_TimeShareWare1TrackResultsService" />
		</client>
		<extensions>
			<behaviorExtensions>
				<add name="flatWsdlBehavior" type="TrackResults.ServiceModel.Extensions.FlatWsdlBehavior, TrackResults.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=null" />
				<add name="contractSecurityServiceBehavior" type="TrackResults.ServiceModel.Extensions.ContractSecurityServiceBehavior, TrackResults.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=null" />
			</behaviorExtensions>
		</extensions>
		<serviceHostingEnvironment multipleSiteBindingsEnabled="true" />
		<services>
			<service name="TrackResults.Web.ApiServices._25.ToursService">
				<endpoint address="" binding="basicHttpBinding" bindingNamespace="trackresults.net/ApiServices/25" contract="TrackResults.Web.ApiServices._25.ToursService" />
			</service>
			<service name="TrackResults.Web.ApiServices._26.ToursService">
				<endpoint address="" binding="basicHttpBinding" bindingNamespace="trackresults.net/ApiServices/26" contract="TrackResults.Web.ApiServices._26.ToursService" />
			</service>
			<service name="TrackResults.Web.ApiServices._27.ToursService">
				<endpoint address="" binding="basicHttpBinding" bindingNamespace="trackresults.net/ApiServices/27" contract="TrackResults.Web.ApiServices._27.ToursService" />
			</service>
			<service name="TrackResults.Web.ApiServices._28.ToursService">
				<endpoint address="" binding="basicHttpBinding" bindingNamespace="trackresults.net/ApiServices/28" contract="TrackResults.Web.ApiServices._28.ToursService" />
			</service>
			<service name="TrackResults.Web.ApiServices._28.SystemService">
				<endpoint address="" binding="basicHttpBinding" bindingNamespace="trackresults.net/ApiServices/28" contract="TrackResults.Web.ApiServices._28.SystemService" />
			</service>
			<service name="TrackResults.Web.ApiServices._29.ToursService">
				<endpoint address="" binding="basicHttpBinding" bindingNamespace="trackresults.net/ApiServices/29" contract="TrackResults.Web.ApiServices._29.ToursService" />
			</service>
			<service name="TrackResults.Web.ApiServices._29.SystemService">
				<endpoint address="" binding="basicHttpBinding" bindingNamespace="trackresults.net/ApiServices/29" contract="TrackResults.Web.ApiServices._29.SystemService" />
			</service>
			<service name="TrackResults.Web.ApiServices._29.ReportsService">
				<endpoint address="" binding="basicHttpBinding" bindingNamespace="trackresults.net/ApiServices/29" contract="TrackResults.Web.ApiServices._29.ReportsService" />
			</service>
		</services>
	</system.serviceModel>
	<runtime>
		<gcServer enabled="true" />
		<assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.AspNet.Web.Optimization" publicKeyToken="31bf3856ad364e35" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Buffers" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
		</assemblyBinding>
		<assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
			<dependentAssembly>
				<!-- This is needed because the Telerik dependency on AWSSDK is newer than that used the BES project, if AWSSDK is updated this could be removed. -->
				<assemblyIdentity name="AWSSDK" publicKeyToken="9F476D3089B52BE3" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-2.0.5.0" newVersion="1.5.25.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="AWSSDK" publicKeyToken="9f476d3089b52be3" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-1.5.25.0" newVersion="1.5.25.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Web.Helpers" publicKeyToken="31bf3856ad364e35" />
				<bindingRedirect oldVersion="1.0.0.0-3.0.0.0" newVersion="3.0.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Web.WebPages" publicKeyToken="31bf3856ad364e35" />
				<bindingRedirect oldVersion="1.0.0.0-3.0.0.0" newVersion="3.0.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Web.Mvc" publicKeyToken="31bf3856ad364e35" />
				<bindingRedirect oldVersion="1.0.0.0-5.2.7.0" newVersion="5.2.7.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-13.0.0.0" newVersion="13.0.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="WebGrease" publicKeyToken="31bf3856ad364e35" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-1.5.2.14234" newVersion="1.5.2.14234" />
			</dependentAssembly>
		</assemblyBinding>
	</runtime>
	<loggingConfiguration name="Logging Application Block" tracingEnabled="true" defaultCategory="Critical" logWarningsWhenNoCategoriesMatch="true">
		<listeners>
			<add listenerDataType="Microsoft.Practices.EnterpriseLibrary.Logging.Configuration.CustomTraceListenerData, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" traceOutputOptions="None" type="TrackResults.Common.Logging.TraceListeners.DebugTraceListener, TrackResults.Common" name="Debug TraceListener" initializeData="" formatter="Custom Exception Formatter" />
			<add databaseInstanceName="LoggingConnectionString" writeLogStoredProcName="WriteLog" addCategoryStoredProcName="AddCategory" formatter="Custom Exception Formatter" listenerDataType="Microsoft.Practices.EnterpriseLibrary.Logging.Database.Configuration.FormattedDatabaseTraceListenerData, Microsoft.Practices.EnterpriseLibrary.Logging.Database, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" traceOutputOptions="None" type="Microsoft.Practices.EnterpriseLibrary.Logging.Database.FormattedDatabaseTraceListener, Microsoft.Practices.EnterpriseLibrary.Logging.Database, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" name="Database TraceListener" />
			<add source="Enterprise Library Logging" formatter="Custom Exception Formatter" log="Application" machineName="." listenerDataType="Microsoft.Practices.EnterpriseLibrary.Logging.Configuration.FormattedEventLogTraceListenerData, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" traceOutputOptions="None" type="Microsoft.Practices.EnterpriseLibrary.Logging.TraceListeners.FormattedEventLogTraceListener, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" name="EventLog TraceListener" />
		</listeners>
		<formatters>
			<add template="--------------------   {timestamp(local:dddd, MM/dd/yyyy hh:mm:ss tt)} --------------------&#xA;Title:{title}&#xA;Category: {category}&#xA;Message: {message}&#xA;&#xA;RequestUrl: {keyvalue(RequestUrl)}&#xA;UserHostAddress: {keyvalue(UserHostAddress)}&#xA;Username: {keyvalue(Username)}&#xA;&#xA;Exception.Message: {keyvalue(ExceptionMessage)}&#xA;Exception.Source: {keyvalue(ExceptionSource)}&#xA;Exception.StackTrace:&#xA;{keyvalue(ExceptionStackTrace)}&#xA;&#xA;Inner.Exception.Message: {keyvalue(InnerExceptionMessage)}&#xA;Inner.Exception.Source: {keyvalue(InnerExceptionSource)}&#xA;Inner.Exception.StackTrace:&#xA;{keyvalue(InnerExceptionStackTrace)}&#xA;&#xA;Machine: {machine}&#xA;Application Domain: {appDomain}&#xA;&#xA;ReferrerUrl: {keyvalue(ReferrerUrl)}&#xA;UserAgent: {keyvalue(UserAgent)}&#xA;&#xA;Process Id: {processId}&#xA;Process Name: {processName}&#xA;Win32 Thread Id: {win32ThreadId}&#xA;Thread Name: {threadName}&#xA;--------------------------------------------------------------------------------" type="Microsoft.Practices.EnterpriseLibrary.Logging.Formatters.TextFormatter, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" name="Custom Exception Formatter" />
		</formatters>
		<logFilters>
			<add enabled="true" type="Microsoft.Practices.EnterpriseLibrary.Logging.Filters.LogEnabledFilter, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" name="LogEnabled Filter" />
		</logFilters>
		<categorySources>
			<add switchValue="All" name="Critical">
				<listeners>
					<add name="Database TraceListener" />
				</listeners>
			</add>
			<add switchValue="All" name="CriticalDatabase">
				<listeners>
					<add name="Database TraceListener" />
				</listeners>
			</add>
			<add switchValue="All" name="Error">
				<listeners>
					<add name="Database TraceListener" />
				</listeners>
			</add>
			<add switchValue="All" name="Warning">
				<listeners>
					<add name="Database TraceListener" />
				</listeners>
			</add>
			<add switchValue="All" name="Information">
				<listeners>
					<add name="Database TraceListener" />
				</listeners>
			</add>
		</categorySources>
		<specialSources>
			<notProcessed switchValue="All" name="Unprocessed Category">
				<listeners>
					<add name="Database TraceListener" />
				</listeners>
			</notProcessed>
			<errors switchValue="All" name="Logging Errors &amp; Warnings">
				<listeners>
					<add name="EventLog TraceListener" />
				</listeners>
			</errors>
		</specialSources>
	</loggingConfiguration>
	<exceptionHandling>
		<exceptionPolicies>
			<add name="Global">
				<exceptionTypes>
					<add type="System.Exception, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" postHandlingAction="None" name="Exception">
						<exceptionHandlers>
							<add logCategory="Critical" eventId="1" severity="Critical" title="Global Policy System.Exception" priority="0" exceptionLogEntryType="TrackResults.Common.Logging.WebApplicationExceptionLogEntry, TrackResults.Common" type="TrackResults.Common.Exceptions.Handlers.ExceptionLogLoggingExceptionHandler, TrackResults.Common" name="ExceptionLogLoggingExceptionHandler" />
							<add type="TrackResults.Web.Exceptions.Handlers.DefaultMessageHandler, TrackResults.Web" name="DefaultMessageHandler" />
						</exceptionHandlers>
					</add>
					<add type="System.Web.HttpUnhandledException, System.Web, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" postHandlingAction="None" name="HttpUnhandledException">
						<exceptionHandlers>
							<add type="TrackResults.Web.Exceptions.Handlers.WrappedExceptionHandler, TrackResults.Web" name="WrappedExceptionHandler" />
						</exceptionHandlers>
					</add>
					<add type="System.Web.HttpException, System.Web, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" postHandlingAction="None" name="HttpException">
						<exceptionHandlers>
							<add type="TrackResults.Web.Exceptions.Handlers.HttpExceptionHandler, TrackResults.Web" name="HttpExceptionHandler" />
						</exceptionHandlers>
					</add>
					<add type="System.Web.HttpParseException, System.Web, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" postHandlingAction="None" name="HttpParseException">
						<exceptionHandlers>
							<add logCategory="Critical" eventId="1" severity="Critical" title="Global Policy System.Web.HttpParseException" priority="0" exceptionLogEntryType="TrackResults.Common.Logging.WebApplicationExceptionLogEntry, TrackResults.Common" type="TrackResults.Common.Exceptions.Handlers.ExceptionLogLoggingExceptionHandler, TrackResults.Common" name="ExceptionLogLoggingExceptionHandler" />
							<add type="TrackResults.Web.Exceptions.Handlers.DefaultMessageHandler, TrackResults.Web" name="DefaultMessageHandler" />
						</exceptionHandlers>
					</add>
					<add type="System.Data.SqlClient.SqlException, System.Data, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" postHandlingAction="None" name="SqlException">
						<exceptionHandlers>
							<add logCategory="CriticalDatabase" eventId="1" severity="Critical" title="Global Policy System.Data.SqlClient.SqlException" priority="0" exceptionLogEntryType="TrackResults.Common.Logging.WebApplicationExceptionLogEntry, TrackResults.Common" type="TrackResults.Common.Exceptions.Handlers.ExceptionLogLoggingExceptionHandler, TrackResults.Common" name="ExceptionLogLoggingExceptionHandler" />
							<add type="TrackResults.Web.Exceptions.Handlers.DefaultMessageHandler, TrackResults.Web" name="DefaultMessageHandler" />
						</exceptionHandlers>
					</add>
					<add type="TrackResults.Common.Exceptions.PageNotFoundException, TrackResults.Common" postHandlingAction="None" name="PageNotFoundException">
						<exceptionHandlers>
							<add type="TrackResults.Web.Exceptions.Handlers._404Handler, TrackResults.Web" name="_404Handler" />
						</exceptionHandlers>
					</add>
					<add type="TrackResults.Common.Exceptions.UnauthorizedException, TrackResults.Common" postHandlingAction="None" name="UnauthorizedException">
						<exceptionHandlers>
							<add type="TrackResults.Web.Exceptions.Handlers.UnauthorizedHandler, TrackResults.Web" name="UnauthorizedHandler" />
						</exceptionHandlers>
					</add>
				</exceptionTypes>
			</add>
		</exceptionPolicies>
	</exceptionHandling>
	<quartz>
		<add key="quartz.threadPool.threadCount" value="1" />
	</quartz>
	<location path="Security">
		<system.web>
			<authorization>
				<allow users="*" />
			</authorization>
		</system.web>
	</location>
	<location path="login">
		<system.web>
			<authorization>
				<allow users="*" />
			</authorization>
		</system.web>
	</location>
	<location path="Errors">
		<system.web>
			<authorization>
				<allow users="*" />
			</authorization>
		</system.web>
	</location>
	<location path="ScriptServices">
		<system.web>
			<authorization>
				<allow users="*" />
			</authorization>
		</system.web>
	</location>
	<location path="favicon.ico">
		<system.web>
			<pages enableSessionState="false" />
			<authorization>
				<allow users="*" />
			</authorization>
		</system.web>
	</location>
	<location path="_Test">
		<system.web>
			<authorization>
				<allow roles="System Administrator" />
				<deny users="*" />
			</authorization>
		</system.web>
	</location>
	<location path="Administration/System">
		<system.web>
			<authorization>
				<allow roles="System Administrator" />
				<deny users="*" />
			</authorization>
		</system.web>
	</location>
	<location path="Reports/adhocadmin.aspx">
		<system.web>
			<authorization>
				<allow roles="System Administrator" />
				<deny users="*" />
			</authorization>
		</system.web>
	</location>
</configuration>