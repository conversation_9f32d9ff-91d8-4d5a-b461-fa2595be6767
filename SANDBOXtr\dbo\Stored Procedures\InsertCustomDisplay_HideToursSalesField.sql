﻿
CREATE PROCEDURE [dbo].[InsertCustomDisplay_HideToursSalesField]
(
	@GroupType<PERSON>ey nvarchar(64),
	@UIFieldNameCamel nvarchar(64),
	@UIFieldNamePascal nvarchar(64),
	@DatabaseFieldNameCamel nvarchar(64),
	@DatabaseFieldNamePascalPlural nvarchar(64),
	@UseNameCode bit
)
AS
BEGIN

	DECLARE @controlID nvarchar(64)
	DECLARE @controlDelegates nvarchar(4000)
	
	DELETE FROM CustomPageControlDisplayRights WHERE (customPageControlDisplayRightGroupTypeKey = @GroupTypeKey)

	DECLARE @Add int SET @Add = 1
	DECLARE @Remove int SET @Remove = 2

	DECLARE @Insert int SET @Insert = 1
	DECLARE @Select int SET @Select = 2
	DECLARE @Update int SET @Update = 3

	DECLARE @Visible int SET @Visible = 1
	DECLARE @Enabled int SET @Enabled = 2
	DECLARE @Text int SET @Text = 3
	DECLARE @Delegate int SET @Delegate = 4
	DECLARE @Authorized int SET @Authorized = 5
	DECLARE @Administrated int SET @Administrated = 6

	DECLARE @Name nvarchar(64) SET @Name = ''

	IF @UseNameCode = 1
		SET @Name = 'Name'

	DECLARE @PagePathID [varchar] (256)

	SET @PagePathID = 'ASP.customers_tours_usercontrols_sales_ascx_editlistview'

	SET @controlID = 'tr' + @UIFieldNamePascal
	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Insert,@controlID,@Visible,0,NULL,NULL
	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,@controlID,@Visible,0,NULL,NULL
	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Update,@controlID,@Visible,0,NULL,NULL

	SET @PagePathID = 'ASP.customers_tours_usercontrols_sales_ascx_itemListView'

	SET @controlID = 'tr' + @UIFieldNamePascal
	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Insert,@controlID,@Visible,0,NULL,NULL
	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,@controlID,@Visible,0,NULL,NULL
	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Update,@controlID,@Visible,0,NULL,NULL

	SET @PagePathID = 'ASP.reports_usercontrols_toursearchcriteria_ascx'

	SET @controlID = 'tr' + @UIFieldNamePascal
	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,@controlID,@Visible,0,NULL,NULL

	SET @PagePathID = 'ASP.reports_tourreport_aspx'

	SET @controlID = 'legend' + @UIFieldNamePascal + 'Area'
	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,@controlID,@Visible,0,NULL,NULL

	SET @PagePathID = 'ASP.reports_tourreport_aspx_gridView'

	SET @controlID = @UIFieldNameCamel + 'Area'
	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,@controlID,@Visible,0,NULL,NULL

	SET @PagePathID = 'ASP.reports_salestourreport_aspx'

	SET @controlID = 'legend' + @UIFieldNamePascal + 'Area'
	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,@controlID,@Visible,0,NULL,NULL

	SET @PagePathID = 'ASP.reports_salestourreport_aspx_gridView'

	SET @controlID = @UIFieldNameCamel + 'Area'
	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,@controlID,@Visible,0,NULL,NULL

	SET @PagePathID = 'ASP.reports_usercontrols_reportby_ascx'

	SET @controlDelegates = 'TrackResults.Web.Security.MenuDisplay.ReportByTypes,TrackResults.Web:Remove(TrackResults.BES.Data.Types.ReportByType ' + @DatabaseFieldNamePascalPlural + ')'
	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'menuReportByType',@Delegate,NULL,NULL,@controlDelegates

	SET @PagePathID = 'TrackResults.BES.Services.ImportExportService'
	
	SET @controlDelegates = 'TrackResults.BES.Security.CollectionRules:RemoveKeyValuePairByStringKey(System.String ' + @DatabaseFieldNameCamel + @Name + ')'
	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'columnKeyDisplayNames',@Delegate,NULL,NULL,@controlDelegates

	IF @UseNameCode = 1
	BEGIN
		SET @controlDelegates = 'TrackResults.BES.Security.CollectionRules:RemoveKeyValuePairByStringKey(System.String ' + @DatabaseFieldNameCamel + 'Code)'
		EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'columnKeyDisplayNames',@Delegate,NULL,NULL,@controlDelegates
	END

END