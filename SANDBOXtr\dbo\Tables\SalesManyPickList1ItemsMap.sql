﻿CREATE TABLE [dbo].[SalesManyPickList1ItemsMap] (
    [salesManyPickList1ItemMapID] INT IDENTITY (1, 1) NOT NULL,
    [tourID]                      INT NOT NULL,
    [salesManyPickList1ItemID]    INT NOT NULL,
    CONSTRAINT [PK_SalesManyPickList1ItemsMap] PRIMARY KEY CLUSTERED ([salesManyPickList1ItemMapID] ASC),
    CONSTRAINT [FK_SalesManyPickList1ItemsMap_salesManyPickList1ItemID] FOREIGN KEY ([salesManyPickList1ItemID]) REFERENCES [dbo].[SalesManyPickList1Items] ([salesManyPickList1ItemID]),
    CONSTRAINT [FK_SalesManyPickList1ItemsMap_tourID] FOREIGN KEY ([tourID]) REFERENCES [dbo].[Tours] ([tourID])
);

