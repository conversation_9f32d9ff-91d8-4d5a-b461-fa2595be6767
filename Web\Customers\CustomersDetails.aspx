<%@ Page Language="C#" MasterPageFile="~/MasterPages/Default.Master" CodeBehind="CustomersDetails.aspx.cs" Inherits="TrackResults.Web.Customers.CustomersDetails"
	EnablePartialRendering="True" EnableServerSidePagePersister="true" %>
	
<%@ Register Src="UserControls/DetailsCommands.ascx" TagName="DetailsCommands" TagPrefix="uc" %>
<%@ Register Src="UserControls/CustomersDetails.ascx" TagName="CustomersDetails" TagPrefix="uc" %>
<%@ Register Src="Tours/UserControls/ToursMaster.ascx" TagName="ToursMaster" TagPrefix="uc" %>
<%@ Register Src="Tours/UserControls/ToursDetailsLeads.ascx" TagName="ToursDetailsLeads" TagPrefix="uc" %>
<%@ Register Src="Tours/UserControls/ToursDetailsCalls.ascx" TagName="ToursDetailsCalls" TagPrefix="uc" %>
<%@ Register Src="Tours/UserControls/ToursDetailsTours.ascx" TagName="ToursDetailsTours" TagPrefix="uc" %>
<%@ Register Src="Tours/UserControls/ToursDetailsMarketing.ascx" TagName="ToursDetailsMarketing" TagPrefix="uc" %>
<%@ Register Src="Tours/UserControls/ToursDetailsSales.ascx" TagName="ToursDetailsSales" TagPrefix="uc" %>
<%@ Register Src="Tours/UserControls/ToursDetailsExit.ascx" TagName="ToursDetailsExit" TagPrefix="uc" %>
<%@ Register Src="Tours/UserControls/ToursDetailsForm.ascx" TagName="ToursDetailsForm" TagPrefix="uc" %>
<%@ Register Src="Purchases/UserControls/PurchasesList.ascx" TagName="PurchasesList" TagPrefix="uc" %>
<%@ Register Src="UserControls/Audits.ascx" TagName="Audits" TagPrefix="uc" %>

<asp:Content ID="c1Content" ContentPlaceHolderID="c1" runat="server">
		<script type="text/javascript" src="customers_popup.js"></script>
		<script>

        </script>
		<link href="customers_popup.css" rel="stylesheet" />
    <asp:PlaceHolder ID="toursCommandsPlaceHolder" runat="server" Visible="false">
	
		<div class="stickyMostTopContainer">
		<div class="band band-white-space-small gray-lighter-background">
		<div class="container">
		<div class="row">
		<div class="col-xs-offset-6 col-xs-6">

			<uc:DetailsCommands ID="toursCommands" runat="server" PropertyKey="TierOne.Tour" DisableEditCommands="true" DisableStickyTop="true" />

		</div>
		</div>
		</div>
		</div>
		</div>

    </asp:PlaceHolder>

	<div id="myModal" class="modal">
		<div class="modal-dialog" role="document">
            <div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title" id="exampleModalLabel">System Message</h5>
					<span class="close" onclick="closeModal()">&times;</span>
				</div>
				<div class="modal-body" id="message_body_generic">
					<p id="message_generic"></p>
				</div>
				<div class="modal-body" id="message_body_sync">
					<p>The previous value: <b id="valueProperty"></b> was imported: <b id="valueDays"></b> ago.</p>
					<p>Do you want to update the latest score through API?</p>
				</div>
                <div class="modal-footer" id="message_footer_sync">
					<button onclick="executeAction('api')" class="btn btn-primary">Yes</button>
					<button onclick="executeAction('db')" class="btn btn-secondary">No, reuse the same one</button>
				</div>
				<div class="modal-footer" id="message_footer_generic">
					<button onclick="closeModal()" class="btn btn-secondary">Close</button>
				</div>
            </div>
		</div>
    </div>

    <div class="band band-white-space">
    <div class="container">
    <div class="row">
    <div class="col-xs-6">

        <div class="panel panel-default">
	        <div class="panel-heading">
						
				<trwc:FeaturePlaceHolder runat="server" FeatureKey="Customer.DoNotCall">
					<div class="pull-right" id="divmultiViewDoNotCall" runat="server" visible="true">
						<asp:MultiView ID="multiViewDoNotCall" ActiveViewIndex="0" runat="server">
							<asp:View ID="viewAddDoNotCall" runat="server">
								<trwc:ExtendedLinkButton ID="linkAddDoNotCall" runat="server" CssClass="smaller" CausesValidation="false" OnClick="linkAddDoNotCall_Click">
									Add <asp:Literal runat="server" Text="<%$ Resources: resources,Customer.DoNotCall %>" />
								</trwc:ExtendedLinkButton></asp:View><asp:View ID="viewDoNotCall" runat="server">
								<span class="highlightAlternate"><asp:Literal runat="server" Text="<%$ Resources: resources,Customer.DoNotCall %>" /></span>
							</asp:View>
						</asp:MultiView>
						<div  style="float:left;">
							<i class="fa fa-plus-square" aria-hidden="true" style="font-size: 18px;margin-right: 4px;"></i>
						</div>
					</div>
				</trwc:FeaturePlaceHolder>

				<asp:Literal runat="server" Text="<%$ Resources: resources,TierOne.Customer %>" />
				
				<div class="pull-right" style="margin-right: 10px;" id="divlinkSwitchCustomer" runat="server" visible="true">
					<trwc:ExtendedLinkButton ID="linkSwitchCustomer" runat="server" CssClass="smaller" CausesValidation="false" OnClick="linkSwitchCustomer_Click" OnClientClick="showSpinner(1)">
						Switch Customer
					</trwc:ExtendedLinkButton>
					<div id="spinner" style="float:left; display:none">
						<i class="fa fa-spinner fa-spin" style="font-size: 18px;margin-right: 4px;"></i>
					</div>
					<div id="retweet" style="float:left; display:block">
						<i class="fa fa-retweet" style="font-size: 18px;margin-right: 4px;"></i>
					</div>
				</div>

				<div class="pull-right" style="margin-right: 10px;" id="divlinkCheckDoNotCall" runat="server" visible="true">
					<trwc:ExtendedLinkButton ID="linkCheckDoNotCall" runat="server" CssClass="smaller" CausesValidation="false" Onclick="linkCheckDoNotCall_Click">
									Check Do not call
					</trwc:ExtendedLinkButton>
					<div  style="float:left;">
						<i class="fa fa-exclamation-circle" aria-hidden="true" style="font-size: 18px;margin-right: 4px;"></i>
					</div>
				</div>

	        </div>            
	        <div class="panel-body sectionTopHalf-inner">
			
				<asp:UpdatePanel runat="server" UpdateMode="Conditional">
				<ContentTemplate>

	                <asp:PlaceHolder ID="customersDetailsCommandsPlaceHolder" runat="server">
		                <uc:DetailsCommands ID="customersDetailsCommands" runat="server" PropertyKey="TierOne.Customer" IDFeatureKey="Customer.CustomerID"
			                ExternalIDFeatureKey="Customer.ExternalCustomerID" EditPropertyKey="TierOne.EditCustomer" DeletePropertyKey="TierOne.DeleteCustomer"
			                DeleteNamePropertyKey="TierOne.Customer" />
	                </asp:PlaceHolder>

                   <uc:CustomersDetails ID="customersDetails" runat="server" />
					
				</ContentTemplate>
				</asp:UpdatePanel>
                
	        </div>        
        </div>
				
        <trwc:FeaturePlaceHolder runat="server" FeatureKey="TierOne.TourSectionLeads" OrFeatureKey="TierOne.TourSectionCalls">
	        <div class="panel panel-default">
		        <div class="panel-heading">
			        <asp:Literal ID="leadsCallsHeaderLiteral" runat="server" /> Information </div><div class="panel-body sectionTopHalf-inner">

		        <asp:UpdatePanel runat="server" UpdateMode="Conditional">
				<ContentTemplate>
		
		            <asp:PlaceHolder ID="toursDetailsLeadsCallsCommandsPlaceHolder" runat="server">
			            <uc:DetailsCommands ID="toursDetailsLeadsCallsCommands" runat="server" PropertyKey="TierOne.TourSectionLeads"
				            OrPropertyKey="TierOne.TourSectionCalls" ExternalIDFeatureKey="Tour.ExternalLeadID" EditPropertyKey="TierOne.EditTourSectionLeads"
				            OrEditPropertyKey="TierOne.EditTourSectionCalls" />
		            </asp:PlaceHolder>						

			        <trwc:FeaturePlaceHolder runat="server" FeatureKey="TierOne.TourSectionLeads">
				        <uc:ToursDetailsLeads ID="toursDetailsLeads" runat="server" />
			        </trwc:FeaturePlaceHolder>
				
			        <trwc:FeaturePlaceHolder runat="server" FeatureKey="TierOne.TourSectionCalls">
				        <uc:ToursDetailsCalls ID="toursDetailsCalls" runat="server" />
			        </trwc:FeaturePlaceHolder>
					
				</ContentTemplate>
		        </asp:UpdatePanel>
                
	        </div>        
	        </div>
        </trwc:FeaturePlaceHolder>
			
			
        <trwc:FeaturePlaceHolder runat="server" FeatureKey="TierOne.ViewMultiTours">
        <asp:UpdatePanel ID="toursMasterUpdatePanel" runat="server" UpdateMode="Conditional">
		<ContentTemplate>

	        <uc:ToursMaster ID="toursMaster" runat="server" />

		</ContentTemplate>
		</asp:UpdatePanel>
        </trwc:FeaturePlaceHolder>
        	
        <trwc:FeaturePlaceHolder runat="server" FeatureKey="TierOne.Purchase">
        <asp:PlaceHolder ID="purchasesListPlaceHolder" runat="server">
		<asp:UpdatePanel runat="server" UpdateMode="Conditional">
		<ContentTemplate>

	        <uc:PurchasesList ID="purchasesList" runat="server" />
			
		</ContentTemplate>
		</asp:UpdatePanel>
        </asp:PlaceHolder>
        </trwc:FeaturePlaceHolder>



    </div>
    <div class="col-xs-6"> 



        <asp:PlaceHolder ID="rescheduledToPlaceHolder" runat="server" Visible="false">
		    <div class="alert alert-danger">
			    This <asp:Literal runat="server" Text="<%$ Resources: resources,TierOne.Tour %>" /> was Rescheduled to <asp:Literal runat="server" Text="<%$ Resources: resources,Tour.TourID %>" />: <asp:Literal ID="rescheduledToTourIDLiteral" runat="server" />
		    </div>
        </asp:PlaceHolder>
				
        <trwc:FeaturePlaceHolder runat="server" FeatureKey="TierOne.TourSectionTours" OrFeatureKey="TierOne.TourSectionMarketing">
	        <div class="panel panel-default">
		        <div class="panel-heading">
					<trwc:FeaturePlaceHolder runat="server" FeatureKey="IntegrationFeatures.SyncTour">
						<div class="pull-right">
							<asp:MultiView ID="multiViewSync" ActiveViewIndex="0" runat="server">
								<asp:View ID="viewSync" runat="server">
									<trwc:ExtendedLinkButton ID="linkSyncTour" runat="server" CssClass="smaller" CausesValidation="false" OnClick="linkSyncTour_Click">
										<i class="fa fa-refresh icon-large"></i> <asp:Literal runat="server" Text="<%$ Resources: resources,IntegrationFeatures.SyncTour %>" />
									</trwc:ExtendedLinkButton></asp:View></asp:MultiView></div></trwc:FeaturePlaceHolder><asp:Literal ID="toursMarketingHeaderLiteral" runat="server" /> Information </div><div class="panel-body sectionTopHalf-inner">
		        <asp:UpdatePanel runat="server" UpdateMode="Conditional">
				<ContentTemplate>
		
		            <asp:PlaceHolder ID="toursDetailsToursMarketingCommandsPlaceHolder" runat="server">
			            <uc:DetailsCommands ID="toursDetailsToursMarketingCommands" runat="server" PropertyKey="TierOne.TourSectionTours"
				            OrPropertyKey="TierOne.TourSectionMarketing" IDFeatureKey="Tour.TourID" ExternalIDFeatureKey="Tour.ExternalTourID"
				            EditPropertyKey="TierOne.EditTourSectionTours" OrEditPropertyKey="TierOne.EditTourSectionMarketing"
				            DeletePropertyKey="TierOne.DeleteTour" DeleteNamePropertyKey="TierOne.Tour" />
		            </asp:PlaceHolder>
				
			        <trwc:FeaturePlaceHolder runat="server" FeatureKey="TierOne.TourSectionTours">
				        <uc:ToursDetailsTours ID="toursDetailsTours" runat="server" />
			        </trwc:FeaturePlaceHolder>

			        <trwc:FeaturePlaceHolder runat="server" FeatureKey="TierOne.TourSectionMarketing">
				        <uc:ToursDetailsMarketing ID="toursDetailsMarketing" runat="server" />
			        </trwc:FeaturePlaceHolder>
					
				</ContentTemplate>
		        </asp:UpdatePanel>
                
	        </div>
	        </div>
        </trwc:FeaturePlaceHolder>
			
        <trwc:FeaturePlaceHolder runat="server" FeatureKey="TierOne.TourSectionSales">
			<asp:PlaceHolder ID="tourSectionSalesPlaceHolder" runat="server">
				<div class="panel panel-default">
					<div class="panel-heading">
						<asp:Literal runat="server" Text="<%$ Resources: resources,TierOne.TourSectionSales %>" /> Information </div><div class="panel-body sectionTopHalf-inner">		

						<asp:UpdatePanel runat="server" UpdateMode="Conditional">
						<ContentTemplate>

							<asp:PlaceHolder ID="toursDetailsSalesCommandsPlaceHolder" runat="server">
								<uc:DetailsCommands ID="toursDetailsSalesCommands" runat="server" PropertyKey="TierOne.TourSectionSales"
									EditPropertyKey="TierOne.EditTourSectionSales" />
							</asp:PlaceHolder>

							<uc:ToursDetailsSales ID="toursDetailsSales" runat="server" />
					
						</ContentTemplate>
						</asp:UpdatePanel>
                
					</div>
				</div>
			</asp:PlaceHolder>
        </trwc:FeaturePlaceHolder>

        <trwc:FeaturePlaceHolder runat="server" FeatureKey="TierOne.TourSectionExit">
        <asp:PlaceHolder ID="tourSectionExitPlaceHolder" runat="server">
	        <div class="panel panel-default">
		        <div class="panel-heading">
			        <asp:Literal runat="server" Text="<%$ Resources: resources,TierOne.TourSectionExit %>" /> Information </div><div class="panel-body sectionTopHalf-inner">		

		        <asp:UpdatePanel runat="server" UpdateMode="Conditional">
				<ContentTemplate>

		            <asp:PlaceHolder ID="toursDetailsExitCommandsPlaceHolder" runat="server">
			            <uc:DetailsCommands ID="toursDetailsExitCommands" runat="server" PropertyKey="TierOne.TourSectionExit"
				            EditPropertyKey="TierOne.EditTourSectionExit" />
		            </asp:PlaceHolder>

			        <uc:ToursDetailsExit ID="toursDetailsExit" runat="server" />
					
				</ContentTemplate>
		        </asp:UpdatePanel>
                
	        </div>
	        </div>
        </asp:PlaceHolder>
        </trwc:FeaturePlaceHolder>

        <trwc:FeaturePlaceHolder runat="server" FeatureKey="TierOne.TourSectionTourForm">
        <asp:PlaceHolder ID="tourSectionTourFormPlaceHolder" runat="server">
	        <div class="panel panel-default">
		        <div class="panel-heading">
			        <asp:Literal runat="server" Text="<%$ Resources: resources,TierOne.TourSectionTourForm %>" /> Information </div><div class="panel-body sectionTopHalf-inner">		

		        <asp:UpdatePanel runat="server" UpdateMode="Conditional">
				<ContentTemplate>

		            <asp:PlaceHolder ID="toursDetailsFormPlaceHolder" runat="server">
			            <uc:DetailsCommands ID="toursDetailsFormCommands" runat="server" PropertyKey="TierOne.TourSectionTourForm"
				            EditPropertyKey="TierOne.EditTourSectionTourForm" />
		            </asp:PlaceHolder>

			        <uc:ToursDetailsForm ID="toursDetailsForm" runat="server" />
					
				</ContentTemplate>
		        </asp:UpdatePanel>
                
	        </div>
	        </div>
        </asp:PlaceHolder>
        </trwc:FeaturePlaceHolder>
			
        <trwc:FeaturePlaceHolder runat="server" FeatureKey="Tour.ImportNumber">
        <asp:PlaceHolder ID="importNumberPlaceHolder" runat="server" Visible="false">
	        <div class="panel panel-default">
	        <div class="panel-body">
				<div class="form-horizontal">
					<div class="form-group">
						<label class="col-xs-4 control-label">
							<asp:Literal runat="server" Text="<%$ Resources: resources,Tour.ImportNumber %>" />
						</label>
						<div class="col-xs-8 control-literal">
							<asp:Literal ID="importNumberLiteral" runat="server" />
						</div>
					</div>
				</div>
	        </div>
	        </div>
        </asp:PlaceHolder>
        </trwc:FeaturePlaceHolder>
			
        <trwc:FeaturePlaceHolder runat="server" FeatureKey="TierOne.AuditHistory">
        <asp:PlaceHolder ID="auditsPlaceHolder" runat="server">
		<asp:UpdatePanel runat="server" UpdateMode="Conditional">
		<ContentTemplate>

		        <uc:Audits ID="audits" runat="server" />
				
		</ContentTemplate>
		</asp:UpdatePanel>
        </asp:PlaceHolder>
        </trwc:FeaturePlaceHolder>

    </div>
    </div>
    </div>
    </div>

</asp:Content>


<asp:Content ID="scriptContent" ContentPlaceHolderID="sc" runat="server">

	<%-- This invisible control is to preload the styles for combobox so that the order is set correctly for the overridden styles --%>
	<div class="none">
		<trwc:DynamicLoadComboBox runat="server" />
	</div>

	<script src="/content/d/scripts/many-picklists-8.js" type="text/javascript"></script>

</asp:Content>