﻿CREATE TABLE [dbo].[ContactStatusTypes] (
    [contactStatusTypeID]   INT           IDENTITY (1, 1) NOT NULL,
    [contactStatusTypeName] NVARCHAR (64) NOT NULL,
    [contactStatusTypeCode] NVARCHAR (64) NOT NULL,
    [sortOrder]             INT           NOT NULL,
    [active]                BIT           NOT NULL,
    CONSTRAINT [PK_ContactStatusTypes] PRIMARY KEY CLUSTERED ([contactStatusTypeID] ASC),
    CONSTRAINT [UK_ContactStatusTypes_contactStatusTypeName] UNIQUE NONCLUSTERED ([contactStatusTypeName] ASC)
);


GO
CREATE TRIGGER [dbo].[ContactStatusTypes.InsertUpdateDelete]
    ON [dbo].[ContactStatusTypes]
    FOR INSERT, UPDATE, DELETE
AS 
BEGIN
    SET NOCOUNT ON;

    DECLARE @Activity  NVARCHAR (8), @id INT;

    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'updated'
    END

    IF EXISTS (SELECT * FROM inserted) AND NOT EXISTS(SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'inserted'
    END

    IF EXISTS (SELECT * FROM deleted) AND NOT EXISTS(SELECT * FROM inserted)
    BEGIN
        SET @Activity = 'deleted'
    END

    

    IF (@Activity = 'deleted')
    BEGIN
        DECLARE cur CURSOR FOR SELECT contactStatusTypeID FROM deleted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'ContactStatusTypes', @id, 'contactStatusTypeID',
                (
                    SELECT *
                    FROM deleted i 
                    WHERE i.contactStatusTypeID =  @id
                    FOR JSON AUTO
                ),0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
    ELSE
    BEGIN
        DECLARE cur CURSOR
        FOR SELECT contactStatusTypeID
        FROM inserted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'ContactStatusTypes', @id, 'contactStatusTypeID',
                (
                    SELECT *
                    FROM inserted i 
                    WHERE i.contactStatusTypeID =  @id
                    FOR JSON AUTO
                ), 0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
END

GO
DISABLE TRIGGER [dbo].[ContactStatusTypes.InsertUpdateDelete]
    ON [dbo].[ContactStatusTypes];

