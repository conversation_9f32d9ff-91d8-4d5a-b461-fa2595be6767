﻿

CREATE FUNCTION dbo.GetConcantenatedNotesCancellations
(
	@ReferenceID int,
	@Delimeter nvarchar(8)
)
RETURNS nvarchar(max)
AS
BEGIN

	DECLARE @ConcantenatedList nvarchar(max)
	SET @ConcantenatedList = ''

	SELECT @ConcantenatedList = @ConcantenatedList + @Delimeter + noteText + ' -' + CONVERT(char(5), NotesCancellations.insertTimeStamp, 101) +
		' ' + ISNULL(Users.firstName,'') + ' ' + ISNULL(Users.lastName,'')
	FROM NotesCancellations LEFT OUTER JOIN
		Users ON NotesCancellations.userID = Users.userID
	WHERE referenceID = @ReferenceID
	ORDER BY noteID

	IF DATALENGTH(@ConcantenatedList) > 0
	BEGIN
		SET @ConcantenatedList = RIGHT(@ConcantenatedList, ((DATALENGTH(@ConcantenatedList)/2) - (DATALENGTH(@Delimeter)/2)))
	END

	RETURN @ConcantenatedList

END