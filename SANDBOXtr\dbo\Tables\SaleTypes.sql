﻿CREATE TABLE [dbo].[SaleTypes] (
    [saleTypeID]   INT           NOT NULL,
    [saleTypeName] NVARCHAR (64) NOT NULL,
    [saleTypeCode] NVARCHAR (64) NULL,
    [sortOrder]    INT           NOT NULL,
    CONSTRAINT [PK_SaleTypes] PRIMARY KEY CLUSTERED ([saleTypeID] ASC),
    CONSTRAINT [UK_SaleTypes_saleTypeName] UNIQUE NONCLUSTERED ([saleTypeName] ASC)
);


GO
CREATE TRIGGER [dbo].[SaleTypes.InsertUpdateDelete]
    ON [dbo].[SaleTypes]
    FOR INSERT, UPDATE, DELETE
AS 
BEGIN
    SET NOCOUNT ON;

    DECLARE @Activity  NVARCHAR (8), @id INT;

    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'updated'
    END

    IF EXISTS (SELECT * FROM inserted) AND NOT EXISTS(SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'inserted'
    END

    IF EXISTS (SELECT * FROM deleted) AND NOT EXISTS(SELECT * FROM inserted)
    BEGIN
        SET @Activity = 'deleted'
    END

    

    IF (@Activity = 'deleted')
    BEGIN
        DECLARE cur CURSOR FOR SELECT saleTypeID FROM deleted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'SaleTypes', @id, 'saleTypeID',
                (
                    SELECT *
                    FROM deleted i 
                    WHERE i.saleTypeID =  @id
                    FOR JSON AUTO
                ),0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
    ELSE
    BEGIN
        DECLARE cur CURSOR
        FOR SELECT saleTypeID
        FROM inserted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'SaleTypes', @id, 'saleTypeID',
                (
                    SELECT *
                    FROM inserted i 
                    WHERE i.saleTypeID =  @id
                    FOR JSON AUTO
                ), 0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
END

GO
DISABLE TRIGGER [dbo].[SaleTypes.InsertUpdateDelete]
    ON [dbo].[SaleTypes];

