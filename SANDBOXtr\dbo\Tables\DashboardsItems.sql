﻿CREATE TABLE [dbo].[DashboardsItems] (
    [dashboardsItemID]    INT            IDENTITY (1, 1) NOT NULL,
    [dashboardSizeTypeID] INT            NOT NULL,
    [dashboardID]         INT            NOT NULL,
    [customReportID]      INT            NOT NULL,
    [name]                NVARCHAR (128) NULL,
    [layoutRow]           INT            NOT NULL,
    [layoutColumn]        INT            NOT NULL,
    CONSTRAINT [PK_DashboardsItems] PRIMARY KEY CLUSTERED ([dashboardsItemID] ASC),
    CONSTRAINT [FK_DashboardsItems_customReportID] FOREIGN KEY ([customReportID]) REFERENCES [dbo].[CustomReports] ([customReportID]),
    CONSTRAINT [FK_DashboardsItems_dashboardID] FOREIGN KEY ([dashboardID]) REFERENCES [dbo].[Dashboards] ([dashboardID]),
    CONSTRAINT [FK_DashboardsItems_dashboardSizeTypeID] FOREIGN KEY ([dashboardSizeTypeID]) REFERENCES [dbo].[DashboardSizeTypes] ([dashboardSizeTypeID])
);


GO
CREATE TRIGGER [dbo].[DashboardsItems.InsertUpdateDelete]
    ON [dbo].[DashboardsItems]
    FOR INSERT, UPDATE, DELETE
AS 
BEGIN
    SET NOCOUNT ON;

    DECLARE @Activity  NVARCHAR (8), @id INT;

    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'updated'
    END

    IF EXISTS (SELECT * FROM inserted) AND NOT EXISTS(SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'inserted'
    END

    IF EXISTS (SELECT * FROM deleted) AND NOT EXISTS(SELECT * FROM inserted)
    BEGIN
        SET @Activity = 'deleted'
    END

    

    IF (@Activity = 'deleted')
    BEGIN
        DECLARE cur CURSOR FOR SELECT dashboardsItemID FROM deleted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'DashboardsItems', @id, 'dashboardsItemID',
                (
                    SELECT *
                    FROM deleted i 
                    WHERE i.dashboardsItemID =  @id
                    FOR JSON AUTO
                ),0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
    ELSE
    BEGIN
        DECLARE cur CURSOR
        FOR SELECT dashboardsItemID
        FROM inserted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'DashboardsItems', @id, 'dashboardsItemID',
                (
                    SELECT *
                    FROM inserted i 
                    WHERE i.dashboardsItemID =  @id
                    FOR JSON AUTO
                ), 0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
END

GO
DISABLE TRIGGER [dbo].[DashboardsItems.InsertUpdateDelete]
    ON [dbo].[DashboardsItems];

