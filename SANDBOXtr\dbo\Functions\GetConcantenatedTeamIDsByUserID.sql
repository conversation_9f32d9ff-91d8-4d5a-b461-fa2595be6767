﻿

CREATE FUNCTION [dbo].[GetConcantenatedTeamIDsByUserID]
(
	@UserID int,
	@Delimeter nvarchar(8)
)
RETURNS nvarchar(2048)
AS
BEGIN

	DECLARE @ConcantenatedList nvarchar(2048)

	SET @ConcantenatedList = ''

	SELECT @ConcantenatedList = @ConcantenatedList + @Delimeter + CAST(teamID AS nvarchar(16)) 
	FROM TeamsUsersMap
	WHERE userID = @UserID
	ORDER BY teamID

	IF DATALENGTH(@ConcantenatedList) > 0
	BEGIN
		SET @ConcantenatedList = RIGHT(@ConcantenatedList, ((DATALENGTH(@ConcantenatedList)/2) - (DATALENGTH(@Delimeter)/2)))
	END

	RETURN @ConcantenatedList

END