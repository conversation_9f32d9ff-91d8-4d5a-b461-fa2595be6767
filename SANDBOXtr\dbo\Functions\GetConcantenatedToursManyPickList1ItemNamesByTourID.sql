﻿

CREATE FUNCTION [dbo].[GetConcantenatedToursManyPickList1ItemNamesByTourID]
(
	@TourID int,
	@Delimeter nvarchar(8)
)
RETURNS nvarchar(2048)
AS
BEGIN

	DECLARE @ConcantenatedList nvarchar(2048)

	SET @ConcantenatedList = ''

	SELECT @ConcantenatedList = @ConcantenatedList + @Delimeter + ToursManyPickList1Items.toursManyPickList1ItemName
	FROM ToursManyPickList1ItemsMap INNER JOIN
		ToursManyPickList1Items ON ToursManyPickList1ItemsMap.toursManyPickList1ItemID = ToursManyPickList1Items.toursManyPickList1ItemID
	WHERE ToursManyPickList1ItemsMap.tourID = @TourID
	ORDER BY ToursManyPickList1Items.toursManyPickList1ItemName

	IF DATALENGTH(@ConcantenatedList) > 0
	BEGIN
		SET @ConcantenatedList = RIGHT(@ConcantenatedList, ((DATALENGTH(@ConcantenatedList)/2) - (DATALENGTH(@Delimeter)/2)))
	END

	RETURN @ConcantenatedList

END