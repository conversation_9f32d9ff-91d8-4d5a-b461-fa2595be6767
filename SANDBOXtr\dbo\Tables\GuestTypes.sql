﻿CREATE TABLE [dbo].[GuestTypes] (
    [guestTypeID]   INT           IDENTITY (1, 1) NOT NULL,
    [guestTypeName] NVARCHAR (64) NOT NULL,
    [sortOrder]     INT           NOT NULL,
    [active]        BIT           NOT NULL,
    CONSTRAINT [PK_GuestTypes] PRIMARY KEY CLUSTERED ([guestTypeID] ASC),
    CONSTRAINT [UK_GuestTypes_guestTypeName] UNIQUE NONCLUSTERED ([guestTypeName] ASC)
);


GO
CREATE TRIGGER [dbo].[GuestTypes.InsertUpdateDelete]
    ON [dbo].[GuestTypes]
    FOR INSERT, UPDATE, DELETE
AS 
BEGIN
    SET NOCOUNT ON;

    DECLARE @Activity  NVARCHAR (8), @id INT;

    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'updated'
    END

    IF EXISTS (SELECT * FROM inserted) AND NOT EXISTS(SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'inserted'
    END

    IF EXISTS (SELECT * FROM deleted) AND NOT EXISTS(SELECT * FROM inserted)
    BEGIN
        SET @Activity = 'deleted'
    END

    

    IF (@Activity = 'deleted')
    BEGIN
        DECLARE cur CURSOR FOR SELECT guestTypeID FROM deleted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'GuestTypes', @id, 'guestTypeID',
                (
                    SELECT *
                    FROM deleted i 
                    WHERE i.guestTypeID =  @id
                    FOR JSON AUTO
                ),0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
    ELSE
    BEGIN
        DECLARE cur CURSOR
        FOR SELECT guestTypeID
        FROM inserted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'GuestTypes', @id, 'guestTypeID',
                (
                    SELECT *
                    FROM inserted i 
                    WHERE i.guestTypeID =  @id
                    FOR JSON AUTO
                ), 0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
END

GO
DISABLE TRIGGER [dbo].[GuestTypes.InsertUpdateDelete]
    ON [dbo].[GuestTypes];

