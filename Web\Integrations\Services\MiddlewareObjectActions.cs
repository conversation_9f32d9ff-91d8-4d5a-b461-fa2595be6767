﻿using ClosedXML.Excel;
using HughAxton.Core.Extensions;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Mail;
using System.Reflection;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Web;
using System.Web.UI;
using System.Xml.Linq;
using Telerik.Web.UI.GridExcelBuilder;
using TrackResults.BES.Data;
using TrackResults.BES.Data.Business;
using TrackResults.BES.Data.Cache;
using TrackResults.BES.Data.Cache.Static;
using TrackResults.BES.Data.Criteria.TierOne;
using TrackResults.BES.Data.Features;
using TrackResults.BES.Data.Types;
using TrackResults.BES.DataAccess;
using TrackResults.BES.DataAccess.Notes;
using TrackResults.BES.DataAccess.Reports;
using TrackResults.BES.DataAccess.Reports.CustomAnalyticViews;
using TrackResults.BES.Rules;
using TrackResults.BES.Services;
using TrackResults.BES.Services.ApiConnectorProviders;
using TrackResults.Common.DAL.Types;
using TrackResults.Common.Exceptions;
using TrackResults.Common.Utilities;
using TrackResults.Web.Reports;
using TrackResults.Web.Reports.UserControls;

namespace TrackResults.Web.Integrations.Services
{
    class MiddlewareObjectActions : WebServiceApiConnectorProvider
    {
        #region[Atributtes]
        public static List<JToken> JsonData;
        public static ApiConnectionRequests request;
        public class DataClient
        {
            public string CustomerId { get; set; }
            public string TourId { get; set; }
            public List<string> PurchasesId { get; set; }
            public string PurchaseId { get; set; }
            public string NotesId { get; set; }
            public string ResponseData { get; set; }
            public string MappingError { get; set; }
        }
        #endregion

        #region[Accesible Methods]
        private int ReportOnDateType(TierOneCriteria tierOneCriteria)
        {
            int dateTypeId = 0;

            if (FeaturesCache.HasFeature("Report.DateTypes") && FeaturesCache.HasFeature("Report.MultipleDateTypes"))
            {
                switch ((DateTypePeriod)tierOneCriteria.DateTimeCriteria.TierOneDateTypeLogicID)
                {
                    case DateTypePeriod.DateType:
                        dateTypeId = tierOneCriteria.DateTimeCriteria.TierOneDateTypeID;
                        break;

                    case DateTypePeriod.MultipleDateTypes:
                        dateTypeId = tierOneCriteria.DateTimeCriteria.TierOneDateTypeIDs[0];
                        break;

                    case DateTypePeriod.DateTypeAndMultipeDateTypes:
                    case DateTypePeriod.DateTypeAndNotMultipleDateTypes:
                        dateTypeId = tierOneCriteria.DateTimeCriteria.TierOneDateTypeID;
                        break;
                }
            }

            else if (FeaturesCache.HasFeature("Report.DateTypes"))
            {
                dateTypeId = tierOneCriteria.DateTimeCriteria.TierOneDateTypeID;
            }

            else if (FeaturesCache.HasFeature("Report.MultipleDateTypes"))
            {
                dateTypeId = tierOneCriteria.DateTimeCriteria.TierOneDateTypeIDs[0];
            }

            return dateTypeId;
        }
        private static byte[] CreateExcelFile(DataSet dataSet)
        {
            dynamic value = null;
            try
            {
                using (var workbook = new XLWorkbook())
                {
                    bool hasValidTable = false; // Para verificar si se ha añadido al menos una tabla

                    foreach (DataTable table in dataSet.Tables)
                    {
                        hasValidTable = true;
                        var worksheet = workbook.Worksheets.Add(table.TableName);

                        // Añadir nombres de columnas
                        for (int i = 0; i < table.Columns.Count; i++)
                        {
                            worksheet.Cell(1, i + 1).Value = table.Columns[i].ColumnName;
                            worksheet.Cell(1, i + 1).Style.Font.Bold = true; // Opcional: poner en negrita las cabeceras
                        }

                        // Añadir datos
                        for (int rowIndex = 0; rowIndex < table.Rows.Count; rowIndex++)
                        {
                            for (int colIndex = 0; colIndex < table.Columns.Count; colIndex++)
                            {
                                var cell = worksheet.Cell(rowIndex + 2, colIndex + 1);
                                value = table.Rows[rowIndex][colIndex];
                                cell.Value = value;
                            }
                        }

                        worksheet.Columns().AdjustToContents();
                    }

                    if (hasValidTable)
                    {
                        using (var stream = new MemoryStream())
                        {
                            workbook.SaveAs(stream);
                            return stream.ToArray();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.Write("error parse in:" + value + "\n" + ex.Message);
                return null;
            }

            return null; // Retornar null si no se encontraron tablas válidas
        }
        private static Dictionary<string, object> DataSetToDictionary(DataSet dataSet)
        {
            var dataSetDictionary = new Dictionary<string, object>();

            foreach (DataTable table in dataSet.Tables)
            {
                List<Dictionary<string, object>> rows = new List<Dictionary<string, object>>();
                foreach (DataRow dr in table.Rows)
                {
                    var row = new Dictionary<string, object>();
                    foreach (DataColumn col in table.Columns)
                    {
                        row[col.ColumnName] = dr[col];
                    }
                    rows.Add(row);
                }
                dataSetDictionary[table.TableName] = rows;
            }

            return dataSetDictionary;
        }
        protected override int ExecuteSendingParams(ApiConnection apiConnection, ApiConnectionRequests requests)
        {
            DateTime end = DateTime.Now;
            var data = ConditionParams.SelectDataByConditionParams(apiConnection.MiddlewareCondition, Convert.ToDateTime(apiConnection.LastMiddlewareActionTime), end);
            foreach (var d in data)
                Request.ExecuteRequestByParams(requests.ApiConnectionRequestId, d);
            return data.Count;
        }
        protected override string ExecuteMiddlewareConditions(int apiConnectionRequestId, string jsonBody)
        {
            string json = "";
            json = Request.ExecuteRequestAction(apiConnectionRequestId, jsonBody: jsonBody);
            return json;
        }
        private DataSet GetDataReport(int customReportID)
        {
            CustomReport customReport = CustomReportsDataAccess.SelectDataByID(customReportID);
            CustomAnalyticView customAnalyticView = CustomAnalyticViewsCache.GetAnalyticView(Convert.ToInt32(customReport.CustomAnalyticViewID));
            ReportByType? reportThenByType = null;
            if (customReport.ReportThenByType != null)
                reportThenByType = (ReportByType)Enum.Parse(typeof(ReportByType), customReport.ReportThenByType.ToString());
            else
                reportThenByType = null;

            ReportByType? reportByType = (ReportByType)Enum.Parse(typeof(ReportByType), customReport.ReportByType.ToString());

            DataSet dataSet = CustomReportsService.GetCachedCustomReportResult(customReportID, true);
            if (dataSet == null)
            {
                TierOneCriteria tierOneCriteria = customReport.TierOneCriteria;

                if (tierOneCriteria == null)
                {
                    tierOneCriteria = new TierOneCriteria();
                    tierOneCriteria.DateTimeCriteria.DateRangePeriodID = (int)DateRangePeriod.CurrentMonth;
                }
                tierOneCriteria.BasicCriteria.ReportFormatID = 7;
                tierOneCriteria.DateTimeCriteria.TierOneDateTypeIDReportBy = ReportOnDateType(tierOneCriteria);
                tierOneCriteria.DateTimeCriteria.TierOneDateSubTypeIDThenReportBy = 2;
                ReportBySubType? reportBySubType = ReportByTypeServices.GetReportBySubType(reportByType, tierOneCriteria);
                bool isReportByCode = false;
                ReportBySubType? reportThenBySubType = ReportByTypeServices.GetReportThenBySubType(reportThenByType, tierOneCriteria);
                ToursReportByBaseDataAccess reportByBaseDataAccess = CustomAnalyticViewsService.GetReportByDataAccess(customAnalyticView, reportByType, reportThenByType);
                bool includeSummary = true;
                return dataSet = reportByBaseDataAccess.SelectReport(tierOneCriteria, reportByType, reportBySubType, isReportByCode, reportThenByType,
                    reportThenBySubType, false, null, null, includeSummary);
            }
            else
                return dataSet;
        }
        protected override string ExecuteSchedulerReport(CustomReportsScheduler customReportsScheduler, List<int> reportsIDs)
        {
            try
            {
                if (customReportsScheduler.ActionTake == 1)
                {

                    try
                    {
                        using (MailMessage email = new MailMessage())
                        {
                            email.From = new MailAddress("<EMAIL>");

                            // Asegúrate de que customReportsScheduler.Emails sea una cadena de correos válidos
                            string[] emailAddresses = customReportsScheduler.Emails.Split(new[] { ',', ';' }, StringSplitOptions.RemoveEmptyEntries);
                            foreach (string address in emailAddresses)
                            {
                                email.To.Add(address.Trim());
                            }

                            email.Subject = "scheduler report";
                            email.Body = "Body scheduler reports";
                            email.IsBodyHtml = true;

                            List<Attachment> attachments = new List<Attachment>();

                            foreach (int id in reportsIDs)
                            {
                                DataSet data = GetDataReport(id);
                                byte[] excelFile = CreateExcelFile(data);
                                MemoryStream ms = new MemoryStream(excelFile);
                                attachments.Add(new Attachment(ms, "report_" + id + ".xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"));
                            }

                            foreach (var attachment in attachments)
                            {
                                email.Attachments.Add(attachment);
                            }

                            using (SmtpClient smtpClient = new SmtpClient("smtp.gmail.com"))
                            {
                                smtpClient.Port = 587; // Puerto SMTP para TLS
                                smtpClient.Credentials = new NetworkCredential("<EMAIL>", "test qwxj rbkx plef");
                                smtpClient.EnableSsl = true; // Activar SSL/TLS

                                smtpClient.Send(email);
                            }

                            foreach (var attachment in attachments)
                            {
                                attachment.Dispose();
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        return "Error sending email: " + ex.Message;
                    }
                }
                else if (customReportsScheduler.ActionTake == 2)
                {
                    var allData = new Dictionary<string, object>();
                    foreach (int id in reportsIDs)
                    {
                        DataSet data = GetDataReport(id);
                        Dictionary<string, object> dataDictionary = DataSetToDictionary(data);
                        allData["Report_" + id] = dataDictionary;
                    }
                    string jsonResult = JsonConvert.SerializeObject(allData, Formatting.Indented);
                    APIReportScheduler aPIReportScheduler = JsonConvert.DeserializeObject<APIReportScheduler>(customReportsScheduler.PropertiesAPI);
                    return Request.ExecuteRequest(aPIReportScheduler, customReportsScheduler.SchedulerID, jsonResult);
                }
            }
            catch (Exception e)
            {
                Console.Write(e.Message);
            }
            return "";
        }
        protected override List<ApiConnectionRequests> ApiConnectionsRequestIds(ApiConnection apiConnection, ApiConnectorProviderPageToken pageToken)
        {
            return ApiConnectionRequestsDataAccess.SelectAllDataByApiConnectionID(apiConnection.ApiConnectionID);
        }

        protected override TourReferences ConvertToTourReferences(ApiConnection apiConnection, IConnectionObject connectionObject)
        {
            DataClient dataClient = new DataClient();

            TourReferences tourReferences = new TourReferences
            {
                CustomersReferences = new CustomerReference { ApiExternalCustomerIDUpdated = true, CustomerReferenceUpdated = true },
                ToursReferences = new List<TourReference>()
            };

            // Parsear el JSON directamente
            JObject objectParseCustomer = null;
            if (connectionObject.ExternalCustomerPath != null && connectionObject.ExternalCustomerID != null &&
                !string.IsNullOrEmpty(connectionObject.JsonObject))
            {
                objectParseCustomer = JObject.Parse(connectionObject.JsonObject);
            }

            JObject objectParseTour = null;
            if (connectionObject.ToursObjects != null && connectionObject.ToursObjects[0].ExternalTourIdPath != null &&
                connectionObject.ToursObjects[0].ExternalTourID != null && !string.IsNullOrEmpty(connectionObject.JsonObject))
            {
                objectParseTour = JObject.Parse(connectionObject.JsonObject);

            }

            JObject objectParseNotes = null;
            if (connectionObject.ExternalNotePath != null && connectionObject.ExternalNoteID != null &&
                !string.IsNullOrEmpty(connectionObject.JsonObject))
            {
                objectParseNotes = JObject.Parse(connectionObject.JsonObject);
            }

            JObject objectParsePurchases = null;
            if (connectionObject.PurchasesObject != null && connectionObject.PurchasesObject[0].ExternalPurchasePath != null &&
                connectionObject.PurchasesObject[0].ExternalPurchaseID != null && !string.IsNullOrEmpty(connectionObject.JsonObject))
            {
                objectParsePurchases = JObject.Parse(connectionObject.JsonObject);
            }

            if (connectionObject.JsonObject != null)
            {
                try
                {
                    ApiRequestResponses apiRequest = ApiRequestResponsesDataAccess.SelectDataByApiConnectionRequestId(MiddlewareObjectActions.request.ApiConnectionRequestId);
                    var customerPropMaps = GetPropertyMappings(apiRequest, "Customer.");
                    var tourPropMaps = GetPropertyMappings(apiRequest, "Tour.");
                    var notesPropMaps = GetPropertyMappings(apiRequest, "Notes.");
                    var purchasePropMaps = GetPropertyMappings(apiRequest, "Purchase.");

                    if (objectParseCustomer != null)
                    {
                        foreach (var property in customerPropMaps)
                            tourReferences.CustomersReferences = SetProperty(apiConnection.ApiConnectionID, tourReferences.CustomersReferences.GetType().GetProperties(), property, tourReferences.CustomersReferences, objectParseCustomer, apiConnection.ApiConnectionID);
                    }

                    if (objectParseTour != null)
                    {
                        var lastTour = connectionObject.ToursObjects.Last();

                        foreach (ToursObjects to in connectionObject.ToursObjects)
                        {
                            //>>> begin mapping tours properties
                            TourReference tourReference = new TourReference();
                            tourReference.TourReferenceUpdated = true;

                            foreach (var property in tourPropMaps)
                                tourReference = SetProperty(apiConnection.ApiConnectionID, tourReference.GetType().GetProperties(), property, tourReference, objectParseTour, apiConnection.ApiConnectionID, parentId: to.ExternalTourID, parentPath: to.ExternalTourIdPath);
                            //>>> end mapping tours properties

                            if (lastTour == to)
                            {
                                if (objectParseNotes != null)
                                {
                                    //>>> begin mapping notes properties
                                    tourReference.NotesReference = new NotesReference();
                                    foreach (var property in notesPropMaps)
                                        tourReference.NotesReference = SetProperty(apiConnection.ApiConnectionID, tourReference.NotesReference.GetType().GetProperties(), property, tourReference.NotesReference, objectParseNotes, apiConnection.ApiConnectionID);

                                    if (objectParseNotes != null && tourReference.NotesReference.noteID == null)
                                        tourReference.NotesReference.noteID = 0;
                                    //>>> end mapping notes properties
                                }

                                if (objectParsePurchases != null)
                                {
                                    if (ConfigurationManager.AppSettings["ApiBaseUrl"] == "https://travelcotr-api.azurewebsites.net/") //logic to travelco
                                    {
                                        for (int i = 0; i < connectionObject.PurchasesObject.Count; i++)
                                        {
                                            tourReference = new TourReference();
                                            tourReference.TourReferenceUpdated = true;
                                            foreach (var property in tourPropMaps)
                                                tourReference = SetProperty(apiConnection.ApiConnectionID, tourReference.GetType().GetProperties(), property, tourReference, objectParseTour, apiConnection.ApiConnectionID, parentId: to.ExternalTourID, parentPath: to.ExternalTourIdPath, pos: i);
                                            tourReference.PurchaseReferences = new List<PurchaseReference>();

                                            //>>> begin mapping notes properties
                                            PurchaseReference purchaseReference = new PurchaseReference();
                                            foreach (var property in purchasePropMaps)
                                                purchaseReference = SetProperty(apiConnection.ApiConnectionID, purchaseReference.GetType().GetProperties(), property, purchaseReference, objectParsePurchases, apiConnection.ApiConnectionID, parentId: connectionObject.PurchasesObject[i].ExternalPurchaseID, parentPath: connectionObject.PurchasesObject[i].ExternalPurchasePath);
                                            if (purchaseReference.ApiExternalPurchaseID == null)
                                                purchaseReference.ApiExternalPurchaseID = purchaseReference.ExternalPurchaseID;
                                            //>>> end mapping notes properties

                                            tourReference.PurchaseReferences.Add(purchaseReference);
                                            if (tourReference.ExternalTourID == null && purchaseReference.TourID != null)
                                                tourReference.ExternalTourID = purchaseReference.TourID.ToString();

                                            if (i > 0)
                                            {
                                                tourReference.TourStatusTypeName = "Custom Sales";
                                                tourReference.ExternalTourID = tourReference.ExternalTourID + i;
                                            }

                                            if (objectParseTour != null && tourReference.ApiExternalTourID == null && tourReference.ExternalTourID != null)
                                            {
                                                tourReference.ApiExternalTourID = tourReference.ExternalTourID;
                                                tourReference.TourID = tourReference.TourID ?? 0;
                                                tourReference.TourIDUpdated = true;
                                            }

                                            tourReferences.ToursReferences.Add(tourReference);
                                        }
                                    }
                                    else //logic others clients
                                    {

                                        tourReference.PurchaseReferences = new List<PurchaseReference>();

                                        for (int i = 0; i < connectionObject.PurchasesObject.Count; i++)
                                        {
                                            //>>> begin mapping notes properties
                                            PurchaseReference purchaseReference = new PurchaseReference();
                                            foreach (var property in purchasePropMaps)
                                                purchaseReference = SetProperty(apiConnection.ApiConnectionID, purchaseReference.GetType().GetProperties(), property, purchaseReference, objectParsePurchases, apiConnection.ApiConnectionID, parentId: connectionObject.PurchasesObject[i].ExternalPurchaseID, parentPath: connectionObject.PurchasesObject[i].ExternalPurchasePath);
                                            if (purchaseReference.ApiExternalPurchaseID == null)
                                                purchaseReference.ApiExternalPurchaseID = purchaseReference.ExternalPurchaseID;
                                            //>>> end mapping notes properties

                                            tourReference.PurchaseReferences.Add(purchaseReference);
                                            if (tourReference.ExternalTourID == null && purchaseReference.TourID != null)
                                                tourReference.ExternalTourID = purchaseReference.TourID.ToString();

                                        }

                                        if (objectParseTour != null && tourReference.ApiExternalTourID == null && tourReference.ExternalTourID != null)
                                        {
                                            tourReference.ApiExternalTourID = tourReference.ExternalTourID;
                                            tourReference.TourID = tourReference.TourID ?? 0;
                                            tourReference.TourIDUpdated = true;
                                        }

                                        tourReferences.ToursReferences.Add(tourReference);
                                    }
                                }
                                else
                                {
                                    if (objectParseTour != null && tourReference.ApiExternalTourID == null && tourReference.ExternalTourID != null)
                                    {
                                        tourReference.ApiExternalTourID = tourReference.ExternalTourID;
                                        tourReference.TourID = tourReference.TourID ?? 0;
                                        tourReference.TourIDUpdated = true;
                                    }

                                    tourReferences.ToursReferences.Add(tourReference);
                                }
                            }
                            else
                            {
                                if (objectParseTour != null && tourReference.ApiExternalTourID == null && tourReference.ExternalTourID != null)
                                {
                                    tourReference.ApiExternalTourID = tourReference.ExternalTourID;
                                    tourReference.TourID = tourReference.TourID ?? 0;
                                    tourReference.TourIDUpdated = true;
                                }

                                tourReferences.ToursReferences.Add(tourReference);
                            }
                        }
                    }

                    if (objectParseCustomer != null && tourReferences.CustomersReferences.ApiExternalCustomerID == null && tourReferences.CustomersReferences.ExternalCustomerID != null)
                    {
                        tourReferences.CustomersReferences.ApiExternalCustomerID = tourReferences.CustomersReferences.ExternalCustomerID;
                        tourReferences.CustomersReferences.CustomerID = tourReferences.CustomersReferences.CustomerID ?? 0;
                        tourReferences.CustomersReferences.CustomerIDUpdated = true;
                    }

                }
                catch (MiddlewareErrorException middlewareErrorException)
                {
                    MiddlewareService.LogMiddlewareErrorException(middlewareErrorException);
                    dataClient.MappingError = "Data correction required.";
                }
            }
            return tourReferences;
        }
        protected override TourReference ConvertToTourReference(ApiConnection apiConnection, IConnectionObject connectionObject)
        {
            DataClient dataClient = new DataClient();
            TourReference tourReference = null;

            // Parsear el JSON directamente
            // Parsear el JSON directamente
            JObject objectParseCustomer = null;
            if (connectionObject.ExternalCustomerPath != null && connectionObject.ExternalCustomerID != null &&
                !string.IsNullOrEmpty(connectionObject.JsonObject))
            {
                objectParseCustomer = JObject.Parse(connectionObject.JsonObject);
            }

            JObject objectParseTour = null;
            if (connectionObject.ToursObjects != null && connectionObject.ToursObjects[0].ExternalTourIdPath != null &&
                connectionObject.ToursObjects[0].ExternalTourID != null && !string.IsNullOrEmpty(connectionObject.JsonObject))
            {
                objectParseTour = JObject.Parse(connectionObject.JsonObject);
            }

            JObject objectParseNotes = null;
            if (connectionObject.ExternalNotePath != null && connectionObject.ExternalNoteID != null &&
                !string.IsNullOrEmpty(connectionObject.JsonObject))
            {
                objectParseNotes = JObject.Parse(connectionObject.JsonObject);
            }

            JObject objectParsePurchases = null;
            if (connectionObject.PurchasesObject != null && connectionObject.PurchasesObject[0].ExternalPurchasePath != null &&
                connectionObject.PurchasesObject[0].ExternalPurchaseID != null && !string.IsNullOrEmpty(connectionObject.JsonObject))
            {
                objectParsePurchases = JObject.Parse(connectionObject.JsonObject);
            }

            try
            {
                if (connectionObject.JsonObject != null)
                {
                    dataClient.CustomerId = connectionObject.ExternalCustomerID;
                    dataClient.TourId = connectionObject.ExternalTourID;
                    dataClient.NotesId = connectionObject.ExternalNoteID;

                    if (MiddlewareObjectActions.request == null)
                        MiddlewareObjectActions.request = ApiConnectionRequestsDataAccess.SelectDataByApiConnectionID(apiConnection.ApiConnectionID);

                    ApiRequestResponses apiRequest = ApiRequestResponsesDataAccess.SelectDataByApiConnectionRequestId(MiddlewareObjectActions.request.ApiConnectionRequestId);
                    tourReference = new TourReference
                    {
                        CustomerReference = new CustomerReference { ApiExternalCustomerIDUpdated = true, CustomerReferenceUpdated = true },
                        NotesReference = new NotesReference(),
                        TourReferenceUpdated = true
                    };

                    foreach(var mapping in apiRequest.PropertyMappings)
                    {
                        if (mapping.Key.StartsWith("Customer.") && objectParseCustomer != null)
                            tourReference.CustomerReference = SetProperty(apiConnection.ApiConnectionID, tourReference.CustomerReference.GetType().GetProperties(), mapping, tourReference.CustomerReference, objectParseCustomer, apiConnection.ApiConnectionID, parentId: connectionObject.ExternalCustomerID);
                        else if (mapping.Key.StartsWith("Tour.") && objectParseTour != null)
                            tourReference = SetProperty(apiConnection.ApiConnectionID, tourReference.GetType().GetProperties(), mapping, tourReference, objectParseTour, apiConnection.ApiConnectionID, parentId: connectionObject.ExternalTourID);
                        else if (mapping.Key.StartsWith("Notes.") && objectParseNotes != null)
                            tourReference.NotesReference = SetProperty(apiConnection.ApiConnectionID, tourReference.NotesReference.GetType().GetProperties(), mapping, tourReference.NotesReference, objectParseNotes, apiConnection.ApiConnectionID, parentId: connectionObject.ExternalNoteID);
                    }

                    if (connectionObject.PurchasesObject != null && objectParsePurchases != null)
                    {
                        tourReference.PurchaseReferences = new List<PurchaseReference>();

                        foreach (PurchasesObjects po in connectionObject.PurchasesObject)
                        {
                            PurchaseReference purchaseReference = new PurchaseReference();
                            foreach(var mapping in apiRequest.PropertyMappings)
                            {
                                if (mapping.Key.StartsWith("Purchase."))
                                    purchaseReference = SetProperty(apiConnection.ApiConnectionID, purchaseReference.GetType().GetProperties(), mapping, purchaseReference, objectParsePurchases, apiConnection.ApiConnectionID, parentId: po.ExternalPurchaseID);
                            }
                            
                            if (purchaseReference.ApiExternalPurchaseID == null)
                                purchaseReference.ApiExternalPurchaseID = purchaseReference.ExternalPurchaseID;

                            tourReference.PurchaseReferences.Add(purchaseReference);

                            if (tourReference.ExternalTourID == null && purchaseReference.TourID != null)
                                tourReference.ExternalTourID = purchaseReference.TourID.ToString();

                            purchaseReference = new PurchaseReference();
                        }
                    }

                    // Actualización de IDs solo si los datos están disponibles
                    if (objectParseTour != null && tourReference.ApiExternalTourID == null && tourReference.ExternalTourID != null)
                    {
                        tourReference.ApiExternalTourID = tourReference.ExternalTourID;
                        tourReference.TourID = tourReference.TourID ?? 0;
                        tourReference.TourIDUpdated = true;
                    }

                    if (objectParseCustomer != null && tourReference.CustomerReference.ApiExternalCustomerID == null && tourReference.CustomerReference.ExternalCustomerID != null)
                    {
                        tourReference.CustomerReference.ApiExternalCustomerID = tourReference.CustomerReference.ExternalCustomerID;
                        tourReference.CustomerReference.CustomerID = tourReference.CustomerReference.CustomerID ?? 0;
                        tourReference.CustomerReference.CustomerIDUpdated = true;
                    }

                    if (objectParseNotes != null && tourReference.NotesReference.noteID == null)
                    {
                        tourReference.NotesReference.noteID = 0;
                    }
                }
            }
            catch (MiddlewareErrorException middlewareErrorException)
            {
                MiddlewareService.LogMiddlewareErrorException(middlewareErrorException);
                dataClient.MappingError = "Data correction required.";
            }

            return tourReference;
        }

        protected override List<IConnectionObject> GetConnectionObjects(ApiConnection apiConnection, ApiConnectorProviderPageToken pageToken, bool esTesting = false, ApiConnectionRequests apiConnectionRequests = null)
        {
            List<IConnectionObject> connectionObjects = new List<IConnectionObject>();

            if (apiConnectionRequests != null)
            {
                MiddlewareObjectActions.request = apiConnectionRequests;
                ApiRequestResponses appResponse = ApiRequestResponsesDataAccess.SelectDataByApiConnectionRequestId(MiddlewareObjectActions.request.ApiConnectionRequestId);
                ApiConnector appConnector = ApiConnectorsDataAccess.SelectDataByID(apiConnection.ApiConnectorID);

                try
                {
                    string json = null;

                    // Optimización: Verificar si la respuesta es grande antes de procesarla
                    if (!esTesting)
                    {
                        json = appConnector.ApiConnectorClassFullName.Contains("KeapV2") ?
                            Request.ExecuteRequestAction(MiddlewareObjectActions.request.ApiConnectionRequestId, formatDate: "dateTimeUTC") :
                            Request.ExecuteRequestAction(MiddlewareObjectActions.request.ApiConnectionRequestId);
                        //json = "{\"ctID\":\"BAA88A17-D99C-49AB-9DF0-148973721F28\",\"cID\":\"C382B820-3DF0-4CC1-9FCE-111AE827BEBC\",\"productCode\":\"IILTPHPSPV\",\"businessPartner\":\"Puerto Vallarta\",\"businessGroup\":\"Puerto Vallarta | HPS\",\"broker\":\"HPS PV\",\"sponsorCode\":\"HPS PV | PACKAGE SALES\",\"datatingestion\":[{\"entityName\":\"HPS_PV_leads624gi_10142024_workfile.txt\",\"leadSource\":\"Puerto Vallarta | HPS\",\"sequence\":1,\"createdOn\":\"2024-10-15T04:15:47.93\",\"name\":\"Puerto Vallarta | HPS | 1 | HPS_PV_leads624gi_10142024_workfile.txt\",\"batch\":\"HPS_PV_leads624gi_10142024_workfile.txt\"}],\"persons\":[{\"personIndex\":1,\"firstName\":\"Cathy\",\"middleName\":null,\"lastName\":\"Keyser\",\"emailAddress\":\"<EMAIL>\",\"phoneNumbers\":[{\"personIndex\":1,\"phoneNumberIndex\":1,\"phoneNumberDigits\":\"6037178267\",\"lineType\":\"Wireless\"}]},{\"personIndex\":2,\"firstName\":\"John\",\"middleName\":null,\"lastName\":\"Guay\",\"emailAddress\":\"<EMAIL>\",\"phoneNumbers\":null}],\"mailingAddress\":{\"address1\":\"95 Park St\",\"address2\":null,\"address3\":null,\"city\":\"Northfield\",\"stateProvince\":\"NH\",\"postalCode\":\"03276\",\"country\":null,\"countryCode\":\"US\",\"district\":null,\"subDistrict\":null,\"latitude\":null,\"longitude\":null},\"customfields\":[{\"name\":\"DataPoint.Demographic.Gender\",\"value\":\"Male\"},{\"name\":\"DataPoint.Marketing.LeadSource.Code\",\"value\":\"HPS PV | Package Sales\"}],\"telephony\":[{\"call\":{\"measurement\":{\"predicates\":{\"contactID\":\"C382B820-3DF0-4CC1-9FCE-111AE827BEBC\",\"dataIngestionEntityName\":\"HPS_PV_leads624gi_10142024_workfile.txt\",\"leadSource\":\"Puerto Vallarta | HPS\",\"startDate\":\"2000-01-01T00:00:00\",\"endDate\":\"2027-01-03T00:00:00\",\"type\":\"Outbound\"},\"startDateTimeUtc\":\"2024-10-18T15:17:15.957\",\"startDateTime\":\"2024-10-18T11:17:15\",\"startDateTimeEpoch\":1729264635955,\"startDate\":\"2024-10-18T00:00:00\",\"endDateTimeUtc\":\"2024-12-23T18:48:18.02\",\"endDateTime\":\"2024-12-23T13:48:18\",\"endDateTimeEpoch\":1734979698019,\"endDate\":\"2024-12-23T00:00:00\",\"total\":{\"dialCount\":18,\"partyConnectCount\":8,\"noConnectCount\":10,\"disconnectCount\":0},\"partyConnect\":{\"positiveSuccessTotalCount\":2,\"willCallBackTotalCount\":0,\"neutralIncompletePresentationTotalCount\":1,\"neutralCustomerServiceTotalCount\":5,\"negativeNQTotalCount\":0,\"negativeForeignLanguageTotalCount\":0,\"negativeNITotalCount\":0,\"negativeDNCTotalCount\":0},\"noConnect\":{\"neutralBusyNoAnswerLeftMessageTotalCount\":10,\"systemFastBusyTotalCount\":0},\"disconnect\":{\"disconnectedWrongNumberTotalCount\":0,\"systemTotalCount\":0}}}}],\"appointments\":[{\"id\":1373076227,\"set\":{\"dateAsString\":\"11/25/2024\",\"date\":\"2024-11-25T00:00:00\",\"agent\":\"JOSEPHRA\",\"office\":\"Oregon Call Center\",\"company\":\"GI\"},\"appointment\":{\"dateAsString\":\"11/27/2024\",\"timeAsString\":\"10:00am\",\"dateTime\":\"2024-11-27T10:00:00\",\"assignedWebinarAgent\":\"GHINTZE\",\"assignedWebinarAgentOffice\":\"Sarasota\",\"assignedWebinarAgentCompany\":\"GI\",\"calendarID\":7043384,\"calendarName\":\"Grace 🟢\"},\"label\":{\"id\":4191796,\"name\":\"$ale\",\"group\":\"Show\",\"isNoShow\":null,\"isShow\":true,\"isCancelled\":null,\"isOther\":null,\"isNoLabelAssigned\":null,\"isDeposit\":null,\"isCustomerService\":null},\"aValidFrom\":\"2024-11-30T04:46:02.661215\"}],\"sales\":[{\"recordtype\":\"Header\",\"sequenceAsDecimal\":1,\"salesAgent\":{\"id\":\"GHINTZE\",\"agent\":\"GHINTZE\",\"name\":\"Grace Hintze TAG All\",\"office\":\"Sarasota\",\"company\":\"GI\"},\"saleDate\":\"2024-11-27T00:00:00\",\"product\":{\"code\":\"IILTP\",\"taxonomyType\":\"LTP\",\"assignedID\":\"367192\",\"ctID\":\"6D816941-A8E6-4A1F-9726-0B1A3315B790\",\"ctrID\":\"B54B654F-BDC3-46B4-BD5D-F7BE84C7BE01\"},\"volume\":{\"totalAmount\":2699,\"netAmount\":2699,\"currency\":null},\"currentStatus\":\"Canceled\",\"discount\":{\"has\":false,\"currency\":null,\"totalAmount\":0},\"downPayment\":{\"has\":true,\"currency\":null,\"count\":1,\"totalAmount\":2699},\"processable\":{\"percentageAsDecimal\":1,\"dayOfSalePercentageAsDecimal\":1,\"dayOfSaleCategory\":\"Full down\"},\"adp\":{\"scheduled\":{\"has\":false,\"currency\":null,\"totalAmount\":0,\"count\":0},\"collected\":{\"has\":false,\"currency\":null,\"totalAmount\":0,\"count\":0},\"outstanding\":{\"has\":false,\"currency\":null,\"totalAmount\":0,\"count\":0,\"isDueCount\":0,\"isDueTotalAmount\":0}},\"refund\":{\"has\":true,\"currency\":null,\"chargeback\":true,\"isPartial\":false,\"firstDate\":\"2024-12-17T00:00:00\",\"averageDaysToCancel\":20,\"totalAmount\":-2699,\"reasonlist\":\"GI Chargeback\"},\"bonus\":{\"01\":\"J8 Worldwide\",\"01Quantity\":2,\"02\":\"J8 MEX\",\"02Quantity\":1,\"03\":\"AIR Rebate $500\",\"03Quantity\":2,\"04\":null,\"04Quantity\":null,\"05\":\"$75 Activity\",\"05Quantity\":2},\"cID\":\"C382B820-3DF0-4CC1-9FCE-111AE827BEBC\",\"ctValidFrom\":\"2025-01-02T04:50:10.0102717\",\"ctrValidFrom\":\"2024-12-27T14:26:46.9808307\"},{\"recordtype\":\"Header\",\"sequenceAsDecimal\":2,\"salesAgent\":{\"id\":\"CTCGGVTC\",\"agent\":\"CTCGGVTC\",\"name\":\"CTCGGVTC_PURCHASED\",\"office\":\"Sarasota\",\"company\":\"GI\"},\"saleDate\":\"2024-12-23T00:00:00\",\"product\":{\"code\":\"IILTP\",\"taxonomyType\":\"LTP\",\"assignedID\":\"367192\",\"ctID\":\"6D816941-A8E6-4A1F-9726-0B1A3315B790\",\"ctrID\":\"DD62CC50-02A2-42BD-8DFA-6590079368E3\"},\"volume\":{\"totalAmount\":2699,\"netAmount\":2699,\"currency\":null},\"currentStatus\":\"Active\",\"discount\":{\"has\":false,\"currency\":null,\"totalAmount\":0},\"downPayment\":{\"has\":true,\"currency\":null,\"count\":1,\"totalAmount\":1699},\"processable\":{\"percentageAsDecimal\":1,\"dayOfSalePercentageAsDecimal\":0.62949,\"dayOfSaleCategory\":\"Pender\"},\"adp\":{\"scheduled\":{\"has\":true,\"currency\":null,\"totalAmount\":1000,\"count\":1},\"collected\":{\"has\":true,\"currency\":null,\"totalAmount\":1000,\"count\":1},\"outstanding\":{\"has\":false,\"currency\":null,\"totalAmount\":0,\"count\":0,\"isDueCount\":0,\"isDueTotalAmount\":0}},\"refund\":{\"has\":false,\"currency\":null,\"chargeback\":false,\"isPartial\":false,\"firstDate\":null,\"averageDaysToCancel\":null,\"totalAmount\":0,\"reasonlist\":null},\"bonus\":{\"01\":null,\"01Quantity\":null,\"02\":null,\"02Quantity\":null,\"03\":null,\"03Quantity\":null,\"04\":null,\"04Quantity\":null,\"05\":null,\"05Quantity\":null},\"cID\":\"C382B820-3DF0-4CC1-9FCE-111AE827BEBC\",\"ctValidFrom\":\"2025-01-02T04:50:10.0102717\",\"ctrValidFrom\":\"2024-12-27T14:26:47.683955\"}],\"gifts\":[{\"appointment\":{\"id\":1373076227},\"gift\":{\"isForNoSale\":null,\"isForSale\":true,\"product\":{\"code\":\"LTPOT3HTL_BONUS\",\"assignedID\":\"52989\",\"isDigital\":null,\"email\":\"<EMAIL>\",\"ctID\":\"6D6FFAA1-22C9-45CE-AE73-0A18A7B90811\",\"cID\":\"C382B820-3DF0-4CC1-9FCE-111AE827BEBC\",\"ctValidForm\":null}}}],\"dataSnapshotID\":\"A95641AA-6D11-4B3A-9AF2-2775E9569FC0\",\"dataSnapshotDatetime\":\"2025-01-02T03:25:22.4766667\",\"externalRequestId\":\"2025-01-02 03:27:43.8146100\",\"cValidFrom\":\"2024-12-26T22:32:28.952931\",\"iValidFrom\":\"2024-10-15T11:05:36.255446\"}";



                        // Validación para evitar procesar datos nulos o vacíos
                        if (string.IsNullOrWhiteSpace(json))
                        {
                            MiddlewareErrorsDataAccess.Insert(MiddlewareErrorType.ImportTourReferenceException, apiConnection.ApiConnectionID,
                                       null, null, "JSON response is empty or null.", true, null, null);
                            return connectionObjects;
                        }
                        try
                        {
                            using (StringReader stringReader = new StringReader(json))
                            using (JsonTextReader jsonReader = new JsonTextReader(stringReader))
                            {
                                jsonReader.SupportMultipleContent = true;
                                var jsonSerializer = new JsonSerializer
                                {
                                    Formatting = Formatting.None,            // Evitar formateo adicional
                                    CheckAdditionalContent = false,         // Ignorar contenido extra no relacionado
                                    MissingMemberHandling = MissingMemberHandling.Ignore, // Ignorar propiedades faltantes
                                    NullValueHandling = NullValueHandling.Ignore,         // Ignorar valores nulos
                                    DefaultValueHandling = DefaultValueHandling.Ignore    // Ignorar valores por defecto
                                };

                                List<JToken> jsonItemsBatch = new List<JToken>();
                                int batchSize = 100; // Define el tamaño del lote
                                MiddlewareObjectActions.JsonData = new List<JToken>();

                                //JToken items = JsonConvert.DeserializeObject<JToken>(json);
                                //MiddlewareObjectActions.JsonData.Add(items);

                                while (jsonReader.Read())
                                {
                                    if (jsonReader.TokenType == JsonToken.String)
                                    {
                                        string jsonString = jsonReader.Value.ToString();
                                        try
                                        {
                                            JToken items = JsonConvert.DeserializeObject<JToken>(jsonString);
                                            if (items != null)
                                            {
                                                if (items.Type == JTokenType.Object)
                                                {
                                                    var jsonObject = (JObject)items;
                                                    foreach (var item in jsonObject)
                                                    {
                                                        if (item.Value is JArray array)
                                                            jsonItemsBatch.AddRange(array);
                                                    }
                                                }
                                                else if (items.Type == JTokenType.Array)
                                                {
                                                    jsonItemsBatch.AddRange((JArray)items);
                                                }

                                                if (jsonItemsBatch.Count >= batchSize)
                                                {
                                                    MiddlewareObjectActions.JsonData.AddRange(jsonItemsBatch);
                                                    jsonItemsBatch.Clear(); // Liberar memoria
                                                    GC.Collect();          // Forzar recolección de basura (opcional)
                                                }
                                            }
                                        }
                                        catch (JsonReaderException ex)
                                        {
                                            MiddlewareErrorsDataAccess.Insert(MiddlewareErrorType.ImportTourReferenceException,
                                                apiConnection.ApiConnectionID, null, null,
                                                $"Error deserializing a JSON string: {ex.Message}", true, null, null);
                                        }
                                    }
                                }

                                if (jsonItemsBatch.Count > 0)
                                {
                                    MiddlewareObjectActions.JsonData.AddRange(jsonItemsBatch);
                                    jsonItemsBatch.Clear();
                                    GC.Collect();
                                }

                                if (MiddlewareObjectActions.JsonData.Count > 0)
                                    connectionObjects = ToConnectionObjects(apiConnection.ApiConnectionID);
                            }
                        }
                        catch (JsonReaderException ex)
                        {
                            MiddlewareErrorsDataAccess.Insert(MiddlewareErrorType.ImportTourReferenceException, apiConnection.ApiConnectionID, null, null, $"Error reading JSON: {ex.Message}", true, null, null);
                        }
                        catch (Exception ex)
                        {
                            MiddlewareErrorsDataAccess.Insert(MiddlewareErrorType.ImportTourReferenceException, apiConnection.ApiConnectionID,  null, null, $"General error when processing JSON: {ex.Message}", true, null, null);
                        }
                    }
                    else
                    {
                        json = appResponse.JsonResponse;
                        if (appConnector.ApiConnectorClassFullName.Contains("Acuity"))
                        {
                            json = json.Insert(0, "[");
                            json = json.Insert(json.Length, "]");
                        }
                    }
                }
                catch (Exception exception)
                {
                    MiddlewareErrorsDataAccess.Insert(MiddlewareErrorType.ImportTourReferenceException, apiConnection.ApiConnectionID,
                               null, null, $"Error processing JSON from response: {exception.Message}", true, null, null);
                }
            }
            return connectionObjects;
        }

        protected override void GetPageComplete(ApiConnection apiConnection, ApiConnectorProviderPageToken pageToken) { }
        protected override bool HasNextPage(ApiConnectorProviderPageToken pageToken) { return false; }
        protected override bool UploadRemoteFile(ApiConnection apiConnection, ApiConnectionRequests requests)
        {
            return Sftp.Upload(apiConnection, requests);
        }
        #endregion

        #region[Private Methods]

        private List<IConnectionObject> ToConnectionObjects(int apiconnectionID)
        {
            try
            {
                List<IConnectionObject> connectionObjects = new List<IConnectionObject>();
                ApiRequestResponses apiRequest = ApiRequestResponsesDataAccess.SelectDataByApiConnectionRequestId(MiddlewareObjectActions.request.ApiConnectionRequestId);
                StringComparison comparison = StringComparison.OrdinalIgnoreCase;

                // Seleccionar mapeos relevantes para cada tipo de dato
                var itemCustomer = apiRequest.PropertyMappings
                    .FirstOrDefault(i => i.Key.IndexOf("Customer.ExternalCustomerID", comparison) >= 0) ??
                    apiRequest.PropertyMappings.FirstOrDefault(i => i.PropertyValueResponse.IndexOf("externalCustomerID", comparison) >= 0);

                var itemTour = apiRequest.PropertyMappings
                    .FirstOrDefault(i => i.Key.IndexOf("Tour.ExternalTourID", comparison) >= 0) ??
                    apiRequest.PropertyMappings.FirstOrDefault(i => i.PropertyValueResponse.IndexOf("externalTourID", comparison) >= 0);

                var itemPurchase = apiRequest.PropertyMappings
                    .FirstOrDefault(i => i.Key.IndexOf("Purchase.ExternalPurchaseID", comparison) >= 0) ??
                    apiRequest.PropertyMappings.FirstOrDefault(i => i.PropertyValueResponse.IndexOf("externalPurchaseID", comparison) >= 0);

                var itemNotes = apiRequest.PropertyMappings
                    .FirstOrDefault(i => i.Key.IndexOf("Notes.ExternalNoteID", comparison) >= 0) ??
                    apiRequest.PropertyMappings.FirstOrDefault(i => i.PropertyValueResponse.IndexOf("externalNoteID", comparison) >= 0);

                // Inicialización de variables y paths
                string externalCustomerID = itemCustomer?.ResponseValue[0].Property.ToString();
                string customerPath = itemCustomer?.ResponseValue[0].Path.ToString();

                string externalTourID = itemTour?.ResponseValue[0].Property.ToString();
                string tourPath = itemTour?.ResponseValue[0].Path.ToString();

                string externalPurchaseID = itemPurchase?.ResponseValue[0].Property.ToString();
                string purchasePath = itemPurchase?.ResponseValue[0].Path.ToString();

                string externalNoteID = itemNotes?.ResponseValue[0].Property.ToString();
                string notePath = itemNotes?.ResponseValue[0].Path.ToString();

                object lockObject = new object();  // Objeto para sincronización

                // Verificar si hay datos que procesar
                if ((externalCustomerID != null && customerPath != null) ||
                    (externalTourID != null && tourPath != null) ||
                    (externalPurchaseID != null && purchasePath != null) ||
                    (externalNoteID != null && notePath != null))
                {
                    foreach (var jobject in MiddlewareObjectActions.JsonData)
                    {
                        List<string> pathCustomer = new List<string>();
                        List<string> pathTour = new List<string>();
                        List<string> pathPurchase = new List<string>();
                        List<string> pathNote = new List<string>();

                        Tuple<List<object>, string> responseCustomer = null, responseTour = null, responsePurchase = null, responseNotes = null;
                        ConnectionObject connectionObject = new ConnectionObject();

                        // Procesar cada tipo de dato si corresponde
                        if (externalCustomerID != null)
                            responseCustomer = GetValueJsonParam(jobject, externalCustomerID, customerPath, out pathCustomer);

                        if (externalTourID != null)
                            responseTour = GetValueJsonParam(jobject, externalTourID, tourPath, out pathTour);

                        if (externalPurchaseID != null)
                            responsePurchase = GetValueJsonParam(jobject, externalPurchaseID, purchasePath, out pathPurchase);

                        if (externalNoteID != null)
                            responseNotes = GetValueJsonParam(jobject, externalNoteID, notePath, out pathNote);

                        // Asignar valores a connectionObject
                        if (responseCustomer != null && responseCustomer.Item1.Count > 0)
                        {
                            connectionObject.ExternalCustomerID = responseCustomer.Item1[0].ToString();
                            connectionObject.ExternalCustomerPath = pathCustomer.FirstOrDefault();
                        }

                        if (responseTour != null && responseTour.Item1.Count > 0)
                        {
                            connectionObject.ToursObjects = responseTour.Item1
                                .Select((rt, index) => new ToursObjects
                                {
                                    ExternalTourID = rt.ToString(),
                                    ExternalTourIdPath = pathTour.ElementAtOrDefault(index)
                                }).ToList();
                        }

                        if (responsePurchase != null && responsePurchase.Item1.Count > 0)
                        {
                            connectionObject.PurchasesObject = responsePurchase.Item1
                                .Select((rp, index) => new PurchasesObjects
                                {
                                    ExternalPurchaseID = rp.ToString(),
                                    ExternalPurchasePath = pathPurchase.ElementAtOrDefault(index)
                                }).ToList();
                        }

                        if (responseNotes != null && responseNotes.Item1.Count > 0)
                        {
                            connectionObject.ExternalNoteID = responseNotes.Item1[0].ToString();
                            connectionObject.ExternalNotePath = pathNote.FirstOrDefault();
                        }

                        // Guardar el JSON completo en el objeto de conexión
                        connectionObject.JsonObject = jobject.ToString();

                        lock (lockObject)
                        {
                            connectionObjects.Add(connectionObject);
                        }
                    }
                }

                return connectionObjects;
            }
            catch (Exception exception)
            {
                MiddlewareErrorsDataAccess.Insert(MiddlewareErrorType.ImportTourReferenceException, apiconnectionID,
                            null, null, $"Error processing connection objectsn: {exception.Message}", true, null, null);
                return null;
            }
        }

        protected internal dynamic SetProperty(int apiConnectionID, IList<PropertyInfo> properties, PropertyMappings propertyTR, dynamic parent, JObject json, int apiConnection, string parentId = "", string parentPath = "", int? pos = null)
        {
            try
            {
                dynamic property = null, propertyUpdate = null, valueProperty = null;
                property = properties.FirstOrDefault(i => i.Name == propertyTR.Key.Split('.')[1].ToString().Trim());
                propertyUpdate = properties.FirstOrDefault(i => i.Name == propertyTR.Key.Split('.')[1].ToString().Trim() + "Updated");
                bool itsArray = false;
                dynamic iPath = null;

                if (property != null && propertyUpdate != null && json != null)
                {
                    foreach (var item in propertyTR.ResponseValue)
                    {
                        if (item.Path.StartsWith("$"))
                        {
                            iPath = item.Path;

                            if (property.Name.ToString().Contains("SalesUser") && iPath.ToString().Contains("sales[*]") && pos != null)
                                iPath = iPath.Replace("*", pos.ToString());
                            else if (parentPath != "")
                                iPath = ReplaceArrayIndex(iPath, parentPath);

                            dynamic items = null;

                            try
                            {
                                if (iPath.Contains("[*]") && parentPath != "" && PathsStartEqual(iPath, parentPath))
                                {
                                    // Extraer la parte del path antes de [*]
                                    string parentPt = iPath.Substring(0, iPath.IndexOf("[*]"));
                                    string childPath = iPath.Substring(iPath.IndexOf("[*]") + 3); // Extraer después de [*]
                                    items = json.SelectTokens(parentPt);
                                    foreach (JToken parentItem in items)
                                    {
                                        int firstBracketClose = parentPath.IndexOf(']');

                                        int firstBracketOpen = parentPath.LastIndexOf('[', firstBracketClose);

                                        // Obtenemos el path del padre sin el índice (ej: "$.parent")
                                        string baseParentPath = parentPath.Substring(0, firstBracketOpen);

                                        // Extraemos el índice (ej: 0)
                                        string index = parentPath.Substring(firstBracketOpen + 1, firstBracketClose - firstBracketOpen - 1);

                                        // Concatenamos el path del padre, el índice y el childPath para formar "$.parent[0].childProperty"
                                        string indexedPath = $"{baseParentPath}[{index}]{childPath}";

                                        items = json.SelectToken(indexedPath);
                                    }
                                }
                                else
                                {
                                    items = json.SelectToken(iPath);
                                }
                            }
                            catch
                            {
                                items = json.SelectTokens(iPath);
                                itsArray = true;
                            }

                            if (items != null)
                            {
                                if (!itsArray)
                                {
                                    if (valueProperty == null)
                                        valueProperty = items.ToString();
                                    else
                                        valueProperty = valueProperty + items.ToString();
                                }
                                else
                                {
                                    foreach (JToken data in items)
                                    {
                                        if (valueProperty == null)
                                            valueProperty = data.ToString();
                                        else
                                            valueProperty = valueProperty + "," + data.ToString();
                                    }
                                }
                            }
                            if (valueProperty == "")
                                valueProperty = null;
                        }
                        else if (!item.Path.StartsWith("$") && item.Path.ToString().IndexOf(":") != -1)
                        {
                            var path = "$." + item.Path.Trim().Split(':')[0];
                            var tokens = json.SelectTokens(path); // Usamos SelectTokens para obtener múltiples valores

                            if (tokens != null && tokens.Any())  // Verificamos si se han encontrado tokens
                            {
                                bool itsObject = false;
                                itsArray = false;
                                dynamic data_path = null;

                                // Si los tokens contienen una colección de elementos
                                if (tokens.Count() > 1)
                                {
                                    foreach (var token in tokens)
                                    {
                                        // Convertir cada token a su tipo adecuado
                                        if (token is JArray)
                                        {
                                            data_path = token.ToObject<JArray>();
                                            itsArray = true;
                                        }
                                        else if (token is JObject)
                                        {
                                            data_path = token.ToObject<JObject>();
                                            itsObject = true;
                                        }
                                        else
                                        {
                                            data_path = token.ToString().Replace("(", "").Replace(")", "").Split(',');
                                        }

                                        // Procesar si es un objeto
                                        if (itsObject && !item.Path.Contains("@all"))
                                        {
                                            Regex regex = new Regex(@"^\d+$");
                                            foreach (var pp in data_path.Properties())
                                            {
                                                if (regex.IsMatch(pp.Name) && pp.Value.ToString() != "")
                                                {
                                                    if (valueProperty == null)
                                                        valueProperty = pp.Value.ToString();
                                                    else
                                                        valueProperty += "," + pp.Value.ToString();
                                                }
                                            }
                                        }
                                        // Si es un array y contiene el "@all"
                                        else if (item.Path.Contains("@all"))
                                        {
                                            if (valueProperty == null)
                                                valueProperty = DataInArray(data_path);
                                            else
                                                valueProperty += DataInArray(data_path);
                                        }
                                        // Si el path contiene atributos ("@")
                                        else if (item.Path.Contains("@") && !item.Path.Contains("@all"))
                                        {
                                            if (valueProperty != null)
                                                valueProperty += DataInAttribute(data_path, item.Path);
                                            else
                                                valueProperty = DataInAttribute(data_path, item.Path);
                                        }
                                        // Si no contiene "@all"
                                        else
                                        {
                                            valueProperty = data_path;
                                        }
                                    }
                                }
                                else // Caso de un solo token (no un array de tokens)
                                {
                                    // Asignamos el primer token encontrado a la variable data_path
                                    var token = tokens.First();
                                    if (token is JArray)
                                    {
                                        data_path = token.ToObject<JArray>();
                                        itsArray = true;
                                    }
                                    else if (token is JObject)
                                    {
                                        data_path = token.ToObject<JObject>();
                                        itsObject = true;
                                    }
                                    else
                                    {
                                        data_path = token.ToString().Replace("(", "").Replace(")", "").Split(',');
                                    }

                                    // Procesar si es un objeto
                                    if (itsObject)
                                    {
                                        Regex regex = new Regex(@"^\d+$");
                                        foreach (var pp in data_path.Properties())
                                        {
                                            if (regex.IsMatch(pp.Name) && pp.Value.ToString() != "")
                                            {
                                                if (valueProperty == null)
                                                    valueProperty = pp.Value.ToString();
                                                else
                                                    valueProperty += "," + pp.Value.ToString();
                                            }
                                        }
                                    }
                                    // Si es un array y contiene el "@all"
                                    else if (item.Path.Contains("@all"))
                                    {
                                        if (valueProperty == null)
                                            valueProperty = DataInArray(data_path);
                                        else
                                            valueProperty += DataInArray(data_path);
                                    }
                                    // Si el path contiene atributos ("@")
                                    else if (item.Path.Contains("@") && !item.Path.Contains("@all"))
                                    {
                                        if (valueProperty != null)
                                            valueProperty += DataInAttribute(data_path, item.Path);
                                        else
                                            valueProperty = DataInAttribute(data_path, item.Path);
                                    }
                                    // Si no contiene "@all"
                                    else
                                    {
                                        valueProperty = data_path;
                                    }
                                }
                            }
                            else if (!item.Path.StartsWith("$") && valueProperty != null)
                            {
                                valueProperty += item.Path;
                            }
                            else if (!item.Path.StartsWith("$") && !item.Path.Contains("@all"))
                            {
                                valueProperty = item.Path;
                            }
                        }
                        else if (!item.Path.StartsWith("$") && valueProperty != null)
                            valueProperty += item.Path;
                        else if (!item.Path.StartsWith("$"))
                            valueProperty = item.Path;
                    }


                    if (propertyTR.DataCorrection == true)
                        valueProperty = GetSystemValue(MiddlewareObjectActions.request.ApiConnectionId, propertyTR.Key, valueProperty);

                    string previusly2 = valueProperty;

                    if (valueProperty != null)
                    {
                        string previusly = valueProperty;
                        valueProperty = SetValueType(valueProperty, property, apiConnectionID);

                        if (valueProperty != null)
                        {
                            if (property.Name == "PrimaryPhone" || property.Name == "SecondaryPhone")
                            {
                                try
                                {
                                    string value = BES.Rules.PhoneRules.FormatPhoneForDatabase(valueProperty);
                                }
                                catch (Exception exception)
                                {
                                    valueProperty = null;
                                    MiddlewareErrorsDataAccess.Insert(MiddlewareErrorType.ExecuteConnection, apiConnection, null, null, exception.ToString(), true, parentId, parentId);
                                }
                            }
                            try
                            {
                                property.SetValue(parent, valueProperty);
                                propertyUpdate.SetValue(parent, true);
                            }
                            catch (Exception e)
                            {
                                string error = e.Message + " " + property.Name + " - " + valueProperty + " - " + previusly;
                                MiddlewareErrorsDataAccess.Insert(MiddlewareErrorType.ImportTourReferenceException, apiConnectionID,
                           null, null, error, true, null, null);
                            }
                        }
                    }
                }
                return parent;
            }
            catch (Exception exception)
            {
                MiddlewareErrorsDataAccess.Insert(MiddlewareErrorType.ImportTourReferenceException, apiConnectionID,
                           null, null, exception.ToString(), true, null, null);
                return null;
            }
        }

        private string ReplaceArrayIndex(string itemPath, string parentPath)
        {
            // Encontrar la posición del primer corchete en ambos paths
            int itemBracketIndex = itemPath.IndexOf('[');
            int parentBracketIndex = parentPath.IndexOf('[');
            int parentBracketCloseIndex = parentPath.IndexOf(']');

            // Obtener las partes de los paths antes del primer corchete
            string itemPrefix = itemBracketIndex != -1 ? itemPath.Substring(0, itemBracketIndex) : itemPath;
            string parentPrefix = parentBracketIndex != -1 ? parentPath.Substring(0, parentBracketIndex) : parentPath;
            string parentPrefix2 = parentBracketCloseIndex != -1 ? parentPath.Substring(0, parentBracketCloseIndex + 1) : parentPath;
            // Verificar si ambos comienzan igual hasta el primer corchete
            if (itemPrefix == parentPrefix)
            {
                // Encontrar el índice en parentPath
                int startBracket = parentPath.IndexOf('[');
                int endBracket = parentPath.IndexOf(']', startBracket);

                // Validar que se encontró un índice
                if (startBracket == -1 || endBracket == -1 || endBracket <= startBracket)
                {
                    throw new ArgumentException("parentPath no contiene un índice válido");
                }

                // Extraer el índice de parentPath
                string index = parentPath.Substring(startBracket + 1, endBracket - startBracket - 1);

                // Reemplazar [*] en itemPath con el índice encontrado
                string resultPath = itemPath.Replace("[*]", $"[{index}]");

                return resultPath;
            }

            // Si los prefijos no coinciden, devolver itemPath sin cambios
            return itemPath;
        }


        private bool PathsStartEqual(string itemPath, string parentPath)
        {
            // Extraer la parte antes del primer "[" en itemPath
            string itemPathPrefix = itemPath.Contains("[") ? itemPath.Substring(0, itemPath.IndexOf("[")) : itemPath;

            // Extraer la parte antes del primer "[" en parentPath
            string parentPathPrefix = parentPath.Contains("[") ? parentPath.Substring(0, parentPath.IndexOf("[")) : parentPath;

            // Comparar ambas subcadenas
            return itemPathPrefix == parentPathPrefix;
        }

        private dynamic SetValueType(dynamic value, dynamic property, int apiconnectionID)
        {
            try
            {
                if (value != null)
                {
                    // Si la propiedad es de tipo string o es la propiedad "Age"
                    if (property.PropertyType == typeof(string) || (property.Name == "Age" && value.ToString().Contains("-")))
                    {
                        if (value.ToString().Contains("["))
                        {
                            // Convertimos el valor a una lista de strings si tiene el formato de array
                            string data = "";
                            foreach (var item in value)
                            {
                                data = string.IsNullOrEmpty(data) ? item.ToString() : data + ", " + item.ToString();
                            }
                            value = data;
                        }
                        else if (value.ToString().Contains("-") && property.Name == "Age")
                        {
                            try
                            {
                                var data = value.ToString().Split('-');
                                value = data[0].Trim(); // Tomamos el primer valor antes del guion
                                value = Convert.ToInt32(value); // Convertimos a int
                            }
                            catch (Exception e)
                            {
                                // Capturamos el error y podrías hacer un logging aquí
                                Console.WriteLine($"Error al procesar Age: {e.Message}");
                            }
                        }
                        else
                        {
                            value = value.ToString(); // Si es un string simple, lo convertimos a string
                        }
                    }
                    // Si la propiedad es de tipo decimal
                    else if (property.PropertyType == typeof(decimal) || property.PropertyType == typeof(decimal?))
                    {
                        value = Convert.ToDecimal(value);
                    }
                    // Si la propiedad es de tipo int
                    else if (property.PropertyType == typeof(int) || property.PropertyType == typeof(int?))
                    {
                        value = Convert.ToInt32(value);
                    }
                    // Si la propiedad es de tipo bool
                    else if (property.PropertyType == typeof(bool) || property.PropertyType == typeof(bool?))
                    {
                        // Convertimos strings a valores booleanos específicos
                        var stringValue = value.ToString().ToLower();
                        if (stringValue == "female")
                            value = false; // Female es false
                        else if (stringValue == "male")
                            value = true; // Male es true
                        else if (stringValue == "not determined")
                            value = null; // Not determined se convierte a null
                        else
                            value = Convert.ToBoolean(value); // Convertimos normalmente si no es Female o Male
                    }
                    // Si la propiedad es de tipo DateTime
                    else if (property.PropertyType == typeof(DateTime) || property.PropertyType == typeof(DateTime?))
                    {
                        var cultureInfo = new CultureInfo("en-US");
                        // Lista de formatos comunes
                        string[] dateTimeFormats = {
                            "M/d/yyyy h:mm:ss tt",   // Mes/Día/Año Hora:Minuto:Segundo AM/PM
                            "MM/dd/yyyy hh:mm:ss tt", // Mes/Día/Año Hora:Minuto:Segundo AM/PM (dígitos fijos)
                            "yyyy-MM-ddTHH:mm:ss",  // ISO 8601
                            "M/d/yyyy H:mm:ss",     // Mes/Día/Año Hora (24h)
                            "MM/dd/yyyy HH:mm:ss",  // Mes/Día/Año Hora (24h) (dígitos fijos)
                            "yyyy-MM-dd",           // Año-Mes-Día
                            "dd/MM/yyyy HH:mm:ss",  // Día/Mes/Año Hora (24h)
                            "dd-MM-yyyy HH:mm:ss",  // Día-Mes-Año Hora (24h)
                            "yyyy/MM/dd HH:mm:ss",  // Año/Mes/Día Hora (24h)
                            "dd/MM/yyyy",           // Día/Mes/Año
                            "dd-MM-yyyy"           // Día-Mes-Año
                        };

                        // Lista de formatos de solo hora
                        string[] timeFormats = {
                            "h:mm tt",  // Formato de hora AM/PM, ej: 7:00 PM
                            "hh:mm tt", // Formato de hora con dos dígitos AM/PM, ej: 07:00 PM
                            "HH:mm",     // Formato de hora de 24 horas, ej: 15:30
                            "h:mmtt",  // Formato de hora AM/PM, ej: 7:00 PM
                            "hh:mmtt", // Formato de hora con dos dígitos AM/PM, ej: 07:00 PM
                            "HH:mmtt"     // Formato de hora de 24 horas, ej: 15:30
                        };

                        DateTime parsedTime;
                        string formattedValue = value.ToString()
                            .Replace("a.m.", "AM")
                            .Replace("a. m.", "AM")
                            .Replace("a.m", "AM")
                            .Replace("am", "AM")
                            .Replace("p.m.", "PM")
                            .Replace("p. m.", "PM")
                            .Replace("p.m", "PM")
                            .Replace("pm", "PM");

                        try
                        {
                            // Intentar analizar usando TryParseExact con los formatos definidos
                            if (DateTime.TryParseExact(
                                formattedValue,
                                timeFormats,
                                cultureInfo,
                                DateTimeStyles.None,
                                out parsedTime))
                            {
                                // Si se analiza como solo hora, combinar con la fecha actual
                                DateTime today = DateTime.Today;
                                value = new DateTime(today.Year, today.Month, today.Day, parsedTime.Hour, parsedTime.Minute, parsedTime.Second);
                                //value = DateTimeRules.FormatTime(DateTime.Parse(value));
                            }
                            else if (DateTime.TryParseExact(
                                    formattedValue,
                                    dateTimeFormats,
                                    cultureInfo,
                                    DateTimeStyles.None,
                                    out parsedTime))
                            {
                                value = parsedTime;
                            }
                            else
                            {
                                // Intentar analizar con DateTime.Parse como respaldo
                                parsedTime = DateTime.Parse(formattedValue, cultureInfo, DateTimeStyles.None);

                                value = parsedTime;
                            }
                        }
                        catch (Exception ex)
                        {
                            value = null; // Si falla todo, asignar null
                        }
                    }
                    // Si la propiedad es una lista genérica
                    else if (property.PropertyType.Name == "List`1")
                    {
                        dynamic list = null;
                        Type genericType = property.PropertyType.GenericTypeArguments[0]; // Obtenemos el tipo genérico

                        // Inicializamos la lista si es de tipo string
                        if (genericType == typeof(string))
                        {
                            list = new List<string>();
                        }

                        if (list != null)
                        {
                            // Si el valor no está en formato de lista (no contiene "[") pero tiene múltiples elementos
                            if (!value.ToString().Contains("[") && value.ToString().Contains(","))
                            {
                                string[] items = value.ToString().Split(',');
                                foreach (var item in items)
                                {
                                    list.Add(item.Trim()); // Añadimos cada elemento a la lista
                                }
                            }
                            else
                            {
                                list.Add(value.ToString()); // Si es un solo valor, lo añadimos a la lista
                            }

                            value = list;
                        }
                    }
                    else
                    {
                        // Si no coincide con ningún tipo, asignamos null
                        value = null;
                    }
                }
                else
                {
                    // Si el valor es null, lo mantenemos como null
                    value = null;
                }
                return value;
            }
            catch (Exception exception)
            {
                MiddlewareErrorsDataAccess.Insert(MiddlewareErrorType.ImportTourReferenceException, apiconnectionID,
                           null, null, exception.ToString(), true, null, null);
                return null;
            }
        }

        private string ConvertTo24HourFormat(string inputDate)
        {
            // Regex para capturar la fecha, hora y AM/PM
            string pattern = @"(?<date>\d{1,2}/\d{1,2}/\d{4}) (?<hour>\d{1,2}):(?<minute>\d{2})(:(?<second>\d{2}))? (?<ampm>AM|PM)";

            return Regex.Replace(inputDate, pattern, match =>
            {
                string date = match.Groups["date"].Value;
                int hour = int.Parse(match.Groups["hour"].Value);
                string minute = match.Groups["minute"].Value;
                string second = match.Groups["second"].Success ? match.Groups["second"].Value : "00"; // Predeterminar segundos si no están presentes
                string ampm = match.Groups["ampm"].Value;

                // Convertir la hora si es PM
                if (ampm == "PM" && hour != 12)
                    hour += 12;

                // Ajustar la hora si es AM y las 12 (medianoche)
                if (ampm == "AM" && hour == 12)
                    hour = 0;

                // Retornar la fecha en formato 24 horas
                return "{date} {hour:D2}:{minute}:{second}";
            });
        }
        private JObject GetJsonObject(string jsonObject)
        {
            return JObject.Parse(jsonObject);
        }

        private Tuple<List<object>, string> GetValueJsonParam(JToken item, string key, string path, out List<string> indexesPath)
        {
            indexesPath = new List<string>();
            string indexedPath = "";
            List<object> value = new List<object>();
            
            try
            {
                if (item != null && !string.IsNullOrEmpty(path) && !string.IsNullOrEmpty(key))
                {
                    // Verificar el uso de paths que contienen [*] y procesar adecuadamente
                    if (path.Contains("[*]"))
                    {
                        string parentPath = path.Substring(0, path.IndexOf("[*]"));
                        string childPath = path.Substring(path.IndexOf("[*]") + 3);
                        IEnumerable<JToken> parentItems = item.SelectTokens(parentPath);

                        if (parentItems != null && parentItems.Any())
                        {
                            foreach (JToken parentItem in parentItems)
                            {
                                int index = 0;
                                foreach (JToken childItem in parentItem.Children())
                                {
                                    indexedPath = $"{parentPath}[{index}]{childPath}";
                                    JToken selectedChild = item.SelectToken(indexedPath);
                                    if (selectedChild != null)
                                    {
                                        value.Add(selectedChild);
                                        indexesPath.Add(indexedPath);
                                    }
                                    index++;
                                }
                            }
                        }
                    }
                    else
                    {
                        IEnumerable<JToken> items = item.SelectTokens(path);
                        foreach (JToken data in items)
                        {
                            value.Add(data);
                        }
                        indexesPath.Add(path);
                    }
                }
            }
            catch (Exception exception)
            {
                MiddlewareErrorsDataAccess.Insert(MiddlewareErrorType.ImportTourReferenceException, 0,
                           null, null, $"Error in GetValueJsonParam: {exception.Message}", true, null, null);
            }

            return Tuple.Create(value, indexedPath);
        }

        private String DataInAttribute(dynamic attribute, string data)
        {
            string result = null;
            foreach (var item in attribute)
            {
                if (data.IndexOf(item.ToString()) != -1)
                    result = item;
            }
            return result;
        }
        private String DataInArray(dynamic attribute)
        {
            string result = null;
            foreach (var item in attribute)
            {
                if (result == null)
                    result = item.ToString();
                else
                    result = result + "," + item.ToString();
            }
            return result;
        }

        private List<PropertyMappings> GetPropertyMappings(ApiRequestResponses apiRequest, string prefix)
        {
            return apiRequest.PropertyMappings
                             .Where(p => p.Key.StartsWith(prefix))
                             .Select(p => new PropertyMappings
                             {
                                 Key = p.Key,
                                 PropertyValueResponse = p.PropertyValueResponse,
                                 DataCorrection = p.DataCorrection,
                                 ResponseValue = p.ResponseValue
                             })
                             .ToList();
        }
        #endregion
    }
}