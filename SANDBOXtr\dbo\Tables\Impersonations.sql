﻿CREATE TABLE [dbo].[Impersonations] (
    [impersonationID]    INT      IDENTITY (1, 1) NOT NULL,
    [impersonatorU<PERSON><PERSON>] INT      NOT NULL,
    [impersonatedUserID] INT      NOT NULL,
    [insertTimeStamp]    DATETIME CONSTRAINT [DF_Impersonations_insertTimeStamp] DEFAULT (getdate()) NOT NULL,
    CONSTRAINT [PK_Impersonations] PRIMARY KEY CLUSTERED ([impersonationID] ASC),
    CONSTRAINT [FK_Impersonations_impersonatedUserID] FOREIGN KEY ([impersonatedUserID]) REFERENCES [dbo].[Users] ([userID]),
    CONSTRAINT [FK_Impersonations_impersonatorUserID] FOREIGN KEY ([impersonatorUserID]) REFERENCES [dbo].[Users] ([userID])
);


GO
CREATE TRIGGER [dbo].[Impersonations.InsertUpdateDelete]
    ON [dbo].[Impersonations]
    FOR INSERT, UPDATE, DELETE
AS 
BEGIN
    SET NOCOUNT ON;

    DECLARE @Activity  NVARCHAR (8), @id INT;

    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'updated'
    END

    IF EXISTS (SELECT * FROM inserted) AND NOT EXISTS(SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'inserted'
    END

    IF EXISTS (SELECT * FROM deleted) AND NOT EXISTS(SELECT * FROM inserted)
    BEGIN
        SET @Activity = 'deleted'
    END

    

    IF (@Activity = 'deleted')
    BEGIN
        DECLARE cur CURSOR FOR SELECT impersonationID FROM deleted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'Impersonations', @id, 'impersonationID',
                (
                    SELECT *
                    FROM deleted i 
                    WHERE i.impersonationID =  @id
                    FOR JSON AUTO
                ),0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
    ELSE
    BEGIN
        DECLARE cur CURSOR
        FOR SELECT impersonationID
        FROM inserted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'Impersonations', @id, 'impersonationID',
                (
                    SELECT *
                    FROM inserted i 
                    WHERE i.impersonationID =  @id
                    FOR JSON AUTO
                ), 0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
END

GO
DISABLE TRIGGER [dbo].[Impersonations.InsertUpdateDelete]
    ON [dbo].[Impersonations];

