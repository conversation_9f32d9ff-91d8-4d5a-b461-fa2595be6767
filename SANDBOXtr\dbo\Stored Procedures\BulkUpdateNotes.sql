﻿CREATE PROCEDURE [dbo].[BulkUpdateNotes]
    @DataTableParam dbo.NotesType READONLY -- Use the TVP defined above
AS
BEGIN
    SET NOCOUNT ON;

    -- Use MERGE to update existing records
    MERGE INTO Notes AS target
    USING @DataTableParam AS source
    ON target.noteID = source.noteID
    
    -- When a matching noteID is found, update the existing row
    WHEN MATCHED THEN
        UPDATE SET
            target.externalNoteID = source.externalNoteID,
            target.noteText = source.noteText,
            target.userID = source.userID,
            target.fieldID = source.fieldID,
            target.parentID = source.parentID,
            target.updateTimeStamp = GETDATE(); -- Automatically update the timestamp


END;