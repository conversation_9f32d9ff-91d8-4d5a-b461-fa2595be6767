﻿
CREATE PROCEDURE [dbo].[InsertCustomDisplay_HideWasConfirmed]

AS
BEGIN

	DECLARE @controlID nvarchar(64)
	DECLARE @controlDelegates nvarchar(4000)

	DECLARE @GroupTypeKey nvarchar(64) SET @GroupTypeKey = 'HideWasConfirmed'

	DELETE FROM CustomPageControlDisplayRights WHERE (customPageControlDisplayRightGroupTypeKey = @GroupTypeKey)

	DECLARE @Add int SET @Add = 1
	DECLARE @Remove int SET @Remove = 2

	DECLARE @Insert int SET @Insert = 1
	DECLARE @Select int SET @Select = 2
	DECLARE @Update int SET @Update = 3

	DECLARE @Visible int SET @Visible = 1
	DECLARE @Enabled int SET @Enabled = 2
	DECLARE @Text int SET @Text = 3
	DECLARE @Delegate int SET @Delegate = 4
	DECLARE @Authorized int SET @Authorized = 5
	DECLARE @Administrated int SET @Administrated = 6

	DECLARE @PagePathID [varchar] (256)

	SET @PagePathID = 'ASP.customers_tours_usercontrols_toursdetails_ascx_formView'

	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'divWasConfirmed',@Visible,0,NULL,NULL

	SET @PagePathID = 'ASP.reports_usercontrols_toursearchcriteria_ascx'

	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'trWasConfirmed',@Visible,0,NULL,NULL

	SET @PagePathID = 'TrackResults.BES.Services.ImportExportService'

	SET @controlDelegates = 'TrackResults.BES.Security.CollectionRules:RemoveKeyValuePairByStringKey(System.String ' + 'WasConfirmed' + ')'
	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'columnKeyDisplayNames',@Delegate,NULL,NULL,@controlDelegates

END