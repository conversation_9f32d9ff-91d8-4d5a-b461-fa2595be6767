﻿

CREATE FUNCTION [dbo].[GetConcantenatedTeamNamesByUserID]
(
	@UserID int,
	@Delimeter nvarchar(8)
)
RETURNS nvarchar(2048)
AS
BEGIN

	DECLARE @ConcantenatedList nvarchar(2048)

	SET @ConcantenatedList = ''

	SELECT @ConcantenatedList = @ConcantenatedList + @Delimeter + Teams.name
	FROM TeamsUsersMap INNER JOIN
		Teams ON TeamsUsersMap.teamID = Teams.teamID
	WHERE TeamsUsersMap.userID = @UserID
	ORDER BY Teams.name

	IF DATALENGTH(@ConcantenatedList) > 0
	BEGIN
		SET @ConcantenatedList = RIGHT(@ConcantenatedList, ((DATALENGTH(@ConcantenatedList)/2) - (DATALENGTH(@Delimeter)/2)))
	END

	RETURN @ConcantenatedList

END