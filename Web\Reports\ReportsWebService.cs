﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Reflection;
using System.Web;
using TrackResults.BES.Data.Cache.Static;
using TrackResults.BES.Data.Cache;
using TrackResults.BES.Data.Criteria.TierOne;
using TrackResults.BES.Data;
using TrackResults.BES.Data.Types;
using TrackResults.BES.DataAccess.Reports;
using TrackResults.BES.DataAccess;
using TrackResults.BES.Services;
using TrackResults.Common.Attributes;
using TrackResults.Common.Core.Display;
using TrackResults.Common.Utilities;
using Telerik.Web;
using TrackResults.BES.Data.Business;
using TrackResults.BES.Data.Tables;
using static TrackResults.Web.Administration.System.APIConnectionDetails;
using System.Data.SqlClient;
using TrackResults.Common.Core.Data.SqlClient.MSSql;
using TrackResults.Common.Keys;
using static System.Net.Mime.MediaTypeNames;
using TrackResults.BES.DataAccess.Reports.CustomAnalyticViews;
using System.Web.Script.Serialization;
using System.ComponentModel;
using DisplayNameAttribute = TrackResults.Common.Attributes.DisplayNameAttribute;
using TrackResults.Web.Security.DropDownDisplay;
using TrackResults.Web.Reports.UserControls;
using static TrackResults.BES.DataAccess.Notes.CustomFields;
using TrackResults.Web.Services;
using TrackResults.Common.Exceptions;
using TrackResults.Web.Integrations;
using TrackResults.Web.Keys;
using Telerik.Web.UI.ExportInfrastructure;
using System.Globalization;
using TrackResults.Web.Reports;
using TrackResults.Web.Reports.UserControls.CriteriaUserControls;
using TrackResults.Common.Core.System;
using TrackResults.Common.Core.Data;
using System.Windows.Forms;


namespace TrackResults.Web.Reports
{
	public class ReportsWebService
	{
		#region [properties]
		static string model_purchases = "[{\"featureKey\":\"Purchase.SaleDate\",\"headerText\":\"Purchase.SaleDate\",\"text\":\"saleDate\",\"parse\":\"MM/dd/yyyy\",\"child\":\"\",\"pass\":false},{\"featureKey\":\"TierOne.Customer\",\"headerText\":\"TierOne.Customer\",\"text\":\"\",\"parse\":\"\",\"child\":[{\"featureKey\":\"Customer.LastName\",\"headerText\":\"\",\"text\":\"lastName\",\"parse\":\"\",\"child\":\"\",\"pass\":false},{\"featureKey\":\"Customer.FirstName\",\"headerText\":\"\",\"text\":\"firstName\",\"parse\":\"\",\"child\":\"\",\"pass\":false},{\"featureKey\":\"Customer.GuestLastName\",\"headerText\":\"\",\"text\":\"guestLastName\",\"parse\":\"\",\"child\":\"\",\"pass\":false},{\"featureKey\":\"Customer.GuestFirstName\",\"headerText\":\"\",\"text\":\"guestFirstName\",\"parse\":\"\",\"child\":\"\",\"pass\":false},{\"featureKey\":\"Customer.GuestTypeID\",\"headerText\":\"\",\"text\":\"guestTypeName\",\"parse\":\"\",\"child\":\"\",\"pass\":false}],\"pass\":false},{\"featureKey\":\"Purchase.SaleTypeID\",\"headerText\":\"Purchase.SaleTypeID\",\"text\":\"saleTypeName\",\"parse\":\"\",\"child\":\"\",\"pass\":false},{\"featureKey\":\"Purchase.ProductID\",\"headerText\":\"Purchase.ProductID\",\"text\":\"productCode\",\"parse\":\"\",\"child\":\"\",\"pass\":false},{\"featureKey\":\"Purchase.SaleAmount\",\"headerText\":\"Purchase.SaleAmount\",\"text\":\"saleAmount\",\"parse\":\"\",\"child\":\"\",\"pass\":false},{\"featureKey\":\"Tour.LocationID\",\"headerText\":\"Tour.LocationID\",\"text\":\"locationName\",\"parse\":\"\",\"child\":\"\",\"pass\":false},{\"featureKey\":\"Tour.SalesRepID\",\"headerText\":\"Tour.SalesRepID\",\"text\":\"\",\"parse\":\"\",\"child\":[{\"featureKey\":\"Tour.PodiumID\",\"headerText\":\"\",\"text\":\"podium\",\"parse\":\"\",\"child\":\"\",\"pass\":false},{\"featureKey\":\"Tour.SalesRepID\",\"headerText\":\"\",\"text\":\"salesRep\",\"parse\":\"\",\"child\":\"\",\"pass\":false}],\"pass\":false},{\"featureKey\":\"Tour.SalesCloser1ID\",\"headerText\":\"Tour.SalesCloser1ID\",\"text\":\"\",\"parse\":\"\",\"child\":[{\"featureKey\":\"Tour.SalesCloser1ID\",\"headerText\":\"\",\"text\":\"salesCloser1\",\"parse\":\"\",\"child\":\"\",\"pass\":false},{\"featureKey\":\"Tour.SalesCloser2ID\",\"headerText\":\"\",\"text\":\"salesCloser2\",\"parse\":\"\",\"child\":\"\",\"pass\":false}],\"pass\":false}]";
		static string model_marketing = "[{\"featureKey\":\"TierOne.TourSectionTours\",\"headerText\":\"Status\",\"text\":\"\",\"parse\":\"\",\"child\":[{\"featureKey\":\"Tour.EntryDateTime\",\"headerText\":\"\",\"text\":\"entryDateTime\",\"parse\":\"\",\"child\":\"\",\"pass\":false},{\"featureKey\":\"Tour.TourStatusTypeID\",\"headerText\":\"\",\"text\":\"tourStatusTypeName\",\"parse\":\"\",\"child\":\"\",\"pass\":false},{\"featureKey\":\"Tour.ProductCategoryID\",\"headerText\":\"\",\"text\":\"productCategoryName\",\"parse\":\"\",\"child\":\"\",\"pass\":false}],\"pass\":false},{\"featureKey\":\"TierOne.TourSectionTours\",\"headerText\":\"TierOne.TourSectionTours\",\"text\":\"\",\"parse\":\"\",\"child\":[{\"featureKey\":\"Tour.TourID\",\"headerText\":\"\",\"text\":\"tourID\",\"parse\":\"\",\"child\":\"\",\"pass\":false},{\"featureKey\":\"Tour.LocationID\",\"headerText\":\"\",\"text\":\"locationName\",\"parse\":\"\",\"child\":\"\",\"pass\":false},{\"featureKey\":\"Tour.TourDate\",\"headerText\":\"\",\"text\":\"tourDate\",\"parse\":\"MM/dd/yyyy\",\"child\":\"\",\"pass\":false},{\"featureKey\":\"Tour.TourTime\",\"headerText\":\"\",\"text\":\"tourTime\",\"parse\":\"t\",\"child\":\"\",\"pass\":false}],\"pass\":false},{\"featureKey\":\"TierOne.Customer\",\"headerText\":\"TierOne.Customer\",\"text\":\"\",\"parse\":\"\",\"child\":[{\"featureKey\":\"Customer.LastName\",\"headerText\":\"\",\"text\":\"lastName\",\"parse\":\"\",\"child\":\"\",\"pass\":false},{\"featureKey\":\"Customer.FirstName\",\"headerText\":\"\",\"text\":\"firstName\",\"parse\":\"\",\"child\":\"\",\"pass\":false},{\"featureKey\":\"Customer.GuestLastName\",\"headerText\":\"\",\"text\":\"guestLastName\",\"parse\":\"\",\"child\":\"\",\"pass\":false},{\"featureKey\":\"Customer.GuestFirstName\",\"headerText\":\"\",\"text\":\"guestFirstName\",\"parse\":\"\",\"child\":\"\",\"pass\":false},{\"featureKey\":\"Customer.GuestTypeID\",\"headerText\":\"\",\"text\":\"guestTypeName\",\"parse\":\"\",\"child\":\"\",\"pass\":false},{\"featureKey\":\"Customer.PrimaryPhone\",\"headerText\":\"\",\"text\":\"primaryPhone\",\"parse\":\"\",\"child\":\"\",\"pass\":false},{\"featureKey\":\"Customer.StateID\",\"headerText\":\"\",\"text\":\"stateAbbreviation\",\"parse\":\"\",\"child\":\"\",\"pass\":false},{\"featureKey\":\"Customer.CountryID\",\"headerText\":\"\",\"text\":\"countryName\",\"parse\":\"\",\"child\":\"\",\"pass\":false}],\"pass\":false},{\"featureKey\":\"TierOne.TourSectionMarketing\",\"headerText\":\"TierOne.TourSectionMarketing\",\"text\":\"\",\"parse\":\"\",\"child\":[{\"featureKey\":\"Tour.VenueID\",\"headerText\":\"\",\"text\":\"venueCode\",\"parse\":\"\",\"child\":\"\",\"pass\":false},{\"featureKey\":\"Tour.CampaignID\",\"headerText\":\"\",\"text\":\"campaignCode\",\"parse\":\"\",\"child\":\"\",\"pass\":false},{\"featureKey\":\"Tour.HotelID\",\"headerText\":\"\",\"text\":\"hotelCode\",\"parse\":\"\",\"child\":\"\",\"pass\":false},{\"featureKey\":\"Tour.DepositAmount\",\"headerText\":\"\",\"text\":\"depositAmount\",\"parse\":\"C0\",\"child\":\"\",\"pass\":false}],\"pass\":false},{\"featureKey\":\"TierOne.TourSectionMarketing\",\"headerText\":\"TierOne.MarketingTeamID\",\"text\":\"\",\"parse\":\"\",\"child\":[{\"featureKey\":\"Tour.MarketingTeamID\",\"headerText\":\"\",\"text\":\"marketingTeam\",\"parse\":\"\",\"child\":\"\",\"pass\":false},{\"featureKey\":\"Tour.MarketingAgentID\",\"headerText\":\"\",\"text\":\"marketingAgent\",\"parse\":\"\",\"child\":\"\",\"pass\":false},{\"featureKey\":\"Tour.MarketingCloserID\",\"headerText\":\"\",\"text\":\"marketingCloser\",\"parse\":\"\",\"child\":\"\",\"pass\":false},{\"featureKey\":\"Tour.ConfirmerID\",\"headerText\":\"\",\"text\":\"confirmer\",\"parse\":\"\",\"child\":\"\",\"pass\":false},{\"featureKey\":\"Tour.ResetterID\",\"headerText\":\"\",\"text\":\"resetter\",\"parse\":\"\",\"child\":\"\",\"pass\":false}],\"pass\":false},{\"featureKey\":\"Tour.GiftIDs\",\"headerText\":\"Tour.GiftIDs.Plural\",\"text\":\"\",\"parse\":\"\",\"child\":[{\"featureKey\":\"Tour.GiftIDs\",\"headerText\":\"\",\"text\":\"giftCodes\",\"parse\":\"\",\"child\":\"\",\"pass\":false}],\"pass\":false}]";
		static string model_revenue = "[{\"featureKey\":\"\",\"headerText\":\"TierOne.Customer\",\"text\":\"Customer\",\"parse\":\"\",\"child\":[{\"featureKey\":\"\",\"headerText\":\"Customer.LastName\",\"text\":\"lastName\",\"parse\":\"\",\"child\":\"\",\"pass\":false},{\"featureKey\":\"\",\"headerText\":\"Customer.FirstName\",\"text\":\"firstName\",\"parse\":\"\",\"child\":\"\",\"pass\":false}],\"pass\":false},{\"featureKey\":\"\",\"headerText\":\"Purchase.PurchasesPickList2ItemID\",\"text\":\"purchasesPickList2ItemName\",\"parse\":\"\",\"child\":\"\",\"pass\":false},{\"featureKey\":\"\",\"headerText\":\"Purchase.ProductID\",\"text\":\"productCode\",\"parse\":\"\",\"child\":\"\",\"pass\":true},{\"featureKey\":\"\",\"headerText\":\"Purchase.SaleAmount\",\"text\":\"saleAmount\",\"parse\":\"C0\",\"child\":\"\",\"pass\":true},{\"featureKey\":\"\",\"headerText\":\"Purchase.Fees1Amount\",\"text\":\"fees1Amount\",\"parse\":\"C0\",\"child\":\"\",\"pass\":false},{\"featureKey\":\"\",\"headerText\":\"Purchase.Fees2Amount\",\"text\":\"fees2Amount\",\"parse\":\"C0\",\"child\":\"\",\"pass\":false},{\"featureKey\":\"\",\"headerText\":\"Purchase.Total\",\"text\":\"total\",\"parse\":\"C0\",\"child\":\"\",\"pass\":true},{\"featureKey\":\"\",\"headerText\":\"Purchase.Fees3Amount\",\"text\":\"fees3Amount\",\"parse\":\"C0\",\"child\":\"\",\"pass\":false},{\"featureKey\":\"\",\"headerText\":\"Purchase.Fees4Amount\",\"text\":\"fees4Amount\",\"parse\":\"C0\",\"child\":\"\",\"pass\":false},{\"featureKey\":\"\",\"headerText\":\"Purchase.Fees5Amount\",\"text\":\"fees5Amount\",\"parse\":\"C0\",\"child\":\"\",\"pass\":false},{\"featureKey\":\"\",\"headerText\":\"Purchase.DownPaymentAmount\",\"text\":\"downPaymentAmount\",\"parse\":\"C0\",\"child\":\"\",\"pass\":true},{\"featureKey\":\"\",\"headerText\":\"Purchase.Balance\",\"text\":\"balance\",\"parse\":\"C0\",\"child\":\"\",\"pass\":true}]";
		static string model_sales = "[{\"featureKey\":\"TierOne.TourSectionTours\",\"headerText\":\"TierOne.TourSectionTours\",\"text\":\"Status\",\"parse\":\"\",\"child\":[{\"featureKey\":\"Tour.EntryDateTime\",\"headerText\":\"\",\"text\":\"entryDateTime\",\"parse\":\"MM/dd/yyyy\",\"child\":\"\",\"pass\":false},{\"featureKey\":\"Tour.TourStatusTypeID\",\"headerText\":\"\",\"text\":\"tourStatusTypeName\",\"parse\":\"\",\"child\":\"\",\"pass\":false}],\"pass\":false},{\"featureKey\":\"TierOne.TourSectionTours\",\"headerText\":\"TierOne.TourSectionTours\",\"text\":\"\",\"parse\":\"\",\"child\":[{\"featureKey\":\"Tour.TourID\",\"headerText\":\"\",\"text\":\"tourID\",\"parse\":\"\",\"child\":\"\",\"pass\":false},{\"featureKey\":\"Tour.LocationID\",\"headerText\":\"\",\"text\":\"locationName\",\"parse\":\"\",\"child\":\"\",\"pass\":false},{\"featureKey\":\"Tour.TourDate\",\"headerText\":\"\",\"text\":\"tourDate\",\"parse\":\"MM/dd/yyyy\",\"child\":\"\",\"pass\":false},{\"featureKey\":\"Tour.TourTime\",\"headerText\":\"\",\"text\":\"tourTime\",\"parse\":\"t\",\"child\":\"\",\"pass\":false}],\"pass\":false},{\"featureKey\":\"TierOne.Customer\",\"headerText\":\"TierOne.Customer\",\"text\":\"\",\"parse\":\"\",\"child\":[{\"featureKey\":\"Customer.LastName\",\"headerText\":\"\",\"text\":\"lastName\",\"parse\":\"\",\"child\":\"\",\"pass\":false},{\"featureKey\":\"Customer.FirstName\",\"headerText\":\"\",\"text\":\"firstName\",\"parse\":\"\",\"child\":\"\",\"pass\":false},{\"featureKey\":\"Customer.GuestLastName\",\"headerText\":\"\",\"text\":\"guestLastName\",\"parse\":\"\",\"child\":\"\",\"pass\":false},{\"featureKey\":\"Customer.GuestTypeID\",\"headerText\":\"\",\"text\":\"guestTypeName\",\"parse\":\"\",\"child\":\"\",\"pass\":false},{\"featureKey\":\"Customer.PrimaryPhone\",\"headerText\":\"\",\"text\":\"primaryPhone\",\"parse\":\"\",\"child\":\"\",\"pass\":false},{\"featureKey\":\"Customer.StateID\",\"headerText\":\"\",\"text\":\"stateAbbreviation\",\"parse\":\"\",\"child\":\"\",\"pass\":false},{\"featureKey\":\"Customer.CountryID\",\"headerText\":\"\",\"text\":\"countryName\",\"parse\":\"\",\"child\":\"\",\"pass\":false}],\"pass\":false},{\"featureKey\":\"TierOne.TourSectionMarketing\",\"headerText\":\"TierOne.MarketingTeamID\",\"text\":\"\",\"parse\":\"\",\"child\":[{\"featureKey\":\"Tour.MarketingTeamID\",\"headerText\":\"\",\"text\":\"marketingTeam\",\"parse\":\"\",\"child\":\"\",\"pass\":false},{\"featureKey\":\"Tour.MarketingAgentID\",\"headerText\":\"\",\"text\":\"marketingAgent\",\"parse\":\"\",\"child\":\"\",\"pass\":false},{\"featureKey\":\"Tour.MarketingCloserID\",\"headerText\":\"\",\"text\":\"marketingCloser\",\"parse\":\"\",\"child\":\"\",\"pass\":false},{\"featureKey\":\"Tour.ConfirmerID\",\"headerText\":\"\",\"text\":\"confirmer\",\"parse\":\"\",\"child\":\"\",\"pass\":false},{\"featureKey\":\"Tour.ResetterID\",\"headerText\":\"\",\"text\":\"resetter\",\"parse\":\"\",\"child\":\"\",\"pass\":false}],\"pass\":false},{\"featureKey\":\"TierOne.TourSectionSales\",\"headerText\":\"TierOne.SalesTeamID\",\"text\":\"\",\"parse\":\"\",\"child\":[{\"featureKey\":\"Tour.PodiumID\",\"headerText\":\"\",\"text\":\"podium\",\"parse\":\"\",\"child\":\"\",\"pass\":false},{\"featureKey\":\"Tour.SalesRepID\",\"headerText\":\"\",\"text\":\"salesRep\",\"parse\":\"\",\"child\":\"\",\"pass\":false},{\"featureKey\":\"Tour.SalesCloser1ID\",\"headerText\":\"\",\"text\":\"salesCloser1\",\"parse\":\"\",\"child\":\"\",\"pass\":false},{\"featureKey\":\"Tour.SalesCloser2ID\",\"headerText\":\"\",\"text\":\"salesCloser2\",\"parse\":\"\",\"child\":\"\",\"pass\":false},{\"featureKey\":\"Tour.ExitID\",\"headerText\":\"\",\"text\":\"exit\",\"parse\":\"\",\"child\":\"\",\"pass\":false},{\"featureKey\":\"Tour.VerificationOfficerID\",\"headerText\":\"\",\"text\":\"verificationOfficer\",\"parse\":\"\",\"child\":\"\",\"pass\":false},{\"featureKey\":\"Tour.SalesRepExit1ID\",\"headerText\":\"\",\"text\":\"salesRepExit1\",\"parse\":\"\",\"child\":\"\",\"pass\":false},{\"featureKey\":\"Tour.SalesRepExit2ID\",\"headerText\":\"\",\"text\":\"salesRepExit2\",\"parse\":\"\",\"child\":\"\",\"pass\":false},{\"featureKey\":\"Tour.SalesRepExit3ID\",\"headerText\":\"\",\"text\":\"salesRepExit3\",\"parse\":\"\",\"child\":\"\",\"pass\":false}],\"pass\":false},{\"featureKey\":\"TierOne.Purchase\",\"headerText\":\"TierOne.Purchase\",\"text\":\"Status\",\"parse\":\"\",\"child\":[{\"featureKey\":\"Purchase.ExternalPurchaseID\",\"headerText\":\"\",\"text\":\"ExternalPurchaseID\",\"parse\":\"\",\"child\":\"\",\"pass\":false},{\"featureKey\":\"Purchase.SaleDate\",\"headerText\":\"\",\"text\":\"SaleDate\",\"parse\":\"\",\"child\":\"\",\"pass\":false},{\"featureKey\":\"Purchase.ProductCategoryID\",\"headerText\":\"\",\"text\":\"ProductCategoryID\",\"parse\":\"\",\"child\":\"\",\"pass\":false},{\"featureKey\":\"Purchase.ProductID\",\"headerText\":\"\",\"text\":\"ProductID\",\"parse\":\"\",\"child\":\"\",\"pass\":false},{\"featureKey\":\"Purchase.SaleAmount\",\"headerText\":\"\",\"text\":\"SaleAmount\",\"parse\":\"\",\"child\":\"\",\"pass\":false},{\"featureKey\":\"Purchase.SaleTypeID\",\"headerText\":\"\",\"text\":\"SaleTypeID\",\"parse\":\"\",\"child\":\"\",\"pass\":false},{\"featureKey\":\"Purchase.SaleStatusTypeID\",\"headerText\":\"\",\"text\":\"SaleStatusTypeID\",\"parse\":\"\",\"child\":\"\",\"pass\":false}],\"pass\":false}]";

        private List<(string nameJoin, string queryTables, string queryJoins)> preJoins = new List<(string, string, string)>
		{
			("tdagym",
			@"SELECT Tours.tourID, Tours.tourDate, Tours.tourStatusTypeID, Tours.rescheduledTourID, 
					  Purchases.purchaseID, Purchases.supersededFromPurchaseID, Purchases.supersedingFromPender, 
					  Purchases.saleDate, Purchases.saleStatusTypeID, Purchases.saleAmount, 
					  (SELECT SUM(Gifts.Value) 
					   FROM Gifts 
					   WHERE Gifts.giftID IN 
							 (SELECT ToursGiftsMap.giftID 
							  FROM ToursGiftsMap 
							  WHERE ToursGiftsMap.tourID = Tours.tourID)) AS giftValue, 
					  TourStatusTypes.*, 
					  TourStatusTypes.tourStatusTypeName AS tourStatusTypeName, 
					  SaleStatusTypes.*, 
					  SaleStatusTypes.saleStatusTypeName AS saleStatusTypeName",
			@"FROM Tours 
			  INNER JOIN #FilteredTours ON Tours.tourID = #FilteredTours.tourID 
			  LEFT OUTER JOIN Purchases ON Tours.tourID = Purchases.tourID 
			  LEFT OUTER JOIN Cancellations AS Cancellations1 ON Purchases.purchaseID = Cancellations1.cancellationPurchaseID 
			  LEFT OUTER JOIN TourStatusTypes ON Tours.tourStatusTypeID = TourStatusTypes.tourStatusTypeID 
			  LEFT OUTER JOIN SaleStatusTypes ON Purchases.saleStatusTypeID = SaleStatusTypes.saleStatusTypeID"),

			("fbda84",
			@"SELECT Tours.tourID, Tours.tourStatusTypeID",
			@"FROM Tours 
			  INNER JOIN #FilteredTours ON Tours.tourID = #FilteredTours.tourID"),

			("cqwq9w",
			@"SELECT Tours.tourID, Tours.tourDate, Tours.tourStatusTypeID, Purchases.purchaseID, 
					  Purchases.supersededFromPurchaseID, Purchases.supersedingFromPender, Purchases.saleDate, 
					  Purchases.saleTypeID, Purchases.saleStatusTypeID, Purchases.saleAmount, 
					  Purchases.downPaymentAmount, Purchases.fees2Amount, TourStatusTypes.*, 
					  TourStatusTypes.tourStatusTypeName AS tourStatusTypeName, SaleStatusTypes.*, 
					  SaleStatusTypes.saleStatusTypeName AS saleStatusTypeName",
			@"FROM Tours 
			  INNER JOIN #FilteredTours ON Tours.tourID = #FilteredTours.tourID 
			  LEFT OUTER JOIN Purchases ON Tours.tourID = Purchases.tourID 
			  LEFT OUTER JOIN Cancellations AS Cancellations1 ON Purchases.purchaseID = Cancellations1.cancellationPurchaseID 
			  LEFT OUTER JOIN TourStatusTypes ON Tours.tourStatusTypeID = TourStatusTypes.tourStatusTypeID 
			  LEFT OUTER JOIN SaleStatusTypes ON Purchases.saleStatusTypeID = SaleStatusTypes.saleStatusTypeID"),

			("--",
            @"SELECT Tours.*, C.*, CONCAT(C.firstName, ' ', C.lastName) AS customerName, 
					  Purchases.*, TourStatusTypes.*, 
					  TourStatusTypes.tourStatusTypeName AS tourStatusTypeName, SaleStatusTypes.*, 
					  SaleStatusTypes.saleStatusTypeName AS saleStatusTypeName, SaleTypes.*, TourTypes.*, 
					  podium.fullName AS salesUser1Name, salesRep.fullName AS salesUser2Name, 
					  salesCloser1.fullName AS salesUser3Name, exitUser.fullName AS salesUser4Name, 
					  salesCloser2.fullName AS salesUser5Name, verificationOfficer.fullName AS salesUser6Name 
					  
                      ",

            @"FROM Tours 
			  INNER JOIN #FilteredTours ON Tours.tourID = #FilteredTours.tourID 
			  INNER JOIN Customers as C ON Tours.customerID = C.customerID 
			  LEFT OUTER JOIN TourStatusTypes ON Tours.tourStatusTypeID = TourStatusTypes.tourStatusTypeID 
			  LEFT OUTER JOIN Purchases ON Tours.tourID = Purchases.tourID 
			  LEFT OUTER JOIN SaleStatusTypes ON Purchases.saleStatusTypeID = SaleStatusTypes.saleStatusTypeID 
			  LEFT OUTER JOIN SaleTypes ON Purchases.saleTypeID = SaleTypes.saleTypeID 
			  LEFT OUTER JOIN TourTypes ON Tours.tourTypeID = TourTypes.tourTypeID 
			  LEFT JOIN Users AS podium ON Tours.podiumID = podium.userID 
			  LEFT JOIN Users AS salesRep ON Tours.salesRepID = salesRep.userID 
			  LEFT JOIN Users AS salesCloser1 ON Tours.salesCloser1ID = salesCloser1.userID 
			  LEFT JOIN Users AS exitUser ON Tours.exitID = exitUser.userID 
			  LEFT JOIN Users AS salesCloser2 ON Tours.salesCloser2ID = salesCloser2.userID 
			  LEFT JOIN Users AS verificationOfficer ON Tours.verificationOfficerID = verificationOfficer.userID 
			  
			"),
			("armx24",
			@"SELECT U.FULLNAME AS name, R.Rolename AS role, L.NAME AS location, T.name AS team, 
					  ISNULL(au.username, '') AS loginName, 
					  CASE WHEN AU.USERID IS NULL THEN 'False' ELSE 'True' END AS loginAccess, 
					  CASE WHEN AM.IsLockedOut = 0 THEN 'False' WHEN AM.IsLockedOut = 1 THEN 'True' ELSE '--' END AS lockedOut, 
					  ISNULL(FORMAT(AU.LastActivityDate, 'dd/MM/yyyy'), '') AS lastLogin",
			@"FROM USERS U 
			  LEFT JOIN ROLES R ON U.ROLEID = R.ROLEID 
			  LEFT JOIN [aspnet_Users] au ON u.useraccountid = au.userid 
			  LEFT JOIN TeamsUsersMap TM ON U.USERID = TM.USERID 
			  INNER JOIN TEAMS T ON TM.TEAMID = T.TEAMID 
			  LEFT JOIN TeamsLocationsMap TLM ON T.TEAMID = TLM.TEAMID 
			  INNER JOIN LOCATIONS L ON TLM.LOCATIONID = L.LOCATIONID 
			  LEFT JOIN [aspnet_Membership] AM ON au.userid = AM.USERID")
		};




        private List<(string reportName, string avk, string reportType, string groupBy, int joinId, int reportId, int reportBySubType, string jsonObject, int dateOn, int dateOff)> typeReports = new List<(string, string, string, string, int, int, int, string, int, int)>();

		private class AuditReport
		{
			public string name { get; set; }
			public string role { get; set; }
			public string location { get; set; }
			public string team { get; set; }
			public string loginName { get; set; }
			public string loginAccess { get; set; }
			public string lockedOut { get; set; }
			public string lastLogin { get; set; }

		}
		private class CustomKpi
		{
			public string headerText { get; set; }
			public string pivot { get; set; }
			public string original { get; set; }
		}

		private class ReportsTypeOn
		{
			public string reportId { get; set; }
			public string reportName { get; set; }
		}

		private class DateRanges
		{
			public int key { get; set; }
			public string value { get; set; }
		}

		private class YearRanges
		{
			public int key { get; set; }
			public string value { get; set; }
		}

		private class LocationUser
		{
			public int id { get; set; }
			public string name { get; set; }
		}

		private static readonly Type displayNameAttributeType = typeof(DisplayNameAttribute);
		#endregion

		#region[analytics]

		#endregion
		public string getDateRange()
		{
			List<DateRanges> dateRanges = new List<DateRanges>();
			List<YearRanges> yearRanges = new List<YearRanges>();
			dateRanges.Add(new DateRanges { key = (int)DateRangePeriod.Unknown, value = "-Unassigned" });
			dateRanges.Add(new DateRanges { key = (int)DateRangePeriod.Custom, value = DateRangePeriod.Custom.GetDisplayName() });
			dateRanges.Add(new DateRanges { key = (int)DateRangePeriod.Today, value = DateRangePeriod.Today.GetDisplayName() });
			dateRanges.Add(new DateRanges { key = (int)DateRangePeriod.Tomorrow, value = DateRangePeriod.Tomorrow.GetDisplayName() });
			dateRanges.Add(new DateRanges { key = (int)DateRangePeriod.Yesterday, value = DateRangePeriod.Yesterday.GetDisplayName() });
			dateRanges.Add(new DateRanges { key = (int)DateRangePeriod.LastSevenDays, value = DateRangePeriod.LastSevenDays.GetDisplayName() });
			dateRanges.Add(new DateRanges { key = (int)DateRangePeriod.WeekToDate, value = DateRangePeriod.WeekToDate.GetDisplayName() });
			dateRanges.Add(new DateRanges { key = (int)DateRangePeriod.CurrentWeek, value = DateRangePeriod.CurrentWeek.GetDisplayName() });
			dateRanges.Add(new DateRanges { key = (int)DateRangePeriod.NextWeek, value = DateRangePeriod.NextWeek.GetDisplayName() });
			dateRanges.Add(new DateRanges { key = (int)DateRangePeriod.LastWeek, value = DateRangePeriod.LastWeek.GetDisplayName() });
			dateRanges.Add(new DateRanges { key = (int)DateRangePeriod.LastThirtyDays, value = DateRangePeriod.LastThirtyDays.GetDisplayName() });
			dateRanges.Add(new DateRanges { key = (int)DateRangePeriod.LastSixtyDays, value = DateRangePeriod.LastSixtyDays.GetDisplayName() });
			dateRanges.Add(new DateRanges { key = (int)DateRangePeriod.LastNinetyDays, value = DateRangePeriod.LastNinetyDays.GetDisplayName() });
			dateRanges.Add(new DateRanges { key = (int)DateRangePeriod.MonthToDate, value = DateRangePeriod.MonthToDate.GetDisplayName() });
			dateRanges.Add(new DateRanges { key = (int)DateRangePeriod.CurrentMonth, value = DateRangePeriod.CurrentMonth.GetDisplayName() });
			dateRanges.Add(new DateRanges { key = (int)DateRangePeriod.NextMonth, value = DateRangePeriod.NextMonth.GetDisplayName() });
			dateRanges.Add(new DateRanges { key = (int)DateRangePeriod.LastMonth, value = DateRangePeriod.LastMonth.GetDisplayName() });
			dateRanges.Add(new DateRanges { key = (int)DateRangePeriod.QuarterToDate, value = DateRangePeriod.QuarterToDate.GetDisplayName() });
			dateRanges.Add(new DateRanges { key = (int)DateRangePeriod.LastQuarter, value = DateRangePeriod.LastQuarter.GetDisplayName() });
			dateRanges.Add(new DateRanges { key = (int)DateRangePeriod.YearToDate, value = DateRangePeriod.YearToDate.GetDisplayName() });
			dateRanges.Add(new DateRanges { key = (int)DateRangePeriod.CurrentYear, value = DateRangePeriod.CurrentYear.GetDisplayName() });

			yearRanges.Add(new YearRanges { key = (int)YearRangePeriod.Custom, value = YearRangePeriod.Custom.GetDisplayName() });
			yearRanges.Add(new YearRanges { key = (int)YearRangePeriod.CurrentYear, value = YearRangePeriod.CurrentYear.GetDisplayName() });
			yearRanges.Add(new YearRanges { key = (int)YearRangePeriod.LastYear, value = YearRangePeriod.LastYear.GetDisplayName() });
			yearRanges.Add(new YearRanges { key = (int)YearRangePeriod.TwoYearsAgo, value = YearRangePeriod.TwoYearsAgo.GetDisplayName() });
			yearRanges.Add(new YearRanges { key = (int)YearRangePeriod.ThreeYearsAgo, value = YearRangePeriod.ThreeYearsAgo.GetDisplayName() });
			yearRanges.Add(new YearRanges { key = (int)YearRangePeriod.FourYearsAgo, value = YearRangePeriod.FourYearsAgo.GetDisplayName() });
			yearRanges.Add(new YearRanges { key = (int)YearRangePeriod.FiveYearsAgo, value = YearRangePeriod.FiveYearsAgo.GetDisplayName() });


			var serializerSettings = new JsonSerializerSettings
			{
				PreserveReferencesHandling = PreserveReferencesHandling.None
			};
			var result = JsonConvert.SerializeObject(new { dateRanges, yearRanges }, Formatting.None, serializerSettings);
			return result;
		}

		public string getLocations()
		{
			var userID = SecurityProfile.UserID;
			List<Location> data = null;
			if (SecurityProfile.RoleName == "System Administrator")
				data = LocationsDataAccess.SelectActiveLocationByUser(0);
			else
				data = LocationsDataAccess.SelectActiveLocationByUser(userID);

			var serializerSettings = new JsonSerializerSettings
			{
				PreserveReferencesHandling = PreserveReferencesHandling.None
			};
			var result = JsonConvert.SerializeObject(new { data }, Formatting.None, serializerSettings);
			return result;

		}

		public string getReports()
		{
			typeReports.Clear();
			typeReports.Add(("--", "--", "", "", 0, 0, 0, null, 0, 0));

			var Username = SecurityProfile.Username;
			string sqlStatement = "";
			SqlCommand sqlCommand = null;
			if (SecurityProfile.RoleName == "System Administrator")
			{

				typeReports.Add(("Json File Local", "local_json", "", "", 0, 0, 0, null, 0, 0));
				typeReports.Add(("CSV File Local", "local_csv", "", "", 0, 0, 0, null, 0, 0));
				

				sqlStatement =
						 @" SELECT *
				            FROM Reports
				            WHERE name  != '' AND groupBy != '' ORDER BY ReportID ASC";
				sqlCommand = new SqlCommand(sqlStatement);
			}
			else
			{
				sqlStatement =
                         @" SELECT * FROM Reports R
                            INNER JOIN ReportsUsers RU ON R.reportId = RU.reportId
                            INNER JOIN aspnet_Users AU ON RU.userId = AU.UserId
                            WHERE name  != '' AND groupBy != '' AND AU.UserName = @Username ORDER BY R.ReportID ASC"
                ;
				sqlCommand = new SqlCommand(sqlStatement);
				sqlCommand.Parameters.AddWithValue("@Username", Username);

			}



			MSSqlDataAccess.ExecuteReader(ConnectionStrings.Default, sqlCommand, delegate (SqlDataReader sqlDataReader)
			{
				while (sqlDataReader.Read())
				{
					typeReports.Add((sqlDataReader["name"].ToString(), sqlDataReader["reportId"].ToString(), "", sqlDataReader["groupBy"].ToString(), 0, (sqlDataReader["reportId"] as int?) ?? 0, 0, "", (sqlDataReader["startDate"] as int?) ?? 0, (sqlDataReader["endDate"] as int?) ?? 0));
				}
			});

			var serializerSettings = new JsonSerializerSettings
			{
				PreserveReferencesHandling = PreserveReferencesHandling.None
			};
			var result = JsonConvert.SerializeObject(new { typeReports }, Formatting.None, serializerSettings);
			return result;
		}


		public string getReportsOnType()
		{
			List<ReportsTypeOn> reportsTypes = new List<ReportsTypeOn>();

			ReportsTypeOn reports2 = new ReportsTypeOn();
			reports2.reportName = "--";
			reports2.reportId = "0";
			reportsTypes.Add(reports2);

			foreach (ReportByType reportType in Enum.GetValues(typeof(ReportByType)))
			{
				ReportsTypeOn reports = new ReportsTypeOn();
				ReportByTypeDisplayNames reportBy = new ReportByTypeDisplayNames();
				reports.reportName = reportBy.GetDisplayName(reportType);
				reports.reportId = Convert.ToString((int)reportType);
				reportsTypes.Add(reports);
			}

			reports2 = new ReportsTypeOn();
			reports2.reportName = "Customer Name";
			reports2.reportId = "1000";
			reportsTypes.Add(reports2);

			reports2 = new ReportsTypeOn();
			reports2.reportName = "User FullName";
			reports2.reportId = "2000";
			reportsTypes.Add(reports2);

			var serializerSettings = new JsonSerializerSettings
			{
				PreserveReferencesHandling = PreserveReferencesHandling.None
			};

			var result = JsonConvert.SerializeObject(new { reportsTypes }, Formatting.None, serializerSettings);
			return result;
		}

		public string getKPIsDataByReportId(string avk)
		{
			List<CustomKpi> kpiCodesList = new List<CustomKpi>();
			if (avk != "armx24")
			{
				CustomerReference customerReference = new CustomerReference();
				TourReference tourReference = new TourReference();
				PurchaseReference purchaseReference = new PurchaseReference();
				Gifts giftsReference = new Gifts();
				Cancellation cancellationReference = new Cancellation();
				Locations locationReference = new Locations();



				Type customerType = customerReference.GetType();
				IList<PropertyInfo> customerProperties = new List<PropertyInfo>(customerType.GetProperties());
				Type tourType = tourReference.GetType();
				IList<PropertyInfo> tourProperties = new List<PropertyInfo>(tourType.GetProperties());
				Type purshaceType = purchaseReference.GetType();
				IList<PropertyInfo> purchaseProperties = new List<PropertyInfo>(purshaceType.GetProperties());
				Type giftsType = giftsReference.GetType();
				IList<PropertyInfo> giftsTProperties = new List<PropertyInfo>(giftsType.GetProperties());
				Type cancellationType = cancellationReference.GetType();
				IList<PropertyInfo> cancellationTProperties = new List<PropertyInfo>(cancellationType.GetProperties());
				Type locationType = locationReference.GetType();
				IList<PropertyInfo> locationTProperties = new List<PropertyInfo>(locationType.GetProperties());

				var union = customerProperties.Union(tourProperties).Union(purchaseProperties);

				var dataSource = union.Select(e => new KeyValue
				{
					ColumnKey = e.DeclaringType.Name.Equals("CustomerReference") ? "Customer." + e.Name :
								e.DeclaringType.Name.Equals("TourReference") ? "Tour." + e.Name :
								e.DeclaringType.Name.Equals("PurchaseReference") ? "Purchase." + e.Name : "",
					Key = e.DeclaringType.Name.Equals("CustomerReference") ? "Customer." + e.Name :
						  e.DeclaringType.Name.Equals("TourReference") ? "Tour." + e.Name :
						  e.DeclaringType.Name.Equals("PurchaseReference") ? "Purchase." + e.Name : "",
				}).Where(e => !e.ColumnKey.Contains("Updated")).ToList();


				foreach (var item in dataSource)
				{
					CustomKpi customKpi = new CustomKpi();
					customKpi.pivot = customKpi.headerText = customKpi.original = item.ColumnKey;
					kpiCodesList.Add(customKpi);
				}
				// Primer elemento

				kpiCodesList.Add(new CustomKpi
				{
					pivot = "Tour.LocationID",
					headerText = "Tour.LocationID",
					original = "Tour.LocationID"
				});



				kpiCodesList.Add(new CustomKpi
				{
					pivot = "TourStatusTypes.TourStatusTypeCode",
					headerText = "TourStatusTypes.TourStatusTypeCode",
					original = "TourStatusTypes.TourStatusTypeCode"
				});



				kpiCodesList.Add(new CustomKpi
				{
					pivot = "SaleStatusTypes.saleStatusTypeCode",
					headerText = "SaleStatusTypes.saleStatusTypeCode",
					original = "SaleStatusTypes.saleStatusTypeCode"
				});

				// Tercer elemento
				kpiCodesList.Add(new CustomKpi
				{
					pivot = "Location.LocationID",
					headerText = "Location.LocationID",
					original = "Location.LocationID"
				});

				// Cuarto elemento
				kpiCodesList.Add(new CustomKpi
				{
					pivot = "Location.LocationName",
					headerText = "Location.LocationName",
					original = "Location.LocationName"
				});

				kpiCodesList.Add(new CustomKpi
				{
					pivot = "Product.ProductName",
					headerText = "Product.ProductName",
					original = "Product.ProductName"
				});
				kpiCodesList.Add(new CustomKpi
				{
					pivot = "Product.ProductID",
					headerText = "Product.ProductID",
					original = "Product.ProductID"
				});
				kpiCodesList.Add(new CustomKpi
				{
					pivot = "Customer Name.customerName",
					headerText = "Customer Name.customerName",
					original = "Customer Name.customerName"
				});

			}
			else
			{
				AuditReport auditReference = new AuditReport();
				Type auditType = auditReference.GetType();
				IList<PropertyInfo> auditProperties = new List<PropertyInfo>(auditType.GetProperties());

				var dataSource = auditProperties.Select(e => new KeyValue
				{
					ColumnKey = e.DeclaringType.Name.Equals("AuditReport") ? "Audit." + e.Name : "",
					Key = e.DeclaringType.Name.Equals("AuditReport") ? "Audit." + e.Name : "",
				}).ToList();

				foreach (var item in dataSource)
				{
					CustomKpi customKpi = new CustomKpi();
					customKpi.pivot = customKpi.headerText = customKpi.original = item.ColumnKey;
					kpiCodesList.Add(customKpi);
				}
			}

			var serializerSettings = new JsonSerializerSettings
			{
				PreserveReferencesHandling = PreserveReferencesHandling.None
			};

			var result = JsonConvert.SerializeObject(new { kpiCodesList }, Formatting.None, serializerSettings);
			return result;
		}




		public void setDataForDateCriteria(TierOneCriteria tierOneCriteria, dynamic dateTimeCriteriaObj, string dropDownDateRanges)
		{
            

            // Validar si dateTypeLogic está vacío antes de intentar convertirlo
            tierOneCriteria.DateTimeCriteria.TierOneDateTypeLogicID =
                !String.IsNullOrEmpty(dateTimeCriteriaObj.dateTypeLogic?.ToString())
                ? int.Parse(dateTimeCriteriaObj.dateTypeLogic.ToString())
                : 0;

            // Utilizar int.TryParse para DateRangePeriodID, con valor predeterminado 0 si falla la conversión
            tierOneCriteria.DateTimeCriteria.DateRangePeriodID =
                int.TryParse(dropDownDateRanges, out int dateRangePeriodId) ? dateRangePeriodId : 0;

            // Utilizar int.TryParse para YearRangePeriodID, con valor predeterminado 0 si falla la conversión
            tierOneCriteria.DateTimeCriteria.YearRangePeriodID =
                int.TryParse(dateTimeCriteriaObj.yearRange?.ToString(), out int yearRangePeriodId) ? yearRangePeriodId : 0;

            // Parsear múltiples tipos de fechas si no está vacío o nulo
            tierOneCriteria.DateTimeCriteria.TierOneDateTypeIDs =
                !String.IsNullOrEmpty(dateTimeCriteriaObj.multipleDateTypes?.ToString()) && dateTimeCriteriaObj.multipleDateTypes.ToString() != "All"
                ? ParseMultipleDateTypes(dateTimeCriteriaObj.multipleDateTypes.ToString())
                : null;

            // Parsear días de la semana si no está vacío o nulo
            tierOneCriteria.DateTimeCriteria.DaysOfWeek =
                !String.IsNullOrEmpty(dateTimeCriteriaObj.daysOfWeek?.ToString()) && dateTimeCriteriaObj.daysOfWeek.ToString() != "All"
                ? ParseDaysOfWeek(dateTimeCriteriaObj.daysOfWeek.ToString())
                : null;

            // Parsear semanas si no está vacío o nulo
            tierOneCriteria.DateTimeCriteria.Weeks =
                !String.IsNullOrEmpty(dateTimeCriteriaObj.weeks?.ToString()) && dateTimeCriteriaObj.weeks.ToString() != "All"
                ? ParseListOfInt(dateTimeCriteriaObj.weeks.ToString())
                : null;

            // Parsear meses si no está vacío o nulo
            tierOneCriteria.DateTimeCriteria.Months =
                !String.IsNullOrEmpty(dateTimeCriteriaObj.months?.ToString()) && dateTimeCriteriaObj.months.ToString() != "All"
                ? ParseMonths(dateTimeCriteriaObj.months.ToString())
                : null;

            // Parsear años si no está vacío o nulo
            tierOneCriteria.DateTimeCriteria.Years =
                !String.IsNullOrEmpty(dateTimeCriteriaObj.years?.ToString()) && dateTimeCriteriaObj.years.ToString() != "All"
                ? ParseListOfInt(dateTimeCriteriaObj.years.ToString())
                : null;

            // Parsear horarios de tour si no está vacío o nulo
            tierOneCriteria.DateTimeCriteria.TourTimes =
                !String.IsNullOrEmpty(dateTimeCriteriaObj.times?.ToString()) && dateTimeCriteriaObj.times.ToString() != "All"
                ? ParseListOfTimes(dateTimeCriteriaObj.times.ToString())
                : null;

            string ConvertToDateFormat(string dateStr, string formatFrom, string formatTo)
			{
				// Define an array of potential input formats
				string[] formats = { "d/M/yyyy", "dd/MM/yyyy", "M/d/yyyy", "MM/dd/yyyy", "d/M/yy", "dd/MM/yy", "M/d/yy", "MM/dd/yy" };
				if (DateTime.TryParseExact(dateStr, formats, CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime parsedDate))
				{
					return parsedDate.ToString(formatTo);
				}

				return dateStr; // Return the original string if parsing fails
			}

            tierOneCriteria.DateTimeCriteria.StartDate = Convert.ToDateTime(ConvertToDateFormat(dateTimeCriteriaObj.startDate.ToString(), "d/M/yyyy", "yyyy-MM-dd")); //DateTime.ParseExact(dateTimeCriteriaObj.startDate, format2, provider); //dateTimeCriteriaObj.startDate;

			tierOneCriteria.DateTimeCriteria.EndDate = Convert.ToDateTime(ConvertToDateFormat(dateTimeCriteriaObj.endDate.ToString(), "d/M/yyyy", "yyyy-MM-dd")); //DateTime.ParseExact(dateTimeCriteriaObj.endDate, format2, provider); //dateTimeCriteriaObj.endDate;


            if (dateTimeCriteriaObj.dateRange == null && dateTimeCriteriaObj.yearRange == null && dateTimeCriteriaObj.endDate.ToString() == "" && dateTimeCriteriaObj.startDate.ToString() == "")
			{
				tierOneCriteria.DateTimeCriteria.TierOneDateSubTypeIDThenReportBy = 2;
				tierOneCriteria.DateTimeCriteria.TierOneDateTypeIDs = new List<int>();
                tierOneCriteria.DateTimeCriteria.TierOneDateTypeIDs.Add(7);
                tierOneCriteria.DateTimeCriteria.TierOneDateTypeIDs.Add(0);
                tierOneCriteria.DateTimeCriteria.TierOneDateTypeIDs.Add(2);
				tierOneCriteria.DateTimeCriteria.TierOneDateTypeLogicID = 1;
				tierOneCriteria.DateTimeCriteria.DateRangePeriodID = null;
				tierOneCriteria.DateTimeCriteria.YearRangePeriodID = null;
            }

            if (dateTimeCriteriaObj.dateRange?.ToString() == "2" && dateTimeCriteriaObj.yearRange?.ToString() == "2" && dateTimeCriteriaObj.endDate.ToString() != "" && dateTimeCriteriaObj.startDate.ToString() != "")
            {
                tierOneCriteria.DateTimeCriteria.TierOneDateSubTypeID = 0;
                tierOneCriteria.DateTimeCriteria.TierOneDateSubTypeIDThenReportBy = 2;
                tierOneCriteria.DateTimeCriteria.TierOneDateTypeIDReportBy = 2;
                tierOneCriteria.DateTimeCriteria.TierOneDateTypeIDs = new List<int>();
                tierOneCriteria.DateTimeCriteria.TierOneDateTypeIDs.Add(0);
                tierOneCriteria.DateTimeCriteria.TierOneDateTypeIDs.Add(2);
                tierOneCriteria.DateTimeCriteria.YearRangePeriodID = null;
            }

            // Validar si dateTypeLogic está vacío antes de intentar convertirlo
            tierOneCriteria.DateTimeCriteria.TierOneDateTypeID =
                !String.IsNullOrEmpty(dateTimeCriteriaObj.tierOneDateTypes?.ToString())
                ? int.Parse(dateTimeCriteriaObj.tierOneDateTypes.ToString())
                : 0;

        }
		
		public void setDataForBasicCriteria(TierOneCriteria tierOneCriteria, dynamic basicCriteriaObj)
		{
			tierOneCriteria.BasicCriteria.UserID = int.TryParse(basicCriteriaObj.user?.ToString(), out int user) ? user : (int?)null;
			tierOneCriteria.BasicCriteria.NoteText = basicCriteriaObj.notes.ToString();
			//tierOneCriteria.BasicCriteria.
		}
		public void setDataForCustomerCriteria(TierOneCriteria tierOneCriteria, dynamic customerCriteriaObj, dynamic basicCriteriaObj)
		{
			tierOneCriteria.CustomerCriteria.CustomerDispositionIDs = ParseListOfInt(customerCriteriaObj.customerDisposition?.ToString());
			tierOneCriteria.CustomerCriteria.Name = customerCriteriaObj.name?.ToString();
			tierOneCriteria.CustomerCriteria.Sex = int.TryParse(customerCriteriaObj.sex?.ToString(), out int sex) ? sex : (int?)null;
			tierOneCriteria.CustomerCriteria.CustomersPickList1ItemIDs = ParseListOfInt(customerCriteriaObj.customersPickList1Item?.ToString());
			tierOneCriteria.CustomerCriteria.GuestTypeIDs = ParseListOfInt(customerCriteriaObj.guestType?.ToString());
			tierOneCriteria.CustomerCriteria.GuestSex = int.TryParse(customerCriteriaObj.guestSex?.ToString(), out int guestSex) ? guestSex : (int?)null;
			tierOneCriteria.CustomerCriteria.CustomersPickList2ItemIDs = ParseListOfInt(customerCriteriaObj.customersPickList2Item?.ToString());
			tierOneCriteria.CustomerCriteria.IncomeTypeIDs = ParseListOfInt(customerCriteriaObj.incomeType?.ToString());
			tierOneCriteria.CustomerCriteria.CustomersPickList3ItemIDs = ParseListOfInt(customerCriteriaObj.customersPickList3Item?.ToString());
			tierOneCriteria.CustomerCriteria.CustomersDecimal1 = int.TryParse(customerCriteriaObj.customersDecimal1?.ToString(), out int customersDecimal1) ? customersDecimal1 : (int?)null;
			tierOneCriteria.CustomerCriteria.CustomersBool1 = int.TryParse(customerCriteriaObj.customersBool1?.ToString(), out int customersBool1) ? customersBool1 : (int?)null;
			tierOneCriteria.CustomerCriteria.Phone = customerCriteriaObj.phone?.ToString();
			tierOneCriteria.CustomerCriteria.StreetAddress = customerCriteriaObj.streetAddress?.ToString();
			tierOneCriteria.CustomerCriteria.StreetAddress2 = customerCriteriaObj.streetAddress2?.ToString();
			tierOneCriteria.CustomerCriteria.City = customerCriteriaObj.city?.ToString();
			tierOneCriteria.CustomerCriteria.StateIDs = ParseListOfInt(customerCriteriaObj.state?.ToString());
			tierOneCriteria.CustomerCriteria.Zipcode = customerCriteriaObj.zipcode?.ToString();
			tierOneCriteria.CustomerCriteria.CountryIDs = ParseListOfInt(customerCriteriaObj.country?.ToString());
			tierOneCriteria.CustomerCriteria.BusinessName = customerCriteriaObj.businessName?.ToString();
			tierOneCriteria.CustomerCriteria.Email = customerCriteriaObj.email?.ToString();
			tierOneCriteria.CustomerCriteria.CustomersPickList4ItemIDs = ParseListOfInt(customerCriteriaObj.customersPickList4Item?.ToString());
			tierOneCriteria.CustomerCriteria.CustomersBool2 = int.TryParse(customerCriteriaObj.customersBool2?.ToString(), out int customersBool2) ? customersBool2 : (int?)null;
			tierOneCriteria.CustomerCriteria.CustomersText1 = customerCriteriaObj.customersText?.ToString();
			tierOneCriteria.CustomerCriteria.CustomersText2 = customerCriteriaObj.customersText2?.ToString();
			tierOneCriteria.CustomerCriteria.CustomersText3 = customerCriteriaObj.customersText3?.ToString();

            tierOneCriteria.CustomerCriteria.CustomerIDs = ParseListOfInt(basicCriteriaObj.customerId?.ToString());
            tierOneCriteria.CustomerCriteria.ExternalCustomerIDs = ParseListOfStringt(basicCriteriaObj.extId?.ToString());
        }
		public void setDataForCancellationCriteria(TierOneCriteria tierOneCriteria, dynamic cancellationCriteriaObj)
		{
			tierOneCriteria.CancellationCriteria.CancellationReasonTypeIDs = ParseListOfInt(cancellationCriteriaObj.cxlRequestReason?.ToString());
			tierOneCriteria.CancellationCriteria.CancellationReceivedTypeIDs = ParseListOfInt(cancellationCriteriaObj.cxlRequestReceived?.ToString());
			tierOneCriteria.CancellationCriteria.WithinRescission = int.TryParse(cancellationCriteriaObj.withinRescission?.ToString(), out int withinRescission) ? withinRescission : (int?)null;
			tierOneCriteria.CancellationCriteria.CancellationDispositionTypeIDs = ParseListOfInt(cancellationCriteriaObj.cxlRequestDisposition?.ToString());
			tierOneCriteria.CancellationCriteria.CancellationStatusTypeIDs = ParseListOfInt(cancellationCriteriaObj.cxlRequestStatus?.ToString());
			tierOneCriteria.CancellationCriteria.CancellationUserIDs = ParseListOfInt(cancellationCriteriaObj.cxlRequestUser?.ToString());
		}

		public void setDataForPurchaseCriteria(TierOneCriteria tierOneCriteria, dynamic purchasesCriteriaObj, dynamic basicCriteriaObj)
		{
			tierOneCriteria.PurchaseCriteria.ProductCategoryIDs = ParseListOfInt(purchasesCriteriaObj.productCategory?.ToString());
			tierOneCriteria.PurchaseCriteria.ProductIDs = ParseListOfInt(purchasesCriteriaObj.product?.ToString());
			tierOneCriteria.PurchaseCriteria.SubProductIDs = ParseListOfInt(purchasesCriteriaObj.subProduct?.ToString());
			tierOneCriteria.PurchaseCriteria.SaleAmount = int.TryParse(purchasesCriteriaObj.saleAmount?.ToString(), out int saleAmount) ? saleAmount : (int?)null;
			tierOneCriteria.PurchaseCriteria.DownPaymentAmount = int.TryParse(purchasesCriteriaObj.downPaymentAmount?.ToString(), out int downPaymentAmount) ? downPaymentAmount : (int?)null;
			tierOneCriteria.PurchaseCriteria.Fees1Amount = int.TryParse(purchasesCriteriaObj.fees1?.ToString(), out int fees1) ? fees1 : (int?)null;
			tierOneCriteria.PurchaseCriteria.Fees2Amount = int.TryParse(purchasesCriteriaObj.fees2?.ToString(), out int fees2) ? fees2 : (int?)null;
			tierOneCriteria.PurchaseCriteria.Fees3Amount = int.TryParse(purchasesCriteriaObj.fees3?.ToString(), out int fees3) ? fees3 : (int?)null;
			tierOneCriteria.PurchaseCriteria.Fees4Amount = int.TryParse(purchasesCriteriaObj.fees4?.ToString(), out int fees4) ? fees4 : (int?)null;
			tierOneCriteria.PurchaseCriteria.Fees5Amount = int.TryParse(purchasesCriteriaObj.proposalValue?.ToString(), out int proposalValue) ? proposalValue : (int?)null;
			tierOneCriteria.PurchaseCriteria.SaleTypeIDs = ConvertSaleTypeStringToIds(purchasesCriteriaObj.saleTypes?.ToString());
			tierOneCriteria.PurchaseCriteria.SaleStatusTypeIDs = ConvertSaleStatusStringToIds(purchasesCriteriaObj.saleStatus?.ToString());

            //tierOneCriteria.PurchaseCriteria.MilestoneStateIDSaleStatusTypeIDs = ParseListOfInt(purchasesCriteriaObj.milestones?.ToString());
            tierOneCriteria.PurchaseCriteria.SaleDispositionIDs = ParseListOfInt(purchasesCriteriaObj.saleDisposition?.ToString());
			tierOneCriteria.PurchaseCriteria.PurchasesPickList1ItemIDs = ParseListOfInt(purchasesCriteriaObj.purchasesPickList1Item?.ToString());
			tierOneCriteria.PurchaseCriteria.PurchasesPickList2ItemIDs = ParseListOfInt(purchasesCriteriaObj.purchasesPickList2Item?.ToString());
			tierOneCriteria.PurchaseCriteria.PurchasesPickList3ItemIDs = ParseListOfInt(purchasesCriteriaObj.purchasesPickList3Item?.ToString());
			tierOneCriteria.PurchaseCriteria.PurchasesText1 = purchasesCriteriaObj.purchasesText1?.ToString();
			tierOneCriteria.PurchaseCriteria.PurchasesText2 = purchasesCriteriaObj.purchasesText2?.ToString();
			tierOneCriteria.PurchaseCriteria.PurchasesBool1 = int.TryParse(purchasesCriteriaObj.purchasesBool1?.ToString(), out int purchasesBool1) ? purchasesBool1 : (int?)null;
			tierOneCriteria.PurchaseCriteria.PurchasesBool2 = int.TryParse(purchasesCriteriaObj.purchasesBool2?.ToString(), out int purchasesBool2) ? purchasesBool2 : (int?)null;

            tierOneCriteria.PurchaseCriteria.PurchaseIDs = ParseListOfInt(basicCriteriaObj.purchaseId?.ToString());
            tierOneCriteria.PurchaseCriteria.ExternalPurchaseIDs = ParseListOfStringt(basicCriteriaObj.extPurchaseId?.ToString());
        }

        public void setDataForSalesCriteria(TierOneCriteria tierOneCriteria, dynamic salesCriteriaObj)
        {
            tierOneCriteria.TourSaleCriteria.SalesTeamIDs = ParseListOfInt(salesCriteriaObj.salesTeam?.ToString());
            tierOneCriteria.TourSaleCriteria.PodiumIDs = ParseListOfInt(salesCriteriaObj.podium?.ToString());
            tierOneCriteria.TourSaleCriteria.SalesCloser1IDs = ParseListOfInt(salesCriteriaObj.salesCloser1?.ToString());
            tierOneCriteria.TourSaleCriteria.SalesCloser2IDs = ParseListOfInt(salesCriteriaObj.salesCloser2?.ToString());
            tierOneCriteria.TourSaleCriteria.SalesRepIDs = ParseListOfInt(salesCriteriaObj.salesAgent?.ToString());
            tierOneCriteria.TourSaleCriteria.VerificationOfficerIDs = ParseListOfInt(salesCriteriaObj.verificationOfficer?.ToString());
            tierOneCriteria.TourSaleCriteria.SalesPickList1ItemIDs = ParseListOfInt(salesCriteriaObj.salesPickList1Item?.ToString());
            tierOneCriteria.TourSaleCriteria.SalesText1 = salesCriteriaObj.salesText?.ToString();
            tierOneCriteria.TourSaleCriteria.SalesDecimal1 = int.TryParse(salesCriteriaObj.salesDecimal?.ToString(), out int salesDecimal) ? salesDecimal : (int?)null;
            tierOneCriteria.TourSaleCriteria.SalesBool1 = int.TryParse(salesCriteriaObj.salesBool?.ToString(), out int salesBool) ? salesBool : (int?)null;
            tierOneCriteria.TourSaleCriteria.SalesTeamExitIDs = ParseListOfInt(salesCriteriaObj.salesExitTeam?.ToString());
            tierOneCriteria.TourSaleCriteria.SalesRepExit1IDs = ParseListOfInt(salesCriteriaObj.extRep1?.ToString());
            tierOneCriteria.TourSaleCriteria.SalesRepExit2IDs = ParseListOfInt(salesCriteriaObj.extRep2?.ToString());
            tierOneCriteria.TourSaleCriteria.SalesRepExit3IDs = ParseListOfInt(salesCriteriaObj.extRep3?.ToString());
        }

        public void setDataForMarketingCriteria(TierOneCriteria tierOneCriteria, dynamic marketingCriteriaObj)
        {
            tierOneCriteria.TourMarketingCriteria.MarketingTeamIDs = ParseListOfInt(marketingCriteriaObj.marketingTeam?.ToString());
            tierOneCriteria.TourMarketingCriteria.MarketingAgentIDs = ParseListOfInt(marketingCriteriaObj.marketingAgent?.ToString());
            tierOneCriteria.TourMarketingCriteria.MarketingCloserIDs = ParseListOfInt(marketingCriteriaObj.marketingCloser?.ToString());
            tierOneCriteria.TourMarketingCriteria.ConfirmerIDs = ParseListOfInt(marketingCriteriaObj.confirmer?.ToString());
            tierOneCriteria.TourMarketingCriteria.ResetterIDs = ParseListOfInt(marketingCriteriaObj.resetter?.ToString());
            tierOneCriteria.TourMarketingCriteria.VenueIDs = ParseListOfInt(marketingCriteriaObj.venue?.ToString());
            tierOneCriteria.TourMarketingCriteria.CampaignIDs = ParseListOfInt(marketingCriteriaObj.campaign?.ToString());
            tierOneCriteria.TourMarketingCriteria.ChannelIDs = ParseListOfInt(marketingCriteriaObj.channel?.ToString());
            tierOneCriteria.TourMarketingCriteria.HotelIDs = ParseListOfInt(marketingCriteriaObj.hotel?.ToString());
            tierOneCriteria.TourMarketingCriteria.GiftIDs = ParseListOfInt(marketingCriteriaObj.giftIDs?.ToString());
            tierOneCriteria.TourMarketingCriteria.MarketingPickList1ItemIDs = ParseListOfInt(marketingCriteriaObj.marketingPickList1Item1?.ToString());
            tierOneCriteria.TourMarketingCriteria.MarketingPickList2ItemIDs = ParseListOfInt(marketingCriteriaObj.marketingPickList1Item2?.ToString());
            tierOneCriteria.TourMarketingCriteria.MarketingText1 = marketingCriteriaObj.marketingText?.ToString();
            tierOneCriteria.TourMarketingCriteria.MarketingBool1 = int.TryParse(marketingCriteriaObj.marketingBool?.ToString(), out int marketingBool) ? marketingBool : (int?)null;
            tierOneCriteria.TourMarketingCriteria.DepositAmount = int.TryParse(marketingCriteriaObj.depositAmount?.ToString(), out int depositAmount) ? depositAmount : (int?)null;
            tierOneCriteria.TourMarketingCriteria.DepositRefundable = int.TryParse(marketingCriteriaObj.depositRefundable?.ToString(), out int depositRefundable) ? depositRefundable : (int?)null;
        }

        public void setDataForTourCriteria(TierOneCriteria tierOneCriteria, dynamic tourCriteriaObj, dynamic basicCriteriaObj)
        {
            tierOneCriteria.TourTourCriteria.TourSourceIDs = ParseListOfInt(tourCriteriaObj.tourSource?.ToString());
            tierOneCriteria.TourTourCriteria.TourTypeIDs = ParseListOfInt(tourCriteriaObj.office?.ToString());
            tierOneCriteria.TourTourCriteria.TourStatusTypeIDs = ParseListOfInt(tourCriteriaObj.tourStatus?.ToString());
            tierOneCriteria.TourTourCriteria.TourConcernTypeIDs = ParseListOfInt(tourCriteriaObj.tourDisposition?.ToString());
            tierOneCriteria.TourTourCriteria.TourTypePickList1ItemIDs = ParseListOfInt(tourCriteriaObj.importMethod?.ToString());
            tierOneCriteria.TourTourCriteria.LocationIDs = ParseListOfInt(tourCriteriaObj.businessPartner?.ToString());
            tierOneCriteria.TourTourCriteria.RegionIDs = ParseListOfInt(tourCriteriaObj.businessGroup?.ToString());
            tierOneCriteria.TourTourCriteria.TourBool1 = int.TryParse(tourCriteriaObj.isShow?.ToString(), out int tourBool) ? tourBool : (int?)null;
            tierOneCriteria.TourTourCriteria.ToursManyPickList1ItemIDs = ParseListOfInt(tourCriteriaObj.saleBonusGifts?.ToString());

            tierOneCriteria.TourTourCriteria.TourIDs = ParseListOfInt(basicCriteriaObj.tourId?.ToString());
            tierOneCriteria.TourTourCriteria.ExternalTourIDs = ParseListOfStringt(basicCriteriaObj.extTourId?.ToString());
            tierOneCriteria.TourTourCriteria.ImportNumber = basicCriteriaObj.importNmbr?.ToString();
            tierOneCriteria.TourTourCriteria.PurchasesCount = int.TryParse(basicCriteriaObj.purchase?.ToString(), out int purchase) ? purchase : (int?)null;
        }

		public void setDataForLeadCriteria(TierOneCriteria tierOneCriteria, dynamic leadCriteriaObj)
		{
            tierOneCriteria.TourLeadCriteria.LeadSourceIDs = ParseListOfInt(leadCriteriaObj.leadSourceFile?.ToString());
            tierOneCriteria.TourLeadCriteria.LeadDispositionIDs = ParseListOfInt(leadCriteriaObj.leadDisposition?.ToString());
            tierOneCriteria.TourLeadCriteria.LeadPickList1ItemIDs = ParseListOfInt(leadCriteriaObj.calendarName?.ToString());
            tierOneCriteria.TourLeadCriteria.LeadPickList2ItemIDs = ParseListOfInt(leadCriteriaObj.leadSource?.ToString());
        }

        public static List<int> ConvertSaleTypeStringToIds(string saleTypeString)
        {
            if (string.IsNullOrWhiteSpace(saleTypeString))
            {
                return new List<int>();
            }

            var saleTypeIds = saleTypeString
                .Split(',')              
                .Select(s => s.Trim())      
                .Select(value =>
                {
                    if (Enum.TryParse<SaleType>(value.Replace(" ", ""), true, out var result))
                    {
                        return (int)result;
                    }
                    else
                    {
                        return -1;
                    }
                })
                .Where(id => id != -1)
                .ToList();

            return saleTypeIds;
        }

        public static List<int> ConvertSaleStatusStringToIds(string saleStatusString)
        {
            if (string.IsNullOrWhiteSpace(saleStatusString))
            {
                return new List<int>();
            }

            var saleStatusIds = saleStatusString
                .Split(',')                
                .Select(s => s.Trim())     
                .Select(value =>
                {
                    if (Enum.TryParse<SaleStatusType>(value.Replace(" ",""), true, out var result))
                    {
                        return (int)result; 
                    }
                    else
                    {
                        return -1;
                    }
                })
                .Where(id => id != -1)
                .ToList();

            return saleStatusIds;
        }

        public static List<int> ParseListOfInt(string input)
		{
			if (string.IsNullOrEmpty(input) || input == "All")
			{
				return null;
			}

			List<int> result = new List<int>();
			foreach (var item in input.Split(','))
			{
				if (int.TryParse(item, out int parsedValue))
				{
					result.Add(parsedValue);
				}
			}
			return result;
		}

        public static List<string> ParseListOfStringt(string input)
        {
            if (string.IsNullOrEmpty(input) || input == "All")
            {
                return null;
            }

            List<string> result = new List<string>();
            foreach (var item in input.Split(','))
            {
                result.Add(item);
            }
            return result;
        }
        private List<ExtendedTuple<int, int>> ParseListOfExtendedTuple(string value)
		{
			if (string.IsNullOrEmpty(value))
			{
				return new List<ExtendedTuple<int, int>>();
			}

			return value.Split(',').Select(v =>
			{
				var parts = v.Split(':');
				return new ExtendedTuple<int, int>(int.Parse(parts[0]), int.Parse(parts[1]));
			}).ToList();
		}

		public string getDataForGrid(string avk, int reportonId, string dropDownDateRanges, Boolean kpis, string preJoin, string joins, string locationId, string filters)
		{

			var selectedReport = typeReports.FirstOrDefault(report => report.avk == avk);
			var selectedJoin = preJoins.FirstOrDefault(report => report.nameJoin == preJoin);
			DataSet dataSet = CustomReportsService.GetCachedCustomReportResult(null, true);

			dynamic rowData = null;
			dynamic summary = null;
			dynamic result = null;
            ReportByType? unionReportByType;

            int? customAnalyticViewIDByKey = null;
			CustomAnalyticView customAnalyticView = null;
			List<CustomKpi> kpiCodesList = null;
			int totalCount = 0;

			//dynamic jsonObject = JsonConvert.DeserializeObject<dynamic>(dateoff);

			dynamic filtersObj = JsonConvert.DeserializeObject<dynamic>(filters);

			// Access each criteria object
			dynamic dateTimeCriteriaObj = JsonConvert.DeserializeObject<dynamic>((filtersObj.dateTimeCriteriaObject).ToString());
			dynamic basicCriteriaObj = JsonConvert.DeserializeObject<dynamic>((filtersObj.basicCriteriaObject).ToString());
			dynamic customerCriteriaObj = JsonConvert.DeserializeObject<dynamic>((filtersObj.customerCriteriaObject).ToString());
			dynamic cancellationCriteriaObj = JsonConvert.DeserializeObject<dynamic>((filtersObj.cancellationCriteriaObject).ToString());
			dynamic purchasesCriteriaObj = JsonConvert.DeserializeObject<dynamic>((filtersObj.purchasesCriteriaObject).ToString());
            dynamic salesCriteriaObj = JsonConvert.DeserializeObject<dynamic>((filtersObj.salesCriteriaObject).ToString());
            dynamic marketingCriteriaObj = JsonConvert.DeserializeObject<dynamic>((filtersObj.marketingCriteriaObject).ToString());
            dynamic tourCriteriaObj = JsonConvert.DeserializeObject<dynamic>((filtersObj.tourCriteriaObject).ToString());
			dynamic leadCriteriaObj = JsonConvert.DeserializeObject<dynamic>((filtersObj.leadCriteriaObject).ToString());

            TierOneCriteria tierOneCriteria = new TierOneCriteria();
			tierOneCriteria.BasicCriteria.ReportFormatID = 6;


			setDataForBasicCriteria(tierOneCriteria, basicCriteriaObj);
			setDataForDateCriteria(tierOneCriteria, dateTimeCriteriaObj, dropDownDateRanges);
			setDataForCustomerCriteria(tierOneCriteria, customerCriteriaObj, basicCriteriaObj);
			setDataForCancellationCriteria(tierOneCriteria, cancellationCriteriaObj);
			setDataForPurchaseCriteria(tierOneCriteria, purchasesCriteriaObj, basicCriteriaObj);
            setDataForSalesCriteria(tierOneCriteria, salesCriteriaObj);
            setDataForMarketingCriteria(tierOneCriteria, marketingCriteriaObj);
			setDataForTourCriteria(tierOneCriteria, tourCriteriaObj, basicCriteriaObj);
			setDataForLeadCriteria(tierOneCriteria, leadCriteriaObj);


			if (dateTimeCriteriaObj != null)
			{
				string ConvertToDateFormat(string dateStr, string formatFrom, string formatTo)
				{
					// Define an array of potential input formats
					string[] formats = { "d/M/yyyy", "dd/MM/yyyy", "M/d/yyyy", "MM/dd/yyyy", "d/M/yy", "dd/MM/yy", "M/d/yy", "MM/dd/yy" };
					if (DateTime.TryParseExact(dateStr, formats, CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime parsedDate))
					{
						return parsedDate.ToString(formatTo);
					}

					return dateStr; // Return the original string if parsing fails
				}

				string AddLeadingZeros(string dateStr)
				{
					string[] dateParts = dateStr.Split('-');
					if (dateParts.Length == 3)
					{
						dateParts[1] = dateParts[1].PadLeft(2, '0'); // Add leading zero to month if necessary
						dateParts[2] = dateParts[2].PadLeft(2, '0'); // Add leading zero to day if necessary
						return string.Join("-", dateParts);
					}
					return dateStr; // Return the original string if it doesn't have exactly 3 parts
				}

				// Convert endDateStr and startDateStr from "dd/MM/yyyy" to "yyyy-MM-dd"
				string endDateStr = dateTimeCriteriaObj.endDate.ToString("dd/MM/yyyy");
				string startDateStr = dateTimeCriteriaObj.startDate.ToString("dd/MM/yyyy");

				// Convert the date strings to "yyyy-MM-dd"
				endDateStr = ConvertToDateFormat(endDateStr, "d/M/yyyy", "yyyy-MM-dd");
				startDateStr = ConvertToDateFormat(startDateStr, "d/M/yyyy", "yyyy-MM-dd");

				// Add leading zeros if necessary
				endDateStr = AddLeadingZeros(endDateStr);
				startDateStr = AddLeadingZeros(startDateStr);

				if (endDateStr == null || endDateStr == String.Empty)
					tierOneCriteria.DateTimeCriteria.EndDate = (DateTime?)null;
				else
					tierOneCriteria.DateTimeCriteria.EndDate =
						String.IsNullOrEmpty(endDateStr) ?
						(DateTime?)null : DateTime.ParseExact(endDateStr, "yyyy-MM-dd", CultureInfo.InvariantCulture);
				if (startDateStr == null || startDateStr == String.Empty)
					tierOneCriteria.DateTimeCriteria.StartDate = (DateTime?)null;
				else
					tierOneCriteria.DateTimeCriteria.StartDate =
					String.IsNullOrEmpty(startDateStr) ?
					(DateTime?)null : DateTime.ParseExact(startDateStr, "yyyy-MM-dd", CultureInfo.InvariantCulture);
			}

			if (selectedJoin.nameJoin == "tdagym")
			{
				tierOneCriteria.TourTourCriteria = new TourTourCriteria();
				tierOneCriteria.TourTourCriteria.TourStatusTypeIDs = new List<int> { -3, 1 };
				tierOneCriteria.AddInternalJoin("Purchases", " LEFT OUTER JOIN Purchases ON Tours.tourID = Purchases.tourID");

			}

			if (selectedReport.avk == "tdagym" || selectedReport.avk == "fbda84" || selectedReport.avk == "cqwq9w")
			{
				customAnalyticViewIDByKey = CustomAnalyticViewsDataAccess.SelectCustomAnalyticViewIDByKey(selectedReport.avk);
				customAnalyticView = CustomAnalyticViewsCache.GetAnalyticView(Convert.ToInt32(customAnalyticViewIDByKey));
				ReportByType? reportByType = (ReportByType)Enum.Parse(typeof(ReportByType), reportonId.ToString());
				ReportBySubType? reportBySubType = (ReportBySubType)Enum.Parse(typeof(ReportBySubType), selectedReport.reportBySubType.ToString());
				ToursReportByBaseDataAccess reportByBaseDataAccess = CustomAnalyticViewsService.GetReportByDataAccess(customAnalyticView, reportByType, null);
				kpiCodesList = new List<CustomKpi>();

				if (!kpis)
				{
					dataSet = reportByBaseDataAccess.SelectReport(tierOneCriteria, reportByType, reportBySubType, false, null,
							null, false, null, null, true);
					foreach (CustomAnalyticViewKpi analyticViewKpi in customAnalyticView.CustomAnalyticViewKpis)
					{
						if (analyticViewKpi.IsVisible && analyticViewKpi.Code != analyticViewKpi.ColumnKey)
						{
							CustomKpi kpiCode = new CustomKpi();
							kpiCode.headerText = analyticViewKpi.Code;
							kpiCode.pivot = analyticViewKpi.ColumnKey;
							kpiCodesList.Add(kpiCode);
						}
					}
				}
				else
					dataSet = reportByBaseDataAccess.SelectAllDataReport(tierOneCriteria, reportByType, reportBySubType, false, null,
							null, false, null, null, true);


			}
			else
			{
				switch (selectedReport.avk)
				{
					case "Business.Purchase":
						dataSet = PurchaseReportDataAccess.SelectPagedPurchaseReport(0, 100, tierOneCriteria, "", out totalCount);
						break;
					case "Business.MarketingTour":
						dataSet = MarketingTourReportDataAccess.SelectPagedMarketingTourReport(0, 100, tierOneCriteria, "", out totalCount);
						break;
					case "Business.Revenue":
						dataSet = RevenueReportDataAccess.SelectPagedRevenueReport(tierOneCriteria, "");
						break;
					case "Business.Sales":
						dataSet = SalesTourReportDataAccess.SelectPagedSalesTourReport(0, 100, tierOneCriteria, "", out totalCount);
						if (dataSet.Tables["Table1"].Columns.Contains("purchases"))
							dataSet.Tables["Table1"].Columns.Remove("purchases");
						break;
					default:

						var query_consult = "";

						if (joins != "{\"joinData\":[]}")
						{
							dynamic dynamicObject = JsonConvert.DeserializeObject(joins);
							if (dynamicObject?.joinData != null)
							{
								foreach (var property in dynamicObject.joinData)
								{
									if (property.joinType == "left" || property.joinType == "inner")
									{
										selectedJoin.queryTables += $" ,{property.leftKey.ToString().Split('.')[0]}.*";
										selectedJoin.queryJoins += $" LEFT OUTER JOIN {property.leftKey.ToString().Split('.')[0]} ON {property.leftKey.ToString()} = {property.rightKey.ToString()}";
									}
									else if (property.joinType == "right")
									{
										selectedJoin.queryTables += $" ,{property.rightKey.ToString().Split('.')[0]}.*";
										selectedJoin.queryJoins += $" LEFT OUTER JOIN {property.leftKey.ToString().Split('.')[0]} ON {property.leftKey.ToString()} = {property.rightKey.ToString()}";
									}
								}
							}
						}

						string sqlStatementAdditionalColumns = null;
						string sqlStatementAdditionalJoins = null;
                        

                        if (reportonId > 0 && reportonId != 2000)
						{
							foreach (ReportByType? reportType in Enum.GetValues(typeof(ReportByType)))
							{
								if ((int)reportType == reportonId)
								{

									unionReportByType = reportType;

                                    ToursReportByBaseDataAccess.GetSqlReportTypeOn(reportType, false, out sqlStatementAdditionalColumns, out sqlStatementAdditionalJoins);

                                    

                                    if (reportType != ReportByType.Purchase_SaleStatusTypeID && reportType != ReportByType.Tour_TourStatusTypeID)
									{
										selectedJoin.queryJoins += " " + (sqlStatementAdditionalJoins != null ? sqlStatementAdditionalJoins : "");
									}

									selectedJoin.queryTables += " " + (sqlStatementAdditionalColumns != null ? sqlStatementAdditionalColumns : "");


									break;
								}

							}
						}

						if(reportonId == 2000) 
						{
							selectedJoin.queryTables += ", " + "allUsers.FullName as userFullName ";
							selectedJoin.queryJoins += " " + "LEFT JOIN Users AS allUsers ON allUsers.userID IN (Tours.podiumID, Tours.salesRepID, Tours.salesCloser1ID, Tours.exitID, Tours.salesCloser2ID, Tours.verificationOfficerID)";
                        }

						if (avk != "armx24")
						{
							dynamic locationIDs = null;
							List<int> result2 = LocationsDataAccess.SelectActiveLocationIDsByUserID(SecurityProfile.UserID);
							if (SecurityProfile.RoleName != "System Administrator" && locationId == "{\"locationsUser\":[\"--\"]}")
							{
								locationIDs = string.Join(",", result2);
								selectedJoin.queryJoins += $" WHERE Tours.LocationID IN ({locationIDs})";
							}
							else if (SecurityProfile.RoleName != "System Administrator" && locationId != "{\"locationsUser\":[\"--\"]}")
							{
								dynamic dynamicObject = JsonConvert.DeserializeObject(locationId);
								locationIDs = "";
								foreach (var property in dynamicObject.locationsUser)
								{
									int matchingLocationIDIndex = result2.IndexOf(Convert.ToInt32(property.Value));
									if (matchingLocationIDIndex != -1)
									{
										if (locationIDs == "")
											locationIDs = result2[matchingLocationIDIndex];
										else
											locationIDs += "," + result2[matchingLocationIDIndex];

										selectedJoin.queryJoins += $" WHERE Tours.LocationID IN ({locationIDs})";
									}

								}
							}
							else if (locationId != "{\"locationsUser\":[\"--\"]}" && locationId != "{\"locationsUser\":[]}")
							{
								dynamic dynamicObject = JsonConvert.DeserializeObject(locationId);
								locationIDs = "";
								foreach (var property in dynamicObject.locationsUser)
								{
									if (locationIDs == "")
										locationIDs += property.Value;
									else
										locationIDs += "," + property.Value;
								}
								selectedJoin.queryJoins += $" WHERE Tours.LocationID IN ({locationIDs})";
							}

						}

						query_consult = selectedJoin.queryTables + " " + selectedJoin.queryJoins;


						if (avk == "armx24")
						{
							ReportByType? reportByType = (ReportByType)Enum.Parse(typeof(ReportByType), reportonId.ToString());
							kpiCodesList = new List<CustomKpi>();
							dataSet = SalesTourReportDataAccess.SelectAllReport(reportByType, 0, 100, tierOneCriteria, "", query_consult, false, out totalCount);

							if (dataSet != null && dataSet.Tables.Count > 0 && dataSet.Tables[0].Columns.Count > 0)
							{

								foreach (DataColumn column in dataSet.Tables[0].Columns)
								{
									CustomKpi customKpi = new CustomKpi
									{
										headerText = column.ColumnName,
										pivot = column.ColumnName,
										original = column.ColumnName
									};

									kpiCodesList.Add(customKpi);
								}

							}
						}
						else
						{
                            ReportByType? reportByType = (ReportByType)Enum.Parse(typeof(ReportByType), reportonId.ToString());
                            dataSet = SalesTourReportDataAccess.SelectAllReport(reportByType, 0, 100, tierOneCriteria, "", query_consult, true, out totalCount);
						}

						break;
				}

				if (avk != "armx24")
				{
					kpiCodesList = new List<CustomKpi>();
					if (selectedReport.jsonObject != null && selectedReport.jsonObject != "")
					{


						List<dynamic> objectsItems = JsonConvert.DeserializeObject<List<dynamic>>(selectedReport.jsonObject);

						foreach (var item in objectsItems)
						{

							string feature_key = "";

							if (!string.IsNullOrEmpty(item["featureKey"].ToString()))
								feature_key = item["featureKey"].ToString();
							else if (!string.IsNullOrEmpty(item["headerText"].ToString()))
								feature_key = item["headerText"].ToString();

							if ((bool)item["pass"] || (!(bool)item["pass"] && FeaturesCache.HasFeature(feature_key)))
							{
								Type type_item = Type.GetType($"TrackResults.BES.Data.Features.{(!string.IsNullOrEmpty(item["featureKey"].ToString()) ? item["featureKey"].ToString() : item["headerText"].ToString()).Split('.')[0]}, TrackResults.BES") ?? Type.GetType($"TrackResults.BES.Data.Business.{(!string.IsNullOrEmpty(item["featureKey"].ToString()) ? item["featureKey"].ToString() : item["headerText"].ToString()).Split('.')[0]}, TrackResults.BES");

								if (type_item != null)
								{

									CustomKpi customKpi = new CustomKpi();

									if (item["headerText"].ToString().Contains("."))
									{
										PropertyInfo property_item = type_item.GetProperties().FirstOrDefault(p => !string.IsNullOrEmpty(item["featureKey"].ToString()) && item["featureKey"].ToString().Contains(".") ? p.Name == item["featureKey"].ToString().Split('.')[1] : p.Name == item["headerText"].ToString().Split('.')[1]);
										customKpi.headerText = property_item != null ? ((DisplayNameAttribute)property_item.GetCustomAttributes(displayNameAttributeType, false)[0]).DisplayName : item["headerText"].ToString().Split('.')[1];
										customKpi.pivot = string.IsNullOrEmpty(item["text"].ToString()) ? item["headerText"].ToString().Split('.')[1] : item["text"].ToString();
									}
									else
										customKpi.pivot = customKpi.headerText = item["headerText"].ToString();

									if (item["child"] is Newtonsoft.Json.Linq.JArray array_child)
									{

										List<string> column_mixed_names = new List<string>();
										foreach (var item_child in array_child)
										{
											feature_key = "";
											if (!string.IsNullOrEmpty(item_child["featureKey"].ToString()))
												feature_key = item_child["featureKey"].ToString();
											else if (!string.IsNullOrEmpty(item_child["headerText"].ToString()))
												feature_key = item_child["headerText"].ToString();

											if ((bool)item_child["pass"] || (!(bool)item_child["pass"] && FeaturesCache.HasFeature(feature_key)))
											{

												if (item_child["parse"].ToString() != "")
												{
													dataSet = ChangeFormattedColumn(dataSet, item_child["parse"].ToString(), item_child["text"].ToString(), "_formatted");
													column_mixed_names.Add(item_child["text"].ToString() + "_formatted");
												}
												else
													column_mixed_names.Add(item_child["text"].ToString());
											}
										}

										if (!dataSet.Tables["Table1"].Columns.Contains(customKpi.pivot))
											dataSet.Tables["Table1"].Columns.Add(customKpi.pivot);

										foreach (DataRow row in dataSet.Tables["Table1"].Rows)
										{
											var nonEmptyColumns = column_mixed_names.Where(column => !string.IsNullOrEmpty(row[column]?.ToString()));
											if (nonEmptyColumns.Any())
												row[customKpi.pivot] = string.Join(", ", nonEmptyColumns.Select(column => row[column].ToString()));
										}


									}
									else if (item["parse"].ToString() != "")
									{
										dataSet = ChangeFormattedColumn(dataSet, item["parse"].ToString(), customKpi.pivot, "_formatted");
										customKpi.pivot = customKpi.pivot + "_formatted";
									}
									kpiCodesList.Add(customKpi);
								}
							}
						}
					}

				}
			}

			rowData = dataSet.Tables["Table1"];
			summary = dataSet.Tables["summary"];

			var serializerSettings = new JsonSerializerSettings
			{
				PreserveReferencesHandling = PreserveReferencesHandling.None
			};

			result = JsonConvert.SerializeObject(new { rowData, kpiCodesList, summary }, Formatting.None, serializerSettings);
			return result;
		}
		public static string ConvertToDoubleDigitDate(string dateStr, string format)
		{
			DateTime date = DateTime.ParseExact(dateStr, format, CultureInfo.InvariantCulture);
			return date.ToString("MM/dd/yyyy");
		}
		private static DataSet ChangeFormattedColumn(DataSet current, string format, string column_name, string fix = "")
		{
			if (!current.Tables["Table1"].Columns.Contains(column_name + fix))
				current.Tables["Table1"].Columns.Add(column_name + fix);

			foreach (DataRow row in current.Tables["Table1"].Rows)
			{
				if (row.Table.Columns.Contains(column_name) && row[column_name] != DBNull.Value)
				{
					Type dataType = row[column_name].GetType();
					string formatted_data = "";
					switch (Type.GetTypeCode(dataType))
					{
						case TypeCode.DateTime:
							DateTime data = (DateTime)row[column_name];
							formatted_data = data.ToString(format);
							break;
						case TypeCode.Decimal:
							decimal decimalData = (row[column_name] != DBNull.Value) ? Convert.ToDecimal(row[column_name]) : 0.0m;
							formatted_data = decimalData.ToString(format);
							break;
					}
					row[column_name + fix] = formatted_data;
				}
			}
			return current;
		}
		// Parse a comma-separated string of integers
		//public static List<int> ParseListOfInt(string input)
		//{
		//    List<int> result = new List<int>();
		//    foreach (var item in input.Split(','))
		//    {
		//        result.Add(int.Parse(item));
		//    }
		//    return result;
		//}

		// Parse a comma-separated string of day names into a list of integers (assuming Monday = 1, etc.)
		public static List<int> ParseDaysOfWeek(string input)
		{
			List<int> result = new List<int>();
			var days = input.Split(',');

			foreach (var day in days)
			{
				switch (day.Trim().ToLower())
				{
					case "sunday":
						result.Add(1);
						break;
					case "monday":
						result.Add(2);
						break;
					case "tuesday":
						result.Add(3);
						break;
					case "wednesday":
						result.Add(4);
						break;
					case "thursday":
						result.Add(5);
						break;
					case "friday":
						result.Add(6);
						break;
					case "saturday":
						result.Add(7);
						break;

				}
			}
			return result;
		}

		// Parse a comma-separated string of month names into a list of integers (assuming January = 1, etc.)
		public static List<int> ParseMonths(string input)
		{
			List<int> result = new List<int>();
			var months = input.Split(',');

			foreach (var month in months)
			{
				switch (month.Trim().ToLower())
				{
					case "january":
						result.Add(1);
						break;
					case "february":
						result.Add(2);
						break;
					case "march":
						result.Add(3);
						break;
					case "april":
						result.Add(4);
						break;
					case "may":
						result.Add(5);
						break;
					case "june":
						result.Add(6);
						break;
					case "july":
						result.Add(7);
						break;
					case "august":
						result.Add(8);
						break;
					case "september":
						result.Add(9);
						break;
					case "october":
						result.Add(10);
						break;
					case "november":
						result.Add(11);
						break;
					case "december":
						result.Add(12);
						break;
				}
			}
			return result;
		}

		// Parse a comma-separated string of times into a list of DateTime objects
		public static List<DateTime> ParseListOfTimes(string input)
		{
			List<DateTime> result = new List<DateTime>();
			var times = input.Split(',');

			foreach (var time in times)
			{
				var parsedTime = DateTime.ParseExact(time.Trim(), "h:mm tt", CultureInfo.InvariantCulture);
				result.Add(new DateTime(1901, 1, 1, parsedTime.Hour, parsedTime.Minute, parsedTime.Second));
			}
			return result;
		}

		public static List<int> ParseMultipleDateTypes(string input)
		{
			List<int> result = new List<int>();
			var dateTypes = input.Split(',');

			foreach (var dateType in dateTypes)
			{
				switch (dateType.Trim().ToLower())
				{
					case "call back date":
						result.Add(1);
						break;
					case "tour date":
						result.Add(2);
						break;
					case "entry date time":
						result.Add(3);
						break;
					case "purchase/due date":
						result.Add(4);
						break;
					case "closing date":
						result.Add(5);
						break;
					case "cxl request date":
						result.Add(6);
						break;
					default:
						// Optionally handle unknown date types here
						break;
				}
			}
			return result;
		}
	}
}