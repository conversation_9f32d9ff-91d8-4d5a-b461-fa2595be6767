﻿CREATE TABLE [dbo].[CustomReports] (
    [customReportID]          INT            IDENTITY (1, 1) NOT NULL,
    [customReportTypeID]      INT            NOT NULL,
    [customReportKey]         NVARCHAR (64)  NULL,
    [name]                    NVARCHAR (128) NOT NULL,
    [customAnalyticViewID]    INT            NULL,
    [reportPath]              NVARCHAR (128) NULL,
    [reportCalculateSettings] NVARCHAR (MAX) NULL,
    [rowIDs]                  NVARCHAR (MAX) NULL,
    [tierOneCriteria]         NVARCHAR (MAX) NOT NULL,
    [sortExpression]          NVARCHAR (64)  NULL,
    [sortDirection]           NVARCHAR (4)   NULL,
    [reportByTypeID]          INT            NULL,
    [isReportByCode]          BIT            CONSTRAINT [DF_CustomReports_isReportByCode] DEFAULT ((0)) NOT NULL,
    [reportThenByTypeID]      INT            NULL,
    [isReportThenByCode]      BIT            CONSTRAINT [DF_CustomReports_isReportThenByCode] DEFAULT ((0)) NOT NULL,
    [reportViewTypeID]        INT            NULL,
    [reportKpisPropertyKeys]  NVARCHAR (MAX) NULL,
    [userID]                  INT            NULL,
    [sortOrder]               INT            CONSTRAINT [DF_CustomReports_sortOrder] DEFAULT ((0)) NULL,
    [insertTimeStamp]         DATETIME       CONSTRAINT [DF_CustomReports_insertTimeStamp] DEFAULT (getdate()) NOT NULL,
    CONSTRAINT [PK_CustomReports] PRIMARY KEY CLUSTERED ([customReportID] ASC),
    CONSTRAINT [FK_CustomReports_customAnalyticViewID] FOREIGN KEY ([customAnalyticViewID]) REFERENCES [dbo].[CustomAnalyticViews] ([customAnalyticViewID]),
    CONSTRAINT [FK_CustomReports_reportViewTypeID] FOREIGN KEY ([reportViewTypeID]) REFERENCES [dbo].[ReportViewTypes] ([reportViewTypeID]),
    CONSTRAINT [FK_CustomReports_userID] FOREIGN KEY ([userID]) REFERENCES [dbo].[Users] ([userID])
);


GO
CREATE UNIQUE NONCLUSTERED INDEX [UK_CustomReports_customReportKey]
    ON [dbo].[CustomReports]([customReportKey] ASC) WHERE ([customReportKey] IS NOT NULL);


GO
CREATE TRIGGER [dbo].[CustomReports.InsertUpdateDelete]
    ON [dbo].[CustomReports]
    FOR INSERT, UPDATE, DELETE
AS 
BEGIN
    SET NOCOUNT ON;

    DECLARE @Activity  NVARCHAR (8), @id INT;

    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'updated'
    END

    IF EXISTS (SELECT * FROM inserted) AND NOT EXISTS(SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'inserted'
    END

    IF EXISTS (SELECT * FROM deleted) AND NOT EXISTS(SELECT * FROM inserted)
    BEGIN
        SET @Activity = 'deleted'
    END

    

    IF (@Activity = 'deleted')
    BEGIN
        DECLARE cur CURSOR FOR SELECT customReportID FROM deleted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'CustomReports', @id, 'customReportID',
                (
                    SELECT *
                    FROM deleted i 
                    WHERE i.customReportID =  @id
                    FOR JSON AUTO
                ),0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
    ELSE
    BEGIN
        DECLARE cur CURSOR
        FOR SELECT customReportID
        FROM inserted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'CustomReports', @id, 'customReportID',
                (
                    SELECT *
                    FROM inserted i 
                    WHERE i.customReportID =  @id
                    FOR JSON AUTO
                ), 0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
END

GO
DISABLE TRIGGER [dbo].[CustomReports.InsertUpdateDelete]
    ON [dbo].[CustomReports];

