﻿CREATE TABLE [dbo].[Tours] (
    [tourID]                   INT            IDENTITY (1, 1) NOT NULL,
    [externalTourID]           NVARCHAR (128) NULL,
    [customerID]               INT            NOT NULL,
    [externalLeadID]           NVARCHAR (128) NULL,
    [acquisitionDate]          DATETIME       NULL,
    [acquisitionTime]          DATETIME       NULL,
    [leadSourceID]             INT            NULL,
    [leadTypeID]               INT            NULL,
    [leadStatusTypeID]         INT            NULL,
    [leadDispositionID]        INT            NULL,
    [checkInDate]              DATETIME       NULL,
    [checkInTime]              DATETIME       NULL,
    [checkOutDate]             DATETIME       NULL,
    [checkOutTime]             DATETIME       NULL,
    [leadPickList1ItemID]      INT            NULL,
    [leadText1]                NVARCHAR (512) NULL,
    [leadDecimal1]             DECIMAL (9, 2) NULL,
    [leadBool1]                BIT            NULL,
    [leadPickList2ItemID]      INT            NULL,
    [leadText2]                NVARCHAR (512) NULL,
    [leadDecimal2]             DECIMAL (9, 2) NULL,
    [leadBool2]                BIT            NULL,
    [contactStatusTypeID]      INT            NULL,
    [contactDispositionID]     INT            NULL,
    [callBackDate]             DATETIME       NULL,
    [callBackTime]             DATETIME       NULL,
    [callBackPickList1ItemID]  INT            NULL,
    [callBackText1]            NVARCHAR (512) NULL,
    [callBackDecimal1]         DECIMAL (9, 2) NULL,
    [callBackBool1]            BIT            NULL,
    [tourSourceID]             INT            NULL,
    [tourTypeID]               INT            NULL,
    [tourStatusTypeID]         INT            NULL,
    [tourConcernTypeID]        INT            NULL,
    [tourTypePickList1ItemID]  INT            NULL,
    [locationID]               INT            NULL,
    [regionID]                 INT            NULL,
    [tourDate]                 DATETIME       NULL,
    [tourTime]                 DATETIME       NULL,
    [entryDateTime]            DATETIME       CONSTRAINT [DF_Tours_entryDate] DEFAULT ([dbo].[GetDeploymentDateTime]()) NOT NULL,
    [tourText1]                NVARCHAR (512) NULL,
    [tourDecimal1]             DECIMAL (9, 2) NULL,
    [tourBool1]                BIT            NULL,
    [rescheduledCount]         INT            CONSTRAINT [DF_Tours_rescheduledCount] DEFAULT ((0)) NOT NULL,
    [rescheduledTourID]        INT            NULL,
    [parentRescheduledTourID]  INT            NULL,
    [marketingTeamID]          INT            NULL,
    [marketingAgentID]         INT            NULL,
    [marketingCloserID]        INT            NULL,
    [confirmerID]              INT            NULL,
    [resetterID]               INT            NULL,
    [venueID]                  INT            NULL,
    [campaignID]               INT            NULL,
    [channelID]                INT            NULL,
    [hotelID]                  INT            NULL,
    [marketingPickList1ItemID] INT            NULL,
    [marketingPickList2ItemID] INT            NULL,
    [marketingText1]           NVARCHAR (512) NULL,
    [marketingDecimal1]        DECIMAL (9, 2) NULL,
    [marketingBool1]           BIT            NULL,
    [marketingDate1]           DATETIME       NULL,
    [marketingTime1]           DATETIME       NULL,
    [depositAmount]            DECIMAL (9, 2) NULL,
    [depositRefundable]        BIT            NULL,
    [salesTeamID]              INT            NULL,
    [podiumID]                 INT            NULL,
    [salesRepID]               INT            NULL,
    [salesCloser1ID]           INT            NULL,
    [salesCloser2ID]           INT            NULL,
    [exitID]                   INT            NULL,
    [verificationOfficerID]    INT            NULL,
    [salesPickList1ItemID]     INT            NULL,
    [salesText1]               NVARCHAR (512) NULL,
    [salesDecimal1]            DECIMAL (9, 2) NULL,
    [salesBool1]               BIT            NULL,
    [salesDate1]               DATETIME       NULL,
    [salesTime1]               DATETIME       NULL,
    [importNumber]             NVARCHAR (64)  NULL,
    [apiConnectionID]          INT            NULL,
    [apiExternalTourID]        NVARCHAR (128) NULL,
    [apiExternalConnectionID]  NVARCHAR (128) NULL,
    [insertTimeStamp]          DATETIME       CONSTRAINT [DF_Tours_insertTimeStamp] DEFAULT (getdate()) NOT NULL,
    [updateTimeStamp]          DATETIME       CONSTRAINT [DF_Tours_updateTimeStamp] DEFAULT (getdate()) NOT NULL,
    [salesTeamExitID]          INT            NULL,
    [salesRepExit1ID]          INT            NULL,
    [salesRepExit2ID]          INT            NULL,
    [salesRepExit3ID]          INT            NULL,
    CONSTRAINT [PK_Tours] PRIMARY KEY CLUSTERED ([tourID] ASC),
    CONSTRAINT [FK_Customers_contactDispositionID] FOREIGN KEY ([contactDispositionID]) REFERENCES [dbo].[ContactDispositions] ([contactDispositionID]),
    CONSTRAINT [FK_Customers_contactStatusTypeID] FOREIGN KEY ([contactStatusTypeID]) REFERENCES [dbo].[ContactStatusTypes] ([contactStatusTypeID]),
    CONSTRAINT [FK_Customers_leadDispositionID] FOREIGN KEY ([leadDispositionID]) REFERENCES [dbo].[LeadDispositions] ([leadDispositionID]),
    CONSTRAINT [FK_Customers_leadPickList1ItemID] FOREIGN KEY ([leadPickList1ItemID]) REFERENCES [dbo].[LeadPickList1Items] ([leadPickList1ItemID]),
    CONSTRAINT [FK_Customers_leadPickList2ItemID] FOREIGN KEY ([leadPickList2ItemID]) REFERENCES [dbo].[LeadPickList2Items] ([leadPickList2ItemID]),
    CONSTRAINT [FK_Customers_leadSourceID] FOREIGN KEY ([leadSourceID]) REFERENCES [dbo].[LeadSources] ([leadSourceID]),
    CONSTRAINT [FK_Customers_leadStatusTypeID] FOREIGN KEY ([leadStatusTypeID]) REFERENCES [dbo].[LeadStatusTypes] ([leadStatusTypeID]),
    CONSTRAINT [FK_Customers_leadTypeID] FOREIGN KEY ([leadTypeID]) REFERENCES [dbo].[LeadTypes] ([leadTypeID]),
    CONSTRAINT [FK_Customers_regionID] FOREIGN KEY ([regionID]) REFERENCES [dbo].[Regions] ([regionID]),
    CONSTRAINT [FK_Customers_tourSourceID] FOREIGN KEY ([tourSourceID]) REFERENCES [dbo].[TourSources] ([tourSourceID]),
    CONSTRAINT [FK_Customers_tourTypeID] FOREIGN KEY ([tourTypeID]) REFERENCES [dbo].[TourTypes] ([tourTypeID]),
    CONSTRAINT [FK_Customers_tourTypePickList1ItemID] FOREIGN KEY ([tourTypePickList1ItemID]) REFERENCES [dbo].[TourTypePickList1Items] ([tourTypePickList1ItemID]),
    CONSTRAINT [FK_Tours_apiConnectionID] FOREIGN KEY ([apiConnectionID]) REFERENCES [dbo].[ApiConnections] ([apiConnectionID]),
    CONSTRAINT [FK_Tours_callBackPickList1ItemID] FOREIGN KEY ([callBackPickList1ItemID]) REFERENCES [dbo].[CallBackPickList1Items] ([callBackPickList1ItemID]),
    CONSTRAINT [FK_Tours_Campaign] FOREIGN KEY ([campaignID]) REFERENCES [dbo].[Campaigns] ([campaignID]),
    CONSTRAINT [FK_Tours_channelID] FOREIGN KEY ([channelID]) REFERENCES [dbo].[Channels] ([channelID]),
    CONSTRAINT [FK_Tours_Customer] FOREIGN KEY ([customerID]) REFERENCES [dbo].[Customers] ([customerID]),
    CONSTRAINT [FK_Tours_Hotel] FOREIGN KEY ([hotelID]) REFERENCES [dbo].[Hotels] ([hotelID]),
    CONSTRAINT [FK_Tours_Location] FOREIGN KEY ([locationID]) REFERENCES [dbo].[Locations] ([locationID]),
    CONSTRAINT [FK_Tours_MarketingPickList1Items] FOREIGN KEY ([marketingPickList1ItemID]) REFERENCES [dbo].[MarketingPickList1Items] ([marketingPickList1ItemID]),
    CONSTRAINT [FK_Tours_marketingPickList2ItemID] FOREIGN KEY ([marketingPickList2ItemID]) REFERENCES [dbo].[MarketingPickList2Items] ([marketingPickList2ItemID]),
    CONSTRAINT [FK_Tours_MarketingTeam] FOREIGN KEY ([marketingTeamID]) REFERENCES [dbo].[Teams] ([teamID]),
    CONSTRAINT [FK_Tours_MarketingUser1] FOREIGN KEY ([marketingAgentID]) REFERENCES [dbo].[Users] ([userID]),
    CONSTRAINT [FK_Tours_MarketingUser2] FOREIGN KEY ([marketingCloserID]) REFERENCES [dbo].[Users] ([userID]),
    CONSTRAINT [FK_Tours_MarketingUser3] FOREIGN KEY ([confirmerID]) REFERENCES [dbo].[Users] ([userID]),
    CONSTRAINT [FK_Tours_MarketingUser4] FOREIGN KEY ([resetterID]) REFERENCES [dbo].[Users] ([userID]),
    CONSTRAINT [FK_Tours_RescheduledTour] FOREIGN KEY ([rescheduledTourID]) REFERENCES [dbo].[Tours] ([tourID]),
    CONSTRAINT [FK_Tours_salesPickList1ItemID] FOREIGN KEY ([salesPickList1ItemID]) REFERENCES [dbo].[SalesPickList1Items] ([salesPickList1ItemID]),
    CONSTRAINT [FK_Tours_SalesUser1] FOREIGN KEY ([podiumID]) REFERENCES [dbo].[Users] ([userID]),
    CONSTRAINT [FK_Tours_SalesUser2] FOREIGN KEY ([salesRepID]) REFERENCES [dbo].[Users] ([userID]),
    CONSTRAINT [FK_Tours_SalesUser3] FOREIGN KEY ([salesCloser1ID]) REFERENCES [dbo].[Users] ([userID]),
    CONSTRAINT [FK_Tours_SalesUser4] FOREIGN KEY ([exitID]) REFERENCES [dbo].[Users] ([userID]),
    CONSTRAINT [FK_Tours_SalesUser5] FOREIGN KEY ([salesCloser2ID]) REFERENCES [dbo].[Users] ([userID]),
    CONSTRAINT [FK_Tours_SalesUser6] FOREIGN KEY ([verificationOfficerID]) REFERENCES [dbo].[Users] ([userID]),
    CONSTRAINT [FK_Tours_SalesUser7] FOREIGN KEY ([salesRepExit1ID]) REFERENCES [dbo].[Users] ([userID]),
    CONSTRAINT [FK_Tours_SalesUser8] FOREIGN KEY ([salesRepExit2ID]) REFERENCES [dbo].[Users] ([userID]),
    CONSTRAINT [FK_Tours_SalesUser9] FOREIGN KEY ([salesRepExit3ID]) REFERENCES [dbo].[Users] ([userID]),
    CONSTRAINT [FK_Tours_TourConcernType] FOREIGN KEY ([tourConcernTypeID]) REFERENCES [dbo].[TourConcernTypes] ([tourConcernTypeID]),
    CONSTRAINT [FK_Tours_TourStatusType] FOREIGN KEY ([tourStatusTypeID]) REFERENCES [dbo].[TourStatusTypes] ([tourStatusTypeID]),
    CONSTRAINT [FK_Tours_Venue] FOREIGN KEY ([venueID]) REFERENCES [dbo].[Venues] ([venueID])
);


GO
CREATE NONCLUSTERED INDEX [IX_Tours_apiConnectionID]
    ON [dbo].[Tours]([apiConnectionID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_Tours_apiExternalTourID]
    ON [dbo].[Tours]([apiExternalTourID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_Tours_customerID]
    ON [dbo].[Tours]([customerID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_Tours_entryDateTime]
    ON [dbo].[Tours]([entryDateTime] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_Tours_locationID]
    ON [dbo].[Tours]([locationID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_Tours_parentRescheduledTourID]
    ON [dbo].[Tours]([parentRescheduledTourID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_Tours_rescheduledTourID]
    ON [dbo].[Tours]([rescheduledTourID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_Tours_tourDate]
    ON [dbo].[Tours]([tourDate] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_Tours_tourStatusTypeID]
    ON [dbo].[Tours]([tourStatusTypeID] ASC);


GO
CREATE TRIGGER [dbo].[Tours.InsertUpdateDelete]
    ON [dbo].[Tours]
    FOR INSERT, UPDATE, DELETE
AS 
BEGIN
    SET NOCOUNT ON;

    DECLARE @Activity  NVARCHAR (8), @id INT;

    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'updated'
    END

    IF EXISTS (SELECT * FROM inserted) AND NOT EXISTS(SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'inserted'
    END

    IF EXISTS (SELECT * FROM deleted) AND NOT EXISTS(SELECT * FROM inserted)
    BEGIN
        SET @Activity = 'deleted'
    END

    

    IF (@Activity = 'deleted')
    BEGIN
        DECLARE cur CURSOR FOR SELECT tourID FROM deleted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'Tours', @id, 'tourID',
                (
                    SELECT *
                    FROM deleted i 
                    WHERE i.tourID =  @id
                    FOR JSON AUTO
                ),0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
    ELSE
    BEGIN
        DECLARE cur CURSOR
        FOR SELECT tourID
        FROM inserted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'Tours', @id, 'tourID',
                (
                    SELECT *
                    FROM inserted i 
                    WHERE i.tourID =  @id
                    FOR JSON AUTO
                ), 0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
END
GO
DISABLE TRIGGER [dbo].[Tours.InsertUpdateDelete]
    ON [dbo].[Tours];

