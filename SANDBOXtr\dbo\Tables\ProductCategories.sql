﻿CREATE TABLE [dbo].[ProductCategories] (
    [productCategoryID]   INT           IDENTITY (1, 1) NOT NULL,
    [productCategoryName] NVARCHAR (64) NOT NULL,
    [productCategoryCode] NVARCHAR (64) NOT NULL,
    [sortOrder]           INT           NOT NULL,
    [active]              BIT           NOT NULL,
    CONSTRAINT [PK_ProductCategories] PRIMARY KEY CLUSTERED ([productCategoryID] ASC),
    CONSTRAINT [UK_ProductCategories_productCategoryName] UNIQUE NONCLUSTERED ([productCategoryName] ASC),
    CONSTRAINT [UK_Purchases_productCategoryName] UNIQUE NONCLUSTERED ([productCategoryName] ASC)
);


GO
CREATE TRIGGER [dbo].[ProductCategories.InsertUpdateDelete]
    ON [dbo].[ProductCategories]
    FOR INSERT, UPDATE, DELETE
AS 
BEGIN
    SET NOCOUNT ON;

    DECLARE @Activity  NVARCHAR (8), @id INT;

    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'updated'
    END

    IF EXISTS (SELECT * FROM inserted) AND NOT EXISTS(SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'inserted'
    END

    IF EXISTS (SELECT * FROM deleted) AND NOT EXISTS(SELECT * FROM inserted)
    BEGIN
        SET @Activity = 'deleted'
    END

    

    IF (@Activity = 'deleted')
    BEGIN
        DECLARE cur CURSOR FOR SELECT productCategoryID FROM deleted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'ProductCategories', @id, 'productCategoryID',
                (
                    SELECT *
                    FROM deleted i 
                    WHERE i.productCategoryID =  @id
                    FOR JSON AUTO
                ),0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
    ELSE
    BEGIN
        DECLARE cur CURSOR
        FOR SELECT productCategoryID
        FROM inserted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'ProductCategories', @id, 'productCategoryID',
                (
                    SELECT *
                    FROM inserted i 
                    WHERE i.productCategoryID =  @id
                    FOR JSON AUTO
                ), 0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
END

GO
DISABLE TRIGGER [dbo].[ProductCategories.InsertUpdateDelete]
    ON [dbo].[ProductCategories];

