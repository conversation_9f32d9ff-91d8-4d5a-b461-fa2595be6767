﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="4.0">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <Name>SANDBOXtr</Name>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectVersion>4.1</ProjectVersion>
    <ProjectGuid>{6779f890-59cb-49ea-92c7-a442fa4bd5c8}</ProjectGuid>
    <DSP>Microsoft.Data.Tools.Schema.Sql.SqlAzureV12DatabaseSchemaProvider</DSP>
    <OutputType>Database</OutputType>
    <RootPath>
    </RootPath>
    <RootNamespace>SANDBOXtr</RootNamespace>
    <AssemblyName>SANDBOXtr</AssemblyName>
    <ModelCollation>1033,CI</ModelCollation>
    <DefaultFileStructure>BySchemaAndSchemaType</DefaultFileStructure>
    <DeployToDatabase>True</DeployToDatabase>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <TargetLanguage>CS</TargetLanguage>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <SqlServerVerification>False</SqlServerVerification>
    <IncludeCompositeObjects>True</IncludeCompositeObjects>
    <TargetDatabaseSet>True</TargetDatabaseSet>
    <DefaultCollation>SQL_Latin1_General_CP1_CI_AS</DefaultCollation>
    <DefaultFilegroup>PRIMARY</DefaultFilegroup>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <OutputPath>bin\Release\</OutputPath>
    <BuildScriptName>$(MSBuildProjectName).sql</BuildScriptName>
    <TreatWarningsAsErrors>False</TreatWarningsAsErrors>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <DefineDebug>false</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <OutputPath>bin\Debug\</OutputPath>
    <BuildScriptName>$(MSBuildProjectName).sql</BuildScriptName>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">11.0</VisualStudioVersion>
    <!-- Default to the v11.0 targets path if the targets file for the current VS version is not found -->
    <SSDTExists Condition="Exists('$(MSBuildExtensionsPath)\Microsoft\VisualStudio\v$(VisualStudioVersion)\SSDT\Microsoft.Data.Tools.Schema.SqlTasks.targets')">True</SSDTExists>
    <VisualStudioVersion Condition="'$(SSDTExists)' == ''">11.0</VisualStudioVersion>
  </PropertyGroup>
  <Import Condition="'$(SQLDBExtensionsRefPath)' != ''" Project="$(SQLDBExtensionsRefPath)\Microsoft.Data.Tools.Schema.SqlTasks.targets" />
  <Import Condition="'$(SQLDBExtensionsRefPath)' == ''" Project="$(MSBuildExtensionsPath)\Microsoft\VisualStudio\v$(VisualStudioVersion)\SSDT\Microsoft.Data.Tools.Schema.SqlTasks.targets" />
  <ItemGroup>
    <Folder Include="Properties" />
    <Folder Include="dbo\" />
    <Folder Include="dbo\Tables\" />
    <Folder Include="dbo\Views\" />
    <Folder Include="dbo\Functions\" />
    <Folder Include="dbo\Stored Procedures\" />
    <Folder Include="dbo\User Defined Types\" />
  </ItemGroup>
  <ItemGroup>
    <Build Include="dbo\Tables\LeadPickList2Items.sql" />
    <Build Include="dbo\Tables\Cancellations.sql" />
    <Build Include="dbo\Tables\StateGroupTypes.sql" />
    <Build Include="dbo\Tables\LeadSources.sql" />
    <Build Include="dbo\Tables\States.sql" />
    <Build Include="dbo\Tables\CancellationsPickups.sql" />
    <Build Include="dbo\Tables\LeadStatusTypes.sql" />
    <Build Include="dbo\Tables\SubProducts.sql" />
    <Build Include="dbo\Tables\CancellationsPickupsAudits.sql" />
    <Build Include="dbo\Tables\LeadTypes.sql" />
    <Build Include="dbo\Tables\CancellationsPickupsAuditTypes.sql" />
    <Build Include="dbo\Tables\Teams.sql" />
    <Build Include="dbo\Tables\Localizations.sql" />
    <Build Include="dbo\Tables\CancellationsSupersededPurchases.sql" />
    <Build Include="dbo\Tables\CancellationStatusTypes.sql" />
    <Build Include="dbo\Tables\Testintong.sql" />
    <Build Include="dbo\Tables\TeamsUsersMap.sql" />
    <Build Include="dbo\Tables\Channels.sql" />
    <Build Include="dbo\Tables\LocationsGiftsMap.sql" />
    <Build Include="dbo\Tables\TeamTypes.sql" />
    <Build Include="dbo\Tables\ContactDispositions.sql" />
    <Build Include="dbo\Tables\LocationsHotelsMap.sql" />
    <Build Include="dbo\Tables\TourConcernTypes.sql" />
    <Build Include="dbo\Tables\ContactStatusTypes.sql" />
    <Build Include="dbo\Tables\LocationsVenuesMap.sql" />
    <Build Include="dbo\Tables\ControlDisplayRightTypes.sql" />
    <Build Include="dbo\Tables\TourDecimal1Groups.sql" />
    <Build Include="dbo\Tables\Countries.sql" />
    <Build Include="dbo\Tables\MarketingDecimal1Groups.sql" />
    <Build Include="dbo\Tables\MarketingManyPickList1Items.sql" />
    <Build Include="dbo\Tables\CriteriaRights.sql" />
    <Build Include="dbo\Tables\ToursManyPickList1Items.sql" />
    <Build Include="dbo\Tables\MarketingManyPickList1ItemsMap.sql" />
    <Build Include="dbo\Tables\CustomAnalyticViews.sql" />
    <Build Include="dbo\Tables\ToursManyPickList1ItemsMap.sql" />
    <Build Include="dbo\Tables\MarketingNotesCountGroups.sql" />
    <Build Include="dbo\Tables\CustomAnalyticViewsCalculateSettings.sql" />
    <Build Include="dbo\Tables\ToursManyPickList2Items.sql" />
    <Build Include="dbo\Tables\MarketingPickList1Items.sql" />
    <Build Include="dbo\Tables\DeploymentMetaData.sql" />
    <Build Include="dbo\Tables\CustomAnalyticViewsKpis.sql" />
    <Build Include="dbo\Tables\ToursManyPickList2ItemsMap.sql" />
    <Build Include="dbo\Tables\MarketingPickList2Items.sql" />
    <Build Include="dbo\Tables\CustomCriteriaRights.sql" />
    <Build Include="dbo\Tables\ToursManyPickList3Items.sql" />
    <Build Include="dbo\Tables\ApiConnections.sql" />
    <Build Include="dbo\Tables\CustomFields.sql" />
    <Build Include="dbo\Tables\CustomerDispositions.sql" />
    <Build Include="dbo\Tables\ToursManyPickList3ItemsMap.sql" />
    <Build Include="dbo\Tables\Notes.sql" />
    <Build Include="dbo\Tables\MiddlewareErrorTypes.sql" />
    <Build Include="dbo\Tables\TourSources.sql" />
    <Build Include="dbo\Tables\MilestonesSaleStatusTypes.sql" />
    <Build Include="dbo\Tables\CustomersDecimal1Groups.sql" />
    <Build Include="dbo\Tables\TourStatusStates.sql" />
    <Build Include="dbo\Tables\WorkFlows.sql" />
    <Build Include="dbo\Tables\MilestonesTourStatusTypes.sql" />
    <Build Include="dbo\Tables\CustomersDecimal2Groups.sql" />
    <Build Include="dbo\Tables\ScoreFico.sql" />
    <Build Include="dbo\Tables\TourStatusTypes.sql" />
    <Build Include="dbo\Tables\WebhookWorkFlows.sql" />
    <Build Include="dbo\Tables\NotesCancellations.sql" />
    <Build Include="dbo\Tables\CustomersNotesCountGroups.sql" />
    <Build Include="dbo\Tables\TourTypePickList1Items.sql" />
    <Build Include="dbo\Tables\NotesCustomers.sql" />
    <Build Include="dbo\Tables\CustomersPickList1Items.sql" />
    <Build Include="dbo\Tables\TourTypes.sql" />
    <Build Include="dbo\Tables\NotesToursCalls.sql" />
    <Build Include="dbo\Tables\CustomersPickList2Items.sql" />
    <Build Include="dbo\Tables\NotesToursExit.sql" />
    <Build Include="dbo\Tables\CustomersPickList3Items.sql" />
    <Build Include="dbo\Tables\NotesToursForm1.sql" />
    <Build Include="dbo\Tables\CustomersPickList4Items.sql" />
    <Build Include="dbo\Tables\NotesToursForm10.sql" />
    <Build Include="dbo\Tables\CustomerStatusTypes.sql" />
    <Build Include="dbo\Tables\UsersLoginHistory.sql" />
    <Build Include="dbo\Tables\NotesToursForm11.sql" />
    <Build Include="dbo\Tables\CustomerTypes.sql" />
    <Build Include="dbo\Tables\Venues.sql" />
    <Build Include="dbo\Tables\NotesToursForm12.sql" />
    <Build Include="dbo\Tables\CustomLocalizations.sql" />
    <Build Include="dbo\Tables\Version.sql" />
    <Build Include="dbo\Tables\NotesToursForm13.sql" />
    <Build Include="dbo\Tables\Waves.sql" />
    <Build Include="dbo\Tables\CustomPageControlDisplayRights.sql" />
    <Build Include="dbo\Tables\Schedulers.sql" />
    <Build Include="dbo\Tables\NotesToursForm14.sql" />
    <Build Include="dbo\Tables\CustomReports.sql" />
    <Build Include="dbo\Tables\NotesToursForm15.sql" />
    <Build Include="dbo\Tables\CustomReportsGoals.sql" />
    <Build Include="dbo\Tables\NotesToursForm2.sql" />
    <Build Include="dbo\Tables\CustomReportTypes.sql" />
    <Build Include="dbo\Tables\NotesToursForm3.sql" />
    <Build Include="dbo\Tables\ApiResponseActions.sql" />
    <Build Include="dbo\Tables\Dashboards.sql" />
    <Build Include="dbo\Tables\NotesToursForm4.sql" />
    <Build Include="dbo\Tables\ApiHttpRequestMethods.sql" />
    <Build Include="dbo\Tables\DashboardsItems.sql" />
    <Build Include="dbo\Tables\NotesToursForm5.sql" />
    <Build Include="dbo\Tables\ApiTriggers.sql" />
    <Build Include="dbo\Tables\DashboardSizeTypes.sql" />
    <Build Include="dbo\Tables\NotesToursForm6.sql" />
    <Build Include="dbo\Tables\DashboardTypes.sql" />
    <Build Include="dbo\Tables\NotesToursForm7.sql" />
    <Build Include="dbo\Tables\ApiConnectionRequests.sql" />
    <Build Include="dbo\Tables\DBModeTypes.sql" />
    <Build Include="dbo\Tables\NotesToursForm8.sql" />
    <Build Include="dbo\Tables\DepositAmountGroups.sql" />
    <Build Include="dbo\Tables\NotesToursForm9.sql" />
    <Build Include="dbo\Tables\DoNotCalls.sql" />
    <Build Include="dbo\Tables\CustomsReportScheduler.sql" />
    <Build Include="dbo\Tables\NotesToursLeads.sql" />
    <Build Include="dbo\Tables\DownPaymentAmountGroups.sql" />
    <Build Include="dbo\Tables\NotesToursMarketing.sql" />
    <Build Include="dbo\Tables\Users.sql" />
    <Build Include="dbo\Tables\EulaAcceptances.sql" />
    <Build Include="dbo\Tables\NotesToursSales.sql" />
    <Build Include="dbo\Tables\Kpis.sql" />
    <Build Include="dbo\Tables\ExportMappings.sql" />
    <Build Include="dbo\Tables\AgeGroups.sql" />
    <Build Include="dbo\Tables\PageControlDisplayRights.sql" />
    <Build Include="dbo\Tables\Features.sql" />
    <Build Include="dbo\Tables\ProductCategories.sql" />
    <Build Include="dbo\Tables\FeaturesDependenciesRoles.sql" />
    <Build Include="dbo\Tables\ApiConnectionsDataMappings.sql" />
    <Build Include="dbo\Tables\FeaturesDependenciesTourStatusStates.sql" />
    <Build Include="dbo\Tables\Products.sql" />
    <Build Include="dbo\Tables\FeaturesDependenciesUsers.sql" />
    <Build Include="dbo\Tables\FeaturesFormatStrings.sql" />
    <Build Include="dbo\Tables\ApiConnectors.sql" />
    <Build Include="dbo\Tables\Fees1AmountGroups.sql" />
    <Build Include="dbo\Tables\Purchases.sql" />
    <Build Include="dbo\Tables\Fees2AmountGroups.sql" />
    <Build Include="dbo\Tables\ApplicationSettings.sql" />
    <Build Include="dbo\Tables\PurchasesPickList1Items.sql" />
    <Build Include="dbo\Tables\Fees3AmountGroups.sql" />
    <Build Include="dbo\Tables\SchemaLog.sql" />
    <Build Include="dbo\Tables\aspnet_Applications.sql" />
    <Build Include="dbo\Tables\PurchasesPickList2Items.sql" />
    <Build Include="dbo\Tables\Fees4AmountGroups.sql" />
    <Build Include="dbo\Tables\ToursGiftsMap.sql" />
    <Build Include="dbo\Tables\Fees5AmountGroups.sql" />
    <Build Include="dbo\Tables\TestingSync.sql" />
    <Build Include="dbo\Tables\Tours.sql" />
    <Build Include="dbo\Tables\PurchasesPickList3Items.sql" />
    <Build Include="dbo\Tables\aspnet_Membership.sql" />
    <Build Include="dbo\Tables\Gifts.sql" />
    <Build Include="dbo\Tables\aspnet_Roles.sql" />
    <Build Include="dbo\Tables\Regions.sql" />
    <Build Include="dbo\Tables\GuestAgeGroups.sql" />
    <Build Include="dbo\Tables\aspnet_SchemaVersions.sql" />
    <Build Include="dbo\Tables\ReportViewTypes.sql" />
    <Build Include="dbo\Tables\GuestTypes.sql" />
    <Build Include="dbo\Tables\aspnet_Users.sql" />
    <Build Include="dbo\Tables\RescheduledCountGroups.sql" />
    <Build Include="dbo\Tables\aspnet_UsersInRoles.sql" />
    <Build Include="dbo\Tables\Roles.sql" />
    <Build Include="dbo\Tables\Hotels.sql" />
    <Build Include="dbo\Tables\AuditsCustomers.sql" />
    <Build Include="dbo\Tables\SaleAmountGroups.sql" />
    <Build Include="dbo\Tables\Customers.sql" />
    <Build Include="dbo\Tables\AuditsExports.sql" />
    <Build Include="dbo\Tables\Impersonations.sql" />
    <Build Include="dbo\Tables\AuditsPurchases.sql" />
    <Build Include="dbo\Tables\SaleDispositions.sql" />
    <Build Include="dbo\Tables\AuditsTours.sql" />
    <Build Include="dbo\Tables\ReportsKpis.sql" />
    <Build Include="dbo\Tables\ImportMappings.sql" />
    <Build Include="dbo\Tables\MiddlewareErrors.sql" />
    <Build Include="dbo\Tables\AuditsUsers.sql" />
    <Build Include="dbo\Tables\ApiRequestResponses.sql" />
    <Build Include="dbo\Tables\TeamsLocationsMap.sql" />
    <Build Include="dbo\Tables\AuditsUsersWebAccounts.sql" />
    <Build Include="dbo\Tables\ReportsJoins.sql" />
    <Build Include="dbo\Tables\SalesDecimal1Groups.sql" />
    <Build Include="dbo\Tables\IncomeTypes.sql" />
    <Build Include="dbo\Tables\CallBackDecimal1Groups.sql" />
    <Build Include="dbo\Tables\Locations.sql" />
    <Build Include="dbo\Tables\UsersLocationsMap.sql" />
    <Build Include="dbo\Tables\Reports.sql" />
    <Build Include="dbo\Tables\SalesManyPickList1Items.sql" />
    <Build Include="dbo\Tables\CallBackPickList1Items.sql" />
    <Build Include="dbo\Tables\ReportsUsers.sql" />
    <Build Include="dbo\Tables\IPSecurities.sql" />
    <Build Include="dbo\Tables\SalesManyPickList1ItemsMap.sql" />
    <Build Include="dbo\Tables\CallNotesCountGroups.sql" />
    <Build Include="dbo\Tables\ReportsFilters.sql" />
    <Build Include="dbo\Tables\LeadDecimal1Groups.sql" />
    <Build Include="dbo\Tables\SalesNotesCountGroups.sql" />
    <Build Include="dbo\Tables\Campaigns.sql" />
    <Build Include="dbo\Tables\LeadDecimal2Groups.sql" />
    <Build Include="dbo\Tables\SalesPickList1Items.sql" />
    <Build Include="dbo\Tables\LeadDispositions.sql" />
    <Build Include="dbo\Tables\CancellationDispositionTypes.sql" />
    <Build Include="dbo\Tables\SaleStatusTypes.sql" />
    <Build Include="dbo\Tables\LeadNotesCountGroups.sql" />
    <Build Include="dbo\Tables\CancellationReasonTypes.sql" />
    <Build Include="dbo\Tables\SaleTypes.sql" />
    <Build Include="dbo\Tables\LeadPickList1Items.sql" />
    <Build Include="dbo\Tables\CancellationReceivedTypes.sql" />
    <Build Include="dbo\Views\vwToursApi.sql" />
    <Build Include="dbo\Views\vw_aspnet_Applications.sql">
      <QuotedIdentifier>Off</QuotedIdentifier>
    </Build>
    <Build Include="dbo\Views\vw_aspnet_MembershipUsers.sql">
      <QuotedIdentifier>Off</QuotedIdentifier>
    </Build>
    <Build Include="dbo\Views\vw_aspnet_Roles.sql">
      <QuotedIdentifier>Off</QuotedIdentifier>
    </Build>
    <Build Include="dbo\Views\vw_aspnet_Users.sql">
      <QuotedIdentifier>Off</QuotedIdentifier>
    </Build>
    <Build Include="dbo\Views\vw_aspnet_UsersInRoles.sql">
      <QuotedIdentifier>Off</QuotedIdentifier>
    </Build>
    <Build Include="dbo\Views\vw_purchases.sql" />
    <Build Include="dbo\Views\AllUsersLocationsMapView.sql" />
    <Build Include="dbo\Views\FlattenedCancellationsPickupsView.sql" />
    <Build Include="dbo\Views\FlattenedPurchasesView.sql" />
    <Build Include="dbo\Views\MilestonesWasPendingView.sql" />
    <Build Include="dbo\Views\NotesExtended.sql" />
    <Build Include="dbo\Functions\GetDeploymentDateTime.sql" />
    <Build Include="dbo\Functions\GetConcantenatedNotes.sql" />
    <Build Include="dbo\Functions\GetWeekOfYear.sql" />
    <Build Include="dbo\Functions\GetStringWhenIntIsNotNull.sql" />
    <Build Include="dbo\Functions\GetStringForBitTrueOrFalse.sql" />
    <Build Include="dbo\Functions\GetNotesToursFormLabel.sql" />
    <Build Include="dbo\Functions\GetConcantenatedToursManyPickList1ItemNamesByTourID.sql" />
    <Build Include="dbo\Functions\GetConcantenatedTeamNamesByUserID.sql" />
    <Build Include="dbo\Functions\GetConcantenatedTeamIDsByUserID.sql" />
    <Build Include="dbo\Functions\GetConcantenatedNotesToursSales.sql" />
    <Build Include="dbo\Functions\GetConcantenatedNotesToursMarketing.sql" />
    <Build Include="dbo\Functions\GetConcantenatedNotesToursLeads.sql" />
    <Build Include="dbo\Functions\GetConcantenatedNotesToursForm9.sql" />
    <Build Include="dbo\Functions\GetConcantenatedNotesToursForm8.sql" />
    <Build Include="dbo\Functions\GetConcantenatedNotesToursForm7.sql" />
    <Build Include="dbo\Functions\GetConcantenatedNotesToursForm6.sql" />
    <Build Include="dbo\Functions\GetConcantenatedNotesToursForm5.sql" />
    <Build Include="dbo\Functions\GetConcantenatedNotesToursForm4.sql" />
    <Build Include="dbo\Functions\GetConcantenatedNotesToursForm3.sql" />
    <Build Include="dbo\Functions\GetConcantenatedNotesToursForm2.sql" />
    <Build Include="dbo\Functions\GetConcantenatedNotesToursForm15.sql" />
    <Build Include="dbo\Functions\fn_testing.sql" />
    <Build Include="dbo\Functions\GetConcantenatedNotesToursForm14.sql" />
    <Build Include="dbo\Functions\GetConcantenatedNotesToursForm13.sql" />
    <Build Include="dbo\Functions\GetConcantenatedNotesToursForm12.sql" />
    <Build Include="dbo\Functions\GetConcantenatedNotesToursForm11.sql" />
    <Build Include="dbo\Functions\GetConcantenatedNotesToursForm10.sql" />
    <Build Include="dbo\Functions\GetConcantenatedNotesToursForm1.sql" />
    <Build Include="dbo\Functions\GetConcantenatedNotesToursExit.sql" />
    <Build Include="dbo\Functions\GetConcantenatedNotesToursCalls.sql" />
    <Build Include="dbo\Functions\GetConcantenatedNotesCustomers.sql" />
    <Build Include="dbo\Functions\GetConcantenatedNotesCancellations.sql" />
    <Build Include="dbo\Functions\GetConcantenatedMilestonesTourStatusTypes.sql" />
    <Build Include="dbo\Functions\GetConcantenatedMilestonesSaleStatusTypes.sql" />
    <Build Include="dbo\Functions\GetConcantenatedLocationsByUserID.sql" />
    <Build Include="dbo\Functions\GetConcantenatedLocationsByTeamID.sql" />
    <Build Include="dbo\Functions\GetConcantenatedGiftValuesByTourID.sql" />
    <Build Include="dbo\Functions\GetConcantenatedGiftNamesByTourID.sql" />
    <Build Include="dbo\Functions\GetConcantenatedGiftCodesByTourID.sql" />
    <Build Include="dbo\Stored Procedures\InsertCustomDisplay_HideCustomersField.sql" />
    <Build Include="dbo\Stored Procedures\InsertCustomDisplay_HideCreateMultiplePurchases.sql" />
    <Build Include="dbo\Stored Procedures\SPS_GetRangeObject.sql" />
    <Build Include="dbo\Stored Procedures\InsertCustomDisplay_HideCourtesyTour.sql" />
    <Build Include="dbo\Stored Procedures\InsertCustomDisplay_HideCancellationRequestModule.sql" />
    <Build Include="dbo\Stored Procedures\InsertCustomDisplay_HideCallCenter.sql" />
    <Build Include="dbo\Stored Procedures\InsertCustomDisplay_AddRequireConfirmerForConfirmed.sql" />
    <Build Include="dbo\Stored Procedures\InsertCustomCriteria_SetToursAsLeads.sql" />
    <Build Include="dbo\Stored Procedures\InsertCustomCriteria_SetSalesAgentAsSalesDashboard.sql" />
    <Build Include="dbo\Stored Procedures\aspnet_Membership_CreateUser.sql">
      <QuotedIdentifier>Off</QuotedIdentifier>
    </Build>
    <Build Include="dbo\Stored Procedures\sp_BlitzIndex.sql" />
    <Build Include="dbo\Stored Procedures\sp_BlitzCache.sql" />
    <Build Include="dbo\Stored Procedures\InsertPageControlDisplayRights.sql" />
    <Build Include="dbo\Stored Procedures\InsertLocalizations.sql" />
    <Build Include="dbo\Stored Procedures\InsertGlobalPageControlDisplayRights.sql" />
    <Build Include="dbo\Stored Procedures\InsertGlobalCustomPageControlDisplayRights.sql" />
    <Build Include="dbo\Stored Procedures\InsertCustomPageControlDisplayRights.sql" />
    <Build Include="dbo\Stored Procedures\InsertCustomLocalizations.sql" />
    <Build Include="dbo\Stored Procedures\InsertCustomCriteriaRights.sql" />
    <Build Include="dbo\Stored Procedures\InsertCustomAnalyticViewsKpis_New.sql" />
    <Build Include="dbo\Stored Procedures\InsertCustomAnalyticViewsKpis.sql" />
    <Build Include="dbo\Stored Procedures\InsertCriteriaRights.sql" />
    <Build Include="dbo\Stored Procedures\aspnet_UsersInRoles_RemoveUsersFromRoles.sql">
      <QuotedIdentifier>Off</QuotedIdentifier>
    </Build>
    <Build Include="dbo\Stored Procedures\aspnet_UsersInRoles_IsUserInRole.sql">
      <QuotedIdentifier>Off</QuotedIdentifier>
    </Build>
    <Build Include="dbo\Stored Procedures\aspnet_UsersInRoles_GetUsersInRoles.sql">
      <QuotedIdentifier>Off</QuotedIdentifier>
    </Build>
    <Build Include="dbo\Stored Procedures\aspnet_UsersInRoles_GetRolesForUser.sql">
      <QuotedIdentifier>Off</QuotedIdentifier>
    </Build>
    <Build Include="dbo\Stored Procedures\aspnet_UsersInRoles_FindUsersInRole.sql">
      <QuotedIdentifier>Off</QuotedIdentifier>
    </Build>
    <Build Include="dbo\Stored Procedures\aspnet_UsersInRoles_AddUsersToRoles.sql">
      <QuotedIdentifier>Off</QuotedIdentifier>
    </Build>
    <Build Include="dbo\Stored Procedures\aspnet_Users_DeleteUser.sql">
      <QuotedIdentifier>Off</QuotedIdentifier>
    </Build>
    <Build Include="dbo\Stored Procedures\aspnet_Users_CreateUser.sql">
      <QuotedIdentifier>Off</QuotedIdentifier>
    </Build>
    <Build Include="dbo\Stored Procedures\aspnet_UnRegisterSchemaVersion.sql">
      <QuotedIdentifier>Off</QuotedIdentifier>
    </Build>
    <Build Include="dbo\Stored Procedures\aspnet_Roles_RoleExists.sql">
      <QuotedIdentifier>Off</QuotedIdentifier>
    </Build>
    <Build Include="dbo\Stored Procedures\aspnet_Roles_GetAllRoles.sql">
      <QuotedIdentifier>Off</QuotedIdentifier>
    </Build>
    <Build Include="dbo\Stored Procedures\aspnet_Roles_DeleteRole.sql">
      <QuotedIdentifier>Off</QuotedIdentifier>
    </Build>
    <Build Include="dbo\Stored Procedures\aspnet_Roles_CreateRole.sql">
      <QuotedIdentifier>Off</QuotedIdentifier>
    </Build>
    <Build Include="dbo\Stored Procedures\aspnet_RegisterSchemaVersion.sql">
      <QuotedIdentifier>Off</QuotedIdentifier>
    </Build>
    <Build Include="dbo\Stored Procedures\aspnet_Membership_UpdateUserInfo.sql">
      <QuotedIdentifier>Off</QuotedIdentifier>
    </Build>
    <Build Include="dbo\Stored Procedures\aspnet_Membership_UpdateUser.sql">
      <QuotedIdentifier>Off</QuotedIdentifier>
    </Build>
    <Build Include="dbo\Stored Procedures\aspnet_Membership_UnlockUser.sql">
      <QuotedIdentifier>Off</QuotedIdentifier>
    </Build>
    <Build Include="dbo\Stored Procedures\aspnet_Membership_SetPassword.sql">
      <QuotedIdentifier>Off</QuotedIdentifier>
    </Build>
    <Build Include="dbo\Stored Procedures\aspnet_Membership_ResetPassword.sql">
      <QuotedIdentifier>Off</QuotedIdentifier>
    </Build>
    <Build Include="dbo\Stored Procedures\aspnet_Membership_GetUserByUserId.sql">
      <QuotedIdentifier>Off</QuotedIdentifier>
    </Build>
    <Build Include="dbo\Stored Procedures\aspnet_Membership_GetUserByName.sql">
      <QuotedIdentifier>Off</QuotedIdentifier>
    </Build>
    <Build Include="dbo\Stored Procedures\SPA_SwitchCustomer.sql" />
    <Build Include="dbo\Stored Procedures\aspnet_Membership_GetUserByEmail.sql">
      <QuotedIdentifier>Off</QuotedIdentifier>
    </Build>
    <Build Include="dbo\Stored Procedures\aspnet_Membership_GetPasswordWithFormat.sql">
      <QuotedIdentifier>Off</QuotedIdentifier>
    </Build>
    <Build Include="dbo\Stored Procedures\aspnet_Membership_GetPassword.sql">
      <QuotedIdentifier>Off</QuotedIdentifier>
    </Build>
    <Build Include="dbo\Stored Procedures\aspnet_Membership_GetNumberOfUsersOnline.sql">
      <QuotedIdentifier>Off</QuotedIdentifier>
    </Build>
    <Build Include="dbo\Stored Procedures\aspnet_Membership_GetAllUsers.sql">
      <QuotedIdentifier>Off</QuotedIdentifier>
    </Build>
    <Build Include="dbo\Stored Procedures\aspnet_Membership_FindUsersByName.sql">
      <QuotedIdentifier>Off</QuotedIdentifier>
    </Build>
    <Build Include="dbo\Stored Procedures\aspnet_Membership_FindUsersByEmail.sql">
      <QuotedIdentifier>Off</QuotedIdentifier>
    </Build>
    <Build Include="dbo\Stored Procedures\aspnet_Membership_ChangePasswordQuestionAndAnswer.sql">
      <QuotedIdentifier>Off</QuotedIdentifier>
    </Build>
    <Build Include="dbo\Stored Procedures\aspnet_CheckSchemaVersion.sql">
      <QuotedIdentifier>Off</QuotedIdentifier>
    </Build>
    <Build Include="dbo\Stored Procedures\aspnet_Applications_CreateApplication.sql">
      <QuotedIdentifier>Off</QuotedIdentifier>
    </Build>
    <Build Include="dbo\Stored Procedures\BulkUpdateCustomers.sql" />
    <Build Include="dbo\Stored Procedures\BulkUpdateNotes.sql" />
    <Build Include="dbo\Stored Procedures\BulkUpdatePurchases.sql" />
    <Build Include="dbo\Stored Procedures\BulkUpdateTours.sql" />
    <Build Include="dbo\Stored Procedures\InsertCustomDisplay_HideToursSalesFees2Amount.sql" />
    <Build Include="dbo\Stored Procedures\InsertCustomDisplay_HideToursSalesFees1Amount.sql" />
    <Build Include="dbo\Stored Procedures\InsertCustomDisplay_HideToursSalesDate1.sql" />
    <Build Include="dbo\Stored Procedures\InsertCustomDisplay_HideMarketingPickList1Item.sql" />
    <Build Include="dbo\Stored Procedures\InsertCustomDisplay_HideHotel.sql" />
    <Build Include="dbo\Stored Procedures\InsertCustomDisplay_HideCampaign.sql" />
    <Build Include="dbo\Stored Procedures\InsertCustomDisplay_SetTeamExecutiveAsMarketingTeamLeader.sql" />
    <Build Include="dbo\Stored Procedures\InsertCustomDisplay_SetSalesAgentAsSalesDashboard.sql" />
    <Build Include="dbo\Stored Procedures\InsertCustomDisplay_SetRolesForDomainAdministration.sql" />
    <Build Include="dbo\Stored Procedures\InsertCustomDisplay_HideWasIsPending.sql" />
    <Build Include="dbo\Stored Procedures\DeleteAllTours.sql" />
    <Build Include="dbo\Stored Procedures\InsertCustomDisplay_HideWasConfirmed.sql" />
    <Build Include="dbo\Stored Procedures\InsertCustomDisplay_HideUpdateMarketingInformation.sql" />
    <Build Include="dbo\Stored Procedures\InsertCustomDisplay_HideTourStatusEfficiencyReport.sql" />
    <Build Include="dbo\Stored Procedures\InsertCustomDisplay_HideToursSalesPickList2Item.sql" />
    <Build Include="dbo\Stored Procedures\InsertCustomDisplay_HideToursSalesPickList1Item.sql" />
    <Build Include="dbo\Stored Procedures\InsertCustomDisplay_HideToursSalesField.sql" />
    <Build Include="dbo\Stored Procedures\InsertCustomDisplay_HideSalesUser6.sql" />
    <Build Include="dbo\Stored Procedures\InsertCustomDisplay_HideSalesUser5.sql" />
    <Build Include="dbo\Stored Procedures\InsertCustomDisplay_HideSalesUser4.sql" />
    <Build Include="dbo\Stored Procedures\InsertCustomDisplay_HideSalesUser3.sql" />
    <Build Include="dbo\Stored Procedures\InsertCustomDisplay_HideSalesUser2.sql" />
    <Build Include="dbo\Stored Procedures\InsertCustomDisplay_HideSalesUser1.sql" />
    <Build Include="dbo\Stored Procedures\InsertCustomDisplay_HideSalesUser.sql" />
    <Build Include="dbo\Stored Procedures\InsertCustomDisplay_HideRevenueDetailReport.sql" />
    <Build Include="dbo\Stored Procedures\InsertCustomDisplay_HideRescheduleToursToLeads.sql" />
    <Build Include="dbo\Stored Procedures\InsertCustomDisplay_HidePenderEfficiencyReport.sql" />
    <Build Include="dbo\Stored Procedures\InsertCustomDisplay_HidePenderCancellationRequest.sql" />
    <Build Include="dbo\Stored Procedures\InsertCustomDisplay_HideMarketingUser4.sql" />
    <Build Include="dbo\Stored Procedures\InsertCustomDisplay_HideMarketingUser3.sql" />
    <Build Include="dbo\Stored Procedures\InsertCustomDisplay_HideMarketingUser2.sql" />
    <Build Include="dbo\Stored Procedures\InsertCustomDisplay_HideMarketingUser1.sql" />
    <Build Include="dbo\Stored Procedures\InsertCustomDisplay_HideMarketingUser.sql" />
    <Build Include="dbo\Stored Procedures\InsertCustomDisplay_HideMarketingPickListItem.sql" />
    <Build Include="dbo\Stored Procedures\InsertCustomDisplay_HideLostVolumeEfficiencyReport.sql" />
    <Build Include="dbo\Stored Procedures\SP_GetTrackResultsData.sql" />
    <Build Include="dbo\Stored Procedures\InsertCustomDisplay_HideLeadRatioEfficiencyReport.sql" />
    <Build Include="dbo\Stored Procedures\InsertCustomDisplay_HideImportLead.sql" />
    <Build Include="dbo\Stored Procedures\InsertCustomDisplay_HideExport.sql" />
    <Build Include="dbo\Stored Procedures\InsertCustomDisplay_HideCustomersPickList2Item.sql" />
    <Build Include="dbo\Stored Procedures\InsertCustomDisplay_HideCustomersPickList1Item.sql" />
    <Build Include="dbo\User Defined Types\CustomerType.sql" />
    <Build Include="dbo\User Defined Types\NotesType.sql" />
    <Build Include="dbo\User Defined Types\PurchaseType.sql" />
    <Build Include="dbo\User Defined Types\TourType.sql" />
  </ItemGroup>
</Project>