﻿CREATE TABLE [dbo].[Waves] (
    [waveID]     INT      IDENTITY (1, 1) NOT NULL,
    [locationID] INT      NOT NULL,
    [time]       DATETIME NOT NULL,
    [monday]     BIT      NOT NULL,
    [tuesday]    BIT      NOT NULL,
    [wednesday]  BIT      NOT NULL,
    [thursday]   BIT      NOT NULL,
    [friday]     BIT      NOT NULL,
    [saturday]   BIT      NOT NULL,
    [sunday]     BIT      NOT NULL,
    CONSTRAINT [PK_Waves] PRIMARY KEY CLUSTERED ([waveID] ASC)
);


GO
CREATE TRIGGER [dbo].[Waves.InsertUpdateDelete]
    ON [dbo].[Waves]
    FOR INSERT, UPDATE, DELETE
AS 
BEGIN
    SET NOCOUNT ON;

    DECLARE @Activity  NVARCHAR (8), @id INT;

    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'updated'
    END

    IF EXISTS (SELECT * FROM inserted) AND NOT EXISTS(SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'inserted'
    END

    IF EXISTS (SELECT * FROM deleted) AND NOT EXISTS(SELECT * FROM inserted)
    BEGIN
        SET @Activity = 'deleted'
    END

    

    IF (@Activity = 'deleted')
    BEGIN
        DECLARE cur CURSOR FOR SELECT waveID FROM deleted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'Waves', @id, 'waveID',
                (
                    SELECT *
                    FROM deleted i 
                    WHERE i.waveID =  @id
                    FOR JSON AUTO
                ),0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
    ELSE
    BEGIN
        DECLARE cur CURSOR
        FOR SELECT waveID
        FROM inserted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'Waves', @id, 'waveID',
                (
                    SELECT *
                    FROM inserted i 
                    WHERE i.waveID =  @id
                    FOR JSON AUTO
                ), 0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
END

GO
DISABLE TRIGGER [dbo].[Waves.InsertUpdateDelete]
    ON [dbo].[Waves];

