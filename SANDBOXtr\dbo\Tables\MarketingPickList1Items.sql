﻿CREATE TABLE [dbo].[MarketingPickList1Items] (
    [marketingPickList1ItemID]   INT           IDENTITY (1, 1) NOT NULL,
    [marketingPickList1ItemName] NVARCHAR (64) NOT NULL,
    [marketingPickList1ItemCode] NVARCHAR (64) NULL,
    [sortOrder]                  INT           NOT NULL,
    [active]                     BIT           NOT NULL,
    CONSTRAINT [PK_MarketingPickList1Items] PRIMARY KEY CLUSTERED ([marketingPickList1ItemID] ASC),
    CONSTRAINT [UK_MarketingPickList1Items_marketingPickList1ItemName] UNIQUE NONCLUSTERED ([marketingPickList1ItemName] ASC)
);


GO
CREATE TRIGGER [dbo].[MarketingPickList1Items.InsertUpdateDelete]
    ON [dbo].[MarketingPickList1Items]
    FOR INSERT, UPDATE, DELETE
AS 
BEGIN
    SET NOCOUNT ON;

    DECLARE @Activity  NVARCHAR (8), @id INT;

    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'updated'
    END

    IF EXISTS (SELECT * FROM inserted) AND NOT EXISTS(SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'inserted'
    END

    IF EXISTS (SELECT * FROM deleted) AND NOT EXISTS(SELECT * FROM inserted)
    BEGIN
        SET @Activity = 'deleted'
    END

    

    IF (@Activity = 'deleted')
    BEGIN
        DECLARE cur CURSOR FOR SELECT marketingPickList1ItemID FROM deleted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'MarketingPickList1Items', @id, 'marketingPickList1ItemID',
                (
                    SELECT *
                    FROM deleted i 
                    WHERE i.marketingPickList1ItemID =  @id
                    FOR JSON AUTO
                ),0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
    ELSE
    BEGIN
        DECLARE cur CURSOR
        FOR SELECT marketingPickList1ItemID
        FROM inserted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'MarketingPickList1Items', @id, 'marketingPickList1ItemID',
                (
                    SELECT *
                    FROM inserted i 
                    WHERE i.marketingPickList1ItemID =  @id
                    FOR JSON AUTO
                ), 0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
END

GO
DISABLE TRIGGER [dbo].[MarketingPickList1Items.InsertUpdateDelete]
    ON [dbo].[MarketingPickList1Items];

