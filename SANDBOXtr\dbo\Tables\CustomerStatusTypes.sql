﻿CREATE TABLE [dbo].[CustomerStatusTypes] (
    [customerStatusTypeID]   INT           IDENTITY (1, 1) NOT NULL,
    [customerStatusTypeName] NVARCHAR (64) NOT NULL,
    [customerStatusTypeCode] NVARCHAR (64) NOT NULL,
    [sortOrder]              INT           NOT NULL,
    [active]                 BIT           NOT NULL,
    CONSTRAINT [PK_CustomerStatusTypes] PRIMARY KEY CLUSTERED ([customerStatusTypeID] ASC),
    CONSTRAINT [UK_CustomerStatusTypes_customerStatusTypeName] UNIQUE NONCLUSTERED ([customerStatusTypeName] ASC)
);


GO
CREATE TRIGGER [dbo].[CustomerStatusTypes.InsertUpdateDelete]
    ON [dbo].[CustomerStatusTypes]
    FOR INSERT, UPDATE, DELETE
AS 
BEGIN
    SET NOCOUNT ON;

    DECLARE @Activity  NVARCHAR (8), @id INT;

    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'updated'
    END

    IF EXISTS (SELECT * FROM inserted) AND NOT EXISTS(SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'inserted'
    END

    IF EXISTS (SELECT * FROM deleted) AND NOT EXISTS(SELECT * FROM inserted)
    BEGIN
        SET @Activity = 'deleted'
    END

    

    IF (@Activity = 'deleted')
    BEGIN
        DECLARE cur CURSOR FOR SELECT customerStatusTypeID FROM deleted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'CustomerStatusTypes', @id, 'customerStatusTypeID',
                (
                    SELECT *
                    FROM deleted i 
                    WHERE i.customerStatusTypeID =  @id
                    FOR JSON AUTO
                ),0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
    ELSE
    BEGIN
        DECLARE cur CURSOR
        FOR SELECT customerStatusTypeID
        FROM inserted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'CustomerStatusTypes', @id, 'customerStatusTypeID',
                (
                    SELECT *
                    FROM inserted i 
                    WHERE i.customerStatusTypeID =  @id
                    FOR JSON AUTO
                ), 0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
END

GO
DISABLE TRIGGER [dbo].[CustomerStatusTypes.InsertUpdateDelete]
    ON [dbo].[CustomerStatusTypes];

