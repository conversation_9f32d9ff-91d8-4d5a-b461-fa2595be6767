using System;
using System.Collections.Generic;
using System.Text;
using System.Transactions;

using TrackResults.BES.Data.Business;
using TrackResults.BES.Data.Tables;
using TrackResults.BES.Data.Types;
using TrackResults.BES.DataAccess;
using TrackResults.BES.DataAccess.Mappings;
using TrackResults.BES.DataAccess.Secure;
using TrackResults.BES.Properties;
using TrackResults.Common.Core.Data.SqlClient.MSSql;
using TrackResults.BES.DataAccess.ManyPickLists;
using TrackResults.BES.DataAccess.Notes;
using TrackResults.BES.DataAccess.Milestones;
using TrackResults.BES.DataAccess.Audits;
using TrackResults.BES.Data;

namespace TrackResults.BES.Services
{
	public static class ToursService
	{
        public const string ImportNumberDateTimeFormat = "yyMMddHHmmssFFF";

		public static Tour BuildSelectUpdateTourFromPropertyKeys(HashSet<string> propertyKeys)
		{
			Tour selectUpdateTour = new Tour();
			selectUpdateTour.SetUpdated(propertyKeys);

			selectUpdateTour.Customer = new Customer();
			selectUpdateTour.Customer.SetUpdated(propertyKeys);

			if (selectUpdateTour.PurchasesUpdated)
			{
				selectUpdateTour.Purchases = new List<Purchase> { new Purchase() };
				selectUpdateTour.Purchases[0].SetUpdated(propertyKeys);
			}

			return selectUpdateTour;
		}

		public static int RescheduleTour(Tour newTour, int rescheduledFromTourID)
		{
			int newTourID = 0;

			MSSqlConnectionScope.ExecuteTransaction(ConnectionStrings.Default, delegate()
			{
				// old tour = the tour that is being rescheduled from
				// new tour = the new tour being created
				//rescheduledCount - on the old tour + 1 current count, new tour same as old tour
				//rescheduledTourID - On the old tour, this is the new tour id
				//parentRescheduledTourID - On the new tour, this is the first tour id in the chain of reschedules (or old tour parentRescheduledTourID)

				newTourID = AddCopy(rescheduledFromTourID);

				Tour rescheduledFromTour = new Tour();
				rescheduledFromTour.TourID = rescheduledFromTourID;
				rescheduledFromTour.RescheduledCount = 1;
				rescheduledFromTour.RescheduledCountUpdated = true;
				rescheduledFromTour.RescheduledTourID = newTourID;
				rescheduledFromTour.RescheduledTourIDUpdated = true;
				rescheduledFromTour.ParentRescheduledTourID = newTourID;
				rescheduledFromTour.ParentRescheduledTourIDUpdated = true;

				Tuple<int, int> rescheduledCountParentRescheduledTourID =
					ToursDataAccess.SelectRescheduledFromRescheduledCountParentRescheduledTourID(rescheduledFromTourID);

				if (rescheduledCountParentRescheduledTourID != null)
				{
					rescheduledFromTour.RescheduledCount = rescheduledCountParentRescheduledTourID.Item1 + 1;

					ToursDataAccess.UpdateParentRescheduledTourID(rescheduledCountParentRescheduledTourID.Item2, newTourID);
				}

				ToursDataAccess.Update(rescheduledFromTour, false);

				newTour.TourID = newTourID;
				newTour.RescheduledCount = rescheduledFromTour.RescheduledCount;
				newTour.RescheduledCountUpdated = true;

				ToursDataAccess.Update(newTour, false);
			});

			return newTourID;
		}


		public static int RescheduleTourToLead(int tourID)
		{
			int result = 0;

			// TODO28
			//MSSqlConnectionScope.ExecuteTransaction(ConnectionStrings.Default, delegate()
			//	{
			//        Tour currentTour = ToursDataAccess.SelectDataByID(tourID);

			//        if (currentTour == null)
			//        {
			//            throw new ArgumentException(string.Format("Tour for tourID '{0}' was not found.", tourID), "tourID");
			//        }

			//        currentTour.RescheduledCount++;

			//        int rescheduledTourTourID = ToursDataAccess.Insert(currentTour);

			//        // Rescheduled Notes
			//        //todo28
			//        //List<Note> tourNotes = ToursNotesSecureDataAccess.SelectDataByTourID(tourID);

			//        //foreach (Note tourNote in tourNotes)
			//        //{
			//        //    ToursNotesSecureDataAccess.Insert(rescheduledTourTourID, tourNote.NoteTypeID, tourNote.NoteText, tourNote.UserID,
			//        //        tourNote.InsertTimeStamp);
			//        //}

			//        // Rescheduled Gifts

			//        //Maps.ToursGiftsMapDataTable toursGiftsMapDataTable = ToursGiftsMapDataAccess.SelectExtendedDataByTourID(tourID);

			//        //if (toursGiftsMapDataTable.Rows.Count > 0)
			//        //{
			//        //    foreach (Maps.ToursGiftsMapRow toursGiftsMapRow in toursGiftsMapDataTable)
			//        //    {
			//        //        ToursGiftsMapDataAccess.Insert(rescheduledTourTourID, toursGiftsMapRow.giftID);
			//        //    }
			//        //}

			//        ToursDataAccess.UpdateReschedule(currentTour.RescheduledCount, rescheduledTourTourID, tourID);

			//        result = rescheduledTourTourID;

			//       
			//    
			//});

			return result;
		}

		public static int AddCopy(int tourID)
		{
			int newTourID = 0;

			MSSqlConnectionScope.ExecuteTransaction(ConnectionStrings.Default, delegate()
			{
				Tour newTour = ToursDataAccess.SelectDataByID(tourID);

				newTour.LeadNotes = NotesDataAccess.SelectAllByParentID(tourID, "Tour.LeadNotes");
                newTour.CallNotes = NotesDataAccess.SelectAllByParentID(tourID, "Tour.CallNotes");
                newTour.TourStatusTypeIDMilestones = MilestonesTourStatusTypesDataAccess.Instance.SelectMilestoneIDsByReferenceID(tourID);
				newTour.ToursManyPickList1ItemIDs = ToursManyPickList1ItemsManyDataAccess.Instance.SelectToIDs(tourID);
				newTour.GiftIDs = ToursGiftsManyDataAccess.Instance.SelectToIDs(tourID);
				newTour.MarketingNotes = NotesDataAccess.SelectAllByParentID(tourID, "Tour.MarketingNotes");
                newTour.SalesNotes = NotesDataAccess.SelectAllByParentID(tourID, "Tour.SalesNotes");

                newTourID = ToursDataAccess.Insert(newTour, true, true);

				List<Audit> tourAudits = AuditsToursDataAccess.Instance.SelectAuditsByReferenceID(tourID);

				foreach (Audit tourAudit in tourAudits)
				{
					tourAudit.ReferenceID = newTourID;
					AuditsToursDataAccess.Instance.Insert(tourAudit, tourAudit.InsertTimeStamp);
				}
			});

			return newTourID;
		}

		public static int? Delete(int tourID)
		{
			int? returnValue = null;
			int customerID = ToursDataAccess.SelectCustomerIDByID(tourID);
			int countOfTours = CustomersDataAccess.SelectCountOfToursByID(customerID);

			if (countOfTours > 1)
			{
				ToursDataAccess.Delete(tourID);
				returnValue = customerID;
			}
			else
			{
				CustomersDataAccess.Delete(customerID);
			}

			return returnValue;
		}

		public static string GetNewImportNumber()
		{
			return DateTime.Now.ToString(ImportNumberDateTimeFormat);
		}
	}
}