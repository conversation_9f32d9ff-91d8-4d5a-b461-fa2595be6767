const express = require('express');
const router = express.Router();

const { 
    saveKPIsToDatabase,
    updateKPIsToDatabase,
    updateReportKPIRelationship,
    saveReportToDatabase,
    updateReportToDatabase,
    getGiftsFromDatabase,
    saveJoinsToDatabase,
    updateJoinsToDatabase,
    saveReportUserToDB,
    getSavedUserByReportId,
    unAssignReportUser,
    getReportById,
    fetchUserDataByUserName,
    fetchUserData,
    saveKpiVisibility,
    fetchKPIsByKpiIds,
    fetchReportKPIRelationship,
    updateReportWithJoinIdsInDatabase,
    getJoinDefinitionsByReportId,
    getReportsUsersMapByUserId,
    updateFiltersToDataBase,
    saveFiltersToDataBase,
    getFilters,
    getReportInfo,
    unAssignKpiReport,
    unAssignJoinReport,
    unAssignReport,
} = require('./databaseFunctions');

// Define a route to fetch data from Database 1
router.get('/GetGifts', async (req, res) => {
    try {
        // Get the Gifts data from the database
        const gifts = await getGiftsFromDatabase();

        // Respond with the Gifts data
        res.json(gifts);
    } catch (error) {
        ////console.error('Error querying the Gifts table:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// Define the route to save a report
router.post('/saveReport', async (req, res) => {
    try {
        //console.log(req.body);
        let reportId;
        if (req && req.body && req.body.reportId > 0) {
            // If reportId is greater than 0, update the report
            await updateReportToDatabase(req.body);
            reportId = req.body.reportId;
        } else {
            // Otherwise, save the report and get the reportId
            reportId = await saveReportToDatabase(req.body);
        }

        // Respond with success and the generated reportId
        res.json({ success: true, reportId });
    } catch (error) {
        ////console.error('Error saving/updating the report:', error);
        res.status(500).json({ success: false, message: 'Internal server error' });
    }
});


// Define the route to save joins
router.post('/saveJoins', async (req, res) => {
    try {
        const joinDataArray = req.body; // Assuming req.body is an array

        const joinIds = []; // Array to store generated joinIds

        for (const joinData of joinDataArray) {
            let joinId;

            if (joinData.reportJoinId > 0) {
                // If reportJoinId is greater than 0, update the join
                await updateJoinsToDatabase(joinData);
                joinId = joinData.reportJoinId;
            } else {
                joinId = await saveJoinsToDatabase(joinData);
            }

            joinIds.push(joinId); // Store the generated joinId
        }

        // Respond with success and the generated joinIds
        res.json({ success: true, joinIds });
    } catch (error) {
        ////console.error('Error saving/updating the joins:', error);
        res.status(500).json({ success: false, message: 'Internal server error' });
    }
});

router.post('/saveFilters', async(req, res) => {
    try {

        if (req && req.body && req.body.reportFilterId > 0) {
            // If reportId is greater than 0, update the report
            await updateFiltersToDataBase(req.body);
            id = req.body.reportFilterId;
        } else {
            // Otherwise, save the report and get the reportId
            id = await saveFiltersToDataBase(req.body);
        }

        // Respond with success and the generated reportId
        res.json({ success: true, id });
    }
    catch(error) {
        res.status(500).json({ success: false, message: 'Internal server error' });
    }
});

// Define a route to fetch a specific report by reportId
router.get('/getFilters/:reportId', async (req, res) => {
    try {
        const reportId = req.params.reportId;
        const report = await getFilters(reportId);

        if (!report) {
            res.status(404).json({ success: false, message: 'Report filters not found' });
            return;
        }

        // Send the result as JSON
        res.json(report);
    } catch (error) {
        ////console.error('Error retrieving report by ReportId:', error);
        res.status(500).json({ success: false, message: 'Internal server error' });
    }
});


// Define a new route to save KPI data
router.post('/saveKPIs', async (req, res) => {
    try {
        const { reportId, kpiData } = req.body;

        // Filtrar los elementos con kpiId igual a 0
        const kpisToSave = kpiData.filter(item => item.kpiId === 0);
        // Insert KPI data into the database and get the generated KPI IDs

        const generatedKpiIds = await saveKPIsToDatabase(kpisToSave);

        // Filtrar los elementos con kpiId mayor que 0
        const kpisToUpdate = kpiData.filter(item => item.kpiId > 0);
        const kpiIds = kpiData.filter(item => item.kpiId > 0).map(item => item.kpiId);
        //update KPI data
        await updateKPIsToDatabase(kpisToUpdate);

        const allKpiIds = generatedKpiIds.concat(kpiIds);
        // Respond with success and the generated KPI IDs
        res.json({ success: true, kpiIds: allKpiIds });
    } catch (error) {
        ////console.error('Error saving KPI data:', error);
        res.status(500).json({ success: false, message: 'Internal server error' });
    }
});

// Define the route to update the ReportKPIRelationship table
router.post('/updateReportKPIRelationship', async (req, res) => {
    try {
        const { reportId, kpiIds, reportIdOld } = req.body;
        // console.log(kpiIds)
        // Update the ReportKPIRelationship table with kpiIds
        await updateReportKPIRelationship(reportId, kpiIds, reportIdOld);

        // Respond with success
        res.json({ success: true });
    } catch (error) {
        ////console.error('Error updating ReportKPIRelationship:', error);
        res.status(500).json({ success: false, message: 'Internal server error' });
    }
});

router.post('/updateReportWithJoinId', async (req, res) => {
    try {
        const { reportId, joinIds } = req.body;

        // Update the ReportTable with the joinIds
        await updateReportWithJoinIdsInDatabase(reportId, joinIds);

        // Respond with success
        res.json({ success: true });
    } catch (error) {
        ////console.error('Error updating the report:', error);
        res.status(500).json({ success: false, message: 'Internal server error' });
    }
});

// Define a route to fetch data from ReportTable by ReportId
router.get('/getReportInfo/:ReportId', async (req, res) => {
    try {
        const ReportId = req.params.ReportId;
        const reportInfo = await getReportInfo(ReportId);

        // Send the results as JSON
        res.json(reportInfo);
    } catch (error) {
        ////console.error('Error retrieving ReportName and ReportId:', error);
        res.status(500).json({ success: false, message: 'Internal server error' });
    }
});

// Define a route to fetch a specific report by reportId
router.get('/getReportById/:reportId', async (req, res) => {
    try {
        const reportId = req.params.reportId;
        const report = await getReportById(reportId);

        if (!report) {
            res.status(404).json({ success: false, message: 'Report not found' });
            return;
        }

        // Send the result as JSON
        res.json(report);
    } catch (error) {
        ////console.error('Error retrieving report by ReportId:', error);
        res.status(500).json({ success: false, message: 'Internal server error' });
    }
});

// Define a route to fetch a specific report by reportId
router.get('/getSavedUserByReportId/:reportId', async (req, res) => {
    try {
        const reportId = req.params.reportId;
        const report = await getSavedUserByReportId(reportId);

        if (!report) {
            res.status(404).json({ success: false, message: 'saved users not found' });
            return;
        }

        // Send the result as JSON
        res.json(report);
    } catch (error) {
        ////console.error('Error retrieving saved users by ReportId:', error);
        res.status(500).json({ success: false, message: 'Internal server error' });
    }
});
// Define a route to save multiple ReportIds and UserIds into the ReportsUsersMap table
router.post('/unAssignReportUser', async (req, res) => {
    try {
        const { ReportId, UserId } = req.body; // Assuming you send these values in the request body
        const saveResult = await unAssignReportUser(ReportId, UserId);
            if (!saveResult.success) {
                res.status(500).json({ success: false, message: `Error saving data for UserId ${UserId} into ReportsUsersMap.` });
                return;
            }

        res.json({ success: true, message: 'Data saved successfully.' });
    } catch (error) {
        ////console.error('Error handling saveReportUsers request:', error);
        res.status(500).json({ success: false, message: 'Internal server error' });
    }
});

// Define a route to save multiple ReportIds and UserIds into the ReportsUsersMap table
router.post('/unAssignKpiReport', async (req, res) => {
    try {
        const { ReportId, KpiId } = req.body; // Assuming you send these values in the request body
        const saveResult = await unAssignKpiReport(ReportId, KpiId);
        if (!saveResult.success) {
            res.status(500).json({ success: false, message: `Error saving data for UserId ${UserId} into ReportsUsersMap.` });
            return;
        }

        res.json({ success: true, message: 'Data saved successfully.' });
    } catch (error) {
        ////console.error('Error handling saveReportUsers request:', error);
        res.status(500).json({ success: false, message: 'Internal server error' });
    }
});

// Define a route to save multiple ReportIds and UserIds into the ReportsUsersMap table
router.post('/unAssignJoinReport', async (req, res) => {
    try {
        const { JoinId } = req.body; // Assuming you send these values in the request body
        const saveResult = await unAssignJoinReport(JoinId);
        if (!saveResult.success) {
            res.status(500).json({ success: false, message: `Error saving data for UserId ${UserId} into unAssignJoinReport.` });
            return;
        }

        res.json({ success: true, message: 'Data saved successfully.' });
    } catch (error) {
        ////console.error('Error handling saveReportUsers request:', error);
        res.status(500).json({ success: false, message: 'Internal server error' });
    }
});


// Define a route to save multiple ReportIds and UserIds into the ReportsUsersMap table
router.post('/saveReportUsers', async (req, res) => {
    try {
        const { ReportId, UserIds } = req.body; // Assuming you send these values in the request body

        // Check if both ReportId and UserIds are provided
        if (!ReportId || !UserIds || !Array.isArray(UserIds)) {
            res.status(400).json({ success: false, message: 'ReportId and an array of UserIds are required.' });
            return;
        }

        // Loop through UserIds and insert each user for the specified ReportId
        for (const UserId of UserIds) {
            const saveResult = await saveReportUserToDB(ReportId, UserId);
            if (!saveResult.success) {
                res.status(500).json({ success: false, message: `Error saving data for UserId ${UserId} into ReportsUsersMap.` });
                return;
            }
        }

        res.json({ success: true, message: 'Data saved successfully.' });
    } catch (error) {
        ////console.error('Error handling saveReportUsers request:', error);
        res.status(500).json({ success: false, message: 'Internal server error' });
    }
});

router.get('/getReportsUsersMapByUserId/:userId', async (req, res) => {
    try {
        const userId = req.params.userId;
        const returnData = await getReportsUsersMapByUserId(userId);

        // Send the columns as JSON response
        res.json(returnData);
    } catch (error) {
        ////console.error('Error retrieving columns by UserId:', error);
        res.status(500).json({ success: false, message: 'Internal server error' });
    }
});

router.get('/getUserData/:userName', async (req, res) => {
    try {
        const userName = req.params.userName;
        const userData = await fetchUserDataByUserName(userName);

        if (!userData || userData.length === 0) {
            res.status(404).json({ success: false, message: 'User data not found' });
            return;
        }

        // Send the user data as JSON response
        res.json(userData);
    } catch (error) {
        ////console.error('Error retrieving user data by UserName:', error);
        res.status(500).json({ success: false, message: 'Internal server error' });
    }
});

router.get('/saveKpiVisibility/:kpiId/:visible', async (req, res) => {
    try {
        const kpiId = parseInt(req.params.kpiId); // Convert kpiId to integer
        const visible = req.params.visible === 'true'; // Convert visible to boolean

        // Call saveKpiVisibility function with provided parameters
        await saveKpiVisibility(kpiId, visible);

        // Send success response
        res.json({ success: true, message: 'KPI visibility saved successfully.' });
    } catch (error) {
        //console.error('Error saving KPI visibility:', error);
        res.status(500).json({ success: false, message: 'Internal server error' });
    }
});


router.get('/getUsers', async (req, res) => {
    //////console.log("getuser hit");
    try {
        // Fetch user data from the database
        const userData = await fetchUserData();

        // Send the results as JSON
        res.json(userData);
    } catch (error) {
        ////console.error('Error retrieving user data:', error);
        res.status(500).json({ success: false, message: 'Internal server error' });
    }
});

router.get('/getKPIs', async (req, res) => {
    try {
        // Get the list/array of KpiIds from the query parameters
        const { reportId } = req.query;

       
        // Fetch KPI data from the database based on KpiIds
        const kpiData = await fetchKPIsByKpiIds(reportId);

        // Send the results as JSON
        res.json(kpiData);
    } catch (error) {
        ////console.error('Error retrieving KPI data:', error);
        res.status(500).json({ success: false, message: 'Internal server error' });
    }
});

// Define a route to fetch ReportKPIRelationship data by reportId
router.get('/getReportKPIRelationship/:reportId', async (req, res) => {
    try {
        // Get the reportId from the request parameters
        const { reportId } = req.params;

        // Call the fetchReportKPIRelationship function with the reportId
        const reportKPIRelationship = await fetchReportKPIRelationship(reportId);

        // Send the results as JSON
        res.json(reportKPIRelationship);
    } catch (error) {
        ////console.error('Error retrieving ReportKPIRelationship by reportId:', error);
        res.status(500).json({ success: false, message: 'Internal server error' });
    }
});

// Define a route to retrieve join definitions by reportId
router.get('/getJoinDefinitions/:reportId', async (req, res) => {
    try {
        // Get the reportId from the request parameters
        const { reportId } = req.params;

        // Perform a database query to retrieve join definitions based on reportId
        const joinDefinitions = await getJoinDefinitionsByReportId(reportId);

        // Send the join definitions as JSON
        res.json(joinDefinitions);
    } catch (error) {
        ////console.error('Error retrieving join definitions:', error);
        res.status(500).json({ success: false, message: 'Internal server error' });
    }
});

// 
router.post('/unAssignReport', async (req, res) => {
    try {
        const { ReportId } = req.body; // Assuming you send these values in the request body
        const saveResult = await unAssignReport(ReportId);
        if (!saveResult.success) {
            res.status(500).json({ success: false, message: `Error deleted data for reportid ${ReportId} into unAssignReport.` });
            return;
        }

        res.json({ success: true, message: 'Data deleted successfully.' });
    } catch (error) {
        ////console.error('Error handling saveReportUsers request:', error);
        res.status(500).json({ success: false, message: 'Internal server error' });
    }
});


router.get('/', function (req, res) {
    res.send('Welcome to the API');
});

module.exports = router;