﻿CREATE PROCEDURE [dbo].[BulkUpdatePurchases]
    @DataTableParam dbo.PurchaseType READONLY -- Use the TVP defined above
AS
BEGIN
    SET NOCOUNT ON;

    -- Use MERGE to update existing records and insert new ones if needed
    MERGE INTO Purchases AS target
    USING @DataTableParam AS source
    ON target.purchaseID = source.purchaseID
    
    -- When a matching purchaseID is found, update the existing row
    WHEN MATCHED THEN
        UPDATE SET
            target.externalPurchaseID = source.externalPurchaseID,
            target.tourID = source.tourID,
            target.parentSupersededFromPurchaseID = source.parentSupersededFromPurchaseID,
            target.supersededFromPurchaseID = source.supersededFromPurchaseID,
            target.supersedingFromPender = source.supersedingFromPender,
            target.saleDate = source.saleDate,
            target.saleTime = source.saleTime,
            target.productCategoryID = source.productCategoryID,
            target.productID = source.productID,
            target.subProductID = source.subProductID,
            target.saleAmount = source.saleAmount,
            target.downPaymentAmount = source.downPaymentAmount,
            target.fees1Amount = source.fees1Amount,
            target.fees2Amount = source.fees2Amount,
            target.fees3Amount = source.fees3Amount,
            target.fees4Amount = source.fees4Amount,
            target.fees5Amount = source.fees5Amount,
            target.saleTypeID = source.saleTypeID,
            target.saleStatusTypeID = source.saleStatusTypeID,
            target.saleDispositionID = source.saleDispositionID,
            target.purchasesPickList1ItemID = source.purchasesPickList1ItemID,
            target.purchasesPickList2ItemID = source.purchasesPickList2ItemID,
            target.purchasesPickList3ItemID = source.purchasesPickList3ItemID,
            target.purchasesText1 = source.purchasesText1,
            target.purchasesText2 = source.purchasesText2,
            target.purchasesBool1 = source.purchasesBool1,
            target.purchasesBool2 = source.purchasesBool2,
            target.purchasesDate1 = source.purchasesDate1,
            target.purchasesTime1 = source.purchasesTime1,
            target.purchasesDate2 = source.purchasesDate2,
            target.purchasesTime2 = source.purchasesTime2,
            target.apiConnectionID = source.apiConnectionID,
            target.apiExternalPurchaseID = source.apiExternalPurchaseID,
            target.apiExternalConnectionID = source.apiExternalConnectionID,
            target.updateTimeStamp = GETDATE(); -- Automatically update the timestamp

END;