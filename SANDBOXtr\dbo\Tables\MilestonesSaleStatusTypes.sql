﻿CREATE TABLE [dbo].[MilestonesSaleStatusTypes] (
    [milestonesSaleStatusTypeID] INT      IDENTITY (1, 1) NOT NULL,
    [purchaseID]                 INT      NOT NULL,
    [saleStatusTypeID]           INT      NOT NULL,
    [insertTimeStamp]            DATETIME CONSTRAINT [DF_MilestonesSaleStatusTypes_insertTimeStamp] DEFAULT (getdate()) NOT NULL,
    CONSTRAINT [PK_MilestonesSaleStatusTypes] PRIMARY KEY CLUSTERED ([milestonesSaleStatusTypeID] ASC),
    CONSTRAINT [FK_MilestonesSaleStatusTypes_saleStatusTypeID] FOREIGN KEY ([saleStatusTypeID]) REFERENCES [dbo].[SaleStatusTypes] ([saleStatusTypeID])
);


GO
CREATE TRIGGER [dbo].[MilestonesSaleStatusTypes.InsertUpdateDelete]
    ON [dbo].[MilestonesSaleStatusTypes]
    FOR INSERT, UPDATE, DELETE
AS 
BEGIN
    SET NOCOUNT ON;

    DECLARE @Activity  NVARCHAR (8), @id INT;

    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'updated'
    END

    IF EXISTS (SELECT * FROM inserted) AND NOT EXISTS(SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'inserted'
    END

    IF EXISTS (SELECT * FROM deleted) AND NOT EXISTS(SELECT * FROM inserted)
    BEGIN
        SET @Activity = 'deleted'
    END

    

    IF (@Activity = 'deleted')
    BEGIN
        DECLARE cur CURSOR FOR SELECT milestonesSaleStatusTypeID FROM deleted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'MilestonesSaleStatusTypes', @id, 'milestonesSaleStatusTypeID',
                (
                    SELECT *
                    FROM deleted i 
                    WHERE i.milestonesSaleStatusTypeID =  @id
                    FOR JSON AUTO
                ),0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
    ELSE
    BEGIN
        DECLARE cur CURSOR
        FOR SELECT milestonesSaleStatusTypeID
        FROM inserted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'MilestonesSaleStatusTypes', @id, 'milestonesSaleStatusTypeID',
                (
                    SELECT *
                    FROM inserted i 
                    WHERE i.milestonesSaleStatusTypeID =  @id
                    FOR JSON AUTO
                ), 0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
END

GO
DISABLE TRIGGER [dbo].[MilestonesSaleStatusTypes.InsertUpdateDelete]
    ON [dbo].[MilestonesSaleStatusTypes];

