using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using HughAxton.Core.Web.UI.HtmlControls;
using TrackResults.BES.Data.Cache.IDNames;
using TrackResults.BES.Data.Cache.Static;
using TrackResults.BES.DataAccess.ManyPickLists;
using TrackResults.Common.Core.Collections.Generic;
using TrackResults.Common.Core.Extensions;
using TrackResults.Common.DAL;
using TrackResults.Web.Keys;
using TrackResults.Web.WebControls;

namespace TrackResults.Web.UserControls
{
	public partial class ManyPickLists : UserControl
	{
		private const int excludeID = -3;
		private const int unassignedID = 0;

		public string ModifyKey
		{
			get;
			set;
		}

		public DBModes.Mode Mode
		{
			get;
			set;
		}

		public string PropertyKey
		{
			get;
			set;
		}

		public string OrPropertyKey
		{
			get;
			set;
		}

		public string PickListItemsWebServiceMethod
		{
			get;
			set;
		}

		public bool EnableMultipleSelect
		{
			get;
			set;
		}

		public bool IsCriteriaView
		{
			get;
			set;
		}

        public bool IsUsersForm
        {
            get;
            set;
        }

        public bool EnableUpdated
		{
			get;
			set;
		}

        private static DynamicLoadComboBox giftIDsManyPickLists;
        public bool Enabled
        {
            get
            {
                return manyPickListsDropDown.Enabled;
            }
            set
            {
                manyPickListsDropDown.Enabled = value;
            }
        }

		private List<int> OldToIDs
		{
			get
			{
				return ViewState["OldToIDs"] as List<int>;
			}

			set
			{
				ViewState["OldToIDs"] = value;
			}
		}

		// TODO: Remove this as it is only for an issue with update panel not working on the
		//  Manage Users Details page.
		public bool EnableListViewState
		{
			get;
			set;
		}

        public void ClearDropGift()
        {
            giftIDsManyPickLists.SelectedValue = null;
            idsTextBox.Text = "";
        }
        public void ClearRepeaterGift()
        {
            editRepeater.DataSource = null;
            editRepeater.DataBind();
            editManyPickListsUnorderedList.Controls.Clear();
        }

		public void Load_PreselectedData(List<int> toIDs, IIDNamesCache idNamesCache)
		{
			editRepeater.DataSource = BuildIDNames(toIDs, idNamesCache);
			editRepeater.DataBind();

			if (editRepeater.Items.Count > 0)
			{
				editManyPickListsUnorderedList.RemoveCssClass("none");
			}

			if (!FeaturesCache.HasModify(PropertyKey))
			{
				foreach (RepeaterItem item in editRepeater.Items)
				{
					HyperLink link = item.FindControl("linkRemove") as HyperLink;
					link.CssClass = "noClick";

				}
			}
		}
        public void BindData(List<int> toIDs, IIDNamesCache idNamesCache)
		{
			idsTextBox.Text = null;

			if (FeaturesCache.HasFeature(PropertyKey) || (!string.IsNullOrEmpty(OrPropertyKey) && FeaturesCache.HasFeature(OrPropertyKey)))
			{
				if (Mode == DBModes.Mode.Update)
				{
					if (!toIDs.IsNullOrEmpty())
					{
						idsTextBox.Text = new DelimitedCollection<int>(",", toIDs).ToString();

						editRepeater.DataSource = BuildIDNames(toIDs, idNamesCache);
						editRepeater.DataBind();

						if (editRepeater.Items.Count > 0)
						{
							editManyPickListsUnorderedList.RemoveCssClass("none");
						}
					}

					multiView.SetActiveView(editView);
				}
				else if (Mode == DBModes.Mode.Select)
				{
					if (!toIDs.IsNullOrEmpty())
					{
						itemRepeater.DataSource = BuildIDNames(toIDs, idNamesCache);
						itemRepeater.DataBind();

						if (itemRepeater.Items.Count > 0)
						{
							itemManyPickListsUnorderedList.RemoveCssClass("none");
						}
					}

					multiView.SetActiveView(itemView);
				}
			}

			if (EnableUpdated)
			{
				OldToIDs = toIDs;
			}
		}

		public void BindData(int id, ManyPickListItemsDataAccess manyPickListItemsDataAccess, IIDNamesCache idNamesCache)
		{
			List<int> toIDs = null;

			if (FeaturesCache.HasFeature(PropertyKey) || (!string.IsNullOrEmpty(OrPropertyKey) && FeaturesCache.HasFeature(OrPropertyKey)))
			{
				if (Mode == DBModes.Mode.Update)
				{
					toIDs = manyPickListItemsDataAccess.SelectToIDs(id);

					if (!toIDs.IsNullOrEmpty())
					{
						idsTextBox.Text = new DelimitedCollection<int>(",", toIDs).ToString();

						editRepeater.DataSource = BuildIDNames(toIDs, idNamesCache);
						editRepeater.DataBind();

						if (editRepeater.Items.Count > 0)
						{
							editManyPickListsUnorderedList.RemoveCssClass("none");
						}
						
						if (!FeaturesCache.HasModify(PropertyKey))
                        {	
							foreach(RepeaterItem item in editRepeater.Items)
                            {
								HyperLink link = item.FindControl("linkRemove") as HyperLink;
								link.CssClass = "noClick";

							}
						}
					}

					multiView.SetActiveView(editView);
				}
				else if (Mode == DBModes.Mode.Select)
				{
					toIDs = manyPickListItemsDataAccess.SelectToIDs(id);

					if (!toIDs.IsNullOrEmpty())
					{
						itemRepeater.DataSource = BuildIDNames(toIDs, idNamesCache);
						itemRepeater.DataBind();

						if (itemRepeater.Items.Count > 0)
						{
							itemManyPickListsUnorderedList.RemoveCssClass("none");
						}
					}

					multiView.SetActiveView(itemView);
				}
			}

			if (EnableUpdated)
			{
				OldToIDs = toIDs;
			}
		}

		public List<int> GetIDs()
		{
			return DelimitedCollection<int>.Parse(idsTextBox.Text, ",", StringSplitOptions.RemoveEmptyEntries, true);
		}

        public bool GetUpdated(List<int> toIDs)
		{
			bool isUpdated = false;
			List<int> oldToIDs = OldToIDs;

			if (!oldToIDs.IsNullOrEmpty())
			{
				if (!toIDs.IsNullOrEmpty())
				{
					if (oldToIDs.Count == toIDs.Count)
					{
						List<int> removedList = new List<int>(toIDs);
						int removeItem;
						foreach (int oldToID in oldToIDs)
						{
							// NOTE: This method does not work for comparison if the list contains a value of 0
							//  toIDs will never have a value of 0
							// INVESTIGATE: This could be updated to check the 0 case or default case first and then possibly made into an extension method

							removeItem = removedList.Find(x => x == oldToID);
							removedList.Remove(removeItem);
						}

						isUpdated = (removedList.Count > 0);
					}
					else
					{
						isUpdated = true;
					}
				}
				else
				{
					isUpdated = true;
				}
			}
			else if (!toIDs.IsNullOrEmpty())
			{
				isUpdated = true;
			}

			return isUpdated;
		}

		protected override void OnLoad(EventArgs e)
		{
			base.OnLoad(e);

			manyPickListsDropDown.WebServiceSettings.Method = PickListItemsWebServiceMethod;

			enableMultipleSelectLiteral.Text = EnableMultipleSelect.ToString();

            if (IsCriteriaView)
            {
                manyPickListsDropDown.EmptyMessage = "All (select or type to search)";
                manyPickListsDropDown.EmptyItemText = "All";
                manyPickListsDropDown.AppendedItemText = "-Exclude";
                manyPickListsDropDown.AppendedItemValue = "-3";
                manyPickListsDropDown.AppendedItem2Text = "-Unassigned";
                manyPickListsDropDown.AppendedItem2Value = "0";
            }
            else if (manyPickListsDropDown.ClientID.Contains("locationsManyPickLists_manyPickListsDropDown"))
            {
                manyPickListsDropDown.AppendedItem2Value = "0";
                manyPickListsDropDown.AppendedItem2Text = "-Unassigned";

            }
            else if (manyPickListsDropDown.ClientID.Contains("giftIDsManyPickLists"))
            {
                giftIDsManyPickLists = manyPickListsDropDown;
            }

            //if (IsCriteriaView)
            //{
            //	manyPickListsDropDown.EmptyMessage = "All (select or type to search)";
            //	manyPickListsDropDown.EmptyItemText = "All";
            //	manyPickListsDropDown.AppendedItemText = "-Exclude";
            //	manyPickListsDropDown.AppendedItemValue = "-3";
            //	manyPickListsDropDown.AppendedItem2Text = "-Unassigned";
            //	manyPickListsDropDown.AppendedItem2Value = "0";
            //}
            //else
            //{
            //	if(manyPickListsDropDown.WebServiceSettings.Method != "GetGifts")
            //	{
            //		manyPickListsDropDown.AppendedItem2Value = "0";
            //		manyPickListsDropDown.AppendedItem2Text = "-Unassigned";
            //	}

            //         }
            //         else if (manyPickListsDropDown.ClientID.Contains("giftIDsManyPickLists"))
            //         {
            //             giftIDsManyPickLists = manyPickListsDropDown;
            //         }



            // TODO: Remove this as it is only for an issue with update panel not working on the
            //  Manage Users Details page.
            if (EnableListViewState)
			{
				editRepeater.ViewStateMode = ViewStateMode.Enabled;
				itemRepeater.ViewStateMode = ViewStateMode.Enabled;
			}
		}

		protected override void OnPreRender(EventArgs e)
        {
			PageBase pageBase = Page as PageBase;

			if (pageBase == null)
			{
				throw new HttpException("This control requires the Page to be of type PageBase.");
			}

			if (ModifyKey != null)
			{
				if (!FeaturesCache.HasModify(ModifyKey))
				{
					Enabled = false;
				}
			}


			base.OnPreRender(e);
		}

		private List<Tuple<int, string>> BuildIDNames(List<int> toIDs, IIDNamesCache idNamesCache)
		{
			List<Tuple<int, string>> idNames = new List<Tuple<int, string>>();

			foreach (int toID in toIDs)
			{
				if (toID == excludeID)
				{
					idNames.Add(new Tuple<int, string>(toID, "-Exclude"));
				}
				else if (toID == unassignedID)
				{
					idNames.Add(new Tuple<int, string>(toID, "-Unassigned"));
				}
				else
				{
					idNames.Add(new Tuple<int, string>(toID, idNamesCache.GetName(toID)));
				}
			}

			idNames.Sort((priority1, priority2) => String.CompareOrdinal(priority1.Item2, priority2.Item2));

			return idNames;
		}
 

    }
}