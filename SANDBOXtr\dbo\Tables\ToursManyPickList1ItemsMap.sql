﻿CREATE TABLE [dbo].[ToursManyPickList1ItemsMap] (
    [toursManyPickList1ItemMapID] INT IDENTITY (1, 1) NOT NULL,
    [tourID]                      INT NOT NULL,
    [toursManyPickList1ItemID]    INT NOT NULL,
    CONSTRAINT [PK_ToursManyPickList1ItemsMap] PRIMARY KEY CLUSTERED ([toursManyPickList1ItemMapID] ASC),
    CONSTRAINT [FK_ToursManyPickList1ItemsMap_tourID] FOREIGN KEY ([tourID]) REFERENCES [dbo].[Tours] ([tourID]),
    CONSTRAINT [FK_ToursManyPickList1ItemsMap_toursManyPickList1ItemID] FOREIGN KEY ([toursManyPickList1ItemID]) REFERENCES [dbo].[ToursManyPickList1Items] ([toursManyPickList1ItemID])
);


GO
CREATE TRIGGER [dbo].[ToursManyPickList1ItemsMap.InsertUpdateDelete]
    ON [dbo].[ToursManyPickList1ItemsMap]
    FOR INSERT, UPDATE, DELETE
AS 
BEGIN
    SET NOCOUNT ON;

    DECLARE @Activity  NVARCHAR (8), @id INT;

    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'updated'
    END

    IF EXISTS (SELECT * FROM inserted) AND NOT EXISTS(SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'inserted'
    END

    IF EXISTS (SELECT * FROM deleted) AND NOT EXISTS(SELECT * FROM inserted)
    BEGIN
        SET @Activity = 'deleted'
    END

    

    IF (@Activity = 'deleted')
    BEGIN
        DECLARE cur CURSOR FOR SELECT toursManyPickList1ItemMapID FROM deleted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'ToursManyPickList1ItemsMap', @id, 'toursManyPickList1ItemMapID',
                (
                    SELECT *
                    FROM deleted i 
                    WHERE i.toursManyPickList1ItemMapID =  @id
                    FOR JSON AUTO
                ),0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
    ELSE
    BEGIN
        DECLARE cur CURSOR
        FOR SELECT toursManyPickList1ItemMapID
        FROM inserted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'ToursManyPickList1ItemsMap', @id, 'toursManyPickList1ItemMapID',
                (
                    SELECT *
                    FROM inserted i 
                    WHERE i.toursManyPickList1ItemMapID =  @id
                    FOR JSON AUTO
                ), 0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
END

GO
DISABLE TRIGGER [dbo].[ToursManyPickList1ItemsMap.InsertUpdateDelete]
    ON [dbo].[ToursManyPickList1ItemsMap];

