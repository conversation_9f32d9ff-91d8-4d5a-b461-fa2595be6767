<%@ Control Language="C#" CodeBehind="ToursDetailsTours.ascx.cs" Inherits="TrackResults.Web.Customers.Tours.UserControls.ToursDetailsTours" %>

<%@ Import Namespace="TrackResults.BES.Data.Business" %>
<%@ Import Namespace="TrackResults.BES.Data.Cache.IDNames" %>
<%@ Import Namespace="TrackResults.BES.Services" %>
<%@ Import Namespace="TrackResults.Common.Core.Extensions" %>
<%@ Import Namespace="TrackResults.Common.Web.Globalization" %>

<%@ Register Src="../../UserControls/NotesDetails.ascx" TagName="NotesDetails" TagPrefix="uc" %>
<%@ Register Namespace="TrackResults.Web.Customers.Tours.UserControls" Assembly="TrackResults.Web" TagPrefix="wc" %>

<asp:FormView ID="formView" runat="server" OnDataBound="formView_DataBound">
		
	<EditItemTemplate>

		<ha:ExtendedHiddenField ID="locationIDGlobal" runat="server" ClientIDMode="Static" Value='<%# ((Tour)Container.DataItem).LocationID %>' />

		<div class="form-horizontal">

			<trwc:FeaturePlaceHolder runat="server" FeatureKey="Tour.TourSourceID">
				<div class="form-group">
					<label class="col-xs-4 control-label">
						<asp:Label ID="sourceIDLabel" runat="server" EnableRenderID="true">
							<trwc:FeaturePlaceHolder runat="server" FeatureKey="Tour.TourSourceID.Required">
								<ha:TargetedCssRequiredFieldValidator runat="server" ControlToValidate="dropDownTourSources" TargetControlID="sourceIDLabel" />
								<span class="is-required">*</span>
							</trwc:FeaturePlaceHolder>
							<asp:Literal runat="server" Text="<%$ Resources: resources,Tour.TourSourceID %>" />
						</asp:Label>
					</label>
					<div class="col-xs-8">
						<trwc:DynamicLoadComboBox ID="dropDownTourSources" runat="server" SkinID="update" ModifyKey="Tour.TourSourceID">
							<WebServiceSettings Path="~/WebServices/PickListItemsWebService.asmx" Method="GetTourSources" />
						</trwc:DynamicLoadComboBox>
					</div>
				</div>
			</trwc:FeaturePlaceHolder>

			<trwc:FeaturePlaceHolder runat="server" FeatureKey="Tour.TourTypeID">
				<div class="form-group">
					<label class="col-xs-4 control-label">
						<asp:Label ID="tourTypeIDLabel" runat="server" EnableRenderID="true">
							<trwc:FeaturePlaceHolder runat="server" FeatureKey="Tour.TourTypeID.Required">
								<ha:TargetedCssRequiredFieldValidator runat="server" ControlToValidate="dropDownTourTypes" TargetControlID="tourTypeIDLabel" />
								<span class="is-required">*</span>
							</trwc:FeaturePlaceHolder>
							<asp:Literal runat="server" Text="<%$ Resources: resources,Tour.TourTypeID %>" />
						</asp:Label>
					</label>
					<div class="col-xs-8">
						<trwc:DynamicLoadComboBox ID="dropDownTourTypes" runat="server" SkinID="update" ModifyKey="Tour.TourTypeID">
							<WebServiceSettings Path="~/WebServices/PickListItemsWebService.asmx" Method="GetTourTypes" />
						</trwc:DynamicLoadComboBox>
					</div>
				</div>
			</trwc:FeaturePlaceHolder>
					
			<trwc:FeaturePlaceHolder runat="server" FeatureKey="Tour.TourStatusTypeID">
				<div class="form-group">
					<label class="col-xs-4 control-label">
						<asp:Label ID="tourStatusTypeIDLabel" runat="server" EnableRenderID="true">
							<trwc:FeaturePlaceHolder runat="server" FeatureKey="Tour.TourStatusTypeID.Required">
								<ha:TargetedCssRequiredFieldValidator runat="server" ControlToValidate="tourStatusTypesDropDown" TargetControlID="tourStatusTypeIDLabel" />
								<span class="is-required">*</span>
							</trwc:FeaturePlaceHolder>
							<asp:Literal runat="server" Text="<%$ Resources: resources,Tour.TourStatusTypeID %>" />
						</asp:Label>
					</label>
					<div class="col-xs-8">
						<asp:DropDownList ID="tourStatusTypesDropDown" runat="server" DataTextField="Value" DataValueField="Key"
							ModifyKey="Tour.TourStatusTypeID" />
					</div>
				</div>
			</trwc:FeaturePlaceHolder>
					
			<trwc:FeaturePlaceHolder runat="server" FeatureKey="Tour.TourConcernTypeID">
				<div class="form-group">
					<label class="col-xs-4 control-label">
						<asp:Label ID="tourConcernTypeIDLabel" runat="server" EnableRenderID="true">
							<trwc:FeaturePlaceHolder runat="server" FeatureKey="Tour.TourConcernTypeID.Required">
								<ha:TargetedCssRequiredFieldValidator runat="server" ControlToValidate="dropDownTourConcernTypes" TargetControlID="tourConcernTypeIDLabel" />
								<span class="is-required">*</span>
							</trwc:FeaturePlaceHolder>
							<asp:Literal runat="server" Text="<%$ Resources: resources,Tour.TourConcernTypeID %>" />
						</asp:Label>
					</label>
					<div class="col-xs-8">
						<trwc:DynamicLoadComboBox ID="dropDownTourConcernTypes" runat="server" SkinID="update" ModifyKey="Tour.TourConcernTypeID">
							<WebServiceSettings Path="~/WebServices/PickListItemsWebService.asmx" Method="GetTourConcernTypes" />
						</trwc:DynamicLoadComboBox>
					</div>
				</div>
			</trwc:FeaturePlaceHolder>

			<trwc:FeaturePlaceHolder runat="server" FeatureKey="Tour.TourTypePickList1ItemID">
				<div class="form-group">
					<label class="col-xs-4 control-label">
						<asp:Label ID="tourTypePickList1ItemIDLabel" runat="server" EnableRenderID="true">
							<trwc:FeaturePlaceHolder runat="server" FeatureKey="Tour.TourTypePickList1ItemID.Required">
								<ha:TargetedCssRequiredFieldValidator runat="server" ControlToValidate="dropDownTourTypePickList1Items" TargetControlID="tourTypePickList1ItemIDLabel" />
								<span class="is-required">*</span>
							</trwc:FeaturePlaceHolder>
							<asp:Literal runat="server" Text="<%$ Resources: resources,Tour.TourTypePickList1ItemID %>" />
						</asp:Label>
					</label>
					<div class="col-xs-8">
						<trwc:DynamicLoadComboBox ID="dropDownTourTypePickList1Items" runat="server" SkinID="update" ModifyKey="Tour.TourTypePickList1ItemID">
							<WebServiceSettings Path="~/WebServices/PickListItemsWebService.asmx" Method="GetTourTypePickList1Items" />
						</trwc:DynamicLoadComboBox>
					</div>
				</div>
			</trwc:FeaturePlaceHolder>
					
			<trwc:FeaturePlaceHolder runat="server" FeatureKey="Tour.LocationID">
				<div class="form-group">
					<label class="col-xs-4 control-label">
						<asp:Label ID="locationIDLabel" runat="server" EnableRenderID="true">
							<trwc:FeaturePlaceHolder runat="server" FeatureKey="Tour.LocationID.Required">
								<ha:TargetedCssRequiredFieldValidator runat="server" ControlToValidate="dropDownLocations" TargetControlID="locationIDLabel" />
								<span class="is-required">*</span>
							</trwc:FeaturePlaceHolder>
							<asp:Literal runat="server" Text="<%$ Resources: resources,Tour.LocationID %>" />
						</asp:Label>
					</label>
					<div class="col-xs-8">
						<trwc:DynamicLoadComboBox ID="dropDownLocations" runat="server" SkinID="update" AutoPostBack="true"
							CausesValidation="false" OnSelectedIndexChanged="dropDownLocations_SelectedIndexChanged" ModifyKey="Tour.LocationID">
							<WebServiceSettings Path="~/WebServices/PickListItemsWebService.asmx" Method="GetLocations" />
						</trwc:DynamicLoadComboBox>
					</div>
				</div>
			</trwc:FeaturePlaceHolder>
					
			<trwc:FeaturePlaceHolder runat="server" FeatureKey="Tour.RegionID">
				<div class="form-group">
					<label class="col-xs-4 control-label">
						<asp:Label ID="regionIDLabel" runat="server" EnableRenderID="true">
							<trwc:FeaturePlaceHolder runat="server" FeatureKey="Tour.RegionID.Required">
								<ha:TargetedCssRequiredFieldValidator runat="server" ControlToValidate="dropDownRegions" TargetControlID="regionIDLabel" />
								<span class="is-required">*</span>
							</trwc:FeaturePlaceHolder>
							<asp:Literal runat="server" Text="<%$ Resources: resources,Tour.RegionID %>" />
						</asp:Label>
					</label>
					<div class="col-xs-8">
						<trwc:DynamicLoadComboBox ID="dropDownRegions" runat="server" SkinID="update" ModifyKey="Tour.RegionID">
							<WebServiceSettings Path="~/WebServices/PickListItemsWebService.asmx" Method="GetRegions" />
						</trwc:DynamicLoadComboBox>
					</div>
				</div>
			</trwc:FeaturePlaceHolder>

			<trwc:FeaturePlaceHolder runat="server" FeatureKey="Tour.TourDate">
				<div class="form-group">
					<label class="col-xs-4 control-label">
						<asp:Label ID="tourDateLabel" runat="server" EnableRenderID="true">
							<trwc:FeaturePlaceHolder runat="server" FeatureKey="Tour.TourDate.Required">
								<ha:TargetedCssRequiredFieldValidator runat="server" ControlToValidate="tourDateRadDatePicker" TargetControlID="tourDateLabel" />
								<span class="is-required">*</span>
							</trwc:FeaturePlaceHolder>
							<asp:Literal runat="server" Text="<%$ Resources: resources,Tour.TourDate %>" />
						</asp:Label>
					</label>
					<div class="col-xs-8">
						<trwc:ExtendedRadDatePicker ID="tourDateRadDatePicker" runat="server" EnableTyping="false" Visible="false" />
						<wc:CalendarTourDatesExtender ID="calendarTourDatesExtender" runat="server" TargetControlID="tourDateRadDatePicker" />
						<trwc:FeaturePlaceHolder runat="server" FeatureKey="Validation.RequireTourDateForBookedConfirmed">
							<ha:TargetedCssCustomValidator runat="server" Text="<div>Required</div>" TargetControlID="tourDateLabel" OnServerValidate="RequireTourDateForBookedConfirmed_ServerValidate" />
						</trwc:FeaturePlaceHolder>
					</div>
				</div>
			</trwc:FeaturePlaceHolder>

			<trwc:FeaturePlaceHolder runat="server" FeatureKey="Tour.TourTime">
				<div class="form-group">
					<label class="col-xs-4 control-label">
						<asp:Label ID="tourTimeLabel" runat="server" EnableRenderID="true">
							<trwc:FeaturePlaceHolder runat="server" FeatureKey="Tour.TourTime.Required">
								<ha:TargetedCssRequiredFieldValidator runat="server" ControlToValidate="dropDownTime" TargetControlID="tourTimeLabel" />
								<span class="is-required">*</span>
							</trwc:FeaturePlaceHolder>
							<asp:Literal runat="server" Text="<%$ Resources: resources,Tour.TourTime %>" />
						</asp:Label>
					</label>
					<div class="col-xs-8">
						<asp:DropDownList ID="dropDownTime" AppendDataBoundItems="true" runat="server" Enabled="false" Visible="false" ModifyKey="Tour.TourTime">
							<asp:ListItem Text="( Select )" Value="" Selected="true" />
						</asp:DropDownList>
						<wc:DropDownListTourTimesExtender ID="dropDownListTourTimesExtender" runat="server" TargetControlID="dropDownTime" SourceControlID="tourDateRadDatePicker" />
						<trwc:DropDownListPostViewStateExtender ID="dropDownListPostViewStateExtender" runat="server" TargetControlID="dropDownTime" SourceControlID="dropDownListTourTimesExtender" />
					</div>
				</div>
			</trwc:FeaturePlaceHolder>

			<trwc:FeaturePlaceHolder runat="server" FeatureKey="Tour.EntryDateTime">
				<div class="form-group">
					<label class="col-xs-4 control-label">
						<asp:Literal runat="server" Text="<%$ Resources: resources,Tour.EntryDateTime %>" />
					</label>
					<div class="col-xs-8">
						<asp:Literal runat="server" Text='<%# ((Tour)Container.DataItem).EntryDateTime.ToNullableStandardDateTimeString() %>' />
					</div>
				</div>
			</trwc:FeaturePlaceHolder>

			<trwc:FeaturePlaceHolder runat="server" FeatureKey="Tour.TourText1">
				<div class="form-group">
					<label class="col-xs-4 control-label">
						<asp:Label ID="tourText1Label" runat="server" EnableRenderID="true">
							<trwc:FeaturePlaceHolder runat="server" FeatureKey="Tour.TourText1.Required">
								<ha:TargetedCssRequiredFieldValidator runat="server" ControlToValidate="textBoxTourText1" TargetControlID="tourText1Label" />
								<span class="is-required">*</span>
							</trwc:FeaturePlaceHolder>
							<asp:Literal runat="server" Text="<%$ Resources: resources,Tour.TourText1 %>" />
						</asp:Label>
					</label>
					<div class="col-xs-8">
						<asp:TextBox ID="textBoxTourText1" runat="server" Text='<%# ((Tour)Container.DataItem).TourText1 %>' MaxLength="128" ModifyKey="Tour.TourText1" />
					</div>
				</div>
			</trwc:FeaturePlaceHolder>          

			<trwc:FeaturePlaceHolder runat="server" FeatureKey="Tour.TourDecimal1">
				<div class="form-group">
					<label class="col-xs-4 control-label">                        
						<asp:Label ID="tourDecimal1Label" runat="server" EnableRenderID="true">
							<trwc:FeaturePlaceHolder runat="server" FeatureKey="Tour.TourDecimal1.Required">
								<ha:TargetedCssRequiredFieldValidator runat="server" ControlToValidate="textBoxTourDecimal1" TargetControlID="tourDecimal1Label" />
								<span class="is-required">*</span>
							</trwc:FeaturePlaceHolder>
							<asp:Literal runat="server" Text="<%$ Resources: resources,Tour.TourDecimal1 %>" />
						</asp:Label>
					</label>
					<div class="col-xs-8">
						<asp:TextBox ID="textBoxTourDecimal1" runat="server" Text='<%# ((Tour)Container.DataItem).TourDecimal1.ToFeatureFormatString("Tour.TourDecimal1") %>' MaxLength="16" ModifyKey="Tour.TourDecimal1" />
						<trwc:DecimalTargetedCssRegularExpressionValidator runat="server" ControlToValidate="textBoxTourDecimal1" TargetControlID="tourDecimal1Label" />
					</div>
				</div>
			</trwc:FeaturePlaceHolder>

			<trwc:FeaturePlaceHolder runat="server" FeatureKey="Tour.TourBool1">
				<div class="form-group">
					<label class="col-xs-4 control-label">
						<asp:Label ID="tourBool1Label" runat="server" EnableRenderID="true">
							<trwc:FeaturePlaceHolder runat="server" FeatureKey="Tour.TourBool1.Required">
								<ha:TargetedCssRequiredFieldValidator runat="server" ControlToValidate="dropDownTourBool1" TargetControlID="tourBool1Label" />
								<span class="is-required">*</span>
							</trwc:FeaturePlaceHolder>
							<asp:Literal runat="server" Text="<%$ Resources: resources,Tour.TourBool1 %>" />
						</asp:Label>
					</label>
					<div class="col-xs-8">
						<trwc:ResourcesBooleanDropDownList ID="dropDownTourBool1" runat="server" ResourcesKey="Tour.TourBool1" SelectedValue='<%# ((Tour)Container.DataItem).TourBool1 %>' ModifyKey="Tour.TourBool1" />
					</div>
				</div>
			</trwc:FeaturePlaceHolder>

			<trwc:FeaturePlaceHolder runat="server" FeatureKey="Tour.ToursManyPickList1ItemIDs">
				<div class="form-group">
					<label class="col-xs-4 control-label">
							<asp:Literal runat="server" Text="<%$ Resources: resources,Tour.ToursManyPickList1ItemIDs.Plural %>" />
					</label>
					<div class="col-xs-8">
						<truc:ManyPickLists ID="toursManyPickList1ItemIDsManyPickLists" runat="server" Mode="Update" PropertyKey="Tour.ToursManyPickList1ItemIDs"
							 EnableUpdated="true" PickListItemsWebServiceMethod="GetToursManyPickList1Items" EnableMultipleSelect="true" ModifyKey="Tour.ToursManyPickList1ItemIDs" />
					</div>
				</div>
			</trwc:FeaturePlaceHolder>

		</div>

	</EditItemTemplate>

		
	<InsertItemTemplate>

		<ha:ExtendedHiddenField ID="locationIDGlobal" runat="server" ClientIDMode="Static" />

		<div class="form-horizontal">
					
			<trwc:FeaturePlaceHolder runat="server" FeatureKey="Tour.TourSourceID">
				<div class="form-group">
					<label class="col-xs-4 control-label">
						<asp:Label ID="sourceIDLabel" runat="server" EnableRenderID="true">
							<trwc:FeaturePlaceHolder runat="server" FeatureKey="Tour.TourSourceID.Required">
								<ha:TargetedCssRequiredFieldValidator runat="server" ControlToValidate="dropDownTourSources" TargetControlID="sourceIDLabel" />
								<span class="is-required">*</span>
							</trwc:FeaturePlaceHolder>
							<asp:Literal runat="server" Text="<%$ Resources: resources,Tour.TourSourceID %>" />
						</asp:Label>
					</label>
					<div class="col-xs-8">
						<trwc:DynamicLoadComboBox ID="dropDownTourSources" runat="server" SkinID="update">
							<WebServiceSettings Path="~/WebServices/PickListItemsWebService.asmx" Method="GetTourSources" />
						</trwc:DynamicLoadComboBox>
					</div>
				</div>
			</trwc:FeaturePlaceHolder>
					
			<trwc:FeaturePlaceHolder runat="server" FeatureKey="Tour.TourTypeID">
				<div class="form-group">
					<label class="col-xs-4 control-label">
						<asp:Label ID="tourTypeIDLabel" runat="server" EnableRenderID="true">
							<trwc:FeaturePlaceHolder runat="server" FeatureKey="Tour.TourTypeID.Required">
								<ha:TargetedCssRequiredFieldValidator runat="server" ControlToValidate="dropDownTourTypes" TargetControlID="tourTypeIDLabel" />
								<span class="is-required">*</span>
							</trwc:FeaturePlaceHolder>
							<asp:Literal runat="server" Text="<%$ Resources: resources,Tour.TourTypeID %>" />
						</asp:Label>
					</label>
					<div class="col-xs-8">
						<trwc:DynamicLoadComboBox ID="dropDownTourTypes" runat="server" SkinID="update">
							<WebServiceSettings Path="~/WebServices/PickListItemsWebService.asmx" Method="GetTourTypes" />
						</trwc:DynamicLoadComboBox>
					</div>
				</div>
			</trwc:FeaturePlaceHolder>
					
			<trwc:FeaturePlaceHolder runat="server" FeatureKey="Tour.TourStatusTypeID">
				<div class="form-group">
					<label class="col-xs-4 control-label">
						<asp:Label ID="tourStatusTypeIDLabel" runat="server" EnableRenderID="true">
							<trwc:FeaturePlaceHolder runat="server" FeatureKey="Tour.TourStatusTypeID.Required">
								<ha:TargetedCssRequiredFieldValidator runat="server" ControlToValidate="tourStatusTypesDropDown" TargetControlID="tourStatusTypeIDLabel" />
								<span class="is-required">*</span>
							</trwc:FeaturePlaceHolder>
							<asp:Literal runat="server" Text="<%$ Resources: resources,Tour.TourStatusTypeID %>" />
						</asp:Label>
					</label>
					<div class="col-xs-8">
						<asp:DropDownList ID="tourStatusTypesDropDown" runat="server" DataTextField="Value" DataValueField="Key"
							ModifyKey="Tour.TourStatusTypeID" />
					</div>
				</div>
			</trwc:FeaturePlaceHolder>
					
			<trwc:FeaturePlaceHolder runat="server" FeatureKey="Tour.TourConcernTypeID">
				<div class="form-group">
					<label class="col-xs-4 control-label">
						<asp:Label ID="tourConcernTypeIDLabel" runat="server" EnableRenderID="true">
							<trwc:FeaturePlaceHolder runat="server" FeatureKey="Tour.TourConcernTypeID.Required">
								<ha:TargetedCssRequiredFieldValidator runat="server" ControlToValidate="dropDownTourConcernTypes" TargetControlID="tourConcernTypeIDLabel" />
								<span class="is-required">*</span>
							</trwc:FeaturePlaceHolder>
							<asp:Literal runat="server" Text="<%$ Resources: resources,Tour.TourConcernTypeID %>" />
						</asp:Label>
					</label>
					<div class="col-xs-8">
						<trwc:DynamicLoadComboBox ID="dropDownTourConcernTypes" runat="server" SkinID="update">
							<WebServiceSettings Path="~/WebServices/PickListItemsWebService.asmx" Method="GetTourConcernTypes" />
						</trwc:DynamicLoadComboBox>
					</div>
				</div>
			</trwc:FeaturePlaceHolder>
					
			<trwc:FeaturePlaceHolder runat="server" FeatureKey="Tour.TourTypePickList1ItemID">
				<div class="form-group">
					<label class="col-xs-4 control-label">
						<asp:Label ID="tourTypePickList1ItemIDLabel" runat="server" EnableRenderID="true">
							<trwc:FeaturePlaceHolder runat="server" FeatureKey="Tour.TourTypePickList1ItemID.Required">
								<ha:TargetedCssRequiredFieldValidator runat="server" ControlToValidate="dropDownTourTypePickList1Items" TargetControlID="tourTypePickList1ItemIDLabel" />
								<span class="is-required">*</span>
							</trwc:FeaturePlaceHolder>
							<asp:Literal runat="server" Text="<%$ Resources: resources,Tour.TourTypePickList1ItemID %>" />
						</asp:Label>
					</label>
					<div class="col-xs-8">
						<trwc:DynamicLoadComboBox ID="dropDownTourTypePickList1Items" runat="server" SkinID="update">
							<WebServiceSettings Path="~/WebServices/PickListItemsWebService.asmx" Method="GetTourTypePickList1Items" />
						</trwc:DynamicLoadComboBox>
					</div>
				</div>
			</trwc:FeaturePlaceHolder>
					
			<trwc:FeaturePlaceHolder runat="server" FeatureKey="Tour.LocationID">
				<div class="form-group">
					<label class="col-xs-4 control-label">
						<asp:Label ID="locationIDLabel" runat="server" EnableRenderID="true">
							<trwc:FeaturePlaceHolder runat="server" FeatureKey="Tour.LocationID.Required">
								<ha:TargetedCssRequiredFieldValidator runat="server" ControlToValidate="dropDownLocations" TargetControlID="locationIDLabel" />
								<span class="is-required">*</span>
							</trwc:FeaturePlaceHolder>
							<asp:Literal runat="server" Text="<%$ Resources: resources,Tour.LocationID %>" />
						</asp:Label>
					</label>
					<div class="col-xs-8">
						<trwc:DynamicLoadComboBox ID="dropDownLocations" runat="server" SkinID="update" AutoPostBack="true"
							CausesValidation="false" OnSelectedIndexChanged="dropDownLocations_SelectedIndexChanged">
							<WebServiceSettings Path="~/WebServices/PickListItemsWebService.asmx" Method="GetLocations" />
						</trwc:DynamicLoadComboBox>
					</div>
				</div>
			</trwc:FeaturePlaceHolder>
					
			<trwc:FeaturePlaceHolder runat="server" FeatureKey="Tour.RegionID">
				<div class="form-group">
					<label class="col-xs-4 control-label">
						<asp:Label ID="regionIDLabel" runat="server" EnableRenderID="true">
							<trwc:FeaturePlaceHolder runat="server" FeatureKey="Tour.RegionID.Required">
								<ha:TargetedCssRequiredFieldValidator runat="server" ControlToValidate="dropDownRegions" TargetControlID="regionIDLabel" />
								<span class="is-required">*</span>
							</trwc:FeaturePlaceHolder>
							<asp:Literal runat="server" Text="<%$ Resources: resources,Tour.RegionID %>" />
						</asp:Label>
					</label>
					<div class="col-xs-8">
						<trwc:DynamicLoadComboBox ID="dropDownRegions" runat="server" SkinID="update">
							<WebServiceSettings Path="~/WebServices/PickListItemsWebService.asmx" Method="GetRegions" />
						</trwc:DynamicLoadComboBox>
					</div>
				</div>
			</trwc:FeaturePlaceHolder>

			<trwc:FeaturePlaceHolder runat="server" FeatureKey="Tour.TourDate">
				<div class="form-group">
					<label class="col-xs-4 control-label">
						<asp:Label ID="tourDateLabel" runat="server" EnableRenderID="true">
							<trwc:FeaturePlaceHolder runat="server" FeatureKey="Tour.TourDate.Required">
								<ha:TargetedCssRequiredFieldValidator runat="server" ControlToValidate="tourDateRadDatePicker" TargetControlID="tourDateLabel" />
								<span class="is-required">*</span>
							</trwc:FeaturePlaceHolder>
							<asp:Literal runat="server" Text="<%$ Resources: resources,Tour.TourDate %>" />
						</asp:Label>
					</label>
					<div class="col-xs-8">
						<trwc:ExtendedRadDatePicker ID="tourDateRadDatePicker" runat="server" EnableTyping="true" Visible="false" />
						<wc:CalendarTourDatesExtender ID="calendarTourDatesExtender" runat="server" TargetControlID="tourDateRadDatePicker" />
						<trwc:FeaturePlaceHolder runat="server" FeatureKey="Validation.RequireTourDateForBookedConfirmed">
							<ha:TargetedCssCustomValidator runat="server" Text="<div>Required</div>" TargetControlID="tourDateLabel" OnServerValidate="RequireTourDateForBookedConfirmed_ServerValidate" />
						</trwc:FeaturePlaceHolder>
					</div>
				</div>
			</trwc:FeaturePlaceHolder>

			<trwc:FeaturePlaceHolder runat="server" FeatureKey="Tour.TourTime">
				<div class="form-group">
					<label class="col-xs-4 control-label">
						<asp:Label ID="tourTimeLabel" runat="server" EnableRenderID="true">
							<trwc:FeaturePlaceHolder runat="server" FeatureKey="Tour.TourTime.Required">
								<ha:TargetedCssRequiredFieldValidator runat="server" ControlToValidate="dropDownTime" TargetControlID="tourTimeLabel" />
								<span class="is-required">*</span>
							</trwc:FeaturePlaceHolder>
							<asp:Literal runat="server" Text="<%$ Resources: resources,Tour.TourTime %>" />
						</asp:Label>
					</label>
					<div class="col-xs-8">
						<asp:DropDownList ID="dropDownTime" AppendDataBoundItems="true" runat="server" Enabled="false" Visible="false">
							<asp:ListItem Text="( Select )" Value="" Selected="true" />
						</asp:DropDownList>
						<wc:DropDownListTourTimesExtender ID="dropDownListTourTimesExtender" runat="server" TargetControlID="dropDownTime" SourceControlID="tourDateRadDatePicker" />
						<trwc:DropDownListPostViewStateExtender ID="dropDownListPostViewStateExtender" runat="server" TargetControlID="dropDownTime" SourceControlID="dropDownListTourTimesExtender" />
					</div>
				</div>
			</trwc:FeaturePlaceHolder>

			<trwc:FeaturePlaceHolder runat="server" FeatureKey="Tour.TourText1">
				<div class="form-group">
					<label class="col-xs-4 control-label">
						<asp:Label ID="tourText1Label" runat="server" EnableRenderID="true">
							<trwc:FeaturePlaceHolder runat="server" FeatureKey="Tour.TourText1.Required">
								<ha:TargetedCssRequiredFieldValidator runat="server" ControlToValidate="textBoxTourText1" TargetControlID="tourText1Label" />
								<span class="is-required">*</span>
							</trwc:FeaturePlaceHolder>
							<asp:Literal runat="server" Text="<%$ Resources: resources,Tour.TourText1 %>" />
						</asp:Label>
					</label>
					<div class="col-xs-8">
						<asp:TextBox ID="textBoxTourText1" runat="server" MaxLength="128" />
					</div>
				</div>
			</trwc:FeaturePlaceHolder>                      

			<trwc:FeaturePlaceHolder runat="server" FeatureKey="Tour.TourDecimal1">
				<div class="form-group">
					<label class="col-xs-4 control-label">                        
						<asp:Label ID="tourDecimal1Label" runat="server" EnableRenderID="true">
							<trwc:FeaturePlaceHolder runat="server" FeatureKey="Tour.TourDecimal1.Required">
								<ha:TargetedCssRequiredFieldValidator runat="server" ControlToValidate="textBoxTourDecimal1" TargetControlID="tourDecimal1Label" />
								<span class="is-required">*</span>
							</trwc:FeaturePlaceHolder>
							<asp:Literal runat="server" Text="<%$ Resources: resources,Tour.TourDecimal1 %>" />
						</asp:Label>
					</label>
					<div class="col-xs-8">
						<asp:TextBox ID="textBoxTourDecimal1" runat="server" MaxLength="16" />
						<trwc:DecimalTargetedCssRegularExpressionValidator runat="server" ControlToValidate="textBoxTourDecimal1" TargetControlID="tourDecimal1Label" />
					</div>
				</div>
			</trwc:FeaturePlaceHolder>

			<trwc:FeaturePlaceHolder runat="server" FeatureKey="Tour.TourBool1">
				<div class="form-group">
					<label class="col-xs-4 control-label">
						<asp:Label ID="tourBool1Label" runat="server" EnableRenderID="true">
							<trwc:FeaturePlaceHolder runat="server" FeatureKey="Tour.TourBool1.Required">
								<ha:TargetedCssRequiredFieldValidator runat="server" ControlToValidate="dropDownTourBool1" TargetControlID="tourBool1Label" />
								<span class="is-required">*</span>
							</trwc:FeaturePlaceHolder>
							<asp:Literal runat="server" Text="<%$ Resources: resources,Tour.TourBool1 %>" />
						</asp:Label>
					</label>
					<div class="col-xs-8">
						<trwc:ResourcesBooleanDropDownList ID="dropDownTourBool1" runat="server" ResourcesKey="Tour.TourBool1" />
					</div>
				</div>
			</trwc:FeaturePlaceHolder>

			<trwc:FeaturePlaceHolder runat="server" FeatureKey="Tour.ToursManyPickList1ItemIDs">
				<div class="form-group">
					<label class="col-xs-4 control-label">
						<asp:Literal runat="server" Text="<%$ Resources: resources,Tour.ToursManyPickList1ItemIDs.Plural %>" />
					</label>
					<div class="col-xs-8">
						<truc:ManyPickLists ID="toursManyPickList1ItemIDsManyPickLists" runat="server" Mode="Update" PropertyKey="Tour.ToursManyPickList1ItemIDs"
							PickListItemsWebServiceMethod="GetToursManyPickList1Items" EnableMultipleSelect="true" />
					</div>
				</div>
			</trwc:FeaturePlaceHolder>

		</div>

	</InsertItemTemplate>


	<ItemTemplate>

		<ha:ExtendedHiddenField ID="locationIDGlobal" runat="server" ClientIDMode="Static" Value='<%# ((Tour)Container.DataItem).LocationID %>' />

		<div class="form-horizontal">

			<trwc:FeaturePlaceHolder runat="server" FeatureKey="Tour.TourSourceID">
				<div class="form-group">
					<label class="col-xs-4 control-label">
						<asp:Literal runat="server" Text="<%$ Resources: resources,Tour.TourSourceID %>" />
					</label>
					<div class="col-xs-8 control-literal">
						<asp:Literal runat="server" Text='<%# TourSourceIDNamesCache.Instance.GetName(((Tour)Container.DataItem).TourSourceID) %>' />
					</div>
				</div>
			</trwc:FeaturePlaceHolder>

			<trwc:FeaturePlaceHolder runat="server" FeatureKey="Tour.TourTypeID">
				<div class="form-group">
					<label class="col-xs-4 control-label">
						<asp:Literal runat="server" Text="<%$ Resources: resources,Tour.TourTypeID %>" />
					</label>
					<div class="col-xs-8 control-literal">
						<asp:Literal runat="server" Text='<%# TourTypeIDNamesCache.Instance.GetName(((Tour)Container.DataItem).TourTypeID) %>' />
					</div>
				</div>
			</trwc:FeaturePlaceHolder>

			<trwc:FeaturePlaceHolder runat="server" FeatureKey="Tour.TourStatusTypeID">
				<div class="form-group">
					<label class="col-xs-4 control-label">
						<asp:Literal runat="server" Text="<%$ Resources: resources,Tour.TourStatusTypeID %>" />
					</label>
					<div class="col-xs-8 control-literal">
						<asp:Literal runat="server" Text='<%# TourStatusTypeIDNamesCache.Instance.GetName(((Tour)Container.DataItem).TourStatusTypeID) %>' />
					</div>
				</div>
			</trwc:FeaturePlaceHolder>

			
<%-- //TODO28							Was Confirmed:
							<asp:CheckBox ID="checkBoxWasConfirmed" runat="server" Checked='<%# ((Tour)Container.DataItem).WasConfirmed %>' Enabled="false" Style="vertical-align: middle;" />
						</div>
						<div class="break">
						</div>
					</div>
				</td>
			</tr>
			<div class="form-group">
				<label class="col-xs-4 control-label">
					Tour Disposition
				</td>
			</tr>--%>


			<trwc:FeaturePlaceHolder runat="server" FeatureKey="Tour.TourConcernTypeID">
				<div class="form-group">
					<label class="col-xs-4 control-label">
						<asp:Literal runat="server" Text="<%$ Resources: resources,Tour.TourConcernTypeID %>" />
					</label>
					<div class="col-xs-8 control-literal">
						<asp:Literal runat="server" Text='<%# TourConcernTypeIDNamesCache.Instance.GetName(((Tour)Container.DataItem).TourConcernTypeID) %>' />
					</div>
				</div>
			</trwc:FeaturePlaceHolder>

			<trwc:FeaturePlaceHolder runat="server" FeatureKey="Tour.TourTypePickList1ItemID">
				<div class="form-group">
					<label class="col-xs-4 control-label">
						<asp:Literal runat="server" Text="<%$ Resources: resources,Tour.TourTypePickList1ItemID %>" />
					</label>
					<div class="col-xs-8 control-literal">
						<asp:Literal runat="server" Text='<%# TourTypePickList1ItemIDNamesCache.Instance.GetName(((Tour)Container.DataItem).TourTypePickList1ItemID) %>' />
					</div>
				</div>
			</trwc:FeaturePlaceHolder>

			<trwc:FeaturePlaceHolder runat="server" FeatureKey="Tour.LocationID">
				<div class="form-group">
					<label class="col-xs-4 control-label">
						<asp:Literal runat="server" Text="<%$ Resources: resources,Tour.LocationID %>" />
					</label>
					<div class="col-xs-8 control-literal">
						<asp:Literal runat="server" Text='<%# LocationIDNamesCache.Instance.GetName(((Tour)Container.DataItem).LocationID) %>' />
					</div>
				</div>
			</trwc:FeaturePlaceHolder>

			<trwc:FeaturePlaceHolder runat="server" FeatureKey="Tour.RegionID">
				<div class="form-group">
					<label class="col-xs-4 control-label">
						<asp:Literal runat="server" Text="<%$ Resources: resources,Tour.RegionID %>" />
					</label>
					<div class="col-xs-8 control-literal">
						<asp:Literal runat="server" Text='<%# RegionIDNamesCache.Instance.GetName(((Tour)Container.DataItem).RegionID) %>' />
					</div>
				</div>
			</trwc:FeaturePlaceHolder>

			<trwc:FeaturePlaceHolder runat="server" FeatureKey="Tour.TourDate">
				<div class="form-group">
					<label class="col-xs-4 control-label">
						<asp:Literal runat="server" Text="<%$ Resources: resources,Tour.TourDate %>" />
					</label>
					<div class="col-xs-8 control-literal">
						<asp:Literal runat="server" Text='<%# ((Tour)Container.DataItem).TourDate.ToNullableStandardDateString() %>' />
					</div>
				</div>
			</trwc:FeaturePlaceHolder>

			<trwc:FeaturePlaceHolder runat="server" FeatureKey="Tour.TourTime">
				<div class="form-group">
					<label class="col-xs-4 control-label">
						<asp:Literal runat="server" Text="<%$ Resources: resources,Tour.TourTime %>" />
					</label>
					<div class="col-xs-8 control-literal">
						<asp:Literal runat="server" Text='<%# ((Tour)Container.DataItem).TourTime.ToNullableStandardTimeString() %>' />
					</div>
				</div>
			</trwc:FeaturePlaceHolder>

			<trwc:FeaturePlaceHolder runat="server" FeatureKey="Tour.EntryDateTime">
				<div class="form-group">
					<label class="col-xs-4 control-label">
						<asp:Literal runat="server" Text="<%$ Resources: resources,Tour.EntryDateTime %>" />
					</label>
					<div class="col-xs-8 control-literal">
						<asp:Literal runat="server" Text='<%# ((Tour)Container.DataItem).EntryDateTime.ToNullableStandardDateTimeString() %>' />
					</div>
				</div>
			</trwc:FeaturePlaceHolder>

			<trwc:FeaturePlaceHolder runat="server" FeatureKey="Tour.TourText1">
				<div class="form-group">
					<label class="col-xs-4 control-label">
						<asp:Literal runat="server" Text="<%$ Resources: resources,Tour.TourText1 %>" />
					</label>
					<div class="col-xs-8 control-literal">
						<asp:Literal runat="server" Text='<%# ((Tour)Container.DataItem).TourText1 %>' />
					</div>
				</div>
			</trwc:FeaturePlaceHolder>

			<trwc:FeaturePlaceHolder runat="server" FeatureKey="Tour.TourDecimal1">
				<div class="form-group">
					<label class="col-xs-4 control-label">
						<asp:Literal runat="server" Text="<%$ Resources: resources,Tour.TourDecimal1 %>" />
					</label>
					<div class="col-xs-8 control-literal">
						<asp:Literal runat="server" Text='<%# ((Tour)Container.DataItem).TourDecimal1.ToFeatureFormatString("Tour.TourDecimal1") %>' />
					</div>
				</div>
			</trwc:FeaturePlaceHolder>

			<trwc:FeaturePlaceHolder runat="server" FeatureKey="Tour.TourBool1">
				<div class="form-group">
					<label class="col-xs-4 control-label">
						<asp:Literal runat="server" Text="<%$ Resources: resources,Tour.TourBool1 %>" />
					</label>
					<div class="col-xs-8 control-literal">
						<asp:Literal runat="server" Text='<%# GlobalResource.GetString("Tour.TourBool1.Boolean." + ((Tour)Container.DataItem).TourBool1) %>' />
					</div>
				</div>
			</trwc:FeaturePlaceHolder>

			<trwc:FeaturePlaceHolder runat="server" FeatureKey="Tour.ToursManyPickList1ItemIDs">
				<div class="form-group">
					<label class="col-xs-4 control-label">
						<asp:Literal runat="server" Text="<%$ Resources: resources,Tour.ToursManyPickList1ItemIDs.Plural %>" />
					</label>
					<div class="col-xs-8 control-literal">
						<truc:ManyPickLists ID="toursManyPickList1ItemIDsManyPickLists" runat="server" Mode="Select" PropertyKey="Tour.ToursManyPickList1ItemIDs"
							PickListItemsWebServiceMethod="GetToursManyPickList1Items" EnableMultipleSelect="true" />
					</div>
				</div>
			</trwc:FeaturePlaceHolder>
							
		</div>

	</ItemTemplate>

</asp:FormView>