trigger: none

pool:
  vmImage: 'windows-2022'  # Recommended for SQL Server projects

variables:
  solution: '**/*.sln'
  buildPlatform: 'Any CPU'
  buildConfiguration: 'Release'
  sqlProjectPath: 'SANDBOXtr/SANDBOXtr.sqlproj'  # Ensure this path is correct
  dacpacFolder: '$(Build.SourcesDirectory)/SANDBOXtr/bin/Release'
  dacpacFile: 'SANDBOXtr.dacpac'
  sqlPackagePath: 'C:\Program Files\Microsoft SQL Server\150\DAC\bin\SqlPackage.exe'

steps:

# 🔹 1) Install NuGet (some SQL projects require it)
- task: NuGetToolInstaller@1
  displayName: 'Install NuGet'

- task: NuGetCommand@2
  displayName: 'Restore NuGet Packages'
  inputs:
    restoreSolution: '$(solution)'

# 🔹 2) Clean the bin and obj folders before building
- task: PowerShell@2
  displayName: 'Clean bin and obj folders'
  inputs:
    targetType: 'inline'
    script: |
      Remove-Item -Recurse -Force "$(Build.SourcesDirectory)/SANDBOXtr/bin" -ErrorAction Ignore
      Remove-Item -Recurse -Force "$(Build.SourcesDirectory)/SANDBOXtr/obj" -ErrorAction Ignore

# 🔹 3) Build the SQL project to generate the .dacpac file
- task: VSBuild@1
  displayName: 'Build SQL Project'
  inputs:
    solution: '$(sqlProjectPath)'
    platform: '$(buildPlatform)'
    configuration: '$(buildConfiguration)'
    msbuildArgs: '/p:DeployOnBuild=true /p:TargetPlatform="AzureV12" /p:OutputPath="$(dacpacFolder)" /p:ExcludeObjectTypes=Triggers'

# 🔹 4) Verify that the .dacpac file was generated
- task: PowerShell@2
  displayName: 'Verify .dacpac Generation'
  inputs:
    targetType: 'inline'
    script: |
      $dacpacPath = "$(dacpacFolder)/$(dacpacFile)"
      if (Test-Path $dacpacPath) {
        Write-Host "✅ .dacpac found at: $dacpacPath"
      } else {
        Write-Host "❌ ERROR: .dacpac NOT FOUND!"
        Get-ChildItem -Path "$(dacpacFolder)" -Filter "*.dacpac"
        exit 1
      }

# 🔹 5) Copy the .dacpac file to the artifact staging directory
- task: CopyFiles@2
  displayName: 'Copy .dacpac to Artifact Staging'
  inputs:
    SourceFolder: '$(dacpacFolder)'
    Contents: '$(dacpacFile)'
    TargetFolder: '$(Build.ArtifactStagingDirectory)'

# 🔹 6) Publish the .dacpac as an artifact
- task: PublishBuildArtifacts@1
  displayName: 'Publish DB Artifact (.dacpac)'
  inputs:
    PathToPublish: '$(Build.ArtifactStagingDirectory)'
    ArtifactName: 'dropSANDBOXtr'
    publishLocation: 'Container'

# 🔹 7) Find and Verify SqlPackage.exe
- task: PowerShell@2
  displayName: 'Find and Verify SqlPackage.exe'
  inputs:
    targetType: 'inline'
    script: |
      Write-Host "🔍 Searching for SqlPackage.exe..."
      $sqlPackagePath = Get-Command -ErrorAction SilentlyContinue SqlPackage.exe | Select-Object -ExpandProperty Source

      if (-not $sqlPackagePath) {
        # Manually check common locations
        $possiblePaths = @(
          "C:\Program Files\Microsoft SQL Server\160\DAC\bin\SqlPackage.exe",
          "C:\Program Files\Microsoft SQL Server\150\DAC\bin\SqlPackage.exe",
          "C:\Program Files (x86)\Microsoft SQL Server\150\DAC\bin\SqlPackage.exe",
          "C:\Program Files\Microsoft SQL Server\140\DAC\bin\SqlPackage.exe"
        )

        foreach ($path in $possiblePaths) {
          if (Test-Path $path) {
            $sqlPackagePath = $path
            break
          }
        }
      }

      if ($sqlPackagePath) {
        Write-Host "✅ SqlPackage.exe found at: $sqlPackagePath"
        & "$sqlPackagePath" /Version
      } else {
        Write-Host "❌ ERROR: SqlPackage.exe not found in standard locations."
        exit 1
      }

