﻿CREATE TABLE [dbo].[NotesCancellations] (
    [noteID]          INT            IDENTITY (1, 1) NOT NULL,
    [referenceID]     INT            NOT NULL,
    [noteText]        NVARCHAR (MAX) NOT NULL,
    [userID]          INT            NULL,
    [insertTimeStamp] DATETIME       NOT NULL,
    [updateTimeStamp] DATETIME       NOT NULL,
    CONSTRAINT [PK_NotesCancellations] PRIMARY KEY CLUSTERED ([noteID] ASC)
);


GO
CREATE NONCLUSTERED INDEX [IX_NotesCancellations_referenceID]
    ON [dbo].[NotesCancellations]([referenceID] ASC);


GO
CREATE TRIGGER [dbo].[NotesCancellations.InsertUpdateDelete]
    ON [dbo].[NotesCancellations]
    FOR INSERT, UPDATE, DELETE
AS 
BEGIN
    SET NOCOUNT ON;

    DECLARE @Activity  NVARCHAR (8), @id INT;

    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'updated'
    END

    IF EXISTS (SELECT * FROM inserted) AND NOT EXISTS(SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'inserted'
    END

    IF EXISTS (SELECT * FROM deleted) AND NOT EXISTS(SELECT * FROM inserted)
    BEGIN
        SET @Activity = 'deleted'
    END

    

    IF (@Activity = 'deleted')
    BEGIN
        DECLARE cur CURSOR FOR SELECT noteID FROM deleted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'NotesCancellations', @id, 'noteID',
                (
                    SELECT *
                    FROM deleted i 
                    WHERE i.noteID =  @id
                    FOR JSON AUTO
                ),0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
    ELSE
    BEGIN
        DECLARE cur CURSOR
        FOR SELECT noteID
        FROM inserted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'NotesCancellations', @id, 'noteID',
                (
                    SELECT *
                    FROM inserted i 
                    WHERE i.noteID =  @id
                    FOR JSON AUTO
                ), 0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
END

GO
DISABLE TRIGGER [dbo].[NotesCancellations.InsertUpdateDelete]
    ON [dbo].[NotesCancellations];

