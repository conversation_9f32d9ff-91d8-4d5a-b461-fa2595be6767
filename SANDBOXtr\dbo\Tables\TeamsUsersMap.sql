﻿CREATE TABLE [dbo].[TeamsUsersMap] (
    [teamID] INT NOT NULL,
    [userID] INT NOT NULL,
    CONSTRAINT [PK_TeamsUsersMap] PRIMARY KEY CLUSTERED ([teamID] ASC, [userID] ASC)
);


GO
CREATE TRIGGER [dbo].[TeamsUsersMap.InsertUpdateDelete]
    ON [dbo].[TeamsUsersMap]
    FOR INSERT, UPDATE, DELETE
AS 
BEGIN
    SET NOCOUNT ON;

    DECLARE @Activity  NVARCHAR (8), @id INT;

    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'updated'
    END

    IF EXISTS (SELECT * FROM inserted) AND NOT EXISTS(SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'inserted'
    END

    IF EXISTS (SELECT * FROM deleted) AND NOT EXISTS(SELECT * FROM inserted)
    BEGIN
        SET @Activity = 'deleted'
    END

    

    IF (@Activity = 'deleted')
    BEGIN
        DECLARE cur CURSOR FOR SELECT userID FROM deleted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHIL<PERSON> @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'TeamsUsersMap', @id, 'userID',
                (
                    SELECT *
                    FROM deleted i 
                    WHERE i.userID =  @id
                    FOR JSON AUTO
                ),0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
    ELSE
    BEGIN
        DECLARE cur CURSOR
        FOR SELECT userID
        FROM inserted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'TeamsUsersMap', @id, 'userID',
                (
                    SELECT *
                    FROM inserted i 
                    WHERE i.userID =  @id
                    FOR JSON AUTO
                ), 0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
END

GO
DISABLE TRIGGER [dbo].[TeamsUsersMap.InsertUpdateDelete]
    ON [dbo].[TeamsUsersMap];

