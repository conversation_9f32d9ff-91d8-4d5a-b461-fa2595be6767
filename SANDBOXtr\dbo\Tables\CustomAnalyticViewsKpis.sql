﻿CREATE TABLE [dbo].[CustomAnalyticViewsKpis] (
    [customAnalyticViewsKpiID]                 INT            IDENTITY (1, 1) NOT NULL,
    [customAnalyticViewID]                     INT            NOT NULL,
    [propertyKey]                              NVARCHAR (64)  NULL,
    [name]                                     NVARCHAR (64)  NOT NULL,
    [code]                                     NVARCHAR (64)  NOT NULL,
    [columnKey]                                NVARCHAR (64)  NOT NULL,
    [nameValueDataFormatString]                NVARCHAR (64)  NOT NULL,
    [valueDataFormatString]                    NVARCHAR (64)  NOT NULL,
    [drillThroughUrl]                          NVARCHAR (64)  NULL,
    [isCalculated]                             BIT            CONSTRAINT [DF_CustomAnalyticViews_isCalculated] DEFAULT ((0)) NOT NULL,
    [isVisible]                                BIT            CONSTRAINT [DF_CustomAnalyticViews_isVisible] DEFAULT ((1)) NOT NULL,
    [customAnalyticViewsKpiLogicClassFullName] NVARCHAR (128) NULL,
    [customAnalyticViewsKpiLogicClassCode]     NVARCHAR (MAX) NULL,
    [sortOrder]                                INT            NOT NULL,
    [expression]                               TEXT           NULL,
    CONSTRAINT [PK_CustomAnalyticViewsKpis] PRIMARY KEY CLUSTERED ([customAnalyticViewsKpiID] ASC),
    CONSTRAINT [FK_CustomAnalyticViewsKpis_customAnalyticViewID] FOREIGN KEY ([customAnalyticViewID]) REFERENCES [dbo].[CustomAnalyticViews] ([customAnalyticViewID])
);

