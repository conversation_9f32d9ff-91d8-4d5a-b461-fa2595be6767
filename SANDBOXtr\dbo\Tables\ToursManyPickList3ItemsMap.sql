﻿CREATE TABLE [dbo].[ToursManyPickList3ItemsMap] (
    [toursManyPickList3ItemMapID] INT IDENTITY (1, 1) NOT NULL,
    [tourID]                      INT NOT NULL,
    [toursManyPickList3ItemID]    INT NOT NULL,
    CONSTRAINT [PK_ToursManyPickList3ItemsMap] PRIMARY KEY CLUSTERED ([toursManyPickList3ItemMapID] ASC),
    CONSTRAINT [FK_ToursManyPickList3ItemsMap_tourID] FOREIGN KEY ([tourID]) REFERENCES [dbo].[Tours] ([tourID]),
    CONSTRAINT [FK_ToursManyPickList3ItemsMap_toursManyPickList3ItemID] FOREIGN KEY ([toursManyPickList3ItemID]) REFERENCES [dbo].[ToursManyPickList3Items] ([toursManyPickList3ItemID])
);

