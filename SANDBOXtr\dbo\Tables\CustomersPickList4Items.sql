﻿CREATE TABLE [dbo].[CustomersPickList4Items] (
    [customersPickList4ItemID]   INT           IDENTITY (1, 1) NOT NULL,
    [customersPickList4ItemName] NVARCHAR (64) NOT NULL,
    [customersPickList4ItemCode] NVARCHAR (64) NOT NULL,
    [sortOrder]                  INT           NOT NULL,
    [active]                     BIT           NOT NULL,
    CONSTRAINT [PK_CustomersPickList4Items] PRIMARY KEY CLUSTERED ([customersPickList4ItemID] ASC),
    CONSTRAINT [UK_CustomersPickList4Items_customersPickList4ItemName] UNIQUE NONCLUSTERED ([customersPickList4ItemName] ASC)
);


GO
CREATE TRIGGER [dbo].[CustomersPickList4Items.InsertUpdateDelete]
    ON [dbo].[CustomersPickList4Items]
    FOR INSERT, UPDATE, DELETE
AS 
BEGIN
    SET NOCOUNT ON;

    DECLARE @Activity  NVARCHAR (8), @id INT;

    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'updated'
    END

    IF EXISTS (SELECT * FROM inserted) AND NOT EXISTS(SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'inserted'
    END

    IF EXISTS (SELECT * FROM deleted) AND NOT EXISTS(SELECT * FROM inserted)
    BEGIN
        SET @Activity = 'deleted'
    END

    

    IF (@Activity = 'deleted')
    BEGIN
        DECLARE cur CURSOR FOR SELECT customersPickList4ItemID FROM deleted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'CustomersPickList4Items', @id, 'customersPickList4ItemID',
                (
                    SELECT *
                    FROM deleted i 
                    WHERE i.customersPickList4ItemID =  @id
                    FOR JSON AUTO
                ),0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
    ELSE
    BEGIN
        DECLARE cur CURSOR
        FOR SELECT customersPickList4ItemID
        FROM inserted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'CustomersPickList4Items', @id, 'customersPickList4ItemID',
                (
                    SELECT *
                    FROM inserted i 
                    WHERE i.customersPickList4ItemID =  @id
                    FOR JSON AUTO
                ), 0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
END

GO
DISABLE TRIGGER [dbo].[CustomersPickList4Items.InsertUpdateDelete]
    ON [dbo].[CustomersPickList4Items];

