﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TrackResults.BES.Data;
using TrackResults.BES.Data.Business;
using TrackResults.BES.Data.Cache;
using TrackResults.BES.Data.Types;
using TrackResults.BES.DataAccess;
using TrackResults.BES.Services.ApiConnectorProviders;
using TrackResults.BES.Services.ApiConnectorProviders.ApiConnectionFileApiConnectorProviders;
using TrackResults.Common.Core.Collections.Generic;
using TrackResults.Common.Core.Data.SqlClient.MSSql;
using TrackResults.Common.Core.Extensions;
using TrackResults.Common.DAL;
using TrackResults.Common.Exceptions;

namespace TrackResults.BES.Services
{
	
	public static class MiddlewareService
	{
		public static void ExecuteSchedulerReports(int schedulerID) { 
			try
			{
                ApiConnectorProvider apiConnectorProvider = (ApiConnectorProvider)ExtensionsServices.GetNewClassObject("TrackResults.Web.Integrations.Services.MiddlewareObjectActions, TrackResults.Web");

                List<CustomReportsScheduler> schedulers = CustomReportsSchedulerDataAccess.SelectBySchedulerID(schedulerID);
				if(schedulers.Any())
				{
					foreach(CustomReportsScheduler sched in schedulers)
					{
						var reportsIDs = CustomReportsSchedulerDataAccess.SelectReportsBySchedulerID(sched.SchedulerID);
						apiConnectorProvider.SchedulerReports(sched, reportsIDs);
                    }
				}

            }
            catch (MiddlewareErrorException middlewareErrorException)
            {

                LogMiddlewareErrorException(middlewareErrorException);

            }
            catch (Exception exception)
            {
                MiddlewareErrorsDataAccess.Insert(MiddlewareErrorType.ExecuteConnection, schedulerID, null, null, exception.ToString(), true, null,
                    null);
            }
            finally
            {
                ApiConnectionsDataAccess.UpdateIsProcessingShouldContinueProcessing(schedulerID, false, false, null);
            }
        }
		public static string actionMiddleware { get; set; }
		public static void ExecuteApiConnection(int apiConnectionID, bool esTesting = false)
		{
			ApiConnection apiConnection = null;
			ApiConnectorProvider apiConnectorProvider = null, connectorProvider = null;
			try
			{
				bool isProcessing = ApiConnectionsDataAccess.IsProcessingByID(apiConnectionID);
				if (!isProcessing)
				{
					ApiConnectionsDataAccess.UpdateIsProcessingShouldContinueProcessing(apiConnectionID, true, true, DateTime.UtcNow);
					apiConnection = ApiConnectionsDataAccess.SelectDataByID(apiConnectionID);
                    if (apiConnection != null)
                    {
						apiConnection.EsTesting = esTesting;
						apiConnectorProvider = (ApiConnectorProvider)ExtensionsServices.GetNewClassObject("TrackResults.Web.Integrations.Services.MiddlewareObjectActions, TrackResults.Web");

						SecurityProfile.UserID = apiConnection.UserID;

                        // TODO28: Should we use a new role for middleware?
                        SecurityProfile.Role = Role.SystemAdministrator;


                        List<ApiConnectionRequests> requests = ApiConnectionRequestsDataAccess.SelectAllDataByApiConnectionID(apiConnectionID);
                        if (requests.Any())
                        {
                            foreach (var r in requests)
                            {
								if (r.SendingParams == true)
								{
                                    DateTime end = DateTime.Now;
                                    int totalCount = apiConnectorProvider.SendingParams(apiConnection, r);
                                    TimeSpan difference = end - DateTime.Now;
                                    string formattedDifference = difference.ToString(@"hh\:mm\:ss\.fffffff");
                                    double recordsPerMinute = (totalCount / difference.TotalMinutes);
                                    string formattedRecordsPerMinute = $"{recordsPerMinute:0} rec/min";
                                    string result = $"{formattedDifference}, {formattedRecordsPerMinute}";
                                    ApiConnectionsDataAccess.UpdateLastMiddlewareActionTimeTotalCountPerformance(apiConnection.ApiConnectionID,
                                    end, totalCount, result);
                                }
								else
								{
									if (!apiConnection.EnableCondition)
									{
										List<ApiRequestResponses> responses = ApiRequestResponsesDataAccess.SelectAllDataByApiConnectionID(r.ApiConnectionRequestId);
										if (responses.Any())
										{
											foreach (var res in responses)
											{
												if (!string.IsNullOrEmpty(res.ResponseAction))
												{
													switch (res.ResponseAction)
													{
														case "ImportTours":
															MiddlewareService.actionMiddleware = "PUT";
															ImportTours(apiConnection, apiConnectorProvider);
															break;
														case "ImportToursSinceLastUpdatedTime":
															ImportToursSinceLastUpdatedTime(apiConnection, apiConnectorProvider);
															break;

														case "ImportToursSinceLastUpdatedTimeByToursCriteria":
															ImportToursSinceLastUpdatedTimeByToursCriteria(apiConnection, apiConnectorProvider);
															break;
														case "PATCH":
															MiddlewareService.actionMiddleware = "PATCH";
															ImportTours(apiConnection, apiConnectorProvider);
															break;
														case "BulkImport":
															MiddlewareService.actionMiddleware = "PATCH";
															BulkImport(apiConnection, apiConnectorProvider);
                                                            break;
														default:
															throw new ArgumentException("An action is not defined for: " + apiConnection.MiddlewareAction, "MiddlewareAction");
													}
												}
												else
												{

													if (!string.IsNullOrEmpty(apiConnection.MiddlewareAction))
													{
														switch (apiConnection.MiddlewareAction)
														{
															case "ImportTours":
															case "PUT":
																MiddlewareService.actionMiddleware = "PUT";
																ImportTours(apiConnection, apiConnectorProvider);
																break;

															case "ImportToursSinceLastUpdatedTime":
																ImportToursSinceLastUpdatedTime(apiConnection, apiConnectorProvider);
																break;

															case "ImportToursSinceLastUpdatedTimeByToursCriteria":
																ImportToursSinceLastUpdatedTimeByToursCriteria(apiConnection, apiConnectorProvider);
																break;
															case "Patch":
															case "PATCH":
																MiddlewareService.actionMiddleware = "PATCH";
																ImportTours(apiConnection, apiConnectorProvider);
																break;
                                                            default:
																throw new ArgumentException("An action is not defined for: " + apiConnection.MiddlewareAction, "MiddlewareAction");
														}
													}

												}
											}
										}
										//new for sftp
										else
										{

											connectorProvider = ApiConnectorsCache.GetApiConnector(apiConnection.ApiConnectorID).ApiConnectorProvider;
											apiConnectorProvider.GetUploadRemoteFile(apiConnection, r);
											switch (apiConnection.MiddlewareAction)
											{
												case "ImportTours":
												case "PUT":
													MiddlewareService.actionMiddleware = "PUT";
													ImportTours(apiConnection, connectorProvider);
													break;

												case "ImportToursSinceLastUpdatedTime":
													ImportToursSinceLastUpdatedTime(apiConnection, connectorProvider);
													break;

												case "ImportToursSinceLastUpdatedTimeByToursCriteria":
													ImportToursSinceLastUpdatedTimeByToursCriteria(apiConnection, connectorProvider);
													break;
												case "Patch":
												case "PATCH":
													MiddlewareService.actionMiddleware = "PATCH";
													ImportTours(apiConnection, connectorProvider);
													break;
                                                default:
													throw new ArgumentException("An action is not defined for: " + apiConnection.MiddlewareAction, "MiddlewareAction");
											}
										}
									}
									else
									{
										string jsonBody = null;
										DateTime end = DateTime.Now;
										int totalCount = 0;
										int lastSlashIndex = r.RequestEndpoint.LastIndexOf('/');
										int firstQuestionMarkIndex = r.RequestEndpoint.IndexOf('?', lastSlashIndex);
										if (lastSlashIndex != -1)
										{
											string tableName = (firstQuestionMarkIndex != -1) ?
															   r.RequestEndpoint.Substring(lastSlashIndex + 1, firstQuestionMarkIndex - lastSlashIndex - 1) :
															   r.RequestEndpoint.Substring(lastSlashIndex + 1);
											var data = DynamicClass.SelectDataByCondition(tableName, apiConnection.MiddlewareCondition, Convert.ToDateTime(apiConnection.LastMiddlewareActionTime), end);
											totalCount = data.Count;
											jsonBody = JsonConvert.SerializeObject(data);
											if (totalCount > 0)
												apiConnectorProvider.MiddlewareConditions(r.ApiConnectionRequestId, jsonBody);
										}
										TimeSpan difference = end - DateTime.Now;
										string formattedDifference = difference.ToString(@"hh\:mm\:ss\.fffffff");
										double recordsPerMinute = (totalCount / difference.TotalMinutes);
										string formattedRecordsPerMinute = $"{recordsPerMinute:0} rec/min";
										string result = $"{formattedDifference}, {formattedRecordsPerMinute}";
										ApiConnectionsDataAccess.UpdateLastMiddlewareActionTimeTotalCountPerformance(apiConnection.ApiConnectionID,
										end, totalCount, result);

									}
								}
                            }
                        }
						else if(apiConnection.HasFileUpload == true && !string.IsNullOrEmpty(apiConnection.MiddlewareAction))
						{
                            ApiConnectionRequests r2 = new ApiConnectionRequests();
                            connectorProvider = ApiConnectorsCache.GetApiConnector(apiConnection.ApiConnectorID).ApiConnectorProvider;
                            apiConnectorProvider.GetUploadRemoteFile(apiConnection, r2);
                            switch (apiConnection.MiddlewareAction)
                            {
                                case "ImportTours":
                                case "PUT":
                                    MiddlewareService.actionMiddleware = "PUT";
                                    ImportTours(apiConnection, connectorProvider);
                                    break;

                                case "ImportToursSinceLastUpdatedTime":
                                    ImportToursSinceLastUpdatedTime(apiConnection, connectorProvider);
                                    break;

                                case "ImportToursSinceLastUpdatedTimeByToursCriteria":
                                    ImportToursSinceLastUpdatedTimeByToursCriteria(apiConnection, connectorProvider);
                                    break;
                                case "Patch":
                                case "PATCH":
                                    MiddlewareService.actionMiddleware = "PATCH";
                                    ImportTours(apiConnection, connectorProvider);
                                    break;
                                default:
                                    throw new ArgumentException("An action is not defined for: " + apiConnection.MiddlewareAction, "MiddlewareAction");
                            }
                        }
                    }

                }
			}
			catch (MiddlewareErrorException middlewareErrorException)
			{
				
				LogMiddlewareErrorException(middlewareErrorException);
			 
			}
			catch (Exception exception)
			{

				MiddlewareErrorsDataAccess.Insert(MiddlewareErrorType.ExecuteConnection, apiConnectionID, null, null, exception.ToString(), true, null,
					null);
			}
			finally
			{
				ApiConnectionsDataAccess.UpdateIsProcessingShouldContinueProcessing(apiConnectionID, false, false, null);
			}
		}

		public static int BulkImport(ApiConnection apiConnection, ApiConnectorProvider apiConnectorProvider)
		{
            int importCount = 0;
            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();

            Tuple<DateTime?, int> requestDateTimeTotalCount = apiConnectorProvider.GetPagedToursBulk(apiConnection, MiddlewarePreAction,
                delegate (List<TourReferences> tourReference, string importNumber, string fileNamePath)
                {
                    if (ImportTourReferences(apiConnection, tourReference, importNumber, fileNamePath))
                    {
                        importCount++;
                    }
                }, MiddlewarePostAction);

            stopwatch.Stop();
            if (requestDateTimeTotalCount.Item1 != null)
            {
                string processingPerformance =
                    string.Format("{0}, {1} rec/min", stopwatch.Elapsed.Duration(),
                    (requestDateTimeTotalCount.Item2 / stopwatch.Elapsed.TotalMinutes).ToString("N0"));

                ApiConnectionsDataAccess.UpdateLastMiddlewareActionTimeTotalCountPerformance(apiConnection.ApiConnectionID,
                    (DateTime)requestDateTimeTotalCount.Item1, requestDateTimeTotalCount.Item2, processingPerformance);
            }

            return importCount;
        }
        public static int ImportTours(ApiConnection apiConnection, ApiConnectorProvider apiConnectorProvider)
        {
            int importCount = 0;
            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();

            Tuple<DateTime?, int> requestDateTimeTotalCount = apiConnectorProvider.GetPagedTours(apiConnection, MiddlewarePreAction,
                delegate (TourReference tourReference, string importNumber, string fileNamePath)
                {
                    if (ImportTourReference(apiConnection, tourReference, importNumber, fileNamePath))
                    {
                        importCount++;
                    }
                }, MiddlewarePostAction);

            stopwatch.Stop();
            if (requestDateTimeTotalCount.Item1 != null)
            {
                string processingPerformance =
                    string.Format("{0}, {1} rec/min", stopwatch.Elapsed.Duration(),
                    (requestDateTimeTotalCount.Item2 / stopwatch.Elapsed.TotalMinutes).ToString("N0"));

                ApiConnectionsDataAccess.UpdateLastMiddlewareActionTimeTotalCountPerformance(apiConnection.ApiConnectionID,
                    (DateTime)requestDateTimeTotalCount.Item1, requestDateTimeTotalCount.Item2, processingPerformance);
            }

            return importCount;
        }

        public static int ImportToursSinceLastUpdatedTime(ApiConnection apiConnection, ApiConnectorProvider apiConnectorProvider)
		{
			int importCount = 0;
			Stopwatch stopwatch = new Stopwatch();
			stopwatch.Start();

			DateTime? lastUpdatedTime = ApiConnectionsDataAccess.SelectLastMiddlewareActionTimeByID(apiConnection.ApiConnectionID);

			if (lastUpdatedTime == null)
			{
				lastUpdatedTime = DBConstants.MinDate;
			}

			Tuple<DateTime?, int> requestDateTimeTotalCount = apiConnectorProvider.GetPagedToursSinceLastUpdatedTime(apiConnection,
				(DateTime)lastUpdatedTime, MiddlewarePreAction, delegate(TourReference tourReference, string importNumber, string fileNamePath)
				{
					if (ImportTourReference(apiConnection, tourReference, importNumber, fileNamePath))
					{
						importCount++;
					}
				}, MiddlewarePostAction);

			stopwatch.Stop();
			if (requestDateTimeTotalCount.Item1 != null)
			{
				string processingPerformance =
					string.Format("{0} hrs {1} min, {2} records / min", stopwatch.Elapsed.Hours, stopwatch.Elapsed.Minutes,
					(requestDateTimeTotalCount.Item2 / stopwatch.Elapsed.TotalMinutes).ToString("N0"));

				ApiConnectionsDataAccess.UpdateLastMiddlewareActionTimeTotalCountPerformance(apiConnection.ApiConnectionID,
					(DateTime)requestDateTimeTotalCount.Item1, requestDateTimeTotalCount.Item2, processingPerformance);
			}

			return importCount;
		}

		public static int ImportToursSinceLastUpdatedTimeByToursCriteria(ApiConnection apiConnection, ApiConnectorProvider apiConnectorProvider)
		{
			int importCount = 0;
			Stopwatch stopwatch = new Stopwatch();
			stopwatch.Start();

			DateTime? lastUpdatedTime = ApiConnectionsDataAccess.SelectLastMiddlewareActionTimeByID(apiConnection.ApiConnectionID);

			if (lastUpdatedTime == null)
			{
				lastUpdatedTime = DBConstants.MinDate;
			}

			Tuple<DateTime?, int> requestDateTimeTotalCount = apiConnectorProvider.GetPagedToursSinceLastUpdatedTimeByToursCriteria(apiConnection,
				(DateTime)lastUpdatedTime, apiConnection.ArgumentTourStatusTypeIDs, apiConnection.ArgumentLocationIDs,
				apiConnection.ArgumentStartDate, MiddlewarePreAction, delegate(TourReference tourReference, string importNumber, string fileNamePath)
				{
					if (ImportTourReference(apiConnection, tourReference, importNumber, fileNamePath))
					{
						importCount++;
					}
				}, MiddlewarePostAction);

			stopwatch.Stop();
			if (requestDateTimeTotalCount.Item1 != null)
			{
				string processingPerformance =
					string.Format("{0} hrs {1} min, {2} records / min", stopwatch.Elapsed.Hours, stopwatch.Elapsed.Minutes,
					(requestDateTimeTotalCount.Item2 / stopwatch.Elapsed.TotalMinutes).ToString("N0"));

				ApiConnectionsDataAccess.UpdateLastMiddlewareActionTimeTotalCountPerformance(apiConnection.ApiConnectionID,
					(DateTime)requestDateTimeTotalCount.Item1, requestDateTimeTotalCount.Item2, processingPerformance);
			}

			return importCount;
		}

		public static int ImportTour(int apiConnectionID, string externalTourID)
		{
			ApiConnection apiConnection = ApiConnectionsCache.Instance.Data.GetValue(apiConnectionID);
			ApiConnectorProvider apiConnectorProvider = ApiConnectorsCache.GetApiConnector(apiConnection.ApiConnectorID).ApiConnectorProvider;

			return ImportTour(apiConnection, apiConnectorProvider, externalTourID);
		}

		public static int ImportTour(ApiConnection apiConnection, ApiConnectorProvider apiConnectorProvider, string externalTourID)
		{
			int importCount = 0;

			try
			{
				apiConnectorProvider.GetTourByExternalTourID(apiConnection, externalTourID,
					delegate(TourReference tourReference, string importNumber, string fileNamePath)
					{
						if (ImportTourReference(apiConnection, tourReference, importNumber, fileNamePath))
						{
							importCount++;
						}
					});
			}
			catch (MiddlewareErrorException middlewareErrorException)
			{
				LogMiddlewareErrorException(middlewareErrorException);
			}
			catch (Exception exception)
			{
				MiddlewareErrorsDataAccess.Insert(MiddlewareErrorType.ExecuteConnection, apiConnection.ApiConnectionID, null, null,
					exception.ToString(), true, null, null);
			}

			return importCount;
		}

        //public static DataTable GetRawDataPage(int apiConnectionID, int pageNumber, int pagedCount)
        //{
        //    ApiConnection apiConnection = ApiConnectionsCache.Instance.Data.GetValue(apiConnectionID);
        //    ApiConnectorProvider apiConnectorProvider = ApiConnectorsCache.GetApiConnector(apiConnection.ApiConnectorID).ApiConnectorProvider;

        //    return apiConnectorProvider.GetRawDataPage(apiConnection, pageNumber, pagedCount);
        //}

        //public static DataTable GetRawDataByExternalTourID(int apiConnectionID, string externalTourID)
        //{
        //    ApiConnection apiConnection = ApiConnectionsCache.Instance.Data.GetValue(apiConnectionID);
        //    ApiConnectorProvider apiConnectorProvider = ApiConnectorsCache.GetApiConnector(apiConnection.ApiConnectorID).ApiConnectorProvider;

        //    return apiConnectorProvider.GetRawDataByExternalTourID(apiConnection, externalTourID);
        //}

        public static void GetPagedRawData(int apiConnectionID, int pagedCount, ApiConnectorProvider.RawDataCommandBlock rawDataCommandBlock)
        {
            ApiConnection apiConnection = ApiConnectionsCache.Instance.Data.GetValue<int, ApiConnection>(apiConnectionID);
            ApiConnectorsCache.GetApiConnector(apiConnection.ApiConnectorID).ApiConnectorProvider.GetPagedRawData(apiConnection, pagedCount, rawDataCommandBlock);

        }

        public static DataTable GetRawDataByExternalTourID(int apiConnectionID, string externalTourID)
        {
            ApiConnection apiConnection = ApiConnectionsCache.Instance.Data.GetValue<int, ApiConnection>(apiConnectionID);
            return ApiConnectorsCache.GetApiConnector(apiConnection.ApiConnectorID).ApiConnectorProvider.GetRawDataByExternalTourID(apiConnection, externalTourID);

        }

		public static void LogMiddlewareErrorException(MiddlewareErrorException middlewareErrorException)
		{
			if (!middlewareErrorException.SuppressError)
			{
				MiddlewareErrorType middlewareErrorType = MiddlewareErrorType.ExecuteConnection;
				if (middlewareErrorException.MiddlewareErrorTypeID != null)
				{
					middlewareErrorType = (MiddlewareErrorType)middlewareErrorException.MiddlewareErrorTypeID;
				}

				string errorMessage = middlewareErrorException.Message;
				if (middlewareErrorException.IsException)
				{
					errorMessage = middlewareErrorException.ToString();
				}

				MiddlewareErrorsDataAccess.Insert(middlewareErrorType, middlewareErrorException.ApiConnectionID, middlewareErrorException.ImportNumber,
					middlewareErrorException.FileNamePath, errorMessage, middlewareErrorException.IsException,
					middlewareErrorException.ExternalCustomerID, middlewareErrorException.ExternalTourID, middlewareErrorException.Info1,
					middlewareErrorException.Info2, middlewareErrorException.Info3, middlewareErrorException.Info4);
			}
		}

		public static void LogMiddlewareErrorException(MiddlewareErrorType middlewareErrorType, int apiConnectionID, string message,
			string importNumber = null, string fileNamePath = null)
		{
			LogMiddlewareErrorException(middlewareErrorType, apiConnectionID, null, null, null, null, null, null, message, null, false, importNumber,
				fileNamePath);
		}

		public static void LogMiddlewareErrorException(MiddlewareErrorType middlewareErrorType, int apiConnectionID, string externalCustomerID,
			string externalTourID, string message, string importNumber = null, string fileNamePath = null)
		{
			LogMiddlewareErrorException(middlewareErrorType, apiConnectionID, externalCustomerID, externalTourID, null, null, null, null, message,
				null, false, importNumber, fileNamePath);
		}

		public static void LogMiddlewareErrorException(MiddlewareErrorType middlewareErrorType, int apiConnectionID, Exception exception,
			string importNumber = null, string fileNamePath = null)
		{
			LogMiddlewareErrorException(middlewareErrorType, apiConnectionID, null, null, null, null, null, null, null, exception, false,
				importNumber, fileNamePath);
		}

		public static void LogMiddlewareErrorException(MiddlewareErrorType middlewareErrorType, int apiConnectionID, string externalCustomerID,
			string externalTourID, Exception exception, string importNumber = null, string fileNamePath = null)
		{
			LogMiddlewareErrorException(middlewareErrorType, apiConnectionID, externalCustomerID, externalTourID, null, null, null, null, null,
				exception, false, importNumber, fileNamePath);
		}

		public static void LogMiddlewareErrorException(MiddlewareErrorType middlewareErrorType, int apiConnectionID, string info1, string info2,
			string info3, string info4, string message, string importNumber = null, string fileNamePath = null)
		{
			LogMiddlewareErrorException(middlewareErrorType, apiConnectionID, null, null, info1, info2, info3, info4, message, null, false,
				importNumber, fileNamePath);
		}

		public static void LogMiddlewareErrorException(MiddlewareErrorType middlewareErrorType, int apiConnectionID, string externalCustomerID,
			string externalTourID, string info1, string info2, string info3, string info4, string message, string importNumber = null,
			string fileNamePath = null)
		{
			LogMiddlewareErrorException(middlewareErrorType, apiConnectionID, externalCustomerID, externalTourID, info1, info2, info3, info4, message,
				null, false, importNumber, fileNamePath);
		}

		public static void LogMiddlewareErrorException(MiddlewareErrorType middlewareErrorType, int apiConnectionID, string info1, string info2,
			string info3, string info4, Exception exception, string importNumber = null, string fileNamePath = null)
		{
			LogMiddlewareErrorException(middlewareErrorType, apiConnectionID, null, null, info1, info2, info3, info4, null, exception, false,
				importNumber, fileNamePath);
		}

		public static void LogMiddlewareErrorException(MiddlewareErrorType middlewareErrorType, int apiConnectionID, string externalCustomerID,
			string externalTourID, string info1, string info2, string info3, string info4, Exception exception,
			string importNumber = null, string fileNamePath = null)
		{
			LogMiddlewareErrorException(middlewareErrorType, apiConnectionID, externalCustomerID, externalTourID, info1, info2, info3, info4, null,
				exception, false, importNumber, fileNamePath);
		}

		public static void LogMiddlewareErrorException(MiddlewareErrorType middlewareErrorType, int apiConnectionID, string externalCustomerID,
			string externalTourID, string info1, string info2, string info3, string info4, string message, Exception exception, bool suppressError,
			string importNumber, string fileNamePath)
		{
			if (!suppressError)
			{
				string errorMessage = message;
				bool isException = false;

				if (exception != null)
				{
					errorMessage = exception.ToString();
					isException = true;
				}

				MiddlewareErrorsDataAccess.Insert(middlewareErrorType, apiConnectionID, importNumber, fileNamePath, errorMessage, isException,
					externalCustomerID, externalTourID, info1, info2, info3, info4);
			}
		}

		private static void MiddlewarePreAction(ApiConnection apiConnection)
		{
			if (!apiConnection.MiddlewarePreActions.IsNullOrEmpty())
			{
				foreach (string middlewarePreAction in apiConnection.MiddlewarePreActions)
				{
					switch (middlewarePreAction)
					{
						case "DeleteReferentialData":
							MSSqlDataAccess.ExecuteStoredProcedure(ConnectionStrings.Default, "DeleteReferentialData");
							break;

						case "DeleteAllTours":
							MSSqlDataAccess.ExecuteStoredProcedure(ConnectionStrings.Default, "DeleteAllTours");
							break;

                        case "DeleteDateFilteredTours":
                            DateTime startDate;
                            DateTime endDate;

                            if (string.IsNullOrEmpty(apiConnection.Argument2) || !DateTime.TryParse(apiConnection.Argument2, out endDate))
                            {
                                endDate = DateTime.Now;
                            }

                            if (string.IsNullOrEmpty(apiConnection.Argument1) || !DateTime.TryParse(apiConnection.Argument1, out startDate))
                            {
                                startDate = endDate.AddDays(-120);
                            }

                            ToursDataAccess.DeleteDateFilteredTours(startDate, endDate);
                            break;

						default:
							throw new ArgumentException("A pre action is not defined for: " + middlewarePreAction, "middlewarePreAction");
					}
				}
			}
		}

        private static void MiddlewarePostAction(ApiConnection apiConnection)
        {
            if (!apiConnection.MiddlewarePostActions.IsNullOrEmpty())
            {
                foreach (string middlewarePostAction in apiConnection.MiddlewarePostActions)
                {
                    switch (middlewarePostAction)
                    {
                        case "LoadCachedCustomReportResults":
                            CustomReportsService.LoadCachedCustomReportResults();
                            break;
                        default:
                            throw new ArgumentException("A post action is not defined for: " + middlewarePostAction, "middlewarePostAction");
                    }
                }
            }
        }


        public static bool ImportTourReferences(ApiConnection apiConnection, List<TourReferences> tourReferencess, string importNumber, string fileNamePath)
        {
            bool isValid = true;
            List<TourReference> updatedTourReferencesList = new List<TourReference>();
            List<TourReference> tourReferences = new List<TourReference>();

			foreach (var tReference in tourReferencess)
			{
				TourReference tourReference = new TourReference
                {
                    CustomerReference = new CustomerReference
                    {
                        ApiExternalCustomerIDUpdated = true,
                        CustomerReferenceUpdated = true
                    }
                };

                if (tReference.ToursReferences.Count > 0)
				{
					for (int i = 0; i < tReference.ToursReferences.Count; i++)
					{
						if (tReference.ToursReferences[i].TourIDUpdated)
						{
							tourReference = tReference.ToursReferences[i];
							if (tourReference.TourStatusTypeName != "Custom Sales")
								tourReference.TourTypeName = "Webinar";
							else
                                tourReference.TourTypeName = "TM-LTP";
                            tourReference.CustomerReference = tReference.CustomersReferences;
                            tourReferences.Add(tourReference);
                        }
                    }
                }
				else
				{
					tReference.CustomersReferences.CustomersText1 = "TM-" + tReference.CustomersReferences.CustomersText1;
                    tourReference.CustomerReference = tReference.CustomersReferences;
                    tourReferences.Add(tourReference);
                }
                
            }

            if (!apiConnection.DisableImportTourReference && tourReferences != null && tourReferences.Any())
            {
                foreach (var tourReference in tourReferences)
                {
                    string apiExternalCustomerID = null;
                    string apiExternalTourID = null;

                    try
                    {
                        tourReference.CustomerReference.ApiConnectionID = apiConnection.ApiConnectionID;
                        tourReference.CustomerReference.ApiConnectionIDUpdated = true;

                        if (string.IsNullOrEmpty(tourReference.CustomerReference.ApiExternalCustomerID) &&
                            tourReference.CustomerReference.CustomerID != null)
                        {
                            tourReference.CustomerReference.ApiExternalCustomerID = ((int)tourReference.CustomerReference.CustomerID).ToString();
                        }

                        apiExternalCustomerID = tourReference.CustomerReference.ApiExternalCustomerID;
                        tourReference.CustomerReference.ApiExternalConnectionID = apiConnection.ApiExternalConnectionID.ToNullableString();

                        if (apiConnection.ImportTourReferenceIgnoreUpdated || !tourReference.CustomerReference.CustomerIDUpdated)
                        {
                            tourReference.CustomerReference.CustomerID = null;
                        }

                        tourReference.ApiConnectionID = apiConnection.ApiConnectionID;
                        tourReference.ApiConnectionIDUpdated = true;

                        if (string.IsNullOrEmpty(tourReference.ApiExternalTourID) && tourReference.TourID != null)
                        {
                            tourReference.ApiExternalTourID = ((int)tourReference.TourID).ToString();
                        }

                        apiExternalTourID = tourReference.ApiExternalTourID;
                        tourReference.ApiExternalConnectionID = apiConnection.ApiExternalConnectionID.ToNullableString();

                        if (apiConnection.ImportTourReferenceIgnoreUpdated || !tourReference.TourIDUpdated)
                        {
                            tourReference.TourID = null;
                        }

                        if (ApplicationSettingsService.EnableApiConnectionKeyLocationNamePrepend)
                        {
                            tourReference.LocationName = apiConnection.ApiConnectionKey + " " + tourReference.LocationName;
                        }

                        if (tourReference.PurchaseReferences != null)
                        {
                            foreach (PurchaseReference purchaseReference in tourReference.PurchaseReferences)
                            {
                                purchaseReference.ApiConnectionID = apiConnection.ApiConnectionID;
                                purchaseReference.ApiConnectionIDUpdated = true;

                                if (string.IsNullOrEmpty(purchaseReference.ApiExternalPurchaseID) && purchaseReference.PurchaseID != null)
                                {
                                    purchaseReference.ApiExternalPurchaseID = ((int)purchaseReference.PurchaseID).ToString();
                                }

                                purchaseReference.ApiExternalConnectionID = apiConnection.ApiExternalConnectionID.ToNullableString();

                                if (apiConnection.ImportTourReferenceIgnoreUpdated || !purchaseReference.PurchaseIDUpdated)
                                {
                                    purchaseReference.PurchaseID = null;
                                }
                            }
                        }

                        // Add the processed tourReference to the list
                        updatedTourReferencesList.Add(tourReference);
                    }
                    catch (Exception exception)
                    {
                        MiddlewareErrorsDataAccess.Insert(MiddlewareErrorType.ImportTourReferenceException, apiConnection.ApiConnectionID,
                            importNumber, fileNamePath, exception.ToString(), true, apiExternalCustomerID, apiExternalTourID);

                        // Mark isValid as false if any exception occurs
                        isValid = false;
                    }
                }

                if (updatedTourReferencesList.Any())
                {
                    try
                    {
                        List<string> errorMessages = new List<string>();

                        // Now pass the entire list of TourReference objects to UpsertUpdatedTour in one go
                        isValid = TourReferencesService.CreateOrUpdateTourReferences(updatedTourReferencesList, errorMessages, apiConnection.UseTypeIDs,
                            apiConnection.ImportTourReferenceIgnoreUpdated, apiConnection.ApiConnectionID,
                            delegate (Tour tour, List<string> validateTourErrorMessages)
                            {
                                // TODO: Future import tour validation from apiconnection rules.
                                return true;
                            });

                        if (!isValid)
                        {
                            DelimitedCollection<string> delimitedErrorMessages = new DelimitedCollection<string>(Environment.NewLine, errorMessages);
                            MiddlewareErrorsDataAccess.Insert(MiddlewareErrorType.ImportTourReferenceInvalid, apiConnection.ApiConnectionID,
                                importNumber, fileNamePath, delimitedErrorMessages.ToString(), false, null, null);
                        }
                    }
                    catch (Exception exception)
                    {
                        MiddlewareErrorsDataAccess.Insert(MiddlewareErrorType.ImportTourReferenceException, apiConnection.ApiConnectionID,
                            importNumber, fileNamePath, exception.ToString(), true, null, null);

                        isValid = false;
                    }
                }
            }

            return isValid;
        }


        public static bool ImportTourReference(ApiConnection apiConnection, TourReference tourReference, string importNumber, string fileNamePath)
		{
			bool isValid = false;

			if (!apiConnection.DisableImportTourReference && tourReference != null)
			{
				string apiExternalCustomerID = null;
				string apiExternalTourID = null;

				try
				{
					tourReference.CustomerReference.ApiConnectionID = apiConnection.ApiConnectionID;
					tourReference.CustomerReference.ApiConnectionIDUpdated = true;

					if (string.IsNullOrEmpty(tourReference.CustomerReference.ApiExternalCustomerID) &&
						tourReference.CustomerReference.CustomerID != null)
					{
						tourReference.CustomerReference.ApiExternalCustomerID = ((int)tourReference.CustomerReference.CustomerID).ToString();
					}

					apiExternalCustomerID = tourReference.CustomerReference.ApiExternalCustomerID;
					tourReference.CustomerReference.ApiExternalConnectionID = apiConnection.ApiExternalConnectionID.ToNullableString();

					if (apiConnection.ImportTourReferenceIgnoreUpdated || !tourReference.CustomerReference.CustomerIDUpdated)
					{
						tourReference.CustomerReference.CustomerID = null;
					}

					tourReference.ApiConnectionID = apiConnection.ApiConnectionID;
					tourReference.ApiConnectionIDUpdated = true;

					if (string.IsNullOrEmpty(tourReference.ApiExternalTourID) && tourReference.TourID != null)
					{
						tourReference.ApiExternalTourID = ((int)tourReference.TourID).ToString();
					}

					apiExternalTourID = tourReference.ApiExternalTourID;
					tourReference.ApiExternalConnectionID = apiConnection.ApiExternalConnectionID.ToNullableString();

					if (apiConnection.ImportTourReferenceIgnoreUpdated || !tourReference.TourIDUpdated)
					{
						tourReference.TourID = null;
					}

					if (ApplicationSettingsService.EnableApiConnectionKeyLocationNamePrepend)
					{
						tourReference.LocationName = apiConnection.ApiConnectionKey + " " + tourReference.LocationName;
					}

					if (tourReference.PurchaseReferences != null)
					{
						foreach (PurchaseReference purchaseReference in tourReference.PurchaseReferences)
						{
							purchaseReference.ApiConnectionID = apiConnection.ApiConnectionID;
							purchaseReference.ApiConnectionIDUpdated = true;

							if (string.IsNullOrEmpty(purchaseReference.ApiExternalPurchaseID) && purchaseReference.PurchaseID != null)
							{
								purchaseReference.ApiExternalPurchaseID = ((int)purchaseReference.PurchaseID).ToString();
							}

							purchaseReference.ApiExternalConnectionID = apiConnection.ApiExternalConnectionID.ToNullableString();

							if (apiConnection.ImportTourReferenceIgnoreUpdated || !purchaseReference.PurchaseIDUpdated)
							{
								purchaseReference.PurchaseID = null;
							}
						}
					}

					List<string> errorMessages = new List<string>();
					//put object -> create new 
					tourReference.Action = MiddlewareService.actionMiddleware != null ? MiddlewareService.actionMiddleware:"PATCH";
					isValid = TourReferencesService.UpsertUpdatedTour(tourReference, errorMessages, apiConnection.UseTypeIDs,
						apiConnection.ImportTourReferenceIgnoreUpdated, apiConnection.ApiConnectionID,
						delegate(Tour tour, List<string> validateTourErrorMessages)
						{
							// TODO: Future import tour validation from apiconnection rules.
							return true;
						});

					if (!isValid)
					{
						DelimitedCollection<string> delimitedErrorMessages = new DelimitedCollection<string>(Environment.NewLine, errorMessages);

						MiddlewareErrorsDataAccess.Insert(MiddlewareErrorType.ImportTourReferenceInvalid, apiConnection.ApiConnectionID,
							importNumber, fileNamePath, delimitedErrorMessages.ToString(), false, apiExternalCustomerID, apiExternalTourID);
					}
				}
				catch (Exception exception)
				{
					MiddlewareErrorsDataAccess.Insert(MiddlewareErrorType.ImportTourReferenceException, apiConnection.ApiConnectionID,
						importNumber, fileNamePath, exception.ToString(), true, apiExternalCustomerID, apiExternalTourID);
				}
			}

			return isValid;
		}
	}
}