﻿CREATE TABLE [dbo].[Localizations] (
    [localizationID] INT            IDENTITY (1, 1) NOT NULL,
    [resourceSetKey] NVARCHAR (256) NOT NULL,
    [cultureName]    VARCHAR (10)   CONSTRAINT [DF_Localizations_cultureName] DEFAULT ('') NOT NULL,
    [resourceKey]    NVARCHAR (128) NOT NULL,
    [value]          NVARCHAR (MAX) NULL,
    [comment]        NVARCHAR (512) NULL,
    CONSTRAINT [PK_Localizations] PRIMARY KEY CLUSTERED ([localizationID] ASC),
    CONSTRAINT [UK_Localizations] UNIQUE NONCLUSTERED ([resourceSetKey] ASC, [cultureName] ASC, [resourceKey] ASC)
);


GO
CREATE TRIGGER [dbo].[Localizations.InsertUpdateDelete]
    ON [dbo].[Localizations]
    FOR INSERT, UPDATE, DELETE
AS 
BEGIN
    SET NOCOUNT ON;

    DECLARE @Activity  NVARCHAR (8), @id INT;

    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'updated'
    END

    IF EXISTS (SELECT * FROM inserted) AND NOT EXISTS(SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'inserted'
    END

    IF EXISTS (SELECT * FROM deleted) AND NOT EXISTS(SELECT * FROM inserted)
    BEGIN
        SET @Activity = 'deleted'
    END

    

    IF (@Activity = 'deleted')
    BEGIN
        DECLARE cur CURSOR FOR SELECT localizationID FROM deleted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'Localizations', @id, 'localizationID',
                (
                    SELECT *
                    FROM deleted i 
                    WHERE i.localizationID =  @id
                    FOR JSON AUTO
                ),0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
    ELSE
    BEGIN
        DECLARE cur CURSOR
        FOR SELECT localizationID
        FROM inserted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'Localizations', @id, 'localizationID',
                (
                    SELECT *
                    FROM inserted i 
                    WHERE i.localizationID =  @id
                    FOR JSON AUTO
                ), 0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
END

GO
DISABLE TRIGGER [dbo].[Localizations.InsertUpdateDelete]
    ON [dbo].[Localizations];

