﻿
CREATE FUNCTION [dbo].[GetStringForBitTrueOrFalse]
(
	@IntParameter bit,
	@ReplacementTrueStringParameter nvarchar(50),
	@ReplacementFalseStringParameter nvarchar(50)
)
RETURNS nvarchar(50)
AS
BEGIN
    
	DECLARE @ReturnValue nvarchar(50)

	SET @ReturnValue = NULL

	IF (@IntParameter = 1)
	BEGIN
		IF (@ReplacementTrueStringParameter IS NOT NULL)
			SET @ReturnValue = @ReplacementTrueStringParameter
	END
	ELSE
	BEGIN
		IF (@ReplacementFalseStringParameter IS NOT NULL)
			SET @ReturnValue = @ReplacementFalseStringParameter
	END
	
	RETURN @ReturnValue

END