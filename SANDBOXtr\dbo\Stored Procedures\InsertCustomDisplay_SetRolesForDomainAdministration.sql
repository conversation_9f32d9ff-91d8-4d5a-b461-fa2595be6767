﻿
CREATE PROCEDURE [dbo].[InsertCustomDisplay_SetRolesForDomainAdministration]

AS
BEGIN

	DECLARE @GroupTypeKey nvarchar(64) SET @GroupTypeKey = 'SetRolesForDomainAdministration'
	
	DELETE FROM CustomPageControlDisplayRights WHERE (customPageControlDisplayRightGroupTypeKey = @GroupTypeKey)

	DECLARE @Insert int SET @Insert = 1
	DECLARE @Select int SET @Select = 2
	DECLARE @Update int SET @Update = 3

	DECLARE @Add int SET @Add = 1
	DECLARE @Remove int SET @Remove = 2

	DECLARE @Visible int SET @Visible = 1
	DECLARE @Enabled int SET @Enabled = 2
	DECLARE @Text int SET @Text = 3
	DECLARE @Delegate int SET @Delegate = 4
	DECLARE @Authorized int SET @Authorized = 5
	DECLARE @Administrated int SET @Administrated = 6

	DECLARE @PagePathID [varchar] (256)

	
	SET @PagePathID = 'ASP.administration_default_aspx'
	
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Sales Director','linkTeams',@Visible,0,NULL,NULL
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Sales Director','linkGifts',@Visible,0,NULL,NULL
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Sales Director','linkVenues',@Visible,0,NULL,NULL
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Sales Director','linkCampaigns',@Visible,0,NULL,NULL
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Sales Director','linkHotels',@Visible,0,NULL,NULL
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Sales Director','linkProducts',@Visible,0,NULL,NULL

	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Remove,@PagePathID,@Select,'Office Manager','linkUsers',@Visible,0,NULL,NULL
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Office Manager','linkTeams',@Visible,0,NULL,NULL
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Office Manager','linkGifts',@Visible,0,NULL,NULL
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Office Manager','linkVenues',@Visible,0,NULL,NULL
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Office Manager','linkCampaigns',@Visible,0,NULL,NULL
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Office Manager','linkHotels',@Visible,0,NULL,NULL
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Office Manager','linkProducts',@Visible,0,NULL,NULL
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Sales Team Leader','form1',@Authorized,0,NULL,NULL

	
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Remove,@PagePathID,@Select,'Sales Director','panelExport',@Visible,0,NULL,NULL

	SET @PagePathID = 'ASP.administration_users_usersmaster_aspx'
	
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Remove,@PagePathID,@Select,'Office Manager','form1',@Authorized,0,NULL,NULL
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Sales Team Leader','form1',@Authorized,0,NULL,NULL

	SET @PagePathID = 'ASP.administration_users_usersdetails_aspx'
	
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Remove,@PagePathID,@Select,'Office Manager','form1',@Authorized,0,NULL,NULL
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Sales Team Leader','form1',@Authorized,0,NULL,NULL
	
	SET @PagePathID = 'ASP.administration_locations_locationsdetails_aspx'
	
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Sales Director','locationsGifts',@Visible,0,NULL,NULL
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Sales Director','locationsVenues',@Visible,0,NULL,NULL
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Sales Director','locationsHotels',@Visible,0,NULL,NULL
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Office Manager','locationsGifts',@Visible,0,NULL,NULL
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Office Manager','locationsVenues',@Visible,0,NULL,NULL
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Office Manager','locationsHotels',@Visible,0,NULL,NULL

	SET @PagePathID = 'ASP.administration_productsmaster_aspx'
	
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Sales Director','form1',@Authorized,0,NULL,NULL
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Office Manager','form1',@Authorized,0,NULL,NULL
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Sales Team Leader','form1',@Authorized,0,NULL,NULL

	SET @PagePathID = 'ASP.administration_teamsmaster_aspx'
	
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Sales Director','form1',@Authorized,0,NULL,NULL
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Office Manager','form1',@Authorized,0,NULL,NULL

	SET @PagePathID = 'ASP.administration_giftsmaster_aspx'
	
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Sales Director','form1',@Authorized,0,NULL,NULL
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Office Manager','form1',@Authorized,0,NULL,NULL

	SET @PagePathID = 'ASP.administration_venuesmaster_aspx'
	
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Sales Director','form1',@Authorized,0,NULL,NULL
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Office Manager','form1',@Authorized,0,NULL,NULL

	SET @PagePathID = 'ASP.administration_campaignsmaster_aspx'
	
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Sales Director','form1',@Authorized,0,NULL,NULL
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Office Manager','form1',@Authorized,0,NULL,NULL

	SET @PagePathID = 'ASP.administration_hotelsmaster_aspx'
	
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Sales Director','form1',@Authorized,0,NULL,NULL
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Office Manager','form1',@Authorized,0,NULL,NULL

	SET @PagePathID = 'ASP.administration_importexport_tourstocsv_aspx'

	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Remove,@PagePathID,@Select,'Sales Director','form1',@Authorized,0,NULL,NULL


END