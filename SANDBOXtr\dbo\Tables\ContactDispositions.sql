﻿CREATE TABLE [dbo].[ContactDispositions] (
    [contactDispositionID]   INT           IDENTITY (1, 1) NOT NULL,
    [contactDispositionName] NVARCHAR (64) NOT NULL,
    [contactDispositionCode] NVARCHAR (64) NOT NULL,
    [sortOrder]              INT           NOT NULL,
    [active]                 BIT           NOT NULL,
    CONSTRAINT [PK_ContactDispositions] PRIMARY KEY CLUSTERED ([contactDispositionID] ASC),
    CONSTRAINT [UK_ContactDispositions_contactDispositionName] UNIQUE NONCLUSTERED ([contactDispositionName] ASC)
);


GO
CREATE TRIGGER [dbo].[ContactDispositions.InsertUpdateDelete]
    ON [dbo].[ContactDispositions]
    FOR INSERT, UPDATE, DELETE
AS 
BEGIN
    SET NOCOUNT ON;

    DECLARE @Activity  NVARCHAR (8), @id INT;

    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'updated'
    END

    IF EXISTS (SELECT * FROM inserted) AND NOT EXISTS(SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'inserted'
    END

    IF EXISTS (SELECT * FROM deleted) AND NOT EXISTS(SELECT * FROM inserted)
    BEGIN
        SET @Activity = 'deleted'
    END

    

    IF (@Activity = 'deleted')
    BEGIN
        DECLARE cur CURSOR FOR SELECT contactDispositionID FROM deleted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'ContactDispositions', @id, 'contactDispositionID',
                (
                    SELECT *
                    FROM deleted i 
                    WHERE i.contactDispositionID =  @id
                    FOR JSON AUTO
                ),0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
    ELSE
    BEGIN
        DECLARE cur CURSOR
        FOR SELECT contactDispositionID
        FROM inserted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'ContactDispositions', @id, 'contactDispositionID',
                (
                    SELECT *
                    FROM inserted i 
                    WHERE i.contactDispositionID =  @id
                    FOR JSON AUTO
                ), 0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
END

GO
DISABLE TRIGGER [dbo].[ContactDispositions.InsertUpdateDelete]
    ON [dbo].[ContactDispositions];

