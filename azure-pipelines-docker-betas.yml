trigger: none

pool:
  vmImage: 'windows-2019'

variables:
  solution: '**/*.sln'
  buildPlatform: 'Any CPU'
  buildConfiguration: 'Release'

steps:
# 1) Install Nuget Tool
- task: NuGetToolInstaller@1
  displayName: 'Install Nuget Tool'

# 2) Restore Nuget Packages 
- task: NuGetCommand@2
  displayName: 'Restore Nuget Packages'
  inputs:
    restoreSolution: '$(solution)'

# 3) Compile Solution
- task: VSBuild@1
  displayName: 'Compile Solution'
  inputs:
    solution: '$(solution)'
    msbuildArgs: '/p:DeployOnBuild=true /p:WebPublishMethod=Package /p:PackageAsSingleFile=true /p:SkipInvalidConfigurations=true /p:PackageLocation="$(Build.ArtifactStagingDirectory)"'
    platform: '$(buildPlatform)'
    configuration: '$(buildConfiguration)'

# 4) Publish .ZIP file generate like artifact
- task: PublishBuildArtifacts@1
  displayName: 'Publish .ZIP file generate like artifact'
  inputs:
    PathtoPublish: '$(Build.ArtifactStagingDirectory)'
    ArtifactName: 'drop'
    publishLocation: 'Container'

# 5) Create folders 
- task: PowerShell@2
  displayName: 'Create folder to path obj\Docker\publish'
  inputs:
    targetType: 'inline'
    script: |
      $publishPath = "$(Build.SourcesDirectory)/Web/obj/Docker/publish"
      if (-not (Test-Path $publishPath)) {
        New-Item -Path $publishPath -ItemType Directory -Force
        Write-Host "Folder create: $publishPath"
      } else {
        Write-Host "Folder exists previously: $publishPath"
      }

# 6) Publish and Copy solution into obj/Docker/publish
- task: VSBuild@1
  displayName: 'Publish into Web/obj/Docker/publish'
  inputs:
    solution: '**/*.sln'
    platform: 'Any CPU'
    configuration: 'Release'
    msbuildArgs: '/p:WebPublishMethod=FileSystem /p:DeleteExistingFiles=True /p:OutputPath=$(Build.SourcesDirectory)\Web\obj\Docker\publish'


# 7) Verify the files into obj/Docker/publish
- task: PowerShell@2
  displayName: 'Verify and show the files into obj/Docker/publish'
  inputs:
    targetType: 'inline'
    script: |
      $publishPath = "$(Build.SourcesDirectory)\Web\obj\Docker\publish"
      if (Test-Path $publishPath) {
        Write-Host "La carpeta existe. Listando archivos:"
        Get-ChildItem -Path $publishPath -Recurse
      } else {
        Write-Host "The file don't exits. Check the task if working correctly."
        exit 1
      }

# 8) Build and Push docker imagen
- task: Docker@2
  displayName: 'Docker Build & Push'
  inputs:
    containerRegistry: 'cicddockerservice'            # Ajusta con tu Service Connection
    repository: 'cicddockermx/trackresultsweb'        # Ajusta con tu repo en ACR
    command: 'buildAndPush'
    Dockerfile: 'Web/Dockerfile'                      # Ajusta si tu Dockerfile está en otra carpeta
    buildContext: '$(Build.SourcesDirectory)\Web'     # La carpeta donde vive 'obj/Docker/publish'
    tags: |
      $(Build.BuildId)
      latest
