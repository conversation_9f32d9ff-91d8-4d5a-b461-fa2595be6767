﻿CREATE TYPE [dbo].[CustomerType] AS TABLE (
    [customerID]               INT            NULL,
    [externalCustomerID]       NVARCHAR (128) NULL,
    [customerTypeID]           INT            NULL,
    [customerStatusTypeID]     INT            NULL,
    [customerDispositionID]    INT            NULL,
    [firstName]                NVARCHAR (64)  NULL,
    [lastName]                 NVARCHAR (64)  NULL,
    [age]                      INT            NULL,
    [sex]                      BIT            NULL,
    [customersPickList1ItemID] INT            NULL,
    [guestFirstName]           NVARCHAR (64)  NULL,
    [guestLastName]            NVARCHAR (64)  NULL,
    [guestTypeID]              INT            NULL,
    [guestAge]                 INT            NULL,
    [guestSex]                 BIT            NULL,
    [customersPickList2ItemID] INT            NULL,
    [incomeTypeID]             INT            NULL,
    [customersPickList3ItemID] INT            NULL,
    [customersText1]           NVARCHAR (512) NULL,
    [customersDecimal1]        DECIMAL (9, 2) NULL,
    [customersBool1]           BIT            NULL,
    [customersDate1]           DATETIME       NULL,
    [customersTime1]           DATETIME       NULL,
    [doNotCall]                BIT            NULL,
    [primaryPhone]             NVARCHAR (32)  NULL,
    [secondaryPhone]           NVARCHAR (32)  NULL,
    [streetAddress]            NVARCHAR (128) NULL,
    [streetAddress2]           NVARCHAR (128) NULL,
    [city]                     NVARCHAR (64)  NULL,
    [stateID]                  INT            NULL,
    [zipcode]                  NVARCHAR (16)  NULL,
    [countryID]                INT            NULL,
    [businessName]             NVARCHAR (64)  NULL,
    [email]                    NVARCHAR (128) NULL,
    [customersPickList4ItemID] INT            NULL,
    [customersText2]           NVARCHAR (512) NULL,
    [customersText3]           NVARCHAR (512) NULL,
    [customersDecimal2]        DECIMAL (9, 2) NULL,
    [customersBool2]           BIT            NULL,
    [customersDate2]           DATETIME       NULL,
    [customersTime2]           DATETIME       NULL,
    [apiConnectionID]          INT            NULL,
    [apiExternalCustomerID]    NVARCHAR (128) NULL,
    [apiExternalConnectionID]  NVARCHAR (128) NULL,
    [insertTimeStamp]          DATETIME       NULL,
    [updateTimeStamp]          DATETIME       NULL,
    [city2]                    NVARCHAR (64)  NULL,
    [stateID2]                 INT            NULL,
    [zipcode2]                 NVARCHAR (16)  NULL,
    [countryID2]               INT            NULL,
    [userAddDoNotCall]         NVARCHAR (50)  NULL,
    [insertDoNotCall]          DATETIME       NULL,
    [streetAddressGuest]       NVARCHAR (128) NULL,
    [streetAddress2Guest]      NVARCHAR (128) NULL,
    [emailGuest]               NVARCHAR (128) NULL);

