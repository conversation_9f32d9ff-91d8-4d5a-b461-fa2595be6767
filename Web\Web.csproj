﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="12.0">
  <Import Project="..\packages\Microsoft.VisualStudio.Azure.Containers.Tools.Targets.1.21.0\build\Microsoft.VisualStudio.Azure.Containers.Tools.Targets.props" Condition="Exists('..\packages\Microsoft.VisualStudio.Azure.Containers.Tools.Targets.1.21.0\build\Microsoft.VisualStudio.Azure.Containers.Tools.Targets.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>9.0.30729</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{EF70239E-785F-4673-A383-552CF1EBD3E9}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>TrackResults.Web</RootNamespace>
    <AssemblyName>TrackResults.Web</AssemblyName>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <OldToolsVersion>4.0</OldToolsVersion>
    <UpgradeBackupLocation>
    </UpgradeBackupLocation>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <TargetFrameworkProfile />
    <UseIISExpress>true</UseIISExpress>
    <IISExpressSSLPort>44302</IISExpressSSLPort>
    <IISExpressAnonymousAuthentication>enabled</IISExpressAnonymousAuthentication>
    <IISExpressWindowsAuthentication>disabled</IISExpressWindowsAuthentication>
    <IISExpressUseClassicPipelineMode>false</IISExpressUseClassicPipelineMode>
    <UseGlobalApplicationHostFile />
    <Use64BitIISExpress />
    <WebGreaseLibPath>..\packages\WebGrease.1.5.2\lib</WebGreaseLibPath>
    <DockerLaunchAction>LaunchBrowser</DockerLaunchAction>
    <DockerLaunchUrl>http://{ServiceIPAddress}</DockerLaunchUrl>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="ClosedXML, Version=0.70.0.0, Culture=neutral, PublicKeyToken=fd1eb21b62ae805b, processorArchitecture=MSIL">
      <HintPath>..\packages\ClosedXML.0.70.0\lib\net40-client\ClosedXML.dll</HintPath>
    </Reference>
    <Reference Include="ExtendedObjectDataSource">
      <HintPath>..\Common\Libraries\ExtendedObjectDataSource.dll</HintPath>
    </Reference>
    <Reference Include="HughAxton.Core">
      <HintPath>..\Common\Libraries\HughAxton.Core.dll</HintPath>
    </Reference>
    <Reference Include="HughAxton.WebControls">
      <HintPath>..\Common\Libraries\HughAxton.WebControls.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Microsoft.Practices.EnterpriseLibrary.Common, Version=3.1.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL" />
    <Reference Include="Microsoft.Practices.EnterpriseLibrary.Data">
      <HintPath>..\Common\Libraries\Microsoft.Practices.EnterpriseLibrary.Data.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Practices.EnterpriseLibrary.ExceptionHandling, Version=3.1.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL" />
    <Reference Include="Microsoft.Practices.EnterpriseLibrary.Logging, Version=3.1.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL" />
    <Reference Include="Microsoft.Practices.EnterpriseLibrary.Logging.Database">
      <HintPath>..\Common\Libraries\Microsoft.Practices.EnterpriseLibrary.Logging.Database.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Web.Infrastructure, Version=1.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Web.Infrastructure.1.0.0.0\lib\net40\Microsoft.Web.Infrastructure.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="Renci.SshNet, Version=2020.0.2.0, Culture=neutral, PublicKeyToken=1cee9f8bde3db106, processorArchitecture=MSIL">
      <HintPath>..\packages\SSH.NET.2020.0.2\lib\net40\Renci.SshNet.dll</HintPath>
    </Reference>
    <Reference Include="RestSharp, Version=105.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RestSharp.105.0.0\lib\net4\RestSharp.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.IO" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Runtime" />
    <Reference Include="System.Runtime.Caching" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Security.Cryptography.Algorithms" />
    <Reference Include="System.Security.Cryptography.Encoding" />
    <Reference Include="System.Security.Cryptography.Primitives" />
    <Reference Include="System.Security.Cryptography.X509Certificates" />
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.Transactions" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Web.Extensions.Design" />
    <Reference Include="System.Web.Optimization, Version=1.1.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Web.Optimization.1.1.0\lib\net40\System.Web.Optimization.dll</HintPath>
    </Reference>
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="System.Web.Mobile" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="Telerik.Web.Design, Version=2013.3.1114.45, Culture=neutral, PublicKeyToken=121fae78165ba3d4, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Common\Libraries\Telerik.Web.Design.dll</HintPath>
    </Reference>
    <Reference Include="Telerik.Web.UI, Version=2015.2.826.45, Culture=neutral, PublicKeyToken=121fae78165ba3d4, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Telerik.Web.UI.dll</HintPath>
    </Reference>
    <Reference Include="Telerik.Web.UI.Skins">
      <HintPath>..\Common\Libraries\Telerik.Web.UI.Skins.dll</HintPath>
    </Reference>
    <Reference Include="TrackResults.ServiceModel">
      <HintPath>..\Common\Libraries\TrackResults.ServiceModel.dll</HintPath>
    </Reference>
    <Reference Include="WebGrease, Version=1.5.2.14234, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\WebGrease.1.5.2\lib\WebGrease.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Account\EulaAcceptance.aspx" />
    <Content Include="Administration\ImportExport\ToursToCSV.aspx" />
    <Content Include="Administration\Middleware\ApiConnectionsMiddlewareErrorsMaster.aspx" />
    <Content Include="Administration\Middleware\UserControls\ApiConnectionsMaster.ascx" />
    <Content Include="Administration\PickLists\CancellationStatusTypesMaster.aspx" />
    <Content Include="Administration\PickLists\CancellationDispositionTypesMaster.aspx" />
    <Content Include="Administration\PickLists\CancellationReceivedTypesMaster.aspx" />
    <Content Include="Administration\PickLists\CancellationReasonTypesMaster.aspx" />
    <Content Include="Administration\PickLists\Default\CancellationsPickLists.aspx" />
    <Content Include="Administration\PickLists\Default\ToursCallsPickLists.aspx" />
    <Content Include="Administration\PickLists\Default\ToursLeadsPickLists.aspx" />
    <Content Include="Administration\PickLists\Default\CustomersPickLists.aspx" />
    <Content Include="Administration\PickLists\Default\PurchasesPickLists.aspx" />
    <Content Include="Administration\System\SyncSchema.aspx" />
    <Content Include="Administration\System\APIConnectionDetails.aspx" />
    <Content Include="Administration\System\ApiConnectionMapping.aspx" />
    <Content Include="Administration\System\APIConnectionMappingRequest.aspx" />
    <Content Include="Administration\System\DefinedTypes\SaleStatusTypesMaster.aspx" />
    <Content Include="Administration\System\DefinedTypes\SaleTypesMaster.aspx" />
    <Content Include="Administration\ImportExport\LeadsCallsFromCsv.aspx" />
    <Content Include="Administration\ImportExport\LeadsFromCsv.aspx" />
    <Content Include="Administration\ImportExport\ToursFromCsv.aspx" />
    <Content Include="Administration\ImportExport\UserControls\ImportMappings.ascx" />
    <Content Include="Administration\ImportExport\UserControls\ExportMappings.ascx" />
    <Content Include="Administration\PickLists\CallBackPickList1ItemsMaster.aspx" />
    <Content Include="Administration\PickLists\ChannelsMaster.aspx" />
    <Content Include="Administration\PickLists\ContactDispositionsMaster.aspx" />
    <Content Include="Administration\PickLists\Default\ToursMarketingPickLists.aspx" />
    <Content Include="Administration\PickLists\Default\ToursSalesPickLists.aspx" />
    <Content Include="Administration\PickLists\Default\ToursToursPickLists.aspx" />
    <Content Include="Administration\PickLists\GiftsMaster.aspx" />
    <Content Include="Administration\PickLists\LeadPickList1ItemsMaster.aspx" />
    <Content Include="Administration\PickLists\LeadPickList2ItemsMaster.aspx" />
    <Content Include="Administration\PickLists\TourTypePickList1ItemsMaster.aspx" />
    <Content Include="Administration\PickLists\TourSourcesMaster.aspx" />
    <Content Include="Administration\PickLists\LeadTypesMaster.aspx" />
    <Content Include="Administration\PickLists\LeadSourcesMaster.aspx" />
    <Content Include="Administration\PickLists\TourTypesMaster.aspx" />
    <Content Include="Administration\PickLists\ToursManyPickList3ItemsMaster.aspx" />
    <Content Include="Administration\PickLists\ToursManyPickList2ItemsMaster.aspx" />
    <Content Include="Administration\PickLists\ToursManyPickList1ItemsMaster.aspx" />
    <Content Include="Administration\PickLists\TourConcernTypesMaster.aspx" />
    <Content Include="Administration\PickLists\SubProductsMaster.aspx" />
    <Content Include="Administration\PickLists\RegionsMaster.aspx" />
    <Content Include="Administration\PickLists\SaleDispositionsMaster.aspx" />
    <Content Include="Administration\PickLists\SalesPickList1ItemsMaster.aspx" />
    <Content Include="Administration\PickLists\PurchasesManyPickList2ItemsMaster.aspx" />
    <Content Include="Administration\PickLists\PurchasesPickList3ItemsMaster.aspx" />
    <Content Include="Administration\PickLists\PurchasesManyPickList1ItemsMaster.aspx" />
    <Content Include="Administration\PickLists\ProductCategoriesMaster.aspx" />
    <Content Include="Administration\PickLists\MarketingPickList2ItemsMaster.aspx" />
    <Content Include="Administration\PickLists\LeadDispositionsMaster.aspx" />
    <Content Include="Administration\PickLists\LeadStatusTypesMaster.aspx" />
    <Content Include="Administration\PickLists\IncomeTypesMaster.aspx" />
    <Content Include="Administration\PickLists\GuestTypesMaster.aspx" />
    <Content Include="Administration\PickLists\CustomerTypesMaster.aspx" />
    <Content Include="Administration\PickLists\CustomerStatusTypesMaster.aspx" />
    <Content Include="Administration\PickLists\CustomersManyPickList2ItemsMaster.aspx" />
    <Content Include="Administration\PickLists\CustomersPickList3ItemsMaster.aspx" />
    <Content Include="Administration\PickLists\CustomersPickList4ItemsMaster.aspx" />
    <Content Include="Administration\PickLists\CustomersManyPickList1ItemsMaster.aspx" />
    <Content Include="Administration\PickLists\CustomerDispositionsMaster.aspx" />
    <Content Include="Administration\PickLists\ContactStatusTypesMaster.aspx" />
    <Content Include="Administration\PickLists\CustomersPickList1ItemsMaster.aspx" />
    <Content Include="Administration\PickLists\CustomersPickList2ItemsMaster.aspx" />
    <Content Include="Administration\PickLists\MarketingPickList1ItemsMaster.aspx" />
    <Content Include="Administration\PickLists\PurchasesPickList1ItemsMaster.aspx" />
    <Content Include="Administration\PickLists\PurchasesPickList2ItemsMaster.aspx" />
    <Content Include="Administration\System\ApiConnectorsDetails.aspx" />
    <Content Include="Administration\System\ApiConnectorsMaster.aspx" />
    <Content Include="Administration\System\CacheManagement.aspx" />
    <Content Include="Administration\System\CalculatedKpis\CalculatedKpiMaster.aspx" />
    <Content Include="Administration\System\CustomAnalyticViews\CustomAnalyticViewsDetails.aspx" />
    <Content Include="Administration\System\CustomAnalyticViews\CustomAnalyticViewsKpisDetails.aspx" />
    <Content Include="Administration\System\CustomAnalyticViews\CustomAnalyticViewsKpisMaster.aspx" />
    <Content Include="Administration\System\CustomAnalyticViews\CustomAnalyticViewsMaster.aspx" />
    <Content Include="Administration\System\Dashboards\DashboardsDetails.aspx" />
    <Content Include="Administration\System\Dashboards\DashboardsMaster.aspx" />
    <Content Include="Administration\System\Deployments.aspx" />
    <Content Include="Administration\System\Goals\GoalsDetails.aspx" />
    <Content Include="Administration\System\Goals\GoalsMaster.aspx" />
    <Content Include="Administration\System\LocalizationsMaster.aspx" />
    <Content Include="Administration\System\ManageCustomReportsCacheMaster.aspx" />
    <Content Include="Administration\System\PropertyCustomization\DeploymentCustomization.aspx" />
    <Content Include="Administration\System\PropertyCustomization\ManageUsersPropertyCustomization.aspx" />
    <Content Include="Administration\System\RolesMaster.aspx" />
    <Content Include="Administration\ApiConnectionsDataMappings.aspx" />
    <Content Include="Administration\System\ApiConnectionsDetails.aspx" />
    <Content Include="Administration\System\ApiConnectionsMaster.aspx" />
    <Content Include="Administration\System\ApplicationSettings.aspx" />
    <Content Include="Administration\System\EulasMaster.aspx" />
    <Content Include="Administration\System\Impersonation.aspx" />
    <Content Include="Administration\System\PropertyCustomization\Default.aspx" />
    <Content Include="Administration\System\PropertyCustomization\ManagePropertyCustomization.aspx" />
    <Content Include="Administration\System\PropertyCustomization\ManageRolesPropertyCustomization.aspx" />
    <Content Include="Administration\System\SchedulerMaster.aspx" />
    <Content Include="Administration\System\SiteContent\SiteContentPartsSimpleDetails.aspx" />
    <Content Include="Administration\System\SiteContent\SiteContentPartsSimpleMaster.aspx" />
    <Content Include="Administration\MiddlewareErrorsDataMappings.aspx" />
    <Content Include="Administration\System\MiddlewareErrorsMaster.aspx" />
    <Content Include="Administration\System\SiteContent\SiteContentFilesMaster.aspx" />
    <Content Include="Administration\System\SiteContent\SiteContentPagesDetails.aspx" />
    <Content Include="Administration\System\SiteContent\SiteContentPagesMaster.aspx" />
    <Content Include="Administration\System\SiteContent\SiteContentPartsDetails.aspx" />
    <Content Include="Administration\System\SiteContent\SiteContentPartsMaster.aspx" />
    <Content Include="Administration\System\WebAccountsDetails.aspx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Administration\Users\UserAudits\AuditLogin.ascx" />
    <Content Include="Administration\Users\UserAudits\Audits.ascx" />
    <Content Include="AgGrid\ag-grid-community.js" />
    <Content Include="AgGrid\ag-grid.css" />
    <Content Include="AgGrid\ag-theme-alpine.css" />
    <Content Include="AgGrid\ag-theme-alpine.min.css" />
    <Content Include="AnalyticViews\AnalyticView.aspx" />
    <Content Include="ApiServices\25\ToursService.svc" />
    <Content Include="ApiServices\26\ToursService.svc" />
    <Content Include="ApiServices\27\ToursService.svc" />
    <Content Include="ApiServices\29\Sample XML Requests\AddTourRequest.xml" />
    <Content Include="ApiServices\29\Sample XML Requests\CreateOrUpdateCustomerRequest.xml" />
    <Content Include="ApiServices\29\Sample XML Requests\CreateOrUpdateExternalCustomerRequest.xml" />
    <Content Include="ApiServices\29\Sample XML Requests\CreateOrUpdateExternalPurchaseRequest.xml" />
    <Content Include="ApiServices\29\Sample XML Requests\CreateOrUpdateExternalTourRequest.xml" />
    <Content Include="ApiServices\29\Sample XML Requests\CreateOrUpdatePurchaseRequest.xml" />
    <Content Include="ApiServices\29\Sample XML Requests\CreateOrUpdateTourRequest.xml" />
    <Content Include="ApiServices\29\Sample XML Requests\GetNextPagedToursRequest.xml" />
    <Content Include="ApiServices\29\Sample XML Requests\GetPagedToursRequest.xml" />
    <Content Include="ApiServices\29\Sample XML Requests\GetPagedToursSinceLastUpdatedTimeRequest.xml" />
    <Content Include="ApiServices\29\Sample XML Requests\GetTourByTourIDRequest.xml" />
    <Content Include="ApiServices\29\Sample XML Requests\TrackResults29_ToursServiceOverview.PNG" />
    <Content Include="ApiServices\29\SystemService.svc" />
    <Content Include="ApiServices\29\ReportsService.svc" />
    <Content Include="ApiServices\29\ToursService.svc" />
    <Content Include="ApiServices\28\SystemService.svc" />
    <Content Include="ApiServices\28\ToursService.svc" />
    <Content Include="apple-touch-icon.png" />
    <Content Include="content\AdHocAdmin.css" />
    <Content Include="content\custom-ag-grid.css" />
    <Content Include="content\d\fonts\fontawesome-webfont.svg" />
    <Content Include="content\d\fonts\glyphicons-halflings-3-1-0-2\glyphicons-halflings-regular.svg" />
    <Content Include="content\d\fonts\glyphicons-halflings-3-3-5\glyphicons-halflings-regular.svg" />
    <Content Include="content\d\fonts\glyphicons-regular.svg" />
    <Content Include="content\d\fonts\glyphicons-halflings-3-3-5\glyphicons-halflings-regular.eot" />
    <Content Include="content\d\fonts\glyphicons-halflings-3-3-5\glyphicons-halflings-regular.ttf" />
    <Content Include="content\d\fonts\glyphicons-halflings-3-3-5\glyphicons-halflings-regular.woff" />
    <Content Include="content\d\fonts\glyphicons-halflings-3-3-5\glyphicons-halflings-regular.woff2" />
    <Content Include="content\d\less\glyphicons-halflings\glyphicons-halflings-3.3.5.less" />
    <None Include=".dockerignore">
      <DependentUpon>Dockerfile</DependentUpon>
    </None>
    <None Include="content\d\less\gridster\jquery.gridster.less" />
    <Content Include="content\d\scripts\bootstrap-3.3.4.min.js" />
    <Content Include="Administration\Web.config">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="Administration\System\SiteContent\Web.config" />
    <Content Include="content\d\less\glyphicons_pro\glyphicons.less" />
    <Content Include="content\d\fonts\glyphicons-regular.eot" />
    <Content Include="content\d\fonts\glyphicons-regular.ttf" />
    <Content Include="content\d\fonts\glyphicons-regular.woff" />
    <Content Include="content\d\fonts\glyphicons-regular.woff2" />
    <Content Include="content\d\fonts\fontawesome-webfont.eot" />
    <Content Include="content\d\fonts\fontawesome-webfont.ttf" />
    <Content Include="content\d\fonts\fontawesome-webfont.woff" />
    <Content Include="content\d\fonts\fontawesome-webfont.woff2" />
    <Content Include="content\d\fonts\FontAwesome.otf" />
    <Content Include="content\d\less\font-awesome\animated.less" />
    <Content Include="content\d\less\font-awesome\bordered-pulled.less" />
    <Content Include="content\d\less\font-awesome\core.less" />
    <Content Include="content\d\less\font-awesome\fixed-width.less" />
    <Content Include="content\d\less\font-awesome\font-awesome.less" />
    <Content Include="content\d\less\font-awesome\icons.less" />
    <Content Include="content\d\less\font-awesome\larger.less" />
    <Content Include="content\d\less\font-awesome\list.less" />
    <Content Include="content\d\less\font-awesome\mixins.less" />
    <Content Include="content\d\less\font-awesome\path.less" />
    <Content Include="content\d\less\font-awesome\rotated-flipped.less" />
    <Content Include="content\d\less\font-awesome\stacked.less" />
    <Content Include="content\d\less\font-awesome\variables.less" />
    <Content Include="content\d\less\icons.less" />
    <Content Include="content\d\less\bootstrap\less\mixins\alerts.less" />
    <Content Include="content\d\less\bootstrap\less\mixins\background-variant.less" />
    <Content Include="content\d\less\bootstrap\less\mixins\border-radius.less" />
    <Content Include="content\d\less\bootstrap\less\mixins\buttons.less" />
    <Content Include="content\d\less\bootstrap\less\mixins\center-block.less" />
    <Content Include="content\d\less\bootstrap\less\mixins\clearfix.less" />
    <Content Include="content\d\less\bootstrap\less\mixins\forms.less" />
    <Content Include="content\d\less\bootstrap\less\mixins\gradients.less" />
    <Content Include="content\d\less\bootstrap\less\mixins\grid-framework.less" />
    <Content Include="content\d\less\bootstrap\less\mixins\grid.less" />
    <Content Include="content\d\less\bootstrap\less\mixins\hide-text.less" />
    <Content Include="content\d\less\bootstrap\less\mixins\image.less" />
    <Content Include="content\d\less\bootstrap\less\mixins\labels.less" />
    <Content Include="content\d\less\bootstrap\less\mixins\list-group.less" />
    <Content Include="content\d\less\bootstrap\less\mixins\nav-divider.less" />
    <Content Include="content\d\less\bootstrap\less\mixins\nav-vertical-align.less" />
    <Content Include="content\d\less\bootstrap\less\mixins\opacity.less" />
    <Content Include="content\d\less\bootstrap\less\mixins\pagination.less" />
    <Content Include="content\d\less\bootstrap\less\mixins\panels.less" />
    <Content Include="content\d\less\bootstrap\less\mixins\progress-bar.less" />
    <Content Include="content\d\less\bootstrap\less\mixins\reset-filter.less" />
    <Content Include="content\d\less\bootstrap\less\mixins\resize.less" />
    <Content Include="content\d\less\bootstrap\less\mixins\responsive-visibility.less" />
    <Content Include="content\d\less\bootstrap\less\mixins\size.less" />
    <Content Include="content\d\less\bootstrap\less\mixins\tab-focus.less" />
    <Content Include="content\d\less\bootstrap\less\mixins\table-row.less" />
    <Content Include="content\d\less\bootstrap\less\mixins\text-emphasis.less" />
    <Content Include="content\d\less\bootstrap\less\mixins\text-overflow.less" />
    <Content Include="content\d\less\bootstrap\less\mixins\vendor-prefixes.less" />
    <Content Include="content\d\less\bootstrap\less\responsive-embed.less" />
    <Content Include="content\d\fonts\glyphicons-halflings-3-1-0-2\glyphicons-halflings-regular.eot" />
    <Content Include="content\d\fonts\glyphicons-halflings-3-1-0-2\glyphicons-halflings-regular.ttf" />
    <Content Include="content\d\fonts\glyphicons-halflings-3-1-0-2\glyphicons-halflings-regular.woff" />
    <Content Include="content\d\fonts\glyphicons-halflings-3-1-0-2\glyphicons-halflings-regular.woff2" />
    <Content Include="content\d\scripts\jquery.gridster-0.5.6-custom-1.js" />
    <Content Include="content\d\scripts\reports-21.js" />
    <Content Include="content\jquery-ui-1.13.2.custom\AUTHORS.txt" />
    <Content Include="content\jquery-ui-1.13.2.custom\external\jquery\jquery.js" />
    <Content Include="content\jquery-ui-1.13.2.custom\images\ui-icons_444444_256x240.png" />
    <Content Include="content\jquery-ui-1.13.2.custom\images\ui-icons_555555_256x240.png" />
    <Content Include="content\jquery-ui-1.13.2.custom\images\ui-icons_777620_256x240.png" />
    <Content Include="content\jquery-ui-1.13.2.custom\images\ui-icons_777777_256x240.png" />
    <Content Include="content\jquery-ui-1.13.2.custom\images\ui-icons_cc0000_256x240.png" />
    <Content Include="content\jquery-ui-1.13.2.custom\images\ui-icons_ffffff_256x240.png" />
    <Content Include="content\jquery-ui-1.13.2.custom\index.html" />
    <Content Include="content\jquery-ui-1.13.2.custom\jquery-ui.css" />
    <Content Include="content\jquery-ui-1.13.2.custom\jquery-ui.js" />
    <Content Include="content\jquery-ui-1.13.2.custom\jquery-ui.min.css" />
    <Content Include="content\jquery-ui-1.13.2.custom\jquery-ui.min.js" />
    <Content Include="content\jquery-ui-1.13.2.custom\jquery-ui.structure.css" />
    <Content Include="content\jquery-ui-1.13.2.custom\jquery-ui.structure.min.css" />
    <Content Include="content\jquery-ui-1.13.2.custom\jquery-ui.theme.css" />
    <Content Include="content\jquery-ui-1.13.2.custom\jquery-ui.theme.min.css" />
    <Content Include="content\jquery-ui-1.13.2.custom\LICENSE.txt" />
    <Content Include="content\lodash.min.js" />
    <Content Include="content\sweetalert2\sweet-alert.js" />
    <Content Include="content\sweetalert2\sweetalert2.css" />
    <Content Include="content\sweetalert2\sweetalert2.js" />
    <Content Include="content\ui.dropdownchecklist.js" />
    <Content Include="content\ui.dropdownchecklist.standalone.css" />
    <Content Include="content\ui.dropdownchecklist.themeroller.css" />
    <Content Include="Customers\customers_popup.css" />
    <Content Include="Customers\customers_popup.js" />
    <Content Include="Customers\Tours\UserControls\ToursDetailsExit.ascx" />
    <Content Include="Customers\Tours\UserControls\ToursDetailsForm.ascx" />
    <Content Include="dbscript.txt" />
    <Content Include="DLLs\Telerik.Web.Design.dll" />
    <Content Include="DLLs\Telerik.Web.UI.dll" />
    <Content Include="DLLs\Telerik.Web.UI.Skins.dll" />
    <Content Include="Errors\MaintenanceError.html" />
    <Content Include="ExtensionDlls\BreckenridgeGrand.Extensions.dll" />
    <Content Include="ExtensionDlls\BreckenridgeGrand.Extensions.pdb" />
    <Content Include="ExtensionDlls\ClubTesoro.Extensions.dll" />
    <Content Include="ExtensionDlls\ClubTesoro.Extensions.pdb" />
    <Content Include="ExtensionDlls\Credigy.Extensions.dll" />
    <Content Include="ExtensionDlls\Credigy.Extensions.pdb" />
    <Content Include="ExtensionDlls\DAE.Extensions.dll" />
    <Content Include="ExtensionDlls\DAE.Extensions.pdb" />
    <Content Include="ExtensionDlls\DemoVida.Extensions.dll" />
    <Content Include="ExtensionDlls\DemoVida.Extensions.pdb" />
    <Content Include="ExtensionDlls\EliteSolar.Extensions.dll" />
    <Content Include="ExtensionDlls\EliteSolar.Extensions.pdb" />
    <Content Include="ExtensionDlls\Fidelis.Extensions.dll" />
    <Content Include="ExtensionDlls\Fidelis.Extensions.pdb" />
    <Content Include="ExtensionDlls\Gci.Extensions.dll" />
    <Content Include="ExtensionDlls\Gci.Extensions.pdb" />
    <Content Include="ExtensionDlls\Gtn2.Extensions.dll" />
    <Content Include="ExtensionDlls\Gtn2.Extensions.pdb" />
    <Content Include="ExtensionDlls\GtnLeads.Extensions.dll" />
    <Content Include="ExtensionDlls\GtnLeads.Extensions.pdb" />
    <Content Include="ExtensionDlls\HaciendaDelMar.Extensions.dll" />
    <Content Include="ExtensionDlls\HaciendaDelMar.Extensions.pdb" />
    <Content Include="ExtensionDlls\Karma.Extensions.dll" />
    <Content Include="ExtensionDlls\Karma.Extensions.pdb" />
    <Content Include="ExtensionDlls\LeisureLoyalty.Extensions.dll" />
    <Content Include="ExtensionDlls\LeisureLoyalty.Extensions.pdb" />
    <Content Include="ExtensionDlls\Logicall.Extensions.dll" />
    <Content Include="ExtensionDlls\Logicall.Extensions.pdb" />
    <Content Include="ExtensionDlls\Marival.Extensions.dll" />
    <Content Include="ExtensionDlls\Marival.Extensions.pdb" />
    <Content Include="ExtensionDlls\MarkMiller.Extensions.dll" />
    <Content Include="ExtensionDlls\MarkMiller.Extensions.pdb" />
    <Content Include="ExtensionDlls\MVMNTINC.Extensions.dll" />
    <Content Include="ExtensionDlls\MVMNTINC.Extensions.pdb" />
    <Content Include="ExtensionDlls\ResortsWest.Extensions.dll" />
    <Content Include="ExtensionDlls\ResortsWest.Extensions.pdb" />
    <Content Include="ExtensionDlls\RoyalHoliday.Extensions.dll" />
    <Content Include="ExtensionDlls\RoyalHoliday.Extensions.pdb" />
    <Content Include="ExtensionDlls\SeventeenGrapes.Extensions.dll" />
    <Content Include="ExtensionDlls\SeventeenGrapes.Extensions.pdb" />
    <Content Include="ExtensionDlls\Sfx.Extensions.dll" />
    <Content Include="ExtensionDlls\Sfx.Extensions.pdb" />
    <Content Include="ExtensionDlls\Starpoint.Extensions.dll" />
    <Content Include="ExtensionDlls\Starpoint.Extensions.pdb" />
    <Content Include="ExtensionDlls\StaySaverVacations.Extensions.dll" />
    <Content Include="ExtensionDlls\StaySaverVacations.Extensions.pdb" />
    <Content Include="ExtensionDlls\SteeleHill.Extensions.dll" />
    <Content Include="ExtensionDlls\SteeleHill.Extensions.pdb" />
    <Content Include="ExtensionDlls\Temp\CAPITALRESORTS\CapitalResorts.Extensions.dll" />
    <Content Include="ExtensionDlls\Temp\CAPITALRESORTS\CapitalResorts.Extensions.pdb" />
    <Content Include="ExtensionDlls\Temp\CLUBTESORO\ClubTesoro.Extensions.dll" />
    <Content Include="ExtensionDlls\Temp\CLUBTESORO\ClubTesoro.Extensions.pdb" />
    <Content Include="ExtensionDlls\Temp\ELITESOLAR\EliteSolar.Extensions.dll" />
    <Content Include="ExtensionDlls\Temp\ELITESOLAR\EliteSolar.Extensions.pdb" />
    <Content Include="ExtensionDlls\Temp\GRUPOMAYAN\DemoVida.Extensions.dll" />
    <Content Include="ExtensionDlls\Temp\GRUPOMAYAN\DemoVida.Extensions.pdb" />
    <Content Include="ExtensionDlls\Temp\GTN2\Gtn2.Extensions.dll" />
    <Content Include="ExtensionDlls\Temp\GTN2\Gtn2.Extensions.pdb" />
    <Content Include="ExtensionDlls\Temp\HOLIDAYINN\HolidayInn.Extensions.dll" />
    <Content Include="ExtensionDlls\Temp\HOLIDAYINN\HolidayInn.Extensions.pdb" />
    <Content Include="ExtensionDlls\Temp\INDUSTRY\TrackResults.Extensions.dll" />
    <Content Include="ExtensionDlls\Temp\INDUSTRY\TrackResults.Extensions.pdb" />
    <Content Include="ExtensionDlls\Temp\INDUSTRY\Welk.Extensions.dll" />
    <Content Include="ExtensionDlls\Temp\INDUSTRY\Welk.Extensions.pdb" />
    <Content Include="ExtensionDlls\Temp\KARMA\Karma.Extensions.dll" />
    <Content Include="ExtensionDlls\Temp\KARMA\Karma.Extensions.pdb" />
    <Content Include="ExtensionDlls\Temp\MVMNTINC\MVMNTINC.Extensions.dll" />
    <Content Include="ExtensionDlls\Temp\MVMNTINC\MVMNTINC.Extensions.pdb" />
    <Content Include="ExtensionDlls\Temp\PARLIAMENTSTAFFING\ParliamentStaffing.Extensions.dll" />
    <Content Include="ExtensionDlls\Temp\PARLIAMENTSTAFFING\ParliamentStaffing.Extensions.pdb" />
    <Content Include="ExtensionDlls\Temp\STAGER\TrackResults.Extensions.dll" />
    <Content Include="ExtensionDlls\Temp\STAGER\TrackResults.Extensions.pdb" />
    <Content Include="ExtensionDlls\Temp\STAYSAVERVACATIONS\StaySaverVacations.Extensions.dll" />
    <Content Include="ExtensionDlls\Temp\STAYSAVERVACATIONS\StaySaverVacations.Extensions.pdb" />
    <Content Include="ExtensionDlls\Temp\STAYSAVERVACATIONS\TrackResults.Extensions.dll" />
    <Content Include="ExtensionDlls\Temp\STAYSAVERVACATIONS\TrackResults.Extensions.pdb" />
    <Content Include="ExtensionDlls\Temp\STAYSKY\StaySky.Extensions.dll" />
    <Content Include="ExtensionDlls\Temp\STAYSKY\StaySky.Extensions.pdb" />
    <Content Include="ExtensionDlls\Temp\STEELEHILL\SteeleHill.Extensions.dll" />
    <Content Include="ExtensionDlls\Temp\STEELEHILL\SteeleHill.Extensions.pdb" />
    <Content Include="ExtensionDlls\Temp\THEINNRESORTS\TheInnResorts.Extensions.dll" />
    <Content Include="ExtensionDlls\Temp\THEINNRESORTS\TheInnResorts.Extensions.pdb" />
    <Content Include="ExtensionDlls\Temp\WELK\Welk.Extensions.dll" />
    <Content Include="ExtensionDlls\Temp\WELK\Welk.Extensions.pdb" />
    <Content Include="ExtensionDlls\Temp\WGRESORTS\Westgate.Extensions.dll" />
    <Content Include="ExtensionDlls\Temp\WGRESORTS\Westgate.Extensions.pdb" />
    <Content Include="ExtensionDlls\TEP.Extensions.dll" />
    <Content Include="ExtensionDlls\TEP.Extensions.pdb" />
    <Content Include="ExtensionDlls\TheInnResorts.Extensions.dll" />
    <Content Include="ExtensionDlls\TheInnResorts.Extensions.pdb" />
    <Content Include="ExtensionDlls\TrackResults.Extensions.dll" />
    <Content Include="ExtensionDlls\TrackResults.Extensions.pdb" />
    <Content Include="ExtensionDlls\TravelSmartVIP.Extensions.dll" />
    <Content Include="ExtensionDlls\TravelSmartVIP.Extensions.pdb" />
    <Content Include="ExtensionDlls\Welk.Extensions.dll" />
    <Content Include="ExtensionDlls\Welk.Extensions.pdb" />
    <Content Include="ExtensionDlls\Westgate.Extensions.dll" />
    <Content Include="ExtensionDlls\Westgate.Extensions.pdb" />
    <Content Include="ForTest.aspx" />
    <Content Include="Reports\AdHoc.aspx" />
    <Content Include="Reports\AdHocAdmin.aspx" />
    <Content Include="Reports\ApiReportsWebService.aspx" />
    <Content Include="Reports\css\scheduler.css" />
    <Content Include="Reports\js\admin_view.js" />
    <Content Include="Reports\js\admin_view.min.js" />
    <Content Include="Reports\js\ag-grid-enterprise.js" />
    <Content Include="Reports\js\ag-grid-enterprise.min.js" />
    <Content Include="Reports\js\ag-grid-enterprise.min.noStyle.js" />
    <Content Include="Reports\js\ag-grid-enterprise.noStyle.js" />
    <Content Include="Reports\js\modules_shared.js" />
    <Content Include="Reports\js\modules_shared.min.js" />
    <Content Include="Reports\LeadsDetailReport.aspx" />
    <Content Include="Reports\MultipleUpdates\UpdateExitInformation.aspx" />
    <Content Include="Reports\ReceivablesDetail.aspx" />
    <Content Include="Reports\UpdateExitInformationReport.aspx" />
    <Content Include="Reports\UserControls\ReportByDateType.ascx" />
    <Content Include="Reports\UserControls\ReportConfigurationCommandsDateType.ascx" />
    <Content Include="Reports\UserControls\ReportThenByDateType.ascx" />
    <Content Include="_Test\CustomerLookUp.aspx" />
    <Content Include="_Test\DashboardLayout.aspx" />
    <Content Include="_Test\Logging.aspx" />
    <Content Include="_Test\_Unsecure\ProfileViewer.aspx" />
    <Content Include="_Test\_Unsecure\SecureCriteriaBuilder.aspx" />
    <None Include="content\d\less\ladda\ladda.less" />
    <Content Include="content\d\scripts\dashboards-7.js" />
    <Content Include="content\d\scripts\jquery-1.11.1.min.js" />
    <Content Include="content\i\asending-16.png" />
    <Content Include="content\i\area-16.png" />
    <Content Include="content\i\area-24.png" />
    <Content Include="content\i\area-32.png" />
    <Content Include="content\i\bar-16.png" />
    <Content Include="content\i\bar-24.png" />
    <Content Include="content\i\bar-32.png" />
    <Content Include="content\i\column-16.png" />
    <Content Include="content\i\column-24.png" />
    <Content Include="content\i\column-32.png" />
    <Content Include="content\i\desending-16.png" />
    <Content Include="content\i\donut-16.png" />
    <Content Include="content\i\donut-24.png" />
    <Content Include="content\i\donut-32.png" />
    <Content Include="content\i\facebook-24.png" />
    <Content Include="content\i\google-plus-24.png" />
    <Content Include="content\i\grid-16.png" />
    <Content Include="content\i\grid-24.png" />
    <Content Include="content\i\grid-32.png" />
    <Content Include="content\i\line-16.png" />
    <Content Include="content\i\line-24.png" />
    <Content Include="content\i\line-32.png" />
    <Content Include="content\i\linkedin-24.png" />
    <Content Include="content\i\pie-16.png" />
    <Content Include="content\i\pie-24.png" />
    <Content Include="content\i\pie-32.png" />
    <Content Include="content\i\text-16.png" />
    <Content Include="content\i\text-24.png" />
    <Content Include="content\i\text-32.png" />
    <Content Include="content\i\twitter-24.png" />
    <Content Include="Dashboards\DashboardsDetails.aspx" />
    <Content Include="Dashboards\Default.aspx" />
    <Content Include="Dashboards\UserControls\DashboardsItemsDetails.ascx" />
    <Content Include="Dashboards\UserControls\DashboardTitleSave.ascx" />
    <Content Include="Reports\ReportByAnalyticView.aspx" />
    <Content Include="Reports\UserControls\ReportConfigurationCommandsPrimary.ascx" />
    <Content Include="Reports\UserControls\ReportThenBy.ascx" />
    <Content Include="content\d\scripts\click-n-drag-checkboxes-1.js" />
    <Content Include="content\d\scripts\reports-dashboards-14.js" />
    <Content Include="content\d\scripts\respond-1.4.2.js" />
    <Content Include="content\d\scripts\script-standard-18.js" />
    <Content Include="Reports\UserControls\ReportViewTypeReportKpis.ascx" />
    <Content Include="content\d\css\style-telerik-overrides-2.css" />
    <Content Include="content\d\css\style-standard-44.css" />
    <Content Include="content\i\progress-main-1.gif" />
    <Content Include="content\i\sprite-map-2.png" />
    <Content Include="Customers\CustomersDetails.aspx" />
    <Content Include="Customers\CustomersMaster.aspx" />
    <Content Include="Customers\DoNotCallsMaster.aspx" />
    <Content Include="Customers\Tours\UserControls\ToursDetailsCalls.ascx" />
    <Content Include="Customers\Tours\UserControls\ToursDetailsLeads.ascx" />
    <Content Include="Customers\Tours\UserControls\ToursDetailsMarketing.ascx" />
    <Content Include="Customers\Tours\UserControls\ToursDetailsSales.ascx" />
    <Content Include="Customers\UserControls\Audits.ascx" />
    <Content Include="Customers\Tours\UserControls\CalendarTourDatesExtender.2.js" />
    <Content Include="Customers\Purchases\UserControls\CancellationsDetails.ascx" />
    <Content Include="Customers\Tours\UserControls\DropDownListTourTimesExtender.2.js" />
    <Content Include="Customers\UserControls\DetailsCommands.ascx" />
    <Content Include="Global.asax" />
    <Content Include="MasterPages\UserControls\Footer.ascx" />
    <Content Include="MasterPages\UserControls\Header.ascx" />
    <Content Include="MultipleUpdates\MultipleUpdateCustomers.aspx" />
    <Content Include="MultipleUpdates\MultipleUpdateToursLeadsCalls.aspx" />
    <Content Include="MultipleUpdates\MultipleUpdatePurchases.aspx" />
    <Content Include="MultipleUpdates\MultipleUpdateToursSales.aspx" />
    <Content Include="MultipleUpdates\UserControls\MultipleUpdateReport.ascx" />
    <Content Include="Reports\UpdateMarketingPipelineReport.aspx" />
    <Content Include="Reports\UserControls\CriteriaUserControls\CancellationCriteriaDetails.ascx" />
    <Content Include="Reports\UserControls\CriteriaUserControls\DateTimeCriteriaDetails.ascx" />
    <Content Include="Reports\UserControls\CriteriaUserControls\BasicCriteriaDetails.ascx" />
    <Content Include="Reports\UserControls\CriteriaUserControls\ToursCriteriaDetailsCalls.ascx" />
    <Content Include="Reports\UserControls\CriteriaUserControls\ToursCriteriaDetailsMarketing.ascx" />
    <Content Include="Reports\UserControls\ReportCriteriaUserControls\ReportCriteriaAdvanced.ascx" />
    <Content Include="Reports\UserControls\ReportCriteriaUserControls\ReportCriteriaBasic.ascx" />
    <Content Include="Reports\UserControls\ReportCriteriaUserControls\ReportCriteriaMarketing.ascx" />
    <Content Include="Reports\UserControls\ReportCriteriaUserControls\ReportCriteriaSales.ascx" />
    <Content Include="Reports\UserControls\ReportCriteriaUserControls\ReportCriteriaSearch.ascx" />
    <Content Include="content\d\scripts\jquery.sortable.js" />
    <Content Include="content\d\scripts\ladda-0-custom-1.js" />
    <Content Include="content\d\scripts\milestone-state-picklists-1.js" />
    <Content Include="content\d\scripts\pace-0.5.3.js" />
    <Content Include="content\d\scripts\spin-0.js" />
    <Content Include="content\d\scripts\underscore-1.6.0.js" />
    <Content Include="UserControls\Breadcrumb.ascx" />
    <Content Include="UserControls\MultiColumnMenu.ascx" />
    <Content Include="UserControls\SortedLinkCommandsLists.ascx" />
    <Content Include="UserControls\CachedSiteContentPart.ascx" />
    <Content Include="_Test\AsyncPanel.aspx" />
    <Content Include="content\d\scripts\bootstrap-editable-1.5.1-custom-1.js" />
    <Content Include="_Test\Buttons.aspx" />
    <Content Include="_Test\CollapsePanel.aspx" />
    <Content Include="_Test\FormsUI.aspx" />
    <Content Include="_Test\Palette.aspx" />
    <Content Include="_Test\Panels.aspx" />
    <Content Include="_Test\ReportCriteriaAdvanced.aspx" />
    <Content Include="_Test\ReportCriteriaBasic.aspx" />
    <Content Include="UserControls\ManyPickLists.ascx" />
    <Content Include="Customers\UserControls\NotesDetails.ascx" />
    <Content Include="Customers\Purchases\UserControls\PurchasesDetails.ascx" />
    <Content Include="Customers\Tours\UserControls\ToursDetailsTours.ascx" />
    <Content Include="Customers\Tours\UserControls\ToursMaster.ascx" />
    <Content Include="Customers\Purchases\UserControls\PurchasesList.ascx" />
    <Content Include="Customers\UserControls\CustomersDetails.ascx" />
    <Content Include="MultipleUpdates\MultipleUpdateDeleteTours.aspx" />
    <Content Include="MultipleUpdates\MultipleUpdateRescheduleToLeads.aspx" />
    <Content Include="Reports\CancellationRequestDetailReport.aspx" />
    <Content Include="Reports\CancellationRequestEfficiencyReport.aspx" />
    <Content Include="Reports\CallCenterDetailReport.aspx" />
    <Content Include="Reports\LeadsRatioEfficiencyReport.aspx" />
    <Content Include="Reports\TourStatusEffeciencyReport.aspx" />
    <Content Include="Reports\RevenueReport.aspx" />
    <Content Include="Reports\CreateCancellationRequestReport.aspx" />
    <Content Include="Reports\MultipleUpdates\CreateCancellationRequests.aspx" />
    <Content Include="Reports\MultipleUpdates\CreatePurchases.aspx" />
    <Content Include="Reports\PenderEfficiencyReport.aspx" />
    <Content Include="MultipleUpdates\MultipleUpdateCancellations.aspx" />
    <Content Include="Reports\LostVolumeReport.aspx" />
    <Content Include="Reports\CreatePurchasesReport.aspx" />
    <Content Include="MultipleUpdates\MultipleUpdateToursMarketingTours.aspx" />
    <Content Include="Reports\UpdateSalesInformationReport.aspx" />
    <Content Include="Reports\UserControls\CriteriaUserControls\CustomersCriteriaDetails.ascx" />
    <Content Include="Reports\UserControls\CriteriaUserControls\PurchasesCriteriaDetails.ascx" />
    <Content Include="Reports\UserControls\CriteriaUserControls\ToursCriteriaDetailsLeads.ascx" />
    <Content Include="Reports\UserControls\CriteriaUserControls\ToursCriteriaDetailsSales.ascx" />
    <Content Include="Reports\UserControls\CriteriaUserControls\ToursCriteriaDetailsTours.ascx" />
    <Content Include="Reports\UserControls\ReportCommands.ascx" />
    <Content Include="Reports\UserControls\ReportConfigurationDescription.ascx" />
    <Content Include="Reports\UserControls\ReportConfigurationCommands.ascx" />
    <Content Include="Reports\UserControls\ReportTitleSave.ascx" />
    <Content Include="content\d\scripts\jquery.blockUI-2.66-custom-3.js" />
    <Content Include="content\d\scripts\jquery.minMaxHighlight.js" />
    <Content Include="content\d\scripts\many-picklists-8.js" />
    <Content Include="content\d\scripts\datetime-criteria-5.js" />
    <Content Include="content\d\scripts\waypoints-2.0.4-custom-2.js" />
    <Content Include="SiteContentDraftPage.aspx" />
    <Content Include="SiteContentPage.aspx" />
    <Content Include="_Test\CustomersDetails.aspx" />
    <Content Include="_Test\PurchasesDetails.aspx" />
    <Content Include="_Test\Scratch.aspx" />
    <Content Include="_Test\ScratchBlank.aspx" />
    <Content Include="_Test\Tables.aspx" />
    <Content Include="_Test\TablesAlign.aspx" />
    <Content Include="_Test\ToursDetails.aspx" />
    <Content Include="UserControls\MilestoneStatePickLists.ascx" />
    <Content Include="UserControls\CollapsePanel.ascx" />
    <Content Include="Web.config">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="WebControls\ClickExtender\ClickExtender-1.0.js" />
    <Content Include="WebControls\DynamicLoadComboBox\DynamicLoadComboBox-4.js" />
    <Content Include="WebControls\PostViewStateExtender\DropDownListPostViewStateExtender.js" />
    <Content Include="WebControls\PostViewStateExtender\PostViewStateExtender.js" />
    <Content Include="WebControls\TypedCheckboxComboBox\TypedCheckboxComboBox.js" />
    <Content Include="WebServices\PickListItemsWebService.asmx" />
    <Content Include="_Test\WebAccounts.aspx" />
    <Content Include="_Test\_Cache.aspx" />
    <Content Include="_Test\_Upgrades.aspx" />
    <Content Include="WebControls\web.config" />
    <Content Include="_Test\_Unsecure\web.config" />
    <Content Include="content\jquery-ui-1.13.2.custom\package.json" />
    <None Include="Dockerfile" />
    <None Include="packages.config" />
    <None Include="Properties\PublishProfiles\ARDADEMO - FTP.pubxml" />
    <None Include="Properties\PublishProfiles\ARDADEMO - Web Deploy.pubxml" />
    <None Include="Properties\PublishProfiles\ASCENDTR - Web Deploy.pubxml" />
    <None Include="Properties\PublishProfiles\CLUBLOCO - Web Deploy.pubxml" />
    <None Include="Properties\PublishProfiles\DEFAULTtr - FTP.pubxml" />
    <None Include="Properties\PublishProfiles\DEFAULTtr - Web Deploy.pubxml" />
    <None Include="Properties\PublishProfiles\DISCOVERVACATIONS - Web Deploy.pubxml" />
    <None Include="Properties\PublishProfiles\GCITR - Web Deploy.pubxml" />
    <None Include="Properties\PublishProfiles\GREATRESORTS - Web Deploy.pubxml" />
    <None Include="Properties\PublishProfiles\GRUPOMAYAN - Web Deploy.pubxml" />
    <None Include="Properties\PublishProfiles\GTN2 - Web Deploy.pubxml" />
    <None Include="Properties\PublishProfiles\HACIENDADELMAR - Web Deploy.pubxml" />
    <None Include="Properties\PublishProfiles\KALEOTR - FTP.pubxml" />
    <None Include="Properties\PublishProfiles\KALEOTR - Web Deploy.pubxml" />
    <None Include="Properties\PublishProfiles\LIFEGUARDTR - FTP.pubxml" />
    <None Include="Properties\PublishProfiles\LIFEGUARDTR - Web Deploy.pubxml" />
    <None Include="Properties\PublishProfiles\PARLIAMENTTR - FTP.pubxml" />
    <None Include="Properties\PublishProfiles\PARLIAMENTTR - FTP1.pubxml" />
    <None Include="Properties\PublishProfiles\PARLIAMENTTR - Web Deploy.pubxml" />
    <None Include="Properties\PublishProfiles\sandbox-clubleisure - FTP.pubxml" />
    <None Include="Properties\PublishProfiles\sandbox-clubleisure - Web Deploy.pubxml" />
    <None Include="Properties\PublishProfiles\sandbox-KALEO - FTP.pubxml" />
    <None Include="Properties\PublishProfiles\sandbox-KALEO - Web Deploy.pubxml" />
    <None Include="Properties\PublishProfiles\sandbox-tep - FTP.pubxml" />
    <None Include="Properties\PublishProfiles\sandbox-tep - Web Deploy.pubxml" />
    <None Include="Properties\PublishProfiles\SANDBOXtr - FTP.pubxml" />
    <None Include="Properties\PublishProfiles\SANDBOXtr - Web Deploy.pubxml" />
    <None Include="Properties\PublishProfiles\STAGERTR - FTP.pubxml" />
    <None Include="Properties\PublishProfiles\STAGERTR - Web Deploy.pubxml" />
    <None Include="Properties\PublishProfiles\TEPTR - FTP.pubxml" />
    <None Include="Properties\PublishProfiles\TEPTR - Web Deploy.pubxml" />
    <Content Include="Reports\js\admin_view.min.js.map" />
    <Content Include="Reports\js\modules_shared.min.js.map" />
    <None Include="Properties\PublishProfiles\testintong - FTP.pubxml" />
    <None Include="Properties\PublishProfiles\testintong - Web Deploy.pubxml" />
    <None Include="Properties\PublishProfiles\TRAVELCOtr - FTP.pubxml" />
    <None Include="Properties\PublishProfiles\TRAVELCOtr - Web Deploy.pubxml" />
    <None Include="Properties\PublishProfiles\TRAVELCOtr2 - FTP.pubxml" />
    <None Include="Properties\PublishProfiles\TRAVELCOtr2 - Web Deploy.pubxml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Account\ChangePassword.aspx.cs">
      <DependentUpon>ChangePassword.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Account\ChangePassword.aspx.designer.cs">
      <DependentUpon>ChangePassword.aspx</DependentUpon>
    </Compile>
    <Compile Include="Account\EulaAcceptance.aspx.cs">
      <DependentUpon>EulaAcceptance.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Account\EulaAcceptance.aspx.designer.cs">
      <DependentUpon>EulaAcceptance.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\ImportExport\ToursToCSV.aspx.cs">
      <DependentUpon>ToursToCSV.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\ImportExport\ToursToCSV.aspx.designer.cs">
      <DependentUpon>ToursToCSV.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\Middleware\ApiConnectionsMiddlewareErrorsMaster.aspx.cs">
      <DependentUpon>ApiConnectionsMiddlewareErrorsMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\Middleware\ApiConnectionsMiddlewareErrorsMaster.aspx.designer.cs">
      <DependentUpon>ApiConnectionsMiddlewareErrorsMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\Default.aspx.designer.cs">
      <DependentUpon>Default.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\Middleware\UserControls\ApiConnectionsMaster.ascx.cs">
      <DependentUpon>ApiConnectionsMaster.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\Middleware\UserControls\ApiConnectionsMaster.ascx.designer.cs">
      <DependentUpon>ApiConnectionsMaster.ascx</DependentUpon>
    </Compile>
    <Compile Include="Administration\PickLists\CancellationStatusTypesMaster.aspx.cs">
      <DependentUpon>CancellationStatusTypesMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\PickLists\CancellationStatusTypesMaster.aspx.designer.cs">
      <DependentUpon>CancellationStatusTypesMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\PickLists\CancellationDispositionTypesMaster.aspx.cs">
      <DependentUpon>CancellationDispositionTypesMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\PickLists\CancellationDispositionTypesMaster.aspx.designer.cs">
      <DependentUpon>CancellationDispositionTypesMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\PickLists\CancellationReceivedTypesMaster.aspx.cs">
      <DependentUpon>CancellationReceivedTypesMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\PickLists\CancellationReceivedTypesMaster.aspx.designer.cs">
      <DependentUpon>CancellationReceivedTypesMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\PickLists\CancellationReasonTypesMaster.aspx.cs">
      <DependentUpon>CancellationReasonTypesMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\PickLists\CancellationReasonTypesMaster.aspx.designer.cs">
      <DependentUpon>CancellationReasonTypesMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\PickLists\Default\CancellationsPickLists.aspx.cs">
      <DependentUpon>CancellationsPickLists.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\PickLists\Default\CancellationsPickLists.aspx.designer.cs">
      <DependentUpon>CancellationsPickLists.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\PickLists\Default\ToursCallsPickLists.aspx.cs">
      <DependentUpon>ToursCallsPickLists.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\PickLists\Default\ToursCallsPickLists.aspx.designer.cs">
      <DependentUpon>ToursCallsPickLists.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\PickLists\Default\ToursLeadsPickLists.aspx.cs">
      <DependentUpon>ToursLeadsPickLists.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\PickLists\Default\ToursLeadsPickLists.aspx.designer.cs">
      <DependentUpon>ToursLeadsPickLists.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\PickLists\Default\CustomersPickLists.aspx.cs">
      <DependentUpon>CustomersPickLists.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\PickLists\Default\CustomersPickLists.aspx.designer.cs">
      <DependentUpon>CustomersPickLists.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\PickLists\Default\PurchasesPickLists.aspx.cs">
      <DependentUpon>PurchasesPickLists.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\PickLists\Default\PurchasesPickLists.aspx.designer.cs">
      <DependentUpon>PurchasesPickLists.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\System\SyncSchema.aspx.cs">
      <DependentUpon>SyncSchema.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\System\SyncSchema.aspx.designer.cs">
      <DependentUpon>SyncSchema.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\System\APIConnectionDetails.aspx.cs">
      <DependentUpon>APIConnectionDetails.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\System\APIConnectionDetails.aspx.designer.cs">
      <DependentUpon>APIConnectionDetails.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\System\ApiConnectionMapping.aspx.cs">
      <DependentUpon>ApiConnectionMapping.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\System\ApiConnectionMapping.aspx.designer.cs">
      <DependentUpon>ApiConnectionMapping.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\System\APIConnectionMappingRequest.aspx.cs">
      <DependentUpon>APIConnectionMappingRequest.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\System\APIConnectionMappingRequest.aspx.designer.cs">
      <DependentUpon>APIConnectionMappingRequest.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\System\DefinedTypes\SaleStatusTypesMaster.aspx.cs">
      <DependentUpon>SaleStatusTypesMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\System\DefinedTypes\SaleStatusTypesMaster.aspx.designer.cs">
      <DependentUpon>SaleStatusTypesMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\System\DefinedTypes\SaleTypesMaster.aspx.cs">
      <DependentUpon>SaleTypesMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\System\DefinedTypes\SaleTypesMaster.aspx.designer.cs">
      <DependentUpon>SaleTypesMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\ImportExport\ImportFromCsv.master.cs">
      <DependentUpon>ImportFromCsv.master</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\ImportExport\ImportFromCsv.master.designer.cs">
      <DependentUpon>ImportFromCsv.master</DependentUpon>
    </Compile>
    <Compile Include="Administration\ImportExport\ImportPageBase.cs">
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\ImportExport\LeadsCallsFromCsv.aspx.cs">
      <DependentUpon>LeadsCallsFromCsv.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\ImportExport\LeadsCallsFromCsv.aspx.designer.cs">
      <DependentUpon>LeadsCallsFromCsv.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\ImportExport\LeadsFromCsv.aspx.cs">
      <DependentUpon>LeadsFromCsv.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\ImportExport\LeadsFromCsv.aspx.designer.cs">
      <DependentUpon>LeadsFromCsv.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\ImportExport\ToursFromCsv.aspx.cs">
      <DependentUpon>ToursFromCsv.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\ImportExport\ToursFromCsv.aspx.designer.cs">
      <DependentUpon>ToursFromCsv.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\ImportExport\UserControls\ImportMappings.ascx.cs">
      <DependentUpon>ImportMappings.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\ImportExport\UserControls\ImportMappings.ascx.designer.cs">
      <DependentUpon>ImportMappings.ascx</DependentUpon>
    </Compile>
    <Compile Include="Administration\ImportExport\UserControls\ExportMappings.ascx.cs">
      <DependentUpon>ExportMappings.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\ImportExport\UserControls\ExportMappings.ascx.designer.cs">
      <DependentUpon>ExportMappings.ascx</DependentUpon>
    </Compile>
    <Compile Include="Administration\PickLists\ChannelsMaster.aspx.cs">
      <DependentUpon>ChannelsMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\PickLists\ChannelsMaster.aspx.designer.cs">
      <DependentUpon>ChannelsMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\PickLists\ContactDispositionsMaster.aspx.cs">
      <DependentUpon>ContactDispositionsMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\PickLists\ContactDispositionsMaster.aspx.designer.cs">
      <DependentUpon>ContactDispositionsMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\PickLists\Default\ToursMarketingPickLists.aspx.cs">
      <DependentUpon>ToursMarketingPickLists.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\PickLists\Default\ToursMarketingPickLists.aspx.designer.cs">
      <DependentUpon>ToursMarketingPickLists.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\PickLists\Default\ToursSalesPickLists.aspx.cs">
      <DependentUpon>ToursSalesPickLists.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\PickLists\Default\ToursSalesPickLists.aspx.designer.cs">
      <DependentUpon>ToursSalesPickLists.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\PickLists\Default\ToursToursPickLists.aspx.cs">
      <DependentUpon>ToursToursPickLists.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\PickLists\Default\ToursToursPickLists.aspx.designer.cs">
      <DependentUpon>ToursToursPickLists.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\PickLists\GiftsMaster.aspx.cs">
      <DependentUpon>GiftsMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\PickLists\GiftsMaster.aspx.designer.cs">
      <DependentUpon>GiftsMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\PickLists\LeadPickList1ItemsMaster.aspx.cs">
      <DependentUpon>LeadPickList1ItemsMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\PickLists\LeadPickList1ItemsMaster.aspx.designer.cs">
      <DependentUpon>LeadPickList1ItemsMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\PickLists\LeadPickList2ItemsMaster.aspx.cs">
      <DependentUpon>LeadPickList2ItemsMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\PickLists\LeadPickList2ItemsMaster.aspx.designer.cs">
      <DependentUpon>LeadPickList2ItemsMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\PickLists\TourTypePickList1ItemsMaster.aspx.cs">
      <DependentUpon>TourTypePickList1ItemsMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\PickLists\TourTypePickList1ItemsMaster.aspx.designer.cs">
      <DependentUpon>TourTypePickList1ItemsMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\PickLists\TourSourcesMaster.aspx.cs">
      <DependentUpon>TourSourcesMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\PickLists\TourSourcesMaster.aspx.designer.cs">
      <DependentUpon>TourSourcesMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\PickLists\LeadTypesMaster.aspx.cs">
      <DependentUpon>LeadTypesMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\PickLists\LeadTypesMaster.aspx.designer.cs">
      <DependentUpon>LeadTypesMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\PickLists\LeadSourcesMaster.aspx.cs">
      <DependentUpon>LeadSourcesMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\PickLists\LeadSourcesMaster.aspx.designer.cs">
      <DependentUpon>LeadSourcesMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\PickLists\TourTypesMaster.aspx.cs">
      <DependentUpon>TourTypesMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\PickLists\TourTypesMaster.aspx.designer.cs">
      <DependentUpon>TourTypesMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\PickLists\ToursManyPickList3ItemsMaster.aspx.cs">
      <DependentUpon>ToursManyPickList3ItemsMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\PickLists\ToursManyPickList3ItemsMaster.aspx.designer.cs">
      <DependentUpon>ToursManyPickList3ItemsMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\PickLists\ToursManyPickList2ItemsMaster.aspx.cs">
      <DependentUpon>ToursManyPickList2ItemsMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\PickLists\ToursManyPickList2ItemsMaster.aspx.designer.cs">
      <DependentUpon>ToursManyPickList2ItemsMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\PickLists\ToursManyPickList1ItemsMaster.aspx.cs">
      <DependentUpon>ToursManyPickList1ItemsMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\PickLists\ToursManyPickList1ItemsMaster.aspx.designer.cs">
      <DependentUpon>ToursManyPickList1ItemsMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\PickLists\TourConcernTypesMaster.aspx.cs">
      <DependentUpon>TourConcernTypesMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\PickLists\TourConcernTypesMaster.aspx.designer.cs">
      <DependentUpon>TourConcernTypesMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\PickLists\SubProductsMaster.aspx.cs">
      <DependentUpon>SubProductsMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\PickLists\SubProductsMaster.aspx.designer.cs">
      <DependentUpon>SubProductsMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\PickLists\RegionsMaster.aspx.cs">
      <DependentUpon>RegionsMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\PickLists\RegionsMaster.aspx.designer.cs">
      <DependentUpon>RegionsMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\PickLists\SaleDispositionsMaster.aspx.cs">
      <DependentUpon>SaleDispositionsMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\PickLists\SaleDispositionsMaster.aspx.designer.cs">
      <DependentUpon>SaleDispositionsMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\PickLists\SalesPickList1ItemsMaster.aspx.cs">
      <DependentUpon>SalesPickList1ItemsMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\PickLists\SalesPickList1ItemsMaster.aspx.designer.cs">
      <DependentUpon>SalesPickList1ItemsMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\PickLists\PurchasesManyPickList2ItemsMaster.aspx.cs">
      <DependentUpon>PurchasesManyPickList2ItemsMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\PickLists\PurchasesManyPickList2ItemsMaster.aspx.designer.cs">
      <DependentUpon>PurchasesManyPickList2ItemsMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\PickLists\PurchasesPickList3ItemsMaster.aspx.cs">
      <DependentUpon>PurchasesPickList3ItemsMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\PickLists\PurchasesPickList3ItemsMaster.aspx.designer.cs">
      <DependentUpon>PurchasesPickList3ItemsMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\PickLists\PurchasesManyPickList1ItemsMaster.aspx.cs">
      <DependentUpon>PurchasesManyPickList1ItemsMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\PickLists\PurchasesManyPickList1ItemsMaster.aspx.designer.cs">
      <DependentUpon>PurchasesManyPickList1ItemsMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\PickLists\ProductCategoriesMaster.aspx.cs">
      <DependentUpon>ProductCategoriesMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\PickLists\ProductCategoriesMaster.aspx.designer.cs">
      <DependentUpon>ProductCategoriesMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\PickLists\MarketingPickList2ItemsMaster.aspx.cs">
      <DependentUpon>MarketingPickList2ItemsMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\PickLists\MarketingPickList2ItemsMaster.aspx.designer.cs">
      <DependentUpon>MarketingPickList2ItemsMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\PickLists\LeadDispositionsMaster.aspx.cs">
      <DependentUpon>LeadDispositionsMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\PickLists\LeadDispositionsMaster.aspx.designer.cs">
      <DependentUpon>LeadDispositionsMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\PickLists\LeadStatusTypesMaster.aspx.cs">
      <DependentUpon>LeadStatusTypesMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\PickLists\LeadStatusTypesMaster.aspx.designer.cs">
      <DependentUpon>LeadStatusTypesMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\PickLists\IncomeTypesMaster.aspx.cs">
      <DependentUpon>IncomeTypesMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\PickLists\IncomeTypesMaster.aspx.designer.cs">
      <DependentUpon>IncomeTypesMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\PickLists\GuestTypesMaster.aspx.cs">
      <DependentUpon>GuestTypesMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\PickLists\GuestTypesMaster.aspx.designer.cs">
      <DependentUpon>GuestTypesMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\PickLists\CustomerTypesMaster.aspx.cs">
      <DependentUpon>CustomerTypesMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\PickLists\CustomerTypesMaster.aspx.designer.cs">
      <DependentUpon>CustomerTypesMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\PickLists\CustomerStatusTypesMaster.aspx.cs">
      <DependentUpon>CustomerStatusTypesMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\PickLists\CustomerStatusTypesMaster.aspx.designer.cs">
      <DependentUpon>CustomerStatusTypesMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\PickLists\CustomersManyPickList2ItemsMaster.aspx.cs">
      <DependentUpon>CustomersManyPickList2ItemsMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\PickLists\CustomersManyPickList2ItemsMaster.aspx.designer.cs">
      <DependentUpon>CustomersManyPickList2ItemsMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\PickLists\CustomersPickList3ItemsMaster.aspx.cs">
      <DependentUpon>CustomersPickList3ItemsMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\PickLists\CustomersPickList3ItemsMaster.aspx.designer.cs">
      <DependentUpon>CustomersPickList3ItemsMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\PickLists\CustomersPickList4ItemsMaster.aspx.cs">
      <DependentUpon>CustomersPickList4ItemsMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\PickLists\CustomersPickList4ItemsMaster.aspx.designer.cs">
      <DependentUpon>CustomersPickList4ItemsMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\PickLists\CustomersManyPickList1ItemsMaster.aspx.cs">
      <DependentUpon>CustomersManyPickList1ItemsMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\PickLists\CustomersManyPickList1ItemsMaster.aspx.designer.cs">
      <DependentUpon>CustomersManyPickList1ItemsMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\PickLists\CustomerDispositionsMaster.aspx.cs">
      <DependentUpon>CustomerDispositionsMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\PickLists\CustomerDispositionsMaster.aspx.designer.cs">
      <DependentUpon>CustomerDispositionsMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\PickLists\ContactStatusTypesMaster.aspx.cs">
      <DependentUpon>ContactStatusTypesMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\PickLists\ContactStatusTypesMaster.aspx.designer.cs">
      <DependentUpon>ContactStatusTypesMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\PickLists\PickListMaster.master.cs">
      <DependentUpon>PickListMaster.master</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\PickLists\PickListMaster.master.designer.cs">
      <DependentUpon>PickListMaster.master</DependentUpon>
    </Compile>
    <Compile Include="Administration\PickLists\PickListPageBase.cs">
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\PickLists\CallBackPickList1ItemsMaster.aspx.cs">
      <DependentUpon>CallBackPickList1ItemsMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\PickLists\CallBackPickList1ItemsMaster.aspx.designer.cs">
      <DependentUpon>CallBackPickList1ItemsMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\PickLists\CustomersPickList1ItemsMaster.aspx.cs">
      <DependentUpon>CustomersPickList1ItemsMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\PickLists\CustomersPickList1ItemsMaster.aspx.designer.cs">
      <DependentUpon>CustomersPickList1ItemsMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\PickLists\CustomersPickList2ItemsMaster.aspx.cs">
      <DependentUpon>CustomersPickList2ItemsMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\PickLists\CustomersPickList2ItemsMaster.aspx.designer.cs">
      <DependentUpon>CustomersPickList2ItemsMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\PickLists\MarketingPickList1ItemsMaster.aspx.cs">
      <DependentUpon>MarketingPickList1ItemsMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\PickLists\MarketingPickList1ItemsMaster.aspx.designer.cs">
      <DependentUpon>MarketingPickList1ItemsMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\PickLists\PurchasesPickList1ItemsMaster.aspx.cs">
      <DependentUpon>PurchasesPickList1ItemsMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\PickLists\PurchasesPickList1ItemsMaster.aspx.designer.cs">
      <DependentUpon>PurchasesPickList1ItemsMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\PickLists\PurchasesPickList2ItemsMaster.aspx.cs">
      <DependentUpon>PurchasesPickList2ItemsMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\PickLists\PurchasesPickList2ItemsMaster.aspx.designer.cs">
      <DependentUpon>PurchasesPickList2ItemsMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\System\ApiConnectorsDetails.aspx.cs">
      <DependentUpon>ApiConnectorsDetails.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\System\ApiConnectorsDetails.aspx.designer.cs">
      <DependentUpon>ApiConnectorsDetails.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\System\ApiConnectorsMaster.aspx.cs">
      <DependentUpon>ApiConnectorsMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\System\ApiConnectorsMaster.aspx.designer.cs">
      <DependentUpon>ApiConnectorsMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\System\CacheManagement.aspx.cs">
      <DependentUpon>CacheManagement.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\System\CacheManagement.aspx.designer.cs">
      <DependentUpon>CacheManagement.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\System\CalculatedKpis\CalculatedKpiMaster.aspx.cs">
      <DependentUpon>CalculatedKpiMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\System\CalculatedKpis\CalculatedKpiMaster.aspx.designer.cs">
      <DependentUpon>CalculatedKpiMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\System\CustomAnalyticViews\CustomAnalyticViewsDetails.aspx.cs">
      <DependentUpon>CustomAnalyticViewsDetails.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\System\CustomAnalyticViews\CustomAnalyticViewsDetails.aspx.designer.cs">
      <DependentUpon>CustomAnalyticViewsDetails.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\System\CustomAnalyticViews\CustomAnalyticViewsKpisDetails.aspx.cs">
      <DependentUpon>CustomAnalyticViewsKpisDetails.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\System\CustomAnalyticViews\CustomAnalyticViewsKpisDetails.aspx.designer.cs">
      <DependentUpon>CustomAnalyticViewsKpisDetails.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\System\CustomAnalyticViews\CustomAnalyticViewsKpisMaster.aspx.cs">
      <DependentUpon>CustomAnalyticViewsKpisMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\System\CustomAnalyticViews\CustomAnalyticViewsKpisMaster.aspx.designer.cs">
      <DependentUpon>CustomAnalyticViewsKpisMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\System\CustomAnalyticViews\CustomAnalyticViewsMaster.aspx.cs">
      <DependentUpon>CustomAnalyticViewsMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\System\CustomAnalyticViews\CustomAnalyticViewsMaster.aspx.designer.cs">
      <DependentUpon>CustomAnalyticViewsMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\System\Dashboards\DashboardsDetails.aspx.cs">
      <DependentUpon>DashboardsDetails.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\System\Dashboards\DashboardsDetails.aspx.designer.cs">
      <DependentUpon>DashboardsDetails.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\System\Dashboards\DashboardsMaster.aspx.cs">
      <DependentUpon>DashboardsMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\System\Dashboards\DashboardsMaster.aspx.designer.cs">
      <DependentUpon>DashboardsMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\System\Deployments.aspx.cs">
      <DependentUpon>Deployments.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\System\Deployments.aspx.designer.cs">
      <DependentUpon>Deployments.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\System\Goals\GoalsDetails.aspx.cs">
      <DependentUpon>GoalsDetails.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\System\Goals\GoalsDetails.aspx.designer.cs">
      <DependentUpon>GoalsDetails.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\System\Goals\GoalsMaster.aspx.cs">
      <DependentUpon>GoalsMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\System\Goals\GoalsMaster.aspx.designer.cs">
      <DependentUpon>GoalsMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\System\LocalizationsMaster.aspx.cs">
      <DependentUpon>LocalizationsMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\System\LocalizationsMaster.aspx.designer.cs">
      <DependentUpon>LocalizationsMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\System\ManageCustomReportsCacheMaster.aspx.cs">
      <DependentUpon>ManageCustomReportsCacheMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\System\ManageCustomReportsCacheMaster.aspx.designer.cs">
      <DependentUpon>ManageCustomReportsCacheMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\System\PropertyCustomization\DeploymentCustomization.aspx.cs">
      <DependentUpon>DeploymentCustomization.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\System\PropertyCustomization\DeploymentCustomization.aspx.designer.cs">
      <DependentUpon>DeploymentCustomization.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\System\PropertyCustomization\ManageUsersPropertyCustomization.aspx.cs">
      <DependentUpon>ManageUsersPropertyCustomization.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\System\PropertyCustomization\ManageUsersPropertyCustomization.aspx.designer.cs">
      <DependentUpon>ManageUsersPropertyCustomization.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\System\RolesMaster.aspx.cs">
      <DependentUpon>RolesMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\System\RolesMaster.aspx.designer.cs">
      <DependentUpon>RolesMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\ApiConnectionsDataMappings.aspx.cs">
      <DependentUpon>ApiConnectionsDataMappings.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\ApiConnectionsDataMappings.aspx.designer.cs">
      <DependentUpon>ApiConnectionsDataMappings.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\System\ApiConnectionsDetails.aspx.cs">
      <DependentUpon>ApiConnectionsDetails.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\System\ApiConnectionsDetails.aspx.designer.cs">
      <DependentUpon>ApiConnectionsDetails.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\System\ApiConnectionsMaster.aspx.cs">
      <DependentUpon>ApiConnectionsMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\System\ApiConnectionsMaster.aspx.designer.cs">
      <DependentUpon>ApiConnectionsMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\System\ApplicationSettings.aspx.cs">
      <DependentUpon>ApplicationSettings.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\System\ApplicationSettings.aspx.designer.cs">
      <DependentUpon>ApplicationSettings.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\System\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\System\Default.aspx.designer.cs">
      <DependentUpon>Default.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\System\EulasMaster.aspx.cs">
      <DependentUpon>EulasMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\System\EulasMaster.aspx.designer.cs">
      <DependentUpon>EulasMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\System\Impersonation.aspx.cs">
      <DependentUpon>Impersonation.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\System\Impersonation.aspx.designer.cs">
      <DependentUpon>Impersonation.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\System\PropertyCustomization\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\System\PropertyCustomization\Default.aspx.designer.cs">
      <DependentUpon>Default.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\System\PropertyCustomization\ManagePropertyCustomization.aspx.cs">
      <DependentUpon>ManagePropertyCustomization.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\System\PropertyCustomization\ManagePropertyCustomization.aspx.designer.cs">
      <DependentUpon>ManagePropertyCustomization.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\System\PropertyCustomization\ManageRolesPropertyCustomization.aspx.cs">
      <DependentUpon>ManageRolesPropertyCustomization.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\System\PropertyCustomization\ManageRolesPropertyCustomization.aspx.designer.cs">
      <DependentUpon>ManageRolesPropertyCustomization.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\System\SchedulerMaster.aspx.cs">
      <DependentUpon>SchedulerMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\System\SchedulerMaster.aspx.designer.cs">
      <DependentUpon>SchedulerMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\System\SiteContent\SiteContentPartsSimpleDetails.aspx.cs">
      <DependentUpon>SiteContentPartsSimpleDetails.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\System\SiteContent\SiteContentPartsSimpleDetails.aspx.designer.cs">
      <DependentUpon>SiteContentPartsSimpleDetails.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\System\SiteContent\SiteContentPartsSimpleMaster.aspx.cs">
      <DependentUpon>SiteContentPartsSimpleMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\System\SiteContent\SiteContentPartsSimpleMaster.aspx.designer.cs">
      <DependentUpon>SiteContentPartsSimpleMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\MiddlewareErrorsDataMappings.aspx.cs">
      <DependentUpon>MiddlewareErrorsDataMappings.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\MiddlewareErrorsDataMappings.aspx.designer.cs">
      <DependentUpon>MiddlewareErrorsDataMappings.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\System\MiddlewareErrorsMaster.aspx.cs">
      <DependentUpon>MiddlewareErrorsMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\System\MiddlewareErrorsMaster.aspx.designer.cs">
      <DependentUpon>MiddlewareErrorsMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\System\SiteContent\SiteContentFilesMaster.aspx.cs">
      <DependentUpon>SiteContentFilesMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\System\SiteContent\SiteContentFilesMaster.aspx.designer.cs">
      <DependentUpon>SiteContentFilesMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\System\SiteContent\SiteContentPagesDetails.aspx.cs">
      <DependentUpon>SiteContentPagesDetails.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\System\SiteContent\SiteContentPagesDetails.aspx.designer.cs">
      <DependentUpon>SiteContentPagesDetails.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\System\SiteContent\SiteContentPagesMaster.aspx.cs">
      <DependentUpon>SiteContentPagesMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\System\SiteContent\SiteContentPagesMaster.aspx.designer.cs">
      <DependentUpon>SiteContentPagesMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\System\SiteContent\SiteContentPartsDetails.aspx.cs">
      <DependentUpon>SiteContentPartsDetails.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\System\SiteContent\SiteContentPartsDetails.aspx.designer.cs">
      <DependentUpon>SiteContentPartsDetails.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\System\SiteContent\SiteContentPartsMaster.aspx.cs">
      <DependentUpon>SiteContentPartsMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\System\SiteContent\SiteContentPartsMaster.aspx.designer.cs">
      <DependentUpon>SiteContentPartsMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\System\WebAccountsDetails.aspx.cs">
      <DependentUpon>WebAccountsDetails.aspx</DependentUpon>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\System\WebAccountsDetails.aspx.designer.cs">
      <DependentUpon>WebAccountsDetails.aspx</DependentUpon>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Compile>
    <Compile Include="Administration\System\DefinedTypes\TourStatusTypesMaster.aspx.cs">
      <DependentUpon>TourStatusTypesMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\System\DefinedTypes\TourStatusTypesMaster.aspx.designer.cs">
      <DependentUpon>TourStatusTypesMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\Users\UserAudits\AuditLogin.ascx.cs">
      <DependentUpon>AuditLogin.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\Users\UserAudits\AuditLogin.ascx.designer.cs">
      <DependentUpon>AuditLogin.ascx</DependentUpon>
    </Compile>
    <Compile Include="Administration\Users\UserAudits\Audits.ascx.cs">
      <DependentUpon>Audits.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\Users\UserAudits\Audits.ascx.designer.cs">
      <DependentUpon>Audits.ascx</DependentUpon>
    </Compile>
    <Compile Include="Administration\UserControls\WebAccountsDetails.ascx.BusinessLayer.cs">
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\Users\UsersDetails.aspx.BusinessLayer.cs">
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\PickLists\VenuesMaster.aspx.cs">
      <DependentUpon>VenuesMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\PickLists\VenuesMaster.aspx.designer.cs">
      <DependentUpon>VenuesMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="AnalyticViews\AnalyticView.aspx.cs">
      <DependentUpon>AnalyticView.aspx</DependentUpon>
    </Compile>
    <Compile Include="AnalyticViews\AnalyticView.aspx.designer.cs">
      <DependentUpon>AnalyticView.aspx</DependentUpon>
    </Compile>
    <Compile Include="ApiServices\25\Cache\NextPageTokenTourIDsTotalCountCache.cs" />
    <Compile Include="ApiServices\25\Data\PagedTours.cs" />
    <Compile Include="ApiServices\25\Faults\ApiServiceFault.cs" />
    <Compile Include="ApiServices\25\ToursService.svc.cs">
      <DependentUpon>ToursService.svc</DependentUpon>
    </Compile>
    <Compile Include="ApiServices\26\Cache\NextPageTokenTourIDsTotalCountCache.cs" />
    <Compile Include="ApiServices\26\Data\InsertTourResult.cs" />
    <Compile Include="ApiServices\26\Data\PagedTours.cs" />
    <Compile Include="ApiServices\26\Faults\ApiServiceFault.cs" />
    <Compile Include="ApiServices\26\ToursService.Insert.cs" />
    <Compile Include="ApiServices\26\ToursService.svc.cs">
      <DependentUpon>ToursService.svc</DependentUpon>
    </Compile>
    <Compile Include="ApiServices\26\ToursService.Update.cs" />
    <Compile Include="ApiServices\27\Cache\NextPageTokenTourIDsTotalCountCache.cs" />
    <Compile Include="ApiServices\27\Data\InsertTourResult.cs" />
    <Compile Include="ApiServices\27\Data\Item.cs" />
    <Compile Include="ApiServices\27\Data\PagedTours.cs" />
    <Compile Include="ApiServices\27\Faults\ApiServiceFault.cs" />
    <Compile Include="ApiServices\27\ToursService.Insert.cs" />
    <Compile Include="ApiServices\27\ToursService.svc.cs">
      <DependentUpon>ToursService.svc</DependentUpon>
    </Compile>
    <Compile Include="ApiServices\27\ToursService.Update.cs" />
    <Compile Include="ApiServices\29\Cache\NextPageData.cs" />
    <Compile Include="ApiServices\29\Cache\NextPageDataCache.cs" />
    <Compile Include="ApiServices\29\Data\AddCustomerResult.cs" />
    <Compile Include="ApiServices\29\Data\AddPurchaseResult.cs" />
    <Compile Include="ApiServices\29\Data\AddTourResult.cs" />
    <Compile Include="ApiServices\29\Data\Item.cs" />
    <Compile Include="ApiServices\29\Data\PagedTours.cs" />
    <Compile Include="ApiServices\29\Data\ReportResult.cs" />
    <Compile Include="ApiServices\29\Faults\ApiServiceFault.cs" />
    <Compile Include="ApiServices\29\SystemService.svc.cs">
      <DependentUpon>SystemService.svc</DependentUpon>
    </Compile>
    <Compile Include="ApiServices\29\ReportsService.svc.cs">
      <DependentUpon>ReportsService.svc</DependentUpon>
    </Compile>
    <Compile Include="ApiServices\29\ToursService.svc.cs">
      <DependentUpon>ToursService.svc</DependentUpon>
    </Compile>
    <Compile Include="ApiServices\28\Cache\NextPageData.cs" />
    <Compile Include="ApiServices\28\Cache\NextPageDataCache.cs" />
    <Compile Include="ApiServices\28\Data\AddTourResult.cs" />
    <Compile Include="ApiServices\28\Data\Item.cs" />
    <Compile Include="ApiServices\28\Data\PagedTours.cs" />
    <Compile Include="ApiServices\28\Faults\ApiServiceFault.cs" />
    <Compile Include="ApiServices\28\SystemService.svc.cs">
      <DependentUpon>SystemService.svc</DependentUpon>
    </Compile>
    <Compile Include="ApiServices\28\ToursService.svc.cs">
      <DependentUpon>ToursService.svc</DependentUpon>
    </Compile>
    <Compile Include="App_ProjectCode\Data\ExpressionBuilders\SaleStatusTypesExpressionBuilder.cs" />
    <Compile Include="App_ProjectCode\Data\ExpressionBuilders\SaleTypesExpressionBuilder.cs" />
    <Compile Include="App_ProjectCode\IPageBase.cs" />
    <Compile Include="App_ProjectCode\PageBaseUserControl.cs">
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="App_ProjectCode\Services\CustomAnalyticViewKpisDisplayService.cs" />
    <Compile Include="App_ProjectCode\Services\ReportViewChartsTypeService.cs" />
    <Compile Include="App_ProjectCode\Services\PageUrlService.cs" />
    <Compile Include="App_ProjectCode\Constants.cs" />
    <Compile Include="App_ProjectCode\Data\Cache\TourDateTypesPickListCache.cs" />
    <Compile Include="App_ProjectCode\Data\DrillThroughParameters.cs" />
    <Compile Include="App_ProjectCode\Data\ExpressionBuilders\TourStatusTypesExpressionBuilder.cs" />
    <Compile Include="App_ProjectCode\Data\ProfileData.cs" />
    <Compile Include="App_ProjectCode\Data\QueryStringParametersData.cs" />
    <Compile Include="App_ProjectCode\HttpModule\EulaModule.cs" />
    <Compile Include="App_ProjectCode\IScriptManagerControlContainer.cs" />
    <Compile Include="App_ProjectCode\Security\DropDownDisplay\CancellationStatusTypes.cs" />
    <Compile Include="App_ProjectCode\Security\DropDownDisplay\ReportTypes.cs" />
    <Compile Include="App_ProjectCode\Services\CacheDependencyService.cs" />
    <Compile Include="App_ProjectCode\Services\ReportTypeService.cs" />
    <Compile Include="App_ProjectCode\Services\ReportViewGridsTypeService.cs" />
    <Compile Include="App_ProjectCode\Services\ReportViewService.cs" />
    <Compile Include="App_ProjectCode\Services\ReportViewTypeServices.cs" />
    <Compile Include="App_ProjectCode\Services\ShortCodesService.cs" />
    <Compile Include="App_ProjectCode\Services\WebApplicationService.cs" />
    <Compile Include="App_ProjectCode\UI\ControlExtensions.cs" />
    <Compile Include="App_ProjectCode\UI\RadComboBoxExtensions.cs" />
    <Compile Include="App_ProjectCode\Keys\SessionKeys.cs" />
    <Compile Include="App_ProjectCode\UI\TextBoxExtensions.cs" />
    <Compile Include="Customers\DoNotCallsMaster.aspx.BusinessLayer.cs">
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Customers\Tours\UserControls\ToursDetailsExit.ascx.cs">
      <DependentUpon>ToursDetailsExit.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Customers\Tours\UserControls\ToursDetailsExit.ascx.designer.cs">
      <DependentUpon>ToursDetailsExit.ascx</DependentUpon>
    </Compile>
    <Compile Include="Customers\Tours\UserControls\ToursDetailsForm.ascx.cs">
      <DependentUpon>ToursDetailsForm.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Customers\Tours\UserControls\ToursDetailsForm.ascx.designer.cs">
      <DependentUpon>ToursDetailsForm.ascx</DependentUpon>
    </Compile>
    <Compile Include="Customers\Tours\UserControls\ToursDetailsCalls.ascx.cs">
      <DependentUpon>ToursDetailsCalls.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Customers\Tours\UserControls\ToursDetailsCalls.ascx.designer.cs">
      <DependentUpon>ToursDetailsCalls.ascx</DependentUpon>
    </Compile>
    <Compile Include="Customers\Tours\UserControls\ToursDetailsLeads.ascx.cs">
      <DependentUpon>ToursDetailsLeads.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Customers\Tours\UserControls\ToursDetailsLeads.ascx.designer.cs">
      <DependentUpon>ToursDetailsLeads.ascx</DependentUpon>
    </Compile>
    <Compile Include="Customers\Tours\UserControls\ToursDetailsMarketing.ascx.cs">
      <DependentUpon>ToursDetailsMarketing.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Customers\Tours\UserControls\ToursDetailsMarketing.ascx.designer.cs">
      <DependentUpon>ToursDetailsMarketing.ascx</DependentUpon>
    </Compile>
    <Compile Include="Customers\Tours\UserControls\ToursDetailsSales.ascx.cs">
      <DependentUpon>ToursDetailsSales.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Customers\Tours\UserControls\ToursDetailsSales.ascx.designer.cs">
      <DependentUpon>ToursDetailsSales.ascx</DependentUpon>
    </Compile>
    <Compile Include="Customers\CustomersDetails.aspx.cs">
      <DependentUpon>CustomersDetails.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Customers\CustomersDetails.aspx.designer.cs">
      <DependentUpon>CustomersDetails.aspx</DependentUpon>
    </Compile>
    <Compile Include="Customers\CustomersMaster.aspx.cs">
      <DependentUpon>CustomersMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Customers\CustomersMaster.aspx.designer.cs">
      <DependentUpon>CustomersMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Customers\DoNotCallsMaster.aspx.cs">
      <DependentUpon>DoNotCallsMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Customers\DoNotCallsMaster.aspx.designer.cs">
      <DependentUpon>DoNotCallsMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Customers\UserControls\Audits.ascx.cs">
      <DependentUpon>Audits.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Customers\UserControls\Audits.ascx.designer.cs">
      <DependentUpon>Audits.ascx</DependentUpon>
    </Compile>
    <Compile Include="Customers\Tours\UserControls\CalendarTourDatesExtender.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Customers\Purchases\UserControls\CancellationsDetails.ascx.cs">
      <DependentUpon>CancellationsDetails.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Customers\Purchases\UserControls\CancellationsDetails.ascx.designer.cs">
      <DependentUpon>CancellationsDetails.ascx</DependentUpon>
    </Compile>
    <Compile Include="Customers\Tours\UserControls\DropDownListTourTimesExtender.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Customers\UserControls\DetailsCommands.ascx.cs">
      <DependentUpon>DetailsCommands.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Customers\UserControls\DetailsCommands.ascx.designer.cs">
      <DependentUpon>DetailsCommands.ascx</DependentUpon>
    </Compile>
    <Compile Include="Dashboards\DashboardsDetails.aspx.cs">
      <DependentUpon>DashboardsDetails.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Dashboards\DashboardsDetails.aspx.designer.cs">
      <DependentUpon>DashboardsDetails.aspx</DependentUpon>
    </Compile>
    <Compile Include="Dashboards\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Dashboards\Default.aspx.designer.cs">
      <DependentUpon>Default.aspx</DependentUpon>
    </Compile>
    <Compile Include="Dashboards\UserControls\DashboardsItemsDetails.ascx.cs">
      <DependentUpon>DashboardsItemsDetails.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Dashboards\UserControls\DashboardsItemsDetails.ascx.designer.cs">
      <DependentUpon>DashboardsItemsDetails.ascx</DependentUpon>
    </Compile>
    <Compile Include="Dashboards\UserControls\DashboardTitleSave.ascx.cs">
      <DependentUpon>DashboardTitleSave.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Dashboards\UserControls\DashboardTitleSave.ascx.designer.cs">
      <DependentUpon>DashboardTitleSave.ascx</DependentUpon>
    </Compile>
    <Compile Include="ForTest.aspx.cs">
      <DependentUpon>ForTest.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ForTest.aspx.designer.cs">
      <DependentUpon>ForTest.aspx</DependentUpon>
    </Compile>
    <Compile Include="Global.asax.cs">
      <DependentUpon>Global.asax</DependentUpon>
    </Compile>
    <Compile Include="Handlers\ApiDashboardsPostServiceHandler.cs" />
    <Compile Include="Handlers\ApiDashboardsServiceHandler.cs" />
    <Compile Include="Handlers\ApiExportMappingsServiceHandler.cs" />
    <Compile Include="Handlers\ApiImportMappingsServiceHandler.cs" />
    <Compile Include="Handlers\ApiCustomReportsServiceHandler.cs" />
    <Compile Include="Handlers\ApiConnectionFileHandler.cs" />
    <Compile Include="Handlers\LogoutHandler.cs" />
    <Compile Include="Integrations\Acuity.cs" />
    <Compile Include="Integrations\ClubTesoro.cs" />
    <Compile Include="Integrations\Cranberry.cs" />
    <Compile Include="Integrations\CV.cs" />
    <Compile Include="Integrations\Equifax-Wyndham.cs" />
    <Compile Include="Integrations\Generic.cs" />
    <Compile Include="Integrations\KeapV2.cs" />
    <Compile Include="Integrations\Services\Authorizations\Authorizations.cs" />
    <Compile Include="Integrations\Services\Request.cs" />
    <Compile Include="Integrations\Services\MiddlewareObjectActions.cs" />
    <Compile Include="Integrations\Services\Sftp.cs" />
    <Compile Include="Integrations\Services\Actions\Patch.cs" />
    <Compile Include="Integrations\Services\Authorizations\ApiKey.cs" />
    <Compile Include="Integrations\Services\Authorizations\BasicAuth.cs" />
    <Compile Include="Integrations\Services\Authorizations\BearerToken.cs" />
    <Compile Include="Integrations\Services\Authorizations\OAuth1.cs" />
    <Compile Include="Integrations\Services\Authorizations\OAuth2.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Integrations\Services\Functions.cs" />
    <Compile Include="Integrations\Services\Mappings\PropertyMappings.cs" />
    <Compile Include="Integrations\Services\Triggers\Events.cs" />
    <Compile Include="Integrations\EquifaxWyndhamWinningWays.cs" />
    <Compile Include="Integrations\TestingConnection.cs" />
    <Compile Include="Integrations\TrackResultSync.cs" />
    <Compile Include="Integrations\Travelco.cs" />
    <Compile Include="Integrations\WinningWaysResponse.cs" />
    <Compile Include="MasterPages\UserControls\Footer.ascx.cs">
      <DependentUpon>Footer.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="MasterPages\UserControls\Footer.ascx.designer.cs">
      <DependentUpon>Footer.ascx</DependentUpon>
    </Compile>
    <Compile Include="MasterPages\UserControls\Header.ascx.cs">
      <DependentUpon>Header.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="MasterPages\UserControls\Header.ascx.designer.cs">
      <DependentUpon>Header.ascx</DependentUpon>
    </Compile>
    <Compile Include="MultipleUpdates\MultipleUpdateToursSales.aspx.cs">
      <DependentUpon>MultipleUpdateToursSales.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="MultipleUpdates\MultipleUpdateToursSales.aspx.designer.cs">
      <DependentUpon>MultipleUpdateToursSales.aspx</DependentUpon>
    </Compile>
    <Compile Include="MultipleUpdates\UserControls\MultipleUpdateReport.ascx.cs">
      <DependentUpon>MultipleUpdateReport.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="MultipleUpdates\UserControls\MultipleUpdateReport.ascx.designer.cs">
      <DependentUpon>MultipleUpdateReport.ascx</DependentUpon>
    </Compile>
    <Compile Include="Reports\AdHoc.aspx.cs">
      <DependentUpon>AdHoc.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\AdHoc.aspx.designer.cs">
      <DependentUpon>AdHoc.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\AdHocAdmin.aspx.cs">
      <DependentUpon>AdHocAdmin.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\AdHocAdmin.aspx.designer.cs">
      <DependentUpon>AdHocAdmin.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\ApiReportsWebService.aspx.cs">
      <DependentUpon>ApiReportsWebService.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\ApiReportsWebService.aspx.designer.cs">
      <DependentUpon>ApiReportsWebService.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\MultipleUpdates\UpdateExitInformation.aspx.cs">
      <DependentUpon>UpdateExitInformation.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\MultipleUpdates\UpdateExitInformation.aspx.designer.cs">
      <DependentUpon>UpdateExitInformation.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\ReportByAnalyticView.aspx.cs">
      <DependentUpon>ReportByAnalyticView.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\ReportByAnalyticView.aspx.designer.cs">
      <DependentUpon>ReportByAnalyticView.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\ReportCriteriaType.cs" />
    <Compile Include="MultipleUpdates\MultipleUpdateCustomers.aspx.cs">
      <DependentUpon>MultipleUpdateCustomers.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="MultipleUpdates\MultipleUpdateCustomers.aspx.designer.cs">
      <DependentUpon>MultipleUpdateCustomers.aspx</DependentUpon>
    </Compile>
    <Compile Include="MultipleUpdates\MultipleUpdateToursLeadsCalls.aspx.cs">
      <DependentUpon>MultipleUpdateToursLeadsCalls.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="MultipleUpdates\MultipleUpdateToursLeadsCalls.aspx.designer.cs">
      <DependentUpon>MultipleUpdateToursLeadsCalls.aspx</DependentUpon>
    </Compile>
    <Compile Include="MultipleUpdates\MultipleUpdatePurchases.aspx.cs">
      <DependentUpon>MultipleUpdatePurchases.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="MultipleUpdates\MultipleUpdatePurchases.aspx.designer.cs">
      <DependentUpon>MultipleUpdatePurchases.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\ReceivablesDetail.aspx.cs">
      <DependentUpon>ReceivablesDetail.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\ReceivablesDetail.aspx.designer.cs">
      <DependentUpon>ReceivablesDetail.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\ReportsWebService.cs" />
    <Compile Include="Reports\SalesTourReport.aspx.designer.cs">
      <DependentUpon>SalesTourReport.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\LeadsDetailReport.aspx.cs">
      <DependentUpon>LeadsDetailReport.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\LeadsDetailReport.aspx.designer.cs">
      <DependentUpon>LeadsDetailReport.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\UpdateMarketingPipelineReport.aspx.cs">
      <DependentUpon>UpdateMarketingPipelineReport.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\UpdateMarketingPipelineReport.aspx.designer.cs">
      <DependentUpon>UpdateMarketingPipelineReport.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\UpdateExitInformationReport.aspx.cs">
      <DependentUpon>UpdateExitInformationReport.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\UpdateExitInformationReport.aspx.designer.cs">
      <DependentUpon>UpdateExitInformationReport.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\UserControls\CriteriaUserControls\CancellationCriteriaDetails.ascx.cs">
      <DependentUpon>CancellationCriteriaDetails.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\UserControls\CriteriaUserControls\CancellationCriteriaDetails.ascx.designer.cs">
      <DependentUpon>CancellationCriteriaDetails.ascx</DependentUpon>
    </Compile>
    <Compile Include="Reports\UserControls\CriteriaUserControls\DateTimeCriteriaDetails.ascx.cs">
      <DependentUpon>DateTimeCriteriaDetails.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\UserControls\CriteriaUserControls\DateTimeCriteriaDetails.ascx.designer.cs">
      <DependentUpon>DateTimeCriteriaDetails.ascx</DependentUpon>
    </Compile>
    <Compile Include="Reports\UserControls\CriteriaUserControls\BasicCriteriaDetails.ascx.cs">
      <DependentUpon>BasicCriteriaDetails.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\UserControls\CriteriaUserControls\BasicCriteriaDetails.ascx.designer.cs">
      <DependentUpon>BasicCriteriaDetails.ascx</DependentUpon>
    </Compile>
    <Compile Include="Reports\UserControls\CriteriaUserControls\ToursCriteriaDetailsCalls.ascx.cs">
      <DependentUpon>ToursCriteriaDetailsCalls.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\UserControls\CriteriaUserControls\ToursCriteriaDetailsCalls.ascx.designer.cs">
      <DependentUpon>ToursCriteriaDetailsCalls.ascx</DependentUpon>
    </Compile>
    <Compile Include="Reports\UserControls\CriteriaUserControls\ToursCriteriaDetailsMarketing.ascx.cs">
      <DependentUpon>ToursCriteriaDetailsMarketing.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\UserControls\CriteriaUserControls\ToursCriteriaDetailsMarketing.ascx.designer.cs">
      <DependentUpon>ToursCriteriaDetailsMarketing.ascx</DependentUpon>
    </Compile>
    <Compile Include="Reports\UserControls\ReportByDateType.ascx.cs">
      <DependentUpon>ReportByDateType.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\UserControls\ReportByDateType.ascx.designer.cs">
      <DependentUpon>ReportByDateType.ascx</DependentUpon>
    </Compile>
    <Compile Include="Reports\UserControls\ReportConfigurationCommandsDateType.ascx.cs">
      <DependentUpon>ReportConfigurationCommandsDateType.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\UserControls\ReportConfigurationCommandsDateType.ascx.designer.cs">
      <DependentUpon>ReportConfigurationCommandsDateType.ascx</DependentUpon>
    </Compile>
    <Compile Include="Reports\UserControls\ReportConfigurationCommandsPrimary.ascx.cs">
      <DependentUpon>ReportConfigurationCommandsPrimary.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\UserControls\ReportConfigurationCommandsPrimary.ascx.designer.cs">
      <DependentUpon>ReportConfigurationCommandsPrimary.ascx</DependentUpon>
    </Compile>
    <Compile Include="Reports\UserControls\ReportCriteriaUserControls\ReportCriteriaAdvanced.ascx.cs">
      <DependentUpon>ReportCriteriaAdvanced.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\UserControls\ReportCriteriaUserControls\ReportCriteriaAdvanced.ascx.designer.cs">
      <DependentUpon>ReportCriteriaAdvanced.ascx</DependentUpon>
    </Compile>
    <Compile Include="Reports\UserControls\ReportCriteriaUserControls\ReportCriteriaBasic.ascx.cs">
      <DependentUpon>ReportCriteriaBasic.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\UserControls\ReportCriteriaUserControls\ReportCriteriaBasic.ascx.designer.cs">
      <DependentUpon>ReportCriteriaBasic.ascx</DependentUpon>
    </Compile>
    <Compile Include="Reports\UserControls\ReportCriteriaUserControls\ReportCriteriaControl.cs">
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\UserControls\ReportCriteriaUserControls\ReportCriteriaMarketing.ascx.cs">
      <DependentUpon>ReportCriteriaMarketing.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\UserControls\ReportCriteriaUserControls\ReportCriteriaMarketing.ascx.designer.cs">
      <DependentUpon>ReportCriteriaMarketing.ascx</DependentUpon>
    </Compile>
    <Compile Include="Reports\UserControls\ReportCriteriaUserControls\ReportCriteriaSales.ascx.cs">
      <DependentUpon>ReportCriteriaSales.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\UserControls\ReportCriteriaUserControls\ReportCriteriaSales.ascx.designer.cs">
      <DependentUpon>ReportCriteriaSales.ascx</DependentUpon>
    </Compile>
    <Compile Include="Reports\UserControls\ReportCriteriaUserControls\ReportCriteriaSearch.ascx.cs">
      <DependentUpon>ReportCriteriaSearch.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\UserControls\ReportCriteriaUserControls\ReportCriteriaSearch.ascx.designer.cs">
      <DependentUpon>ReportCriteriaSearch.ascx</DependentUpon>
    </Compile>
    <Compile Include="Reports\UserControls\ReportBaseUserControl.cs">
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\UserControls\ReportThenBy.ascx.cs">
      <DependentUpon>ReportThenBy.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\UserControls\ReportThenBy.ascx.designer.cs">
      <DependentUpon>ReportThenBy.ascx</DependentUpon>
    </Compile>
    <Compile Include="Reports\UserControls\ReportThenByDateType.ascx.cs">
      <DependentUpon>ReportThenByDateType.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\UserControls\ReportThenByDateType.ascx.designer.cs">
      <DependentUpon>ReportThenByDateType.ascx</DependentUpon>
    </Compile>
    <Compile Include="Reports\UserControls\ReportViewTypeReportKpis.ascx.cs">
      <DependentUpon>ReportViewTypeReportKpis.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\UserControls\ReportViewTypeReportKpis.ascx.designer.cs">
      <DependentUpon>ReportViewTypeReportKpis.ascx</DependentUpon>
    </Compile>
    <Compile Include="Reports\UserControls\ReportViewTypeReportKpis.ascx.ReportSort.cs">
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UpdateAuthenticationTime.cs" />
    <Compile Include="UserControls\Breadcrumb.ascx.cs">
      <DependentUpon>Breadcrumb.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\Breadcrumb.ascx.designer.cs">
      <DependentUpon>Breadcrumb.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\MultiColumnMenu.ascx.cs">
      <DependentUpon>MultiColumnMenu.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\MultiColumnMenu.ascx.designer.cs">
      <DependentUpon>MultiColumnMenu.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\SortedLinkCommandsLists.ascx.cs">
      <DependentUpon>SortedLinkCommandsLists.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\SortedLinkCommandsLists.ascx.designer.cs">
      <DependentUpon>SortedLinkCommandsLists.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\CachedSiteContentPart.ascx.cs">
      <DependentUpon>CachedSiteContentPart.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\CachedSiteContentPart.ascx.designer.cs">
      <DependentUpon>CachedSiteContentPart.ascx</DependentUpon>
    </Compile>
    <Compile Include="WebControls\ArgumentLinkButton.cs" />
    <Compile Include="WebControls\CellAlignmentFieldService.cs" />
    <Compile Include="WebControls\EmailTargetedCssFieldValidator.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="WebControls\EmailValidator.cs" />
    <Compile Include="WebControls\ExtendedBoundField.cs" />
    <Compile Include="WebControls\ExtendedCheckBox.cs" />
    <Compile Include="WebControls\ExtendedCheckBoxField.cs" />
    <Compile Include="WebControls\ExtendedDropDownList.cs" />
    <Compile Include="WebControls\ExtendedGridView.cs" />
    <Compile Include="WebControls\ExtendedHyperLink.cs" />
    <Compile Include="WebControls\ExtendedHyperLinkField.cs" />
    <Compile Include="WebControls\ExtendedLinkButton.cs" />
    <Compile Include="WebControls\ExtendedTemplateField.cs" />
    <Compile Include="WebControls\ExtendedTextBox.cs" />
    <Compile Include="WebControls\GridViewCommandLinkButton.cs" />
    <Compile Include="WebControls\IAdministratedControl.cs" />
    <Compile Include="WebControls\IEnabledControl.cs" />
    <Compile Include="WebControls\InsertGridView.cs" />
    <Compile Include="WebControls\IntegerTargetedCssRegularExpressionValidator.cs" />
    <Compile Include="WebControls\LabelGridViewTemplate.cs" />
    <Compile Include="WebControls\LabelHyperLinkGridViewTemplate.cs" />
    <Compile Include="WebControls\Mirror.cs" />
    <Compile Include="WebControls\NestedTableGridViewTemplate.cs" />
    <Compile Include="WebControls\PhoneTargetedCssFieldValidator.cs" />
    <Compile Include="WebControls\DecimalTargetedCssFieldValidator.cs" />
    <Compile Include="WebControls\ValidationSummaryExtended.cs" />
    <Compile Include="WebControls\WebControlButtonContract.cs" />
    <Compile Include="WebControls\WebControlButtonExtensions.cs" />
    <Compile Include="WebControls\WebControlButtonType.cs" />
    <Compile Include="_Test\AsyncPanel.aspx.cs">
      <DependentUpon>AsyncPanel.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="_Test\AsyncPanel.aspx.designer.cs">
      <DependentUpon>AsyncPanel.aspx</DependentUpon>
    </Compile>
    <Compile Include="_Test\Buttons.aspx.cs">
      <DependentUpon>Buttons.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="_Test\Buttons.aspx.designer.cs">
      <DependentUpon>Buttons.aspx</DependentUpon>
    </Compile>
    <Compile Include="_Test\CollapsePanel.aspx.cs">
      <DependentUpon>CollapsePanel.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="_Test\CollapsePanel.aspx.designer.cs">
      <DependentUpon>CollapsePanel.aspx</DependentUpon>
    </Compile>
    <Compile Include="_Test\CustomerLookUp.aspx.cs">
      <DependentUpon>CustomerLookUp.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="_Test\CustomerLookUp.aspx.designer.cs">
      <DependentUpon>CustomerLookUp.aspx</DependentUpon>
    </Compile>
    <Compile Include="_Test\DashboardLayout.aspx.cs">
      <DependentUpon>DashboardLayout.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="_Test\DashboardLayout.aspx.designer.cs">
      <DependentUpon>DashboardLayout.aspx</DependentUpon>
    </Compile>
    <Compile Include="_Test\FormsUI.aspx.cs">
      <DependentUpon>FormsUI.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="_Test\FormsUI.aspx.designer.cs">
      <DependentUpon>FormsUI.aspx</DependentUpon>
    </Compile>
    <Compile Include="_Test\Logging.aspx.cs">
      <DependentUpon>Logging.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="_Test\Logging.aspx.designer.cs">
      <DependentUpon>Logging.aspx</DependentUpon>
    </Compile>
    <Compile Include="_Test\Palette.aspx.cs">
      <DependentUpon>Palette.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="_Test\Palette.aspx.designer.cs">
      <DependentUpon>Palette.aspx</DependentUpon>
    </Compile>
    <Compile Include="_Test\Panels.aspx.cs">
      <DependentUpon>Panels.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="_Test\Panels.aspx.designer.cs">
      <DependentUpon>Panels.aspx</DependentUpon>
    </Compile>
    <Compile Include="_Test\ReportCriteriaAdvanced.aspx.cs">
      <DependentUpon>ReportCriteriaAdvanced.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="_Test\ReportCriteriaAdvanced.aspx.designer.cs">
      <DependentUpon>ReportCriteriaAdvanced.aspx</DependentUpon>
    </Compile>
    <Compile Include="_Test\ReportCriteriaBasic.aspx.cs">
      <DependentUpon>ReportCriteriaBasic.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="_Test\ReportCriteriaBasic.aspx.designer.cs">
      <DependentUpon>ReportCriteriaBasic.aspx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\ManyPickLists.ascx.cs">
      <DependentUpon>ManyPickLists.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\ManyPickLists.ascx.designer.cs">
      <DependentUpon>ManyPickLists.ascx</DependentUpon>
    </Compile>
    <Compile Include="Customers\UserControls\NotesDetails.ascx.cs">
      <DependentUpon>NotesDetails.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Customers\UserControls\NotesDetails.ascx.designer.cs">
      <DependentUpon>NotesDetails.ascx</DependentUpon>
    </Compile>
    <Compile Include="Customers\Purchases\UserControls\PurchasesDetails.ascx.cs">
      <DependentUpon>PurchasesDetails.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Customers\Purchases\UserControls\PurchasesDetails.ascx.designer.cs">
      <DependentUpon>PurchasesDetails.ascx</DependentUpon>
    </Compile>
    <Compile Include="Customers\Tours\UserControls\ToursDetailsTours.ascx.cs">
      <DependentUpon>ToursDetailsTours.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Customers\Tours\UserControls\ToursDetailsTours.ascx.designer.cs">
      <DependentUpon>ToursDetailsTours.ascx</DependentUpon>
    </Compile>
    <Compile Include="Customers\Tours\UserControls\ToursMaster.ascx.cs">
      <DependentUpon>ToursMaster.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Customers\Tours\UserControls\ToursMaster.ascx.designer.cs">
      <DependentUpon>ToursMaster.ascx</DependentUpon>
    </Compile>
    <Compile Include="Customers\Purchases\UserControls\PurchasesList.ascx.cs">
      <DependentUpon>PurchasesList.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Customers\Purchases\UserControls\PurchasesList.ascx.designer.cs">
      <DependentUpon>PurchasesList.ascx</DependentUpon>
    </Compile>
    <Compile Include="App_ProjectCode\Keys\RouteNameKeys.cs" />
    <Compile Include="App_ProjectCode\Keys\RouteValueKeys.cs" />
    <Compile Include="Customers\UserControls\CustomersDetails.ascx.cs">
      <DependentUpon>CustomersDetails.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Customers\UserControls\CustomersDetails.ascx.designer.cs">
      <DependentUpon>CustomersDetails.ascx</DependentUpon>
    </Compile>
    <Compile Include="MasterPages\MasterPageBase.cs">
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="MultipleUpdates\MultipleUpdateDeleteTours.aspx.cs">
      <DependentUpon>MultipleUpdateDeleteTours.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="MultipleUpdates\MultipleUpdateDeleteTours.aspx.designer.cs">
      <DependentUpon>MultipleUpdateDeleteTours.aspx</DependentUpon>
    </Compile>
    <Compile Include="MultipleUpdates\MultipleUpdateRescheduleToLeads.aspx.cs">
      <DependentUpon>MultipleUpdateRescheduleToLeads.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="MultipleUpdates\MultipleUpdateRescheduleToLeads.aspx.designer.cs">
      <DependentUpon>MultipleUpdateRescheduleToLeads.aspx</DependentUpon>
    </Compile>
    <Compile Include="App_ProjectCode\PageBase.cs">
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="App_ProjectCode\Providers\CookieProfileProvider.cs" />
    <Compile Include="App_ProjectCode\Providers\SecurityProfileProvider.cs" />
    <Compile Include="App_ProjectCode\Providers\SessionProfileProvider.cs" />
    <Compile Include="Reports\CancellationRequestDetailReport.aspx.cs">
      <DependentUpon>CancellationRequestDetailReport.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\CancellationRequestDetailReport.aspx.designer.cs">
      <DependentUpon>CancellationRequestDetailReport.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\CallCenterDetailReport.aspx.cs">
      <DependentUpon>CallCenterDetailReport.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\CallCenterDetailReport.aspx.designer.cs">
      <DependentUpon>CallCenterDetailReport.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\LeadsRatioEfficiencyReport.aspx.cs">
      <DependentUpon>LeadsRatioEfficiencyReport.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\LeadsRatioEfficiencyReport.aspx.designer.cs">
      <DependentUpon>LeadsRatioEfficiencyReport.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\Report.master.cs">
      <DependentUpon>Report.master</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\Report.master.designer.cs">
      <DependentUpon>Report.master</DependentUpon>
    </Compile>
    <Compile Include="Reports\TourStatusEffeciencyReport.aspx.cs">
      <DependentUpon>TourStatusEffeciencyReport.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\TourStatusEffeciencyReport.aspx.designer.cs">
      <DependentUpon>TourStatusEffeciencyReport.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\RevenueReport.aspx.cs">
      <DependentUpon>RevenueReport.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\RevenueReport.aspx.designer.cs">
      <DependentUpon>RevenueReport.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\CreateCancellationRequestReport.aspx.cs">
      <DependentUpon>CreateCancellationRequestReport.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\CreateCancellationRequestReport.aspx.designer.cs">
      <DependentUpon>CreateCancellationRequestReport.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\MultipleUpdates\CreatePurchases.aspx.cs">
      <DependentUpon>CreatePurchases.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\MultipleUpdates\CreatePurchases.aspx.designer.cs">
      <DependentUpon>CreatePurchases.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\CreatePurchasesReport.aspx.cs">
      <DependentUpon>CreatePurchasesReport.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\CreatePurchasesReport.aspx.designer.cs">
      <DependentUpon>CreatePurchasesReport.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\PenderEfficiencyReport.aspx.cs">
      <DependentUpon>PenderEfficiencyReport.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\PenderEfficiencyReport.aspx.designer.cs">
      <DependentUpon>PenderEfficiencyReport.aspx</DependentUpon>
    </Compile>
    <Compile Include="MultipleUpdates\MultipleUpdateToursMarketingTours.aspx.cs">
      <DependentUpon>MultipleUpdateToursMarketingTours.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="MultipleUpdates\MultipleUpdateToursMarketingTours.aspx.designer.cs">
      <DependentUpon>MultipleUpdateToursMarketingTours.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\UpdateSalesInformationReport.aspx.cs">
      <DependentUpon>UpdateSalesInformationReport.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\UpdateSalesInformationReport.aspx.designer.cs">
      <DependentUpon>UpdateSalesInformationReport.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\MultipleUpdates\CreateCancellationRequests.aspx.cs">
      <DependentUpon>CreateCancellationRequests.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\MultipleUpdates\CreateCancellationRequests.aspx.designer.cs">
      <DependentUpon>CreateCancellationRequests.aspx</DependentUpon>
    </Compile>
    <Compile Include="MultipleUpdates\MultipleUpdateCancellations.aspx.cs">
      <DependentUpon>MultipleUpdateCancellations.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="MultipleUpdates\MultipleUpdateCancellations.aspx.designer.cs">
      <DependentUpon>MultipleUpdateCancellations.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\UserControls\CriteriaUserControls\CustomersCriteriaDetails.ascx.cs">
      <DependentUpon>CustomersCriteriaDetails.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\UserControls\CriteriaUserControls\CustomersCriteriaDetails.ascx.designer.cs">
      <DependentUpon>CustomersCriteriaDetails.ascx</DependentUpon>
    </Compile>
    <Compile Include="Reports\UserControls\CriteriaUserControls\PurchasesCriteriaDetails.ascx.cs">
      <DependentUpon>PurchasesCriteriaDetails.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\UserControls\CriteriaUserControls\PurchasesCriteriaDetails.ascx.designer.cs">
      <DependentUpon>PurchasesCriteriaDetails.ascx</DependentUpon>
    </Compile>
    <Compile Include="Reports\UserControls\CriteriaUserControls\ToursCriteriaDetailsLeads.ascx.cs">
      <DependentUpon>ToursCriteriaDetailsLeads.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\UserControls\CriteriaUserControls\ToursCriteriaDetailsLeads.ascx.designer.cs">
      <DependentUpon>ToursCriteriaDetailsLeads.ascx</DependentUpon>
    </Compile>
    <Compile Include="Reports\UserControls\CriteriaUserControls\ToursCriteriaDetailsSales.ascx.cs">
      <DependentUpon>ToursCriteriaDetailsSales.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\UserControls\CriteriaUserControls\ToursCriteriaDetailsSales.ascx.designer.cs">
      <DependentUpon>ToursCriteriaDetailsSales.ascx</DependentUpon>
    </Compile>
    <Compile Include="Reports\UserControls\CriteriaUserControls\ToursCriteriaDetailsTours.ascx.cs">
      <DependentUpon>ToursCriteriaDetailsTours.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\UserControls\CriteriaUserControls\ToursCriteriaDetailsTours.ascx.designer.cs">
      <DependentUpon>ToursCriteriaDetailsTours.ascx</DependentUpon>
    </Compile>
    <Compile Include="Reports\UserControls\ReportCommands.ascx.cs">
      <DependentUpon>ReportCommands.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\UserControls\ReportCommands.ascx.designer.cs">
      <DependentUpon>ReportCommands.ascx</DependentUpon>
    </Compile>
    <Compile Include="Reports\UserControls\ReportConfigurationDescription.ascx.cs">
      <DependentUpon>ReportConfigurationDescription.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\UserControls\ReportConfigurationDescription.ascx.designer.cs">
      <DependentUpon>ReportConfigurationDescription.ascx</DependentUpon>
    </Compile>
    <Compile Include="Reports\UserControls\ReportConfigurationCommands.ascx.cs">
      <DependentUpon>ReportConfigurationCommands.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\UserControls\ReportConfigurationCommands.ascx.designer.cs">
      <DependentUpon>ReportConfigurationCommands.ascx</DependentUpon>
    </Compile>
    <Compile Include="Reports\UserControls\ReportTitleSave.ascx.cs">
      <DependentUpon>ReportTitleSave.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\UserControls\ReportTitleSave.ascx.designer.cs">
      <DependentUpon>ReportTitleSave.ascx</DependentUpon>
    </Compile>
    <Compile Include="SiteContentDraftPage.aspx.cs">
      <DependentUpon>SiteContentDraftPage.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="SiteContentDraftPage.aspx.designer.cs">
      <DependentUpon>SiteContentDraftPage.aspx</DependentUpon>
    </Compile>
    <Compile Include="SiteContentPage.aspx.cs">
      <DependentUpon>SiteContentPage.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="SiteContentPage.aspx.designer.cs">
      <DependentUpon>SiteContentPage.aspx</DependentUpon>
    </Compile>
    <Compile Include="_Test\CustomersDetails.aspx.cs">
      <DependentUpon>CustomersDetails.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="_Test\CustomersDetails.aspx.designer.cs">
      <DependentUpon>CustomersDetails.aspx</DependentUpon>
    </Compile>
    <Compile Include="_Test\PurchasesDetails.aspx.cs">
      <DependentUpon>PurchasesDetails.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="_Test\PurchasesDetails.aspx.designer.cs">
      <DependentUpon>PurchasesDetails.aspx</DependentUpon>
    </Compile>
    <Compile Include="_Test\Scratch.aspx.cs">
      <DependentUpon>Scratch.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="_Test\Scratch.aspx.designer.cs">
      <DependentUpon>Scratch.aspx</DependentUpon>
    </Compile>
    <Compile Include="_Test\ScratchBlank.aspx.cs">
      <DependentUpon>ScratchBlank.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="_Test\ScratchBlank.aspx.designer.cs">
      <DependentUpon>ScratchBlank.aspx</DependentUpon>
    </Compile>
    <Compile Include="_Test\Tables.aspx.cs">
      <DependentUpon>Tables.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="_Test\Tables.aspx.designer.cs">
      <DependentUpon>Tables.aspx</DependentUpon>
    </Compile>
    <Compile Include="_Test\TablesAlign.aspx.cs">
      <DependentUpon>TablesAlign.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="_Test\TablesAlign.aspx.designer.cs">
      <DependentUpon>TablesAlign.aspx</DependentUpon>
    </Compile>
    <Compile Include="_Test\ToursDetails.aspx.cs">
      <DependentUpon>ToursDetails.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="_Test\ToursDetails.aspx.designer.cs">
      <DependentUpon>ToursDetails.aspx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\MilestoneStatePickLists.ascx.cs">
      <DependentUpon>MilestoneStatePickLists.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\MilestoneStatePickLists.ascx.designer.cs">
      <DependentUpon>MilestoneStatePickLists.ascx</DependentUpon>
    </Compile>
    <Compile Include="WebControls\ClickExtender\ClickExtender.cs" />
    <Compile Include="WebControls\DynamicLoadComboBox\DynamicLoadComboBox.cs" />
    <Compile Include="Reports\MultipleUpdates\UpdateSalesInformation.aspx.cs">
      <DependentUpon>UpdateSalesInformation.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\MultipleUpdates\UpdateSalesInformation.aspx.designer.cs">
      <DependentUpon>UpdateSalesInformation.aspx</DependentUpon>
    </Compile>
    <Compile Include="Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Default.aspx.designer.cs">
      <DependentUpon>Default.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\Locations\LocationsDetails.aspx.cs">
      <DependentUpon>LocationsDetails.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\Locations\LocationsDetails.aspx.designer.cs">
      <DependentUpon>LocationsDetails.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\Locations\LocationsMaster.aspx.cs">
      <DependentUpon>LocationsMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\Locations\LocationsMaster.aspx.designer.cs">
      <DependentUpon>LocationsMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\Locations\UserControls\LocationsGifts.ascx.cs">
      <DependentUpon>LocationsGifts.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\Locations\UserControls\LocationsGifts.ascx.designer.cs">
      <DependentUpon>LocationsGifts.ascx</DependentUpon>
    </Compile>
    <Compile Include="Administration\Locations\UserControls\LocationsTimes.ascx.cs">
      <DependentUpon>LocationsTimes.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\Locations\UserControls\LocationsTimes.ascx.designer.cs">
      <DependentUpon>LocationsTimes.ascx</DependentUpon>
    </Compile>
    <Compile Include="Errors\UnauthorizedAccess.aspx.cs">
      <DependentUpon>UnauthorizedAccess.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Errors\UnauthorizedAccess.aspx.designer.cs">
      <DependentUpon>UnauthorizedAccess.aspx</DependentUpon>
    </Compile>
    <Compile Include="MasterPages\Default.Master.cs">
      <DependentUpon>Default.Master</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="MasterPages\Default.Master.designer.cs">
      <DependentUpon>Default.Master</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Reports\CancellationRequestEfficiencyReport.aspx.cs">
      <DependentUpon>CancellationRequestEfficiencyReport.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\CancellationRequestEfficiencyReport.aspx.designer.cs">
      <DependentUpon>CancellationRequestEfficiencyReport.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\LostVolumeReport.aspx.cs">
      <DependentUpon>LostVolumeReport.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\LostVolumeReport.aspx.designer.cs">
      <DependentUpon>LostVolumeReport.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\SalesReport.aspx.cs">
      <DependentUpon>SalesReport.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\SalesReport.aspx.designer.cs">
      <DependentUpon>SalesReport.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\Default.aspx.designer.cs">
      <DependentUpon>Default.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\UpdateToursStatusReport.aspx.cs">
      <DependentUpon>UpdateToursStatusReport.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\UpdateToursStatusReport.aspx.designer.cs">
      <DependentUpon>UpdateToursStatusReport.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\ReportBase.cs">
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\TourReport.aspx.cs">
      <DependentUpon>TourReport.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\TourReport.aspx.designer.cs">
      <DependentUpon>TourReport.aspx</DependentUpon>
    </Compile>
    <Compile Include="Security\Login.aspx.cs">
      <DependentUpon>Login.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Security\Login.aspx.designer.cs">
      <DependentUpon>Login.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\TeamsMaster.aspx.cs">
      <DependentUpon>TeamsMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\TeamsMaster.aspx.designer.cs">
      <DependentUpon>TeamsMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Security\RecoverPassword.aspx.cs">
      <DependentUpon>RecoverPassword.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Security\RecoverPassword.aspx.designer.cs">
      <DependentUpon>RecoverPassword.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\Users\UsersDetails.aspx.cs">
      <DependentUpon>UsersDetails.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\Users\UsersDetails.aspx.designer.cs">
      <DependentUpon>UsersDetails.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\Users\UsersMaster.aspx.cs">
      <DependentUpon>UsersMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\Users\UsersMaster.aspx.designer.cs">
      <DependentUpon>UsersMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="WebControls\ExtendedRadDatePicker.cs" />
    <Compile Include="WebControls\ExtendedRadTimePicker.cs" />
    <Compile Include="WebControls\FeaturePlaceHolder.cs" />
    <Compile Include="WebControls\FeatureTemplateField.cs" />
    <Compile Include="WebControls\FeatureTextBox.cs" />
    <Compile Include="WebControls\ModifyDropDownList.cs" />
    <Compile Include="WebControls\ModifyTextBox.cs" />
    <Compile Include="WebControls\PostViewStateExtender\DropDownListPostViewStateExtender.cs" />
    <Compile Include="WebControls\PostViewStateExtender\PostViewStateExtender.cs" />
    <Compile Include="WebControls\ResourcesBooleanDropDownList.cs" />
    <Compile Include="WebControls\SystemAdministratorPlaceHolder.cs" />
    <Compile Include="WebControls\TypedCheckboxComboBox\TypedCheckboxComboBox.cs" />
    <Compile Include="WebServices\PickListItemsWebService.asmx.cs">
      <DependentUpon>PickListItemsWebService.asmx</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="_Test\WebAccounts.aspx.cs">
      <DependentUpon>WebAccounts.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="_Test\WebAccounts.aspx.designer.cs">
      <DependentUpon>WebAccounts.aspx</DependentUpon>
    </Compile>
    <Compile Include="_Test\_Cache.aspx.cs">
      <DependentUpon>_Cache.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="_Test\_Cache.aspx.designer.cs">
      <DependentUpon>_Cache.aspx</DependentUpon>
    </Compile>
    <Compile Include="_Test\_Unsecure\ProfileViewer.aspx.cs">
      <DependentUpon>ProfileViewer.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="_Test\_Unsecure\ProfileViewer.aspx.designer.cs">
      <DependentUpon>ProfileViewer.aspx</DependentUpon>
    </Compile>
    <Compile Include="_Test\_Upgrades.aspx.cs">
      <DependentUpon>_Upgrades.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="_Test\_Upgrades.aspx.designer.cs">
      <DependentUpon>_Upgrades.aspx</DependentUpon>
    </Compile>
    <Compile Include="_Test\_Unsecure\SecureCriteriaBuilder.aspx.cs">
      <DependentUpon>SecureCriteriaBuilder.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="_Test\_Unsecure\SecureCriteriaBuilder.aspx.designer.cs">
      <DependentUpon>SecureCriteriaBuilder.aspx</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Default.aspx" />
    <Content Include="MasterPages\Default.Master" />
    <Content Include="Administration\TeamsMaster.aspx" />
    <Content Include="Administration\Users\UsersDetails.aspx" />
    <Content Include="Administration\Users\UsersMaster.aspx" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\BES\BES.csproj">
      <Project>{A2BF1720-80D4-4FE9-9C55-A02FE961057E}</Project>
      <Name>BES</Name>
    </ProjectReference>
    <ProjectReference Include="..\Common\Common.csproj">
      <Project>{0A0866DA-2AEB-414C-AF82-B1ABDBFCB789}</Project>
      <Name>Common</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Administration\Default.aspx" />
    <Content Include="Administration\PickLists\VenuesMaster.aspx" />
    <Content Include="Administration\Locations\LocationsDetails.aspx" />
    <Content Include="Administration\Locations\LocationsMaster.aspx" />
    <Content Include="Administration\Locations\UserControls\LocationsGifts.ascx" />
    <Content Include="Administration\Locations\UserControls\LocationsTimes.ascx" />
    <Content Include="Reports\Default.aspx" />
    <Content Include="Reports\TourReport.aspx" />
    <Content Include="Security\Login.aspx" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Web.sitemap">
      <SubType>Designer</SubType>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Account\ChangePassword.aspx" />
    <Content Include="Administration\System\Default.aspx" />
    <Content Include="Administration\System\DefinedTypes\TourStatusTypesMaster.aspx" />
    <Content Include="Reports\MultipleUpdates\UpdateSalesInformation.aspx" />
    <Content Include="Errors\UnauthorizedAccess.aspx" />
    <Content Include="Reports\ManifestReport.aspx" />
    <Content Include="Reports\SalesReport.aspx" />
    <Content Include="Reports\UpdateToursStatusReport.aspx" />
    <Content Include="Security\RecoverPassword.aspx" />
    <Content Include="Security\RecoverPasswordEmailBody.txt" />
    <Content Include="UserControls\Navigation.ascx" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Administration\PickLists\CampaignsMaster.aspx.cs">
      <DependentUpon>CampaignsMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\PickLists\CampaignsMaster.aspx.designer.cs">
      <DependentUpon>CampaignsMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\PickLists\HotelsMaster.aspx.cs">
      <DependentUpon>HotelsMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\PickLists\HotelsMaster.aspx.designer.cs">
      <DependentUpon>HotelsMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\ImportExport\BookingsFromCsv.aspx.cs">
      <DependentUpon>BookingsFromCsv.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\ImportExport\BookingsFromCsv.aspx.designer.cs">
      <DependentUpon>BookingsFromCsv.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\Locations\UserControls\LocationsHotels.ascx.cs">
      <DependentUpon>LocationsHotels.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\Locations\UserControls\LocationsHotels.ascx.designer.cs">
      <DependentUpon>LocationsHotels.ascx</DependentUpon>
    </Compile>
    <Compile Include="Administration\Locations\UserControls\LocationsVenues.ascx.cs">
      <DependentUpon>LocationsVenues.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\Locations\UserControls\LocationsVenues.ascx.designer.cs">
      <DependentUpon>LocationsVenues.ascx</DependentUpon>
    </Compile>
    <Compile Include="Administration\PickLists\ProductsMaster.aspx.cs">
      <DependentUpon>ProductsMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\PickLists\ProductsMaster.aspx.designer.cs">
      <DependentUpon>ProductsMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\System\DemoSettings.aspx.cs">
      <DependentUpon>DemoSettings.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\System\DemoSettings.aspx.designer.cs">
      <DependentUpon>DemoSettings.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\System\Logging.aspx.cs">
      <DependentUpon>Logging.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\System\Logging.aspx.designer.cs">
      <DependentUpon>Logging.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\System\WebAccountsMaster.aspx.cs">
      <DependentUpon>WebAccountsMaster.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\System\WebAccountsMaster.aspx.designer.cs">
      <DependentUpon>WebAccountsMaster.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\TeamsMaster.aspx.BusinessLayer.cs">
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\UserControls\WebAccountsDetails.ascx.cs">
      <DependentUpon>WebAccountsDetails.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\UserControls\WebAccountsDetails.ascx.designer.cs">
      <DependentUpon>WebAccountsDetails.ascx</DependentUpon>
    </Compile>
    <Compile Include="App_ProjectCode\Adapters\CustomPageAdapter.cs" />
    <Compile Include="App_ProjectCode\Exceptions\Handlers\404Handler.cs" />
    <Compile Include="App_ProjectCode\Exceptions\Handlers\DefaultMessageHandler.cs" />
    <Compile Include="App_ProjectCode\Exceptions\Handlers\HttpExceptionHandler.cs" />
    <Compile Include="App_ProjectCode\Exceptions\Handlers\UnauthorizedHandler.cs" />
    <Compile Include="App_ProjectCode\Exceptions\Handlers\WrappedExceptionHandler.cs" />
    <Compile Include="App_ProjectCode\Keys\QueryStringKeys.cs" />
    <Compile Include="App_ProjectCode\Providers\ExtendedSqlRoleProvider.cs" />
    <Compile Include="App_ProjectCode\Services\PageControlDisplayService.cs" />
    <Compile Include="App_ProjectCode\Providers\Globalization\DatabaseResourceProvider.cs" />
    <Compile Include="App_ProjectCode\Providers\Globalization\DatabaseResourceProviderFactory.cs" />
    <Compile Include="App_ProjectCode\Providers\Globalization\DatabaseResourceReader.cs" />
    <Compile Include="App_ProjectCode\Security\CollectionDisplayRules.cs" />
    <Compile Include="App_ProjectCode\Security\DataTableDisplayRules.cs" />
    <Compile Include="App_ProjectCode\Security\DropDownDisplay\DropDown.cs" />
    <Compile Include="App_ProjectCode\Security\DropDownDisplay\CustomReportTypes.cs" />
    <Compile Include="App_ProjectCode\Security\DropDownDisplay\SaleStatusTypes.cs" />
    <Compile Include="App_ProjectCode\Security\DropDownDisplay\TourStatusTypes.cs" />
    <Compile Include="Errors\404.aspx.cs">
      <DependentUpon>404.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Errors\404.aspx.designer.cs">
      <DependentUpon>404.aspx</DependentUpon>
    </Compile>
    <Compile Include="Errors\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Errors\Default.aspx.designer.cs">
      <DependentUpon>Default.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\SalesTourReport.aspx.cs">
      <DependentUpon>SalesTourReport.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\ManifestReport.aspx.cs">
      <DependentUpon>ManifestReport.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\ManifestReport.aspx.designer.cs">
      <DependentUpon>ManifestReport.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\UserControls\ReportBy.ascx.cs">
      <DependentUpon>ReportBy.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\UserControls\ReportBy.ascx.designer.cs">
      <DependentUpon>ReportBy.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\CollapsePanel.ascx.cs">
      <DependentUpon>CollapsePanel.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\CollapsePanel.ascx.designer.cs">
      <DependentUpon>CollapsePanel.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\MessageBox.ascx.cs">
      <DependentUpon>MessageBox.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\MessageBox.ascx.designer.cs">
      <DependentUpon>MessageBox.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\Navigation.ascx.cs">
      <DependentUpon>Navigation.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\Navigation.ascx.designer.cs">
      <DependentUpon>Navigation.ascx</DependentUpon>
    </Compile>
    <Compile Include="WebControls\BinaryPropertyExtender\BinaryPropertyExtender.cs" />
    <Compile Include="WebControls\CheckBoxCheckingExtender\CheckBoxCheckingExtender.cs" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Administration\ImportExport\BookingsFromCsv.aspx" />
    <Content Include="Administration\PickLists\ProductsMaster.aspx" />
    <Content Include="Administration\System\WebAccountsMaster.aspx" />
    <Content Include="Administration\UserControls\WebAccountsDetails.ascx" />
    <Content Include="Errors\404.aspx" />
    <Content Include="Errors\Default.aspx" />
    <Content Include="UserControls\MessageBox.ascx" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Administration\PickLists\CampaignsMaster.aspx" />
    <Content Include="Administration\PickLists\HotelsMaster.aspx" />
    <Content Include="Administration\Locations\UserControls\LocationsHotels.ascx" />
    <Content Include="Administration\Locations\UserControls\LocationsVenues.ascx" />
    <Content Include="Administration\System\DemoSettings.aspx" />
    <Content Include="Administration\System\Logging.aspx" />
    <Content Include="Reports\SalesTourReport.aspx" />
    <Content Include="Reports\UserControls\ReportBy.ascx" />
    <Content Include="content\d\scripts\date-range-utility-4.js" />
    <Content Include="WebControls\BinaryPropertyExtender\BinaryPropertyExtender.js" />
    <Content Include="WebControls\CheckBoxCheckingExtender\CheckBoxCheckingExtender.js" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="favicon.ico" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Administration\ImportExport\ImportFromCsv.master" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Administration\ImportExport\ImportFromCsv.master.Business.cs">
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Administration\PickLists\PickListMaster.master" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Reports\Report.master" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Administration\ImportExport\Web.config">
      <SubType>Designer</SubType>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <Content Include="content\d\less\base.less" />
    <Content Include="content\d\less\bootstrap-overrides-post.less" />
    <Content Include="content\d\less\bootstrap\less\alerts.less" />
    <Content Include="content\d\less\bootstrap\less\badges.less" />
    <Content Include="content\d\less\bootstrap\less\bootstrap.less" />
    <Content Include="content\d\less\bootstrap\less\breadcrumbs.less" />
    <Content Include="content\d\less\bootstrap\less\button-groups.less" />
    <Content Include="content\d\less\bootstrap\less\buttons.less" />
    <Content Include="content\d\less\bootstrap\less\carousel.less" />
    <Content Include="content\d\less\bootstrap\less\close.less" />
    <Content Include="content\d\less\bootstrap\less\code.less" />
    <Content Include="content\d\less\bootstrap\less\component-animations.less" />
    <Content Include="content\d\less\bootstrap\less\dropdowns.less" />
    <Content Include="content\d\less\bootstrap\less\forms.less" />
    <Content Include="content\d\less\bootstrap\less\glyphicons.less" />
    <Content Include="content\d\less\bootstrap\less\grid.less" />
    <Content Include="content\d\less\bootstrap\less\input-groups.less" />
    <Content Include="content\d\less\bootstrap\less\jumbotron.less" />
    <Content Include="content\d\less\bootstrap\less\labels.less" />
    <Content Include="content\d\less\bootstrap\less\list-group.less" />
    <Content Include="content\d\less\bootstrap\less\media.less" />
    <Content Include="content\d\less\bootstrap\less\mixins.less" />
    <Content Include="content\d\less\bootstrap\less\modals.less" />
    <Content Include="content\d\less\bootstrap\less\navbar.less" />
    <Content Include="content\d\less\bootstrap\less\navs.less" />
    <Content Include="content\d\less\bootstrap\less\normalize.less" />
    <Content Include="content\d\less\bootstrap\less\pager.less" />
    <Content Include="content\d\less\bootstrap\less\pagination.less" />
    <Content Include="content\d\less\bootstrap\less\panels.less" />
    <Content Include="content\d\less\bootstrap\less\popovers.less" />
    <Content Include="content\d\less\bootstrap\less\print.less" />
    <Content Include="content\d\less\bootstrap\less\progress-bars.less" />
    <Content Include="content\d\less\bootstrap\less\responsive-utilities.less" />
    <Content Include="content\d\less\bootstrap\less\scaffolding.less" />
    <Content Include="content\d\less\bootstrap\less\tables.less" />
    <Content Include="content\d\less\bootstrap\less\theme.less" />
    <Content Include="content\d\less\bootstrap\less\thumbnails.less" />
    <Content Include="content\d\less\bootstrap\less\tooltip.less" />
    <Content Include="content\d\less\bootstrap\less\type.less" />
    <Content Include="content\d\less\bootstrap\less\utilities.less" />
    <Content Include="content\d\less\bootstrap\less\variables.less" />
    <Content Include="content\d\less\bootstrap\less\wells.less" />
    <Content Include="content\d\less\chrome.less" />
    <Content Include="content\d\less\panels.less" />
    <Content Include="content\d\less\text.less" />
    <Content Include="content\d\less\tools.less" />
    <Content Include="content\d\less\variables.less" />
    <Content Include="content\d\css\style-standard-44.less" />
    <Content Include="content\web.config" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="App_Themes\V3\Default.skin" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="content\d\less\v1-to-v3-transition.less" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="content\d\less\components.less" />
    <Content Include="content\d\css\style-telerik-overrides-2.less" />
    <Content Include="content\d\less\tables.less" />
    <Content Include="App_Browsers\Default.browser" />
    <Content Include="content\d\less\bootstrap\less\_ChangesToBootstrap.text" />
    <Content Include="content\d\less\bootstrap-overrides-pre.less" />
    <Content Include="content\d\less\print.less" />
    <Content Include="libman.json" />
    <None Include="Properties\PublishProfiles\INDUSTRYTR - Web Deploy.pubxml" />
    <None Include="Properties\PublishProfiles\p.pubxml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Properties\licenses.licx" />
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Connected Services\" />
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">15.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != '' And Exists('$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets')" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v15.0\WebApplications\Microsoft.WebApplication.targets" Condition="!Exists('$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets') And Exists('$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v15.0\WebApplications\Microsoft.WebApplication.targets')" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="!Exists('$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets') And !Exists('$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v15.0\WebApplications\Microsoft.WebApplication.targets')" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  -->
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>False</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>9933</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>http://localhost:9933/</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <PropertyGroup>
    <PostBuildEvent>if exist "$(ProjectDir)..\Common\Libraries\ComponentArt.UIFramework.lic" copy /Y "$(ProjectDir)..\Common\Libraries\ComponentArt.UIFramework.lic" "$(TargetDir)"
if exist "$(ProjectDir)..\Common\Libraries\ExtendedObjectDataSource.lic" copy /Y "$(ProjectDir)..\Common\Libraries\ExtendedObjectDataSource.lic" "$(TargetDir)"</PostBuildEvent>
  </PropertyGroup>
  <PropertyGroup>
    <PreBuildEvent>if exist "$(TargetPath).locked" del "$(TargetPath).locked"
if exist "$(TargetPath)" if not exist "$(TargetPath).locked" move "$(TargetPath)" "$(TargetPath).locked"</PreBuildEvent>
  </PropertyGroup>
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\Microsoft.VisualStudio.Azure.Containers.Tools.Targets.1.21.0\build\Microsoft.VisualStudio.Azure.Containers.Tools.Targets.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.VisualStudio.Azure.Containers.Tools.Targets.1.21.0\build\Microsoft.VisualStudio.Azure.Containers.Tools.Targets.props'))" />
    <Error Condition="!Exists('..\packages\Microsoft.VisualStudio.Azure.Containers.Tools.Targets.1.21.0\build\Microsoft.VisualStudio.Azure.Containers.Tools.Targets.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.VisualStudio.Azure.Containers.Tools.Targets.1.21.0\build\Microsoft.VisualStudio.Azure.Containers.Tools.Targets.targets'))" />
  </Target>
  <Import Project="..\packages\Microsoft.VisualStudio.Azure.Containers.Tools.Targets.1.21.0\build\Microsoft.VisualStudio.Azure.Containers.Tools.Targets.targets" Condition="Exists('..\packages\Microsoft.VisualStudio.Azure.Containers.Tools.Targets.1.21.0\build\Microsoft.VisualStudio.Azure.Containers.Tools.Targets.targets')" />
</Project>