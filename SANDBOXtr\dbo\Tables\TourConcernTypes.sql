﻿CREATE TABLE [dbo].[TourConcernTypes] (
    [tourConcernTypeID]   INT           IDENTITY (1, 1) NOT NULL,
    [tourConcernTypeName] NVARCHAR (64) NOT NULL,
    [tourConcernTypeCode] NVARCHAR (64) NULL,
    [active]              BIT           NOT NULL,
    CONSTRAINT [PK_TourConcernTypes] PRIMARY KEY CLUSTERED ([tourConcernTypeID] ASC),
    CONSTRAINT [UK_TourConcernTypes_tourConcernTypeName] UNIQUE NONCLUSTERED ([tourConcernTypeName] ASC)
);


GO
CREATE TRIGGER [dbo].[TourConcernTypes.InsertUpdateDelete]
    ON [dbo].[TourConcernTypes]
    FOR INSERT, UPDATE, DELETE
AS 
BEGIN
    SET NOCOUNT ON;

    DECLARE @Activity  NVARCHAR (8), @id INT;

    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'updated'
    END

    IF EXISTS (SELECT * FROM inserted) AND NOT EXISTS(SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'inserted'
    END

    IF EXISTS (SELECT * FROM deleted) AND NOT EXISTS(SELECT * FROM inserted)
    BEGIN
        SET @Activity = 'deleted'
    END

    

    IF (@Activity = 'deleted')
    BEGIN
        DECLARE cur CURSOR FOR SELECT tourConcernTypeID FROM deleted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'TourConcernTypes', @id, 'tourConcernTypeID',
                (
                    SELECT *
                    FROM deleted i 
                    WHERE i.tourConcernTypeID =  @id
                    FOR JSON AUTO
                ),0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
    ELSE
    BEGIN
        DECLARE cur CURSOR
        FOR SELECT tourConcernTypeID
        FROM inserted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'TourConcernTypes', @id, 'tourConcernTypeID',
                (
                    SELECT *
                    FROM inserted i 
                    WHERE i.tourConcernTypeID =  @id
                    FOR JSON AUTO
                ), 0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
END

GO
DISABLE TRIGGER [dbo].[TourConcernTypes.InsertUpdateDelete]
    ON [dbo].[TourConcernTypes];

