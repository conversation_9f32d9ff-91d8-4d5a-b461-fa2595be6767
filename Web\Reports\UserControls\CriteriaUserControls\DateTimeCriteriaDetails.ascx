﻿<%@ Control Language="C#" CodeBehind="DateTimeCriteriaDetails.ascx.cs" Inherits="TrackResults.Web.Reports.UserControls.CriteriaUserControls.DateTimeCriteriaDetails" %>

<style>
    .dropdown {
        position: relative;
        display: inline-block;
    }

    .dropdown-content {
        display: none;
        position: absolute;
        background-color: #F9F9F9;
        min-width: 160px;
        box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
        z-index: 1;
        max-height: 150px; /* Maximum height before scrolling */
        overflow-y: auto; /* Add vertical scrollbar */
    }
        /* Items inside the dropdown */

        .dropdown-content a {
            color: black;
            padding: 12px 16px;
            text-decoration: none;
            display: block;
        }
    /* Show the dropdown on hover */

    .dropdown:hover .dropdown-content {
        display: block;
    }
    /* Debugging CSS to see the container's boundaries */

#dropDownWeeks {
    border: 1px solid blue; /* Add border to the dropdown control */
    max-height: 200px;      /* Set max height */
    overflow-y: auto;       /* Enable vertical scrolling */
}
</style>
<trwc:FeaturePlaceHolder runat="server" FeatureKey="Report.DateTimeSection.Filter">

    <manu:ExtendedObjectDataSource ID="dataSourceDateRanges" runat="server" SelectMethod="GetDateRanges"
        TypeName="TrackResults.Web.Reports.UserControls.CriteriaUserControls.DateTimeCriteriaDetails" />
    <manu:ExtendedObjectDataSource ID="dataSourceYearRanges" runat="server" SelectMethod="GetYearRanges"
        TypeName="TrackResults.Web.Reports.UserControls.CriteriaUserControls.DateTimeCriteriaDetails" />
    <manu:ExtendedObjectDataSource ID="dataSourceDaysOfWeek" runat="server" SelectMethod="GetDaysOfWeek"
        TypeName="TrackResults.Web.Reports.UserControls.CriteriaUserControls.DateTimeCriteriaDetails" />
    <manu:ExtendedObjectDataSource ID="dataSourceTierOneDateTypes" runat="server" SelectMethod="GetTierOneDateTypeIDNames"
        TypeName="TrackResults.Web.Reports.UserControls.CriteriaUserControls.DateTimeCriteriaDetails" />

    <manu:ExtendedObjectDataSource ID="dataSourceTourTimes" runat="server" SelectMethod="SelectTimes"
        TypeName="TrackResults.BES.DataAccess.Secure.WavesSecureDataAccess" />
    <manu:ExtendedObjectDataSource ID="dataSourceWeeks" runat="server" SelectMethod="GetData"
        TypeName="TrackResults.BES.Data.Cache.Static.WeeksCache" />
    <manu:ExtendedObjectDataSource ID="dataSourceMonths" runat="server" SelectMethod="GetData"
        TypeName="TrackResults.BES.Data.Cache.Static.MonthsCache" />
    <manu:ExtendedObjectDataSource ID="dataSourceYears" runat="server" SelectMethod="SelectYearRange"
        TypeName="TrackResults.BES.DataAccess.DateTimeDataAccess" />

    <manu:ExtendedObjectDataSource ID="dataSourceDateTypeLogic" runat="server" SelectMethod="GetDateTypeLogic"
        TypeName="TrackResults.Web.Reports.UserControls.CriteriaUserControls.DateTimeCriteriaDetails" />

    <div class="panel panel-default">
        <div class="panel-heading">
            <span class="glyphicon glyphicon-time"></span>Date and Time Information
        </div>
        <div class="panel-body">

            <div class="form-horizontal">
                <div class="form-group">
                    <label class="col-xs-4 control-label">
                        Date Range
                    </label>
                    <div class="col-xs-8">
                        <asp:DropDownList ID="dropDownDateRanges" runat="server" DataSourceID="dataSourceDateRanges" DataTextField="Value"
                            DataValueField="Key" CssClass="dateTimeCriteriaDateRangesDropDown sectionRight input-inline" /><asp:DropDownList
                                ID="dropDownYearRanges" runat="server" DataSourceID="dataSourceYearRanges" DataTextField="Value"
                                DataValueField="Key" ClientIDMode="Static" CssClass="dateTimeCriteriaYearRangesDropDown input-inline" />
                        <asp:HiddenField ID="hiddenFieldFiscalYearStartMonth" runat="server" Encrypted="false" />
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-xs-4 control-label">
                        Start Date
                    </label>
                    <div class="col-xs-8">
                        <trwc:ExtendedRadDatePicker ID="startDateRadDatePicker" runat="server" CssClass="startDateRadDatePicker">
                            <DateInput DateFormat="yyyy-MM-dd" DisplayDateFormat="yyyy-MM-dd">
                                <ClientEvents OnKeyPress="SetCustomDateRange" />
                            </DateInput>
                            <Calendar>
                                <ClientEvents OnDateSelected="SetCustomDateRange" />
                            </Calendar>
                        </trwc:ExtendedRadDatePicker>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-xs-4 control-label">
                        End Date
                    </label>
                    <div class="col-xs-8">
                        <trwc:ExtendedRadDatePicker ID="endDateRadDatePicker" runat="server" CssClass="endDateRadDatePicker">
                            <DateInput DateFormat="yyyy-MM-dd" DisplayDateFormat="yyyy-MM-dd">
                                <ClientEvents OnKeyPress="SetCustomDateRange" />
                            </DateInput>
                            <Calendar>
                                <ClientEvents OnDateSelected="SetCustomDateRange" />
                            </Calendar>
                        </trwc:ExtendedRadDatePicker>
                    </div>
                </div>
                <trwc:FeaturePlaceHolder runat="server" FeatureKey="Report.DateTypes" AndFeatureKey="Report.MultipleDateTypes">
                    <div class="form-group">
                        <label class="col-xs-4 control-label">
                            <asp:Literal runat="server" Text="Date Type Logic" />
                        </label>
                        <div class="col-xs-8">
                            <asp:DropDownList ID="dropDownDateTypeLogic" runat="server" DataSourceID="dataSourceDateTypeLogic" DataTextField="Value" DataValueField="Key" />
                        </div>
                    </div>
                </trwc:FeaturePlaceHolder>
                <trwc:FeaturePlaceHolder runat="server" FeatureKey="Report.DateTypes">
                    <div class="form-group" id="DateTypes">
                        <label class="col-xs-4 control-label">
                            <asp:Literal runat="server" Text="<%$ Resources: resources,Report.DateTypes %>" />
                        </label>
                        <div class="col-xs-8">
                            <asp:DropDownList ID="dropDownTierOneDateTypes" runat="server" DataSourceID="dataSourceTierOneDateTypes" DataTextField="Value" DataValueField="Key" />
                        </div>
                    </div>
                </trwc:FeaturePlaceHolder>
                <trwc:FeaturePlaceHolder runat="server" FeatureKey="Report.MultipleDateTypes">
                    <div class="form-group" id="MultipleDateTypes">
                        <label class="col-xs-4 control-label">
                            <asp:Literal runat="server" Text="<%$ Resources: resources,Report.MultipleDateTypes %>" />
                        </label>
                        <div class="col-xs-8">
                            <trwc:TypedCheckboxComboBox ID="dropDownTierOneDateTypesMultiSelect" runat="server" ViewStateMode="Enabled" SkinID="select"
                                DataSourceID="dataSourceTierOneDateTypes" DataValueField="Key" DataTextField="Value">
                            </trwc:TypedCheckboxComboBox>
                        </div>
                    </div>
                </trwc:FeaturePlaceHolder>
                <div class="form-group">
                    <label class="col-xs-4 control-label">
                        Days of Week
                    </label>
                    <div class="col-xs-8">
                        <trwc:TypedCheckboxComboBox ID="dropDownDaysOfWeek" runat="server" ViewStateMode="Enabled" SkinID="select"
                            DataSourceID="dataSourceDaysOfWeek" DataValueField="Key" DataTextField="Value">
                        </trwc:TypedCheckboxComboBox>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-xs-4 control-label">
                        Weeks
                    </label>
                    <div class="col-xs-8">
                        <trwc:TypedCheckboxComboBox ID="dropDownWeeks" runat="server" ViewStateMode="Enabled" SkinID="select"
                            DataSourceID="dataSourceWeeks" DataValueField="Key" DataTextField="Value">
                        </trwc:TypedCheckboxComboBox>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-xs-4 control-label">
                        Months
                    </label>
                    <div class="col-xs-8">
                        <trwc:TypedCheckboxComboBox ID="dropDownMonths" runat="server" ViewStateMode="Enabled" SkinID="select"
                            DataSourceID="dataSourceMonths" DataValueField="Key" DataTextField="Value">
                        </trwc:TypedCheckboxComboBox>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-xs-4 control-label">
                        Years
                    </label>
                    <div class="col-xs-8">
                        <trwc:TypedCheckboxComboBox ID="dropDownYears" runat="server" ViewStateMode="Enabled" SkinID="select"
                            DataSourceID="dataSourceYears" DataValueField="Key" DataTextField="Value">
                        </trwc:TypedCheckboxComboBox>
                    </div>
                </div>

                <trwc:FeaturePlaceHolder runat="server" FeatureKey="Tour.TourTime.Filter">
                    <div class="form-group">
                        <label class="col-xs-4 control-label">
                            <asp:Literal runat="server" Text="<%$ Resources: resources,Tour.TourTime.Plural %>" />
                        </label>
                        <div class="col-xs-8">
                            <trwc:TypedCheckboxComboBox ID="dropDownTourTimes" runat="server" ViewStateMode="Enabled" SkinID="select"
                                DataSourceID="dataSourceTourTimes" DataTextFormatString="{0:h:mm tt}">
                            </trwc:TypedCheckboxComboBox>
                        </div>
                    </div>
                </trwc:FeaturePlaceHolder>
            </div>

        </div>
    </div>
</trwc:FeaturePlaceHolder>

<script>

    $(document).ready(function () {

        switch (parseInt($("#dropDownDateTypeLogic").val())) {
            case 1:
                $("#DateTypes").show();
                $("#MultipleDateTypes").hide();
                break;

            case 2:
                $("#DateTypes").hide();
                $("#MultipleDateTypes").show();
                break;

            default:
                $("#DateTypes, #MultipleDateTypes").show();
                break;
        }
    });

    $("#dropDownDateTypeLogic").bind("change", function () {

        switch (parseInt($(this).val())) {
            case 1:
                $("#DateTypes").show();
                $("#MultipleDateTypes").hide();
                break;

            case 2:
                $("#DateTypes").hide();
                $("#MultipleDateTypes").show();
                break;

            default:
                $("#DateTypes, #MultipleDateTypes").show();
                break;
        }
    });

</script>
