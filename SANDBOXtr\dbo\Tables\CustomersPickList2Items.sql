﻿CREATE TABLE [dbo].[CustomersPickList2Items] (
    [customersPickList2ItemID]   INT           IDENTITY (1, 1) NOT NULL,
    [customersPickList2ItemName] NVARCHAR (64) NOT NULL,
    [customersPickList2ItemCode] NVARCHAR (64) NOT NULL,
    [sortOrder]                  INT           NOT NULL,
    [active]                     BIT           NOT NULL,
    CONSTRAINT [PK_customersPickList2Items] PRIMARY KEY CLUSTERED ([customersPickList2ItemID] ASC),
    CONSTRAINT [UK_CustomersPickList2Items_customersPickList2ItemName] UNIQUE NONCLUSTERED ([customersPickList2ItemName] ASC)
);


GO
CREATE TRIGGER [dbo].[CustomersPickList2Items.InsertUpdateDelete]
    ON [dbo].[CustomersPickList2Items]
    FOR INSERT, UPDATE, DELETE
AS 
BEGIN
    SET NOCOUNT ON;

    DECLARE @Activity  NVARCHAR (8), @id INT;

    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'updated'
    END

    IF EXISTS (SELECT * FROM inserted) AND NOT EXISTS(SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'inserted'
    END

    IF EXISTS (SELECT * FROM deleted) AND NOT EXISTS(SELECT * FROM inserted)
    BEGIN
        SET @Activity = 'deleted'
    END

    

    IF (@Activity = 'deleted')
    BEGIN
        DECLARE cur CURSOR FOR SELECT customersPickList2ItemID FROM deleted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'CustomersPickList2Items', @id, 'customersPickList2ItemID',
                (
                    SELECT *
                    FROM deleted i 
                    WHERE i.customersPickList2ItemID =  @id
                    FOR JSON AUTO
                ),0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
    ELSE
    BEGIN
        DECLARE cur CURSOR
        FOR SELECT customersPickList2ItemID
        FROM inserted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'CustomersPickList2Items', @id, 'customersPickList2ItemID',
                (
                    SELECT *
                    FROM inserted i 
                    WHERE i.customersPickList2ItemID =  @id
                    FOR JSON AUTO
                ), 0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
END

GO
DISABLE TRIGGER [dbo].[CustomersPickList2Items.InsertUpdateDelete]
    ON [dbo].[CustomersPickList2Items];

