﻿
CREATE PROCEDURE [dbo].[InsertCustomDisplay_HideWasIsPending]

AS
BEGIN

	DECLARE @controlID nvarchar(64)
	DECLARE @controlDelegates nvarchar(4000)

	DECLARE @GroupTypeKey nvarchar(64) SET @GroupTypeKey = 'HideWasIsPending'

	DELETE FROM CustomPageControlDisplayRights WHERE (customPageControlDisplayRightGroupTypeKey = @GroupTypeKey)

	DECLARE @Add int SET @Add = 1
	DECLARE @Remove int SET @Remove = 2

	DECLARE @Insert int SET @Insert = 1
	DECLARE @Select int SET @Select = 2
	DECLARE @Update int SET @Update = 3

	DECLARE @Visible int SET @Visible = 1
	DECLARE @Enabled int SET @Enabled = 2
	DECLARE @Text int SET @Text = 3
	DECLARE @Delegate int SET @Delegate = 4
	DECLARE @Authorized int SET @Authorized = 5
	DECLARE @Administrated int SET @Administrated = 6
	DECLARE @GridColumnVisible int SET @GridColumnVisible = 7

	DECLARE @PagePathID [varchar] (256)

	DECLARE @UIFieldNameCamel nvarchar(64) SET @UIFieldNameCamel = 'wasPending'
	DECLARE @UIFieldNamePascal nvarchar(64) SET @UIFieldNamePascal = 'WasPending'
	DECLARE @DatabaseFieldNameCamel nvarchar(64) SET @DatabaseFieldNameCamel = 'wasPending'
	DECLARE @DatabaseFieldNamePascalPlural nvarchar(64) SET @DatabaseFieldNamePascalPlural = 'WasPendings'


	SET @PagePathID = 'ASP.reports_usercontrols_toursearchcriteria_ascx'

	SET @controlID = 'tr' + @UIFieldNamePascal
	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,@controlID,@Visible,0,NULL,NULL
	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'dropDownSaleStatusTypes',@Delegate,NULL,NULL,'TrackResults.Web.Security.DropDownDisplay.SaleStatusTypes,TrackResults.Web:RemoveBySaleStatusTypes(TrackResults.BES.Data.Types.SaleStatusType[] {PenderCanceled,PenderCancellationRequest,PenderSuperseded})'

	SET @PagePathID = 'ASP.reports_usercontrols_reportby_ascx'

	SET @controlDelegates = 'TrackResults.Web.Security.MenuDisplay.ReportByTypes,TrackResults.Web:Remove(TrackResults.BES.Data.Types.ReportByType DaysInPending)'
	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'menuReportByType',@Delegate,NULL,NULL,@controlDelegates

	SET @PagePathID = 'ASP.reports_tourreport_aspx_gridView'

	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'labelWasPending',@Visible,0,NULL,NULL

	SET @PagePathID = 'ASP.reports_salestourreport_aspx_gridView'

	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'labelWasPending',@Visible,0,NULL,NULL
	
	SET @PagePathID = 'ASP.reports_efficiencysalesreport_aspx_gridViewSummary'

	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'pendingVolumeColumn',@GridColumnVisible,0,'id_pendingVolume',NULL

	SET @PagePathID = 'ASP.reports_efficiencysalesreport_aspx_gridView'

	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'pendingVolumeColumn',@GridColumnVisible,0,'id_pendingVolume',NULL
	
END