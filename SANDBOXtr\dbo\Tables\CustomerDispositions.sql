﻿CREATE TABLE [dbo].[CustomerDispositions] (
    [customerDispositionID]   INT           IDENTITY (1, 1) NOT NULL,
    [customerDispositionName] NVARCHAR (64) NOT NULL,
    [customerDispositionCode] NVARCHAR (64) NOT NULL,
    [sortOrder]               INT           NOT NULL,
    [active]                  BIT           NOT NULL,
    CONSTRAINT [PK_CustomerDispositions] PRIMARY KEY CLUSTERED ([customerDispositionID] ASC),
    CONSTRAINT [UK_CustomerDispositions_customerDispositionName] UNIQUE NONCLUSTERED ([customerDispositionName] ASC)
);


GO
CREATE TRIGGER [dbo].[CustomerDispositions.InsertUpdateDelete]
    ON [dbo].[CustomerDispositions]
    FOR INSERT, UPDATE, DELETE
AS 
BEGIN
    SET NOCOUNT ON;

    DECLARE @Activity  NVARCHAR (8), @id INT;

    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'updated'
    END

    IF EXISTS (SELECT * FROM inserted) AND NOT EXISTS(SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'inserted'
    END

    IF EXISTS (SELECT * FROM deleted) AND NOT EXISTS(SELECT * FROM inserted)
    BEGIN
        SET @Activity = 'deleted'
    END

    

    IF (@Activity = 'deleted')
    BEGIN
        DECLARE cur CURSOR FOR SELECT customerDispositionID FROM deleted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'CustomerDispositions', @id, 'customerDispositionID',
                (
                    SELECT *
                    FROM deleted i 
                    WHERE i.customerDispositionID =  @id
                    FOR JSON AUTO
                ),0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
    ELSE
    BEGIN
        DECLARE cur CURSOR
        FOR SELECT customerDispositionID
        FROM inserted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'CustomerDispositions', @id, 'customerDispositionID',
                (
                    SELECT *
                    FROM inserted i 
                    WHERE i.customerDispositionID =  @id
                    FOR JSON AUTO
                ), 0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
END

GO
DISABLE TRIGGER [dbo].[CustomerDispositions.InsertUpdateDelete]
    ON [dbo].[CustomerDispositions];

