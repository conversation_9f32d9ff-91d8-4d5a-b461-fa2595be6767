﻿<%@ Page Title="Reports Tool" Language="C#" MasterPageFile="~/MasterPages/Default.Master" AutoEventWireup="true"
    CodeBehind="AdHocAdmin.aspx.cs" Inherits="TrackResults.Web.Reports.AdHocAdmin" %>
<%@ Register Src="~/Reports/UserControls/CriteriaUserControls/DateTimeCriteriaDetails.ascx" TagName="DateTimeCriteriaDetails" TagPrefix="uc" %>
<%@ Register Src="~/Reports/UserControls/CriteriaUserControls/ToursCriteriaDetailsSales.ascx" TagName="SalesCriteria" TagPrefix="uc" %>
<%@ Register Src="~/Reports/UserControls/CriteriaUserControls/CustomersCriteriaDetails.ascx" TagName="CustomersCriteria" TagPrefix="uc" %>
<%@ Register Src="~/Reports/UserControls/CriteriaUserControls/CancellationCriteriaDetails.ascx" TagName="CancellationCriteria" TagPrefix="uc" %>
<%@ Register Src="~/Reports/UserControls/CriteriaUserControls/PurchasesCriteriaDetails.ascx" TagName="PurchasesCriteria" TagPrefix="uc" %>
<%@ Register Src="~/Reports/UserControls/CriteriaUserControls/ToursCriteriaDetailsMarketing.ascx" TagName="MarketingCriteria" TagPrefix="uc" %>
<%@ Register Src="~/Reports/UserControls/CriteriaUserControls/ToursCriteriaDetailsTours.ascx" TagName="TourCriteria" TagPrefix="uc" %>
<%@ Register Src="~/Reports/UserControls/CriteriaUserControls/BasicCriteriaDetails.ascx" TagName="BasicCriteria" TagPrefix="uc" %>
<%@ Register Src="~/Reports/UserControls/CriteriaUserControls/ToursCriteriaDetailsLeads.ascx" TagName="LeadCriteria" TagPrefix="uc" %>


<%@ Import Namespace="System.Web.Optimization" %>
<asp:Content ID="Content1" ContentPlaceHolderID="hc" runat="server">
    <link href="../AgGrid/ag-grid.css" rel="stylesheet" />
    <link href="../AgGrid/ag-theme-alpine.css" rel="stylesheet" />
    <link href="../content/jquery-ui-1.13.2.custom/jquery-ui.min.css" rel="stylesheet" />
    <link href="../content/ui.dropdownchecklist.standalone.css" rel="stylesheet" />
    <script src="https://code.jquery.com/jquery-3.6.0.js"></script>
    <script src="https://code.jquery.com/ui/1.13.2/jquery-ui.js"></script>
    <script src="js/ag-grid-enterprise.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/mathjs/11.6.0/math.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css"/>
    <%--<script src="../AgGrid/ag-grid-community.js"></script>--%>


    <%: Scripts.Render("~/bundles/adhoc") %>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/waypoints/4.0.1/jquery.waypoints.min.js"></script>

    <link href="../content/AdHocAdmin.css" rel="stylesheet" />
    <link href="../content/custom-ag-grid.css" rel="stylesheet" />
    <style>
        .compact {
            --ag-grid-size: 4px;
            --ag-list-item-height: 20px;
            --ag-font-size: 10px;
        }
        .ui-accordion-header {
           height: 30px !important;
        }

            .ui-accordion-header h4 {
                font-size: 15px;
                height: 30px;
                padding: 8px 7px;
            }

            .ui-accordion-header > .inputs-container {
                height: 30px;
                padding: 5px 0;
            }

                .ui-accordion-header > .inputs-container > input {
                    height: 20px;
                }

            .ui-accordion-header > .buttons-container {
                height: 30px;
                padding: 5px 2px;
            }

                .ui-accordion-header > .buttons-container > button {
                    height: 20px;
                    padding: 2px 8px;
                }

        .green-bold {
            color: green;
            font-weight: bold;
        }

        .red-bold {
            color: red;
            font-weight: bold;
        }
    </style>
</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="c1" runat="server">
    
    <div class="row">
        <div class="col-xs-12">
          
            <div class="panel-body band band-white-space-small gray-lighter-background">
                <div class="row">
                    <div class="container">
                        <div class="form-horizontal">
                            <div class="col-xs-12">
                                <span class="glyphicon glyphicon-stats icon-larger blue"></span>
                                <span class="emphasize orange large align-middle">Manage Admin Report Adhoc</span>
                            </div>

                            <div class="col-md-6 col-xs-12">
                                <div class="form-group">
                                    <div class="col-xs-4" style="padding-left: 0px; padding-right: 0px;">
                                        <label class="col-xs-12 control-label">Report name</label>
                                        <button onclick="addColumnFormula(); return false;" id="btnaddcolumn" class="col-xs-12 btn btn-primary" style="display:none">
                                            <i class="fas fa-plus"></i> Add Column
                                        </button>
                                    </div>

                                    <div class="col-xs-8">
                                        <input type="hidden" id="initialReportName" />
                                        <input type="text" id="reportName" class="groupColName form-control" placeholder="Create New Report: Name" />
                                        <select id="ddlColumns" class="form-control" style="display:none"></select>
                                    </div>
                                </div>

                                    <div class="form-group" id="">
                                        <label class="col-xs-4 control-label">
                                            Select preJoins
                                        </label>
                                        <div class="col-xs-8">
                                            <select id="preJoinsDropdown" class="form-control options" onchange="populateKpi()">
                                                <option value="--">--</option>
                                                <option value="tdagym">Marketing Efficiency View</option>
                                                <option value="fbda84">Tour Status Efficiency View</option>
                                                <option value="cqwq9w">Sales Efficiency View</option>
                                                <option value="armx24">Audit View</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="form-group" id="control_type_report">
                                        <label class="col-xs-4 control-label">
                                            Pivot (Existing Joins)
                                        </label>
                                        <div class="col-xs-8">
                                            <select id="reportTypeOnDropdown" onchange="onReportTypeOnSelected(this)" class="form-control options">
                                            </select>
                                        </div>
                                    </div>
                               

                               
                         
                            </div>
                            <div class="col-md-6 col-xs-12">
                                <div class="form-group">
                                    <label class="col-xs-4 control-label">Select type report</label>
                                    <div class="col-xs-8">
                                       
                                        <select id="reportNamesDropdown" onchange="onReportSelected(this)"  class="form-control">
                                
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group fileInputData" style="display:none">
                                    <label class="col-xs-4 control-label">Load file</label>
                                    <div class="col-xs-8">
                                        <input type="file" accept=".json, .csv" class="columnName form-control" id="fileInputData"  />
                                    </div>
                                </div>
                                <%-- Panal Start --%>
                                <div id="filterPanel">
                                    <div class="panel-header">
                                    <button class="close-btn" onclick="closeFilters(); return false;">&times;</button>
                                    </div>
                                    <div class="panel-body">
                                        <div class="panel-heading">
                                                 <h4>
                                                     Select Filters
                                                 </h4>
                                        </div>

                                        <%-- Assign User START --%>
                                             <div class="form-group panal-items">
                                                 <div class="col-xs-4" style="padding-left: 0px; padding-right: 0px;">
                                                     <label class="col-xs-12 control-label">Select user</label>
                                                     <br />
                                                     <button onclick="assignUsers(0); return false;" id="btnadduser" class="col-xs-12  btn btn-primary">
                                                         Assign User</button>
                                                 </div>
                                                 <div class="col-xs-8">
                                                     <div id="userListContainer" class="checkbox-list-container">
                                                         <div id="userListMultiSelect" class="checkbox-list">
                                                         </div>
                                                     </div>
                                                 </div>
                                             </div>
                                        <%-- Assign User END --%>


                                        <%-- Location Filter Start--%>
                                        <div class="form-group panal-items" id="">
                                            <label class="col-xs-4 control-label">
                                                Locations
                                            </label>
                                            <div class="col-xs-8">
                                                <select id="locationsUser" class="form-control options" multiple>
                                                    <option value="--">--</option>
                                                </select>
                                            </div>
                                        </div>
                                        <%-- Location Filter END --%>




                                        <!-- Date Criteria Start -->
                                       <div class="header-container control-label col-md-12 panal-items" id="DateTimeCriteriaHeader">
                                            <h5 class="control-label">Date and Time Information</h5> 
                                            <i class="glyphicon glyphicon-chevron-down" style="padding-right: 5px;position: absolute; right: 0; top: 50%; transform: translateY(-50%);"></i> 
                                        </div>

                                        <div class="inputs-container1 col-md-12" style="padding: 0px !important;">
                                            <uc:DateTimeCriteriaDetails runat="server" ID="DateTimeCriteriaDetails" />
                                        </div>
                                        <%-- Date criteria END --%>

                                         <!-- Customers Criteria Start -->
                                        <div class="header-container control-label col-md-12 panal-items"  id="CustomersCriteriaHeader">
                                             <h5 class="control-label">Customer Information</h5> 
                                             <i class="glyphicon glyphicon-chevron-down" style="padding-right: 5px;position: absolute; right: 0; top: 50%; transform: translateY(-50%);"></i> 
                                         </div>

                                         <div class="inputs-container1 col-md-12" style="padding: 0px !important;">
                                             <uc:CustomersCriteria runat="server" ID="CustomersCriteriaDetails" />
                                         </div>
                                         <%-- Customers criteria END --%>


                                         <!-- Cancellation Criteria Start -->
                                            <div class="header-container control-label col-md-12 panal-items" id="CancellationCriteriaHeader">
                                                 <h5 class="control-label">Call Information</h5> 
                                                 <i class="glyphicon glyphicon-chevron-down" style="padding-right: 5px;position: absolute; right: 0; top: 50%; transform: translateY(-50%);"></i> 
                                             </div>

                                             <div class="inputs-container1 col-md-12" style="padding: 0px !important;">
                                                 <uc:CancellationCriteria runat="server" ID="CancellationCriteriaDetails" />
                                             </div>
                                             <%-- Cancellation criteria END --%>

                                         <!-- Leads Criteria Start -->
                                           <div class="header-container control-label col-md-12 panal-items" id="LeadsCriteriaHeader">
                                                <h5 class="control-label">Lead Information</h5> 
                                                <i class="glyphicon glyphicon-chevron-down" style="padding-right: 5px;position: absolute; right: 0; top: 50%; transform: translateY(-50%);"></i> 
                                            </div>

                                            <div class="inputs-container1 col-md-12" style="padding: 0px !important;">
                                                <uc:LeadCriteria runat="server" ID="LeadCriteria" />
                                            </div>
                                            <%-- Leads criteria END --%>

                                         <!-- Purchases Criteria Start -->
                                            <div class="header-container control-label col-md-12 panal-items" id="PurchasesCriteriaHeader">
                                                 <h5 class="control-label">Purchase Information</h5> 
                                                 <i class="glyphicon glyphicon-chevron-down" style="padding-right: 5px;position: absolute; right: 0; top: 50%; transform: translateY(-50%);"></i> 
                                             </div>

                                             <div class="inputs-container1 col-md-12" style="padding: 0px !important;">
                                                 <uc:PurchasesCriteria runat="server" ID="PurchasessCriteriaDetails" />
                                             </div>
                                             <%-- Purchases criteria END --%>
                                        
                                         <!-- Sales Criteria Start -->
                                            <div class="header-container control-label col-md-12 panal-items" id="SalesCriteriaHeader">
                                                 <h5 class="control-label">Sales Information</h5> 
                                                 <i class="glyphicon glyphicon-chevron-down" style="padding-right: 5px;position: absolute; right: 0; top: 50%; transform: translateY(-50%);"></i> 
                                             </div>

                                             <div class="inputs-container1 col-md-12 SalesCriteriaHeader" style="padding: 0px !important;">
                                                 <uc:SalesCriteria runat="server" ID="SalesCriteriaDetails" />
                                             </div>
                                             <%-- Sales criteria END --%>

                                        <!-- Tour Criteria Start -->
                                           <div class="header-container control-label col-md-12 panal-items" id="TourCriteriaHeader">
                                                <h5 class="control-label">Tour Information</h5> 
                                                <i class="glyphicon glyphicon-chevron-down" style="padding-right: 5px;position: absolute; right: 0; top: 50%; transform: translateY(-50%);"></i> 
                                            </div>

                                            <div class="inputs-container1 col-md-12" style="padding: 0px !important;">
                                                <uc:TourCriteria runat="server" ID="TourCriteriaDetails" />
                                            </div>
                                            <%-- Tour criteria END --%>

                                         <!-- Marketing Criteria Start -->
                                            <div class="header-container control-label col-md-12 panal-items" id="MarketingCriteriaHeader">
                                                 <h5 class="control-label">Marketing Information</h5> 
                                                 <i class="glyphicon glyphicon-chevron-down" style="padding-right: 5px;position: absolute; right: 0; top: 50%; transform: translateY(-50%);"></i> 
                                             </div>

                                             <div class="inputs-container1 col-md-12" style="padding: 0px !important;">
                                                 <uc:MarketingCriteria runat="server" ID="MarketingCriteriaDetails" />
                                             </div>
                                             <%-- Marketing criteria END --%>

                                        <!-- Basic Criteria Start -->
                                           <div class="header-container control-label col-md-12 panal-items" id="BasicCriteriaHeader">
                                                <h5 class="control-label">Basic Information</h5> 
                                                <i class="glyphicon glyphicon-chevron-down" style="padding-right: 5px;position: absolute; right: 0; top: 50%; transform: translateY(-50%);"></i> 
                                            </div>

                                            <div class="inputs-container1 col-md-12" style="padding: 0px !important;">
                                                <uc:BasicCriteria runat="server" ID="BasicCriteria1" />
                                            </div>
                                            <%-- Basic criteria END --%>

                                    </div>
                                </div>
                                 <div class="form-group">
                                     <label class="col-xs-4 control-label">KPI Name</label>
                                     <div class="col-xs-8">
                                         <input type="text" class="columnName form-control" id="columnName" placeholder="KPI Name (e.g. KPI01)" />
                                     </div>
                                 </div>
                                 <div class="form-group">

                                       <button onclick="addAccordion(null); return false;" id="btnaddkpi" class="col-xs-4 btn btn-primary">
                                           <i class="fas fa-plus"></i> Add KPI</button>
                                       <div class="col-xs-8">
                                           <select class="aggFunc form-control" id="aggFunc" placeholder="KPI Type">
                                               <option value="count">Count Records</option>
                                               <option value="sum">Sum Values</option>
                                               <option value="derived">Derived KPIs</option>
                                               <option value="none">String Value</option>
                                               <option value="ifelse">IF/ELSE string</option>
                                           </select>
                                       </div>
                                   </div>

                                 <div class="form-group" style="display:none">
                                     <label class="col-xs-4 control-label">Report On(DB Columns)</label>
                                     <div class="col-xs-8">
                                         <select id="ddlKPIsGroupBy"  class="form-control groupColName"></select>
      
                                     </div>
                                 </div>
                                
                           
                            </div>


                            <div class="col-md-6 col-xs-12">
                            </div>


                            <div class="col-md-12 col-xs-12 right-container">
                                
                                <div id="joinContainer1" class="join-inputs-container">
                                      <button style="margin-left: 2px;" onclick="addJoin(); return false;" id="btnaddjoin" class="col-xs-2 btn btn-primary">
                                            <i class="fas fa-link"></i> Add Join
                                        </button>

                               
                                  
                                    <div class="col-xs-10">
                                        <div class="row">
                                              <div class="col-xs-3">
                                                <select class="joinSelect form-control" id="joinSelect1" placeholder="KPI Type" data-id="0">
                                                    <option value="left">LEFT JOIN</option>
                                                    <option value="right">RIGHT JOIN</option>
                                                    <option value="inner">INNER JOIN</option>
                                                </select>
                                            </div>
                                            <div class="col-xs-3">
                                                <input type="text" id="firstJoinKey_1" class="form-control firstJoinKey" placeholder="Enter key1 (e.g. orders.customerId)" />
                                            </div>
                                            <div class="col-xs-3">
                                                <input type="text" id="secondJoinKey_1" class="form-control secondJoinKey" placeholder="Enter key2 (e.g. customers.id)" />
                                            </div>
                                            <div class="col-xs-3 buttons-container">
                                                <input type="text" id="uniqueKey_1" class="form-control uniqueKey" placeholder="Unique key (e.g. appointmentId)" />
                                                 <button  class="btn btn-primary" onclick="removeJoin(this); return false;">X</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>


                            <div class="col-md-12">
                                <div class="form-group">
                                    <label class="col-xs-4"></label>
                                    <div class="col-xs-8" id="rowContainer">
                                        <!-- Rows will be dynamically added here -->
                                    </div>
                                </div>
                            </div>
                                <div class="col-md-12">
                                    <button id="toggleButton" type="button" class="btn btn-primary toggle-button" onclick="toggleAccordion()">
                                        <i id="toggleIcon" class="fas fa-chevron-down"></i> <span id="toggleText">Expand KPIs</span>
                                    </button>
                                    <br />
                                    <div id="accordion" class="collapse down" style="font-size:12px !important"></div>
                                    <br />
                                </div>

                            <div class="form-group">
                                <br />
                                <div class="col-xs-12">
                                    <button id="btnSaveReport" class="btn btn-primary" onclick="saveReport(); return false;">
                                        <i class="fas fa-save"></i> Save Report
                                    </button>
                                    <button id="btnLoadGrid" class="btn btn-primary" onclick="loadGrid(); return false;">
                                        <i class="fas fa-th"></i> Load Grid
                                    </button>
                                    <button id="btnAddFilters" class="btn btn-primary" onclick="showFilters(); return false;">
                                        <i class="fas fa-filter"></i> KPI Filters
                                    </button>
                                    <button id="btnDeleteReport" class="btn btn-danger" onclick="deleteReport(); return false;" style="display:none; float:right">
                                        <i class="fas fa-trash"></i> Delete Report
                                    </button>

                                </div>
                            </div>
                        </div>
                    </div>
                    
                </div>
            </div>
            <div class="form-group band  blue-darker-background">
                <br />
                <div class="col-xs-12" id="summaryDiv" style="margin:15px; border-radius:3px">
                </div>
                <br />
                    <div class="col-xs-12 ">
                        <%--<div id="myGrid" class="ag-theme-alpine ag-grid-custom-theme" style="padding:10px;height: auto !important; width: 100%;"></div>--%>
                        <div id="myGrid" style="flex: 1 1 auto" class="ag-theme-alpine ag-grid-custom-theme"></div>
                    </div>
                 <br />
            </div>
        </div>
    </div>
</asp:Content>


