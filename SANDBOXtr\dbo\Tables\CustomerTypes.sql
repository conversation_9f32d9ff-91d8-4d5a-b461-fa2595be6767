﻿CREATE TABLE [dbo].[CustomerTypes] (
    [customerTypeID]   INT           IDENTITY (1, 1) NOT NULL,
    [customerTypeName] NVARCHAR (64) NOT NULL,
    [customerTypeCode] NVARCHAR (64) NOT NULL,
    [sortOrder]        INT           NOT NULL,
    [active]           BIT           NOT NULL,
    CONSTRAINT [PK_CustomerTypes] PRIMARY KEY CLUSTERED ([customerTypeID] ASC),
    CONSTRAINT [UK_CustomerTypes_customerTypeName] UNIQUE NONCLUSTERED ([customerTypeName] ASC)
);


GO
CREATE TRIGGER [dbo].[CustomerTypes.InsertUpdateDelete]
    ON [dbo].[CustomerTypes]
    FOR INSERT, UPDATE, DELETE
AS 
BEGIN
    SET NOCOUNT ON;

    DECLARE @Activity  NVARCHAR (8), @id INT;

    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'updated'
    END

    IF EXISTS (SELECT * FROM inserted) AND NOT EXISTS(SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'inserted'
    END

    IF EXISTS (SELECT * FROM deleted) AND NOT EXISTS(SELECT * FROM inserted)
    BEGIN
        SET @Activity = 'deleted'
    END

    

    IF (@Activity = 'deleted')
    BEGIN
        DECLARE cur CURSOR FOR SELECT customerTypeID FROM deleted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'CustomerTypes', @id, 'customerTypeID',
                (
                    SELECT *
                    FROM deleted i 
                    WHERE i.customerTypeID =  @id
                    FOR JSON AUTO
                ),0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
    ELSE
    BEGIN
        DECLARE cur CURSOR
        FOR SELECT customerTypeID
        FROM inserted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'CustomerTypes', @id, 'customerTypeID',
                (
                    SELECT *
                    FROM inserted i 
                    WHERE i.customerTypeID =  @id
                    FOR JSON AUTO
                ), 0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
END

GO
DISABLE TRIGGER [dbo].[CustomerTypes.InsertUpdateDelete]
    ON [dbo].[CustomerTypes];

