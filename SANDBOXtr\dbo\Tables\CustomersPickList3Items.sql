﻿CREATE TABLE [dbo].[CustomersPickList3Items] (
    [customersPickList3ItemID]   INT           IDENTITY (1, 1) NOT NULL,
    [customersPickList3ItemName] NVARCHAR (64) NOT NULL,
    [customersPickList3ItemCode] NVARCHAR (64) NOT NULL,
    [sortOrder]                  INT           NOT NULL,
    [active]                     BIT           NOT NULL,
    CONSTRAINT [PK_CustomersPickList3Items] PRIMARY KEY CLUSTERED ([customersPickList3ItemID] ASC),
    CONSTRAINT [UK_CustomersPickList3Items_customersPickList3ItemName] UNIQUE NONCLUSTERED ([customersPickList3ItemName] ASC)
);


GO
CREATE TRIGGER [dbo].[CustomersPickList3Items.InsertUpdateDelete]
    ON [dbo].[CustomersPickList3Items]
    FOR INSERT, UPDATE, DELETE
AS 
BEGIN
    SET NOCOUNT ON;

    DECLARE @Activity  NVARCHAR (8), @id INT;

    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'updated'
    END

    IF EXISTS (SELECT * FROM inserted) AND NOT EXISTS(SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'inserted'
    END

    IF EXISTS (SELECT * FROM deleted) AND NOT EXISTS(SELECT * FROM inserted)
    BEGIN
        SET @Activity = 'deleted'
    END

    

    IF (@Activity = 'deleted')
    BEGIN
        DECLARE cur CURSOR FOR SELECT customersPickList3ItemID FROM deleted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'CustomersPickList3Items', @id, 'customersPickList3ItemID',
                (
                    SELECT *
                    FROM deleted i 
                    WHERE i.customersPickList3ItemID =  @id
                    FOR JSON AUTO
                ),0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
    ELSE
    BEGIN
        DECLARE cur CURSOR
        FOR SELECT customersPickList3ItemID
        FROM inserted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'CustomersPickList3Items', @id, 'customersPickList3ItemID',
                (
                    SELECT *
                    FROM inserted i 
                    WHERE i.customersPickList3ItemID =  @id
                    FOR JSON AUTO
                ), 0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
END

GO
DISABLE TRIGGER [dbo].[CustomersPickList3Items.InsertUpdateDelete]
    ON [dbo].[CustomersPickList3Items];

