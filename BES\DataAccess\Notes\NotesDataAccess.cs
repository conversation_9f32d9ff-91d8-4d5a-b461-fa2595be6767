﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using TrackResults.Common.Core.Caching;
using TrackResults.Common.Core.Data.SqlClient.MSSql;
using TrackResults.Common.Keys;

namespace TrackResults.BES.DataAccess.Notes
{
    public class Notes
    {
        public Int32 NoteID { get; set; }
        public string ExternalNoteID { get; set; }
        public string NoteText { get; set; }
        public Int32 UserID { get; set; }
        public Int32 FieldID { get; set; }
        public Int32 ParentID { get; set; }
        public DateTime InsertTimeStamp { get; set; }
        public DateTime UpdateTimeStamp { get; set; }

    }
    public class CustomFields
    {
        public Int32 FieldID { get; set; }
        public string FieldName { get; set; }
        public string DataType { get; set; }
        public string FeatureKey { get; set; }
        public string DataFormat { get; set; }

        public enum Parent
        {
            CancellationNotes = 1,
            CustomersNotes = 2,
            CallNotes = 3,
            LeadNotes = 4,
            MarketingNotes = 5,
            SalesNotes = 6,
            SalesNotesExit = 7,
            FormNotes1 = 8,
            FormNotes2 = 9,
            FormNotes3 = 10,
            FormNotes4 = 11,
            FormNotes5 = 12,
            FormNotes6 = 13,
            FormNotes7 = 14,
            FormNotes8 = 15,
            FormNotes9 = 16,
            FormNotes10 = 17,
            FormNotes11 = 18,
            FormNotes12 = 19,
            FormNotes13 = 20,
            FormNotes14 = 21,
            FormNotes15 = 22,
            All = 23
        }
    }
    public static class NotesDataAccess
    {
        public static Notes SelectByID(int noteID)
        {
            const string sqlStatement = "SELECT * FROM Notes WHERE notesID = @notesID";
            SqlCommand sqlCommand = new SqlCommand(sqlStatement);
            sqlCommand.Parameters.AddWithValue("@notesID", noteID);
            Notes data = new Notes();
            MSSqlDataAccess.ExecuteReader(ConnectionStrings.Default, sqlCommand, delegate (SqlDataReader sqlDataReader)
            {
                while (sqlDataReader.Read())
                {
                    data = new Notes();
                    data.NoteID = (int)sqlDataReader["NoteID"];
                    data.ExternalNoteID = (string)sqlDataReader["ExternalNoteID"];
                    data.NoteText = (string)sqlDataReader["NoteText"];
                    if (sqlDataReader["UserID"] != null)
                        data.UserID = (int)sqlDataReader["UserID"];
                    else
                        data.UserID = 0;
                    data.FieldID = (int)sqlDataReader["FieldID"];
                    data.ParentID = (int)sqlDataReader["ParentID"];
                    data.InsertTimeStamp = (DateTime)sqlDataReader["InsertTimeStamp"];
                    data.UpdateTimeStamp = (DateTime)sqlDataReader["UpdateTimeStamp"];
                }
            });
            return data;
        }
        public static List<Notes> SelectAllByParentID(int parentID, string featureKey)
        {
            const string sqlStatement = "SELECT * FROM Notes " +
                "INNER JOIN CustomFields ON Notes.fieldID = CustomFields.FieldID " +
                "WHERE Notes.parentID = @parentID AND CustomFields.FeatureKey = @featureKey";
            SqlCommand sqlCommand = new SqlCommand(sqlStatement);
            sqlCommand.Parameters.AddWithValue("@parentID", parentID);
            sqlCommand.Parameters.AddWithValue("@featureKey", featureKey);
            List<Notes> data = new List<Notes>();
            MSSqlDataAccess.ExecuteReader(ConnectionStrings.Default, sqlCommand, delegate (SqlDataReader sqlDataReader)
            {
                while (sqlDataReader.Read())
                {
                    Notes notes = new Notes();
                    notes.NoteID = (int)sqlDataReader["NoteID"];
                    if (sqlDataReader["ExternalNoteID"] != DBNull.Value)
                        notes.ExternalNoteID = (string)sqlDataReader["ExternalNoteID"];
                    notes.NoteText = (string)sqlDataReader["NoteText"];
                    if (sqlDataReader["UserID"] != DBNull.Value)
                        notes.UserID = (int)sqlDataReader["UserID"];
                    else
                        notes.UserID = 0;
                    notes.FieldID = (int)sqlDataReader["FieldID"];
                    notes.ParentID = (int)sqlDataReader["ParentID"];
                    notes.InsertTimeStamp = (DateTime)sqlDataReader["InsertTimeStamp"];
                    notes.UpdateTimeStamp = (DateTime)sqlDataReader["UpdateTimeStamp"];
                    data.Add(notes);
                }
            });
            return data;
        }
        public static int Insert(Notes data, int parentID)
        {
            const string ID_KEY = "identity";
            const string sqlStatement = "INSERT INTO Notes (externalNoteID,noteText,userID,fieldID,parentID,insertTimeStamp,updateTimeStamp) " +
                "VALUES (@externalNoteID,@noteText,@userID,@fieldID,@parentID,@insertTimeStamp,@updateTimeStamp); " +
                "SELECT @" + ID_KEY + "=SCOPE_IDENTITY()";

            SqlCommand sqlCommand = new SqlCommand(sqlStatement);
            sqlCommand.Parameters.AddWithValue("@externalNoteID", data.ExternalNoteID ?? (object)DBNull.Value);
            sqlCommand.Parameters.AddWithValue("@noteText", data.NoteText);
            if (data.UserID > 0)
                sqlCommand.Parameters.AddWithValue("@userID", data.UserID);
            else
                sqlCommand.Parameters.AddWithValue("@userID", DBNull.Value);
            sqlCommand.Parameters.AddWithValue("@fieldID", data.FieldID);
            sqlCommand.Parameters.AddWithValue("@parentID", parentID);

            sqlCommand.Parameters.AddWithValue("@insertTimeStamp",
                data.InsertTimeStamp != null && data.InsertTimeStamp != DateTime.MinValue
                    ? data.InsertTimeStamp
                    : DateTime.Now);

            sqlCommand.Parameters.AddWithValue("@updateTimeStamp",
                data.UpdateTimeStamp != null && data.UpdateTimeStamp != DateTime.MinValue
                    ? data.UpdateTimeStamp
                    : DateTime.Now);


            SqlParameter identityParameter = new SqlParameter(ID_KEY, SqlDbType.Int);
            identityParameter.Direction = ParameterDirection.Output;
            sqlCommand.Parameters.Add(identityParameter);

            MSSqlDataAccess.ExecuteNonQuery(ConnectionStrings.Default, sqlCommand);
            SystemCache.MSSqlInvalidateData(CacheKeys.ApiConnectionsRequest);
            return (int)sqlCommand.Parameters[ID_KEY].Value;
        }
        public static void Update(Notes data)
        {
            const string sqlStatement = "UPDATE Notes SET externalNoteID = ISNULL(@externalNoteID, externalNoteID), noteText = ISNULL(@noteText, noteText)," +
                "userID = ISNULL(@userID, userID),fieldID = ISNULL(@fieldID, fieldID),parentID = ISNULL(@parentID, parentID), updateTimeStamp = GETDATE() " +
                "WHERE noteID = @noteID;";

            SqlCommand sqlCommand = new SqlCommand(sqlStatement);
            if (data.ExternalNoteID != null)
                sqlCommand.Parameters.AddWithValue("@externalNoteID", data.ExternalNoteID);
            else
                sqlCommand.Parameters.AddWithValue("@externalNoteID", (object)DBNull.Value);
            if (data.NoteText != null)
                sqlCommand.Parameters.AddWithValue("@noteText", data.NoteText);
            else
                sqlCommand.Parameters.AddWithValue("@noteText", (object)DBNull.Value);
            if (data.UserID > 0)
                sqlCommand.Parameters.AddWithValue("@userID", data.UserID);
            else
                sqlCommand.Parameters.AddWithValue("@userID", (object)DBNull.Value);
            if (data.FieldID > 0)
                sqlCommand.Parameters.AddWithValue("@fieldID", data.FieldID);
            else
                sqlCommand.Parameters.AddWithValue("@fieldID", (object)DBNull.Value);
            if (data.ParentID > 0)
                sqlCommand.Parameters.AddWithValue("@parentID", data.ParentID);
            else
                sqlCommand.Parameters.AddWithValue("@parentID", (object)DBNull.Value);
            sqlCommand.Parameters.AddWithValue("@noteID", data.NoteID);

            MSSqlDataAccess.ExecuteNonQuery(ConnectionStrings.Default, sqlCommand);
            SystemCache.MSSqlInvalidateData(CacheKeys.ApiConnectionsRequest);
        }
        public static void Delete(int noteID)
        {
            const string sqlStatement = "DELETE FROM Notes WHERE noteID = @noteID;";

            SqlCommand sqlCommand = new SqlCommand(sqlStatement);
            sqlCommand.Parameters.AddWithValue("@noteID", noteID);

            MSSqlDataAccess.ExecuteNonQuery(ConnectionStrings.Default, sqlCommand);
            SystemCache.MSSqlInvalidateData(CacheKeys.ApiConnectionsRequest);
        }

        public static void DeleteByFielIdAndParentId(int fieldID, int parentID)
        {
            const string sqlStatement = "DELETE FROM Notes WHERE fieldID = ISNULL(@fieldID, fieldID) AND parentID = @parentID;";

            SqlCommand sqlCommand = new SqlCommand(sqlStatement);
            if (fieldID != 23)
                sqlCommand.Parameters.AddWithValue("@fieldID", fieldID);
            else
                sqlCommand.Parameters.AddWithValue("@fieldID", DBNull.Value);
            sqlCommand.Parameters.AddWithValue("@parentID", parentID);

            MSSqlDataAccess.ExecuteNonQuery(ConnectionStrings.Default, sqlCommand);
            SystemCache.MSSqlInvalidateData(CacheKeys.ApiConnectionsRequest);
        }

        public static string SelectConcantenatedNotesCustomers(int referenceID, string featureKey)
        {
            string sqlStatement = "SELECT dbo.GetConcantenatedNotes (@referenceID, ', ',@featureKey)";

            SqlCommand sqlCommand = new SqlCommand(sqlStatement);
            sqlCommand.Parameters.AddWithValue("@referenceID", referenceID);
            sqlCommand.Parameters.AddWithValue("@featureKey", featureKey);

            return MSSqlDataAccess.ExecuteScalar<string>(ConnectionStrings.Default, sqlCommand);
        }

        public static int? SelectNoteIDByExternalNoteID(string externalNoteID)
        {
            string sqlStatement =
             @" SELECT noteId
				FROM Notes
			WHERE externalNoteID = @externalNoteID";

            SqlCommand sqlCommand = new SqlCommand(sqlStatement);
            sqlCommand.Parameters.AddWithValue("@externalNoteID", externalNoteID);

            return MSSqlDataAccess.ExecuteScalar<int?>(ConnectionStrings.Default, sqlCommand);
        }
    }

    public static class CustomFieldsDataAccess
    {
        public static CustomFields SelectByID(int fieldID)
        {
            const string sqlStatement = "SELECT * FROM CustomFields WHERE fieldID = @fieldID";
            SqlCommand sqlCommand = new SqlCommand(sqlStatement);
            sqlCommand.Parameters.AddWithValue("@fieldID", fieldID);
            CustomFields data = new CustomFields();
            MSSqlDataAccess.ExecuteReader(ConnectionStrings.Default, sqlCommand, delegate (SqlDataReader sqlDataReader)
            {
                while (sqlDataReader.Read())
                {
                    data = new CustomFields();
                    data.FieldID = (int)sqlDataReader["FieldID"];
                    data.FieldName = (string)sqlDataReader["FieldName"];
                    data.DataType = (string)sqlDataReader["DataType"];
                    data.FeatureKey = (string)sqlDataReader["FeatureKey"];
                    data.DataFormat = (string)sqlDataReader["DataFormat"];
                }
            });
            return data;
        }

        public static int SelectByFieldID(string featureKey)
        {
            int data = 0;
            const string sqlStatement = "SELECT fieldID FROM CustomFields WHERE featureKey = @featureKey";
            SqlCommand sqlCommand = new SqlCommand(sqlStatement);
            sqlCommand.Parameters.AddWithValue("@featureKey", featureKey);

            MSSqlDataAccess.ExecuteReader(ConnectionStrings.Default, sqlCommand, delegate (SqlDataReader sqlDataReader)
            {
                while (sqlDataReader.Read())
                    data = (int)sqlDataReader["FieldID"];

            });
            return data;
        }



        public static int Insert(CustomFields data)
        {
            const string ID_KEY = "identity";
            const string sqlStatement = "INSERT INTO CustomFields (fieldName,dataType,featureKey,dataFormat) " +
                "VALUES (@fieldName,@dataType,@featureKey,@dataFormat); " +
                "SELECT @" + ID_KEY + "=SCOPE_IDENTITY()";

            SqlCommand sqlCommand = new SqlCommand(sqlStatement);
            sqlCommand.Parameters.AddWithValue("@fieldName", data.FieldName);
            sqlCommand.Parameters.AddWithValue("@dataType", data.DataType);
            sqlCommand.Parameters.AddWithValue("@featureKey", data.FeatureKey);
            if (data.DataFormat != "")
                sqlCommand.Parameters.AddWithValue("@DataFormat", data.DataFormat);

            SqlParameter identityParameter = new SqlParameter(ID_KEY, SqlDbType.Int);
            identityParameter.Direction = ParameterDirection.Output;
            sqlCommand.Parameters.Add(identityParameter);

            MSSqlDataAccess.ExecuteNonQuery(ConnectionStrings.Default, sqlCommand);
            SystemCache.MSSqlInvalidateData(CacheKeys.ApiConnectionsRequest);
            return (int)sqlCommand.Parameters[ID_KEY].Value;
        }
        public static void Update(CustomFields data)
        {
            const string sqlStatement = "UPDATE CustomFields SET fieldName = ISNULL(@fieldName, fieldName),dataType = ISNULL(@dataType, dataType)," +
                "featureKey = ISNULL(@featureKey, featureKey),DataFormat = ISNULL(@DataFormat, DataFormat) " +
                "WHERE fieldID = @fieldID";

            SqlCommand sqlCommand = new SqlCommand(sqlStatement);
            if (data.FieldName != "")
                sqlCommand.Parameters.AddWithValue("@fieldName", data.FieldName);
            else
                sqlCommand.Parameters.AddWithValue("@fieldName", (object)DBNull.Value);
            if (data.DataType != "")
                sqlCommand.Parameters.AddWithValue("@dataType", data.DataType);
            else
                sqlCommand.Parameters.AddWithValue("@dataType", (object)DBNull.Value);
            if (data.FeatureKey != "")
                sqlCommand.Parameters.AddWithValue("@featureKey", data.FeatureKey);
            else
                sqlCommand.Parameters.AddWithValue("@featureKey", (object)DBNull.Value);
            if (data.DataFormat != "")
                sqlCommand.Parameters.AddWithValue("@DataFormat", data.DataFormat);
            else
                sqlCommand.Parameters.AddWithValue("@DataFormat", (object)DBNull.Value);
            sqlCommand.Parameters.AddWithValue("@fieldID", data.FieldID);

            MSSqlDataAccess.ExecuteNonQuery(ConnectionStrings.Default, sqlCommand);
            SystemCache.MSSqlInvalidateData(CacheKeys.ApiConnectionsRequest);
        }
        public static void Delete(CustomFields data)
        {
            const string sqlStatement = "DELETE FROM CustomFields WHERE fieldID = @fieldID";

            SqlCommand sqlCommand = new SqlCommand(sqlStatement);
            sqlCommand.Parameters.AddWithValue("@fieldID", data.FieldID);

            MSSqlDataAccess.ExecuteNonQuery(ConnectionStrings.Default, sqlCommand);
            SystemCache.MSSqlInvalidateData(CacheKeys.ApiConnectionsRequest);
        }
    }
}
