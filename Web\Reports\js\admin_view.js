﻿

var lodash = window._;
var options = [];

var typeReports = [];

var kpi_get_report = [];
$(function () {
    $("#accordion").accordion({
        active: false,
        collapsible: true
    });
    $(".header-container").click(function () {
        $(this).next(".inputs-container1").slideToggle();
        $(this).css("background-color", "#f5f5f5");
    });
});


$(document).ready(async function () {

    // Store the original _keydown method
    var originalKeyDown = $.ui.accordion.prototype._keydown;

    // Override the _keydown method
    $.ui.accordion.prototype._keydown = function (event) {
        // Check if the event target is an input element
        if ($(event.target).is('input')) {
            return true; // Allow the event to proceed normally for input elements
        }

        // If spacebar and not on an input element, prevent the event
        if (event.which === 32) {
            return false;
        }

        return originalKeyDown.apply(this, arguments); // Otherwise, call the original _keydown method
    };

    $("#accordion").accordion({
        heightStyle: "content",
        activate: function (event, ui) {
            // Determine the header to process (either the one being opened or the one being closed)
            var header = ui.newHeader.length !== 0 ? ui.newHeader : ui.oldHeader;

            // Get the column name from the header
            var columnName = header.data('column-name');

            // Clear any existing operation and condition values
            var operationInput = header.find(`input.operation`);
            var conditionInput = header.find(`input.condition`);
            operationInput.val('');
            conditionInput.val('');

            // Find the corresponding content section
            var contentSection = header.next();

            // For the operation
            contentSection.find(`.my-widget .setContainer`).each(function () {
                var $this = $(this);
                var inputValue = $this.find(`input[id^="listContainerInput${columnName}"]`).val();
                var operatorValue = $this.find(`select[id^="mathOperators${columnName}"]`).val();

                // Check if inputValue is present
                if (inputValue && inputValue.trim() !== '') {
                    // Append the inputValue to the operation input
                    var existingValue = operationInput.val();
                    operationInput.val(existingValue + inputValue + " ");

                    // Check if operatorValue is present
                    if (operatorValue && operatorValue.trim() !== '') {
                        // Append the operatorValue to the operation input
                        operationInput.val(existingValue + inputValue + " " + operatorValue + " ");
                    }
                }
            });

            // For the condition
            contentSection.find(`.my-widget .setContainer`).each(function () {
                var $this = $(this);

                var inputValue1 = $this.find(`input[id^="firstConditionContainer"]`).first().val();
                var operatorValue = $this.find(`select[id^="operators"]`).val();
                var inputValue2 = $this.find(`input[id^="secondConditionContainer"]`).first().val();
                var logicalOperatorValue = $this.find(`select[id^="logicalOperators"]`).val();

                if (inputValue1 && operatorValue && inputValue2) {
                    var existingValue = conditionInput.val();
                    conditionInput.val(existingValue + inputValue1 + " " + operatorValue + " " + inputValue2 + " " + logicalOperatorValue + " ");
                }
                else {
                    var inputValue11 = $this.find(`input[id^="ffirstConditionContainer"]`).first().val();
                    var operatorValue1 = $this.find(`select[id^="operators"]`).val();
                    var inputValue22 = $this.find(`input[id^="ssecondConditionContainer"]`).first().val();
                    var ifresult = $this.find(`input[id^="ifresultConditionContainer"]`).first().val();
                    var elseresult = $this.find(`input[id^="elseresultConditionContainer"]`).first().val();
                    var inputValue3 = $this.find(`input[id^="fourConditionContainer"]`).first().val();

                    if (inputValue11 && operatorValue && inputValue22 && ifresult && (elseresult || inputValue3)) {
                        var existingValue = conditionInput.val();
                        conditionInput.val(existingValue + 'IF ' + inputValue11 + " " + operatorValue1 + " " + inputValue22 + " " + 'THEN' + " " + ifresult + " " + 'ELSE THEN' + " " + elseresult + " " + inputValue3);
                    }
                }
            });

        }
    });

    $(".ag-tool-panel-wrapper").attr("aria-hidden", "true").addClass("ag-hidden");
});

// Run the loadUserDataAndPopulateSelect function when the document is ready
document.addEventListener('DOMContentLoaded', loadUserDataAndPopulateSelect);
var checkboxSelection = function (params) {
    // we put checkbox on the name if we are not doing grouping
    return params.api.getRowGroupColumns().length === 0;
};
var headerCheckboxSelection = function (params) {
    // we put checkbox on the name if we are not doing grouping
    return params.api.getRowGroupColumns().length === 0;
};


let gridApi;

const gridOptions = {
    defaultColDef: {
        flex: 1,
        enableValue: true,
        enableRowGroup: true,
        enablePivot: true,
        resizable: true,
        sortable: true,
        filter: false,
        cellRenderer: 'agGroupCellRenderer',
        suppressMenu: true,
    },
    rowData: [],
    domLayout: 'autoHeight',
    columnDefs: [],
    groupDefaultExpanded: -1,
    autoGroupColumnDef: {
        headerName: 'Group',
        cellRendererParams: {
            checkbox: false,
            footerValueGetter: params => {
                const isRootLevel = params.node.level === -1;
                if (isRootLevel) {
                    return 'Summary';
                }
                return `Sub Total(${params.value})`;
            },
        }
    },
    groupIncludeTotalFooter: false,
    groupUseEntireRow: true,
    sideBar: true,
    pivotMode: true,
    suppressAggFuncInHeader: true,
    onGridReady: function (params) {
        gridApi = params.api;
        params.api.sizeColumnsToFit();
    },
    onRowClicked: function (params) {
        params.node.setExpanded(!params.node.expanded);
    },
    getRowClass: function (params) {
        if (params.node.rowPinned) {
            return 'bold-row';
        }
    },
    pagination: true,
    paginationPageSize: 50,
    paginationPageSizeSelector: [50, 100, 200, 500, 1000],
    onFirstDataRendered: onFirstDataRendered,
    paginateChildRows: true,
    paginationNumberFormatter: (params) => {
        return "[" + params.value.toLocaleString() + "]";
    },
    //onRowDataUpdated: function (params) {
    //    const currentColumnDefs = params.api.getColumnDefs();  // Usar params.api en lugar de gridApi
    //    const rowCount = params.api.getDisplayedRowCount();  // Obtener la cantidad de filas visibles (incluyendo pivoteo)

    //    let visibleRows = [];

    //    // Recorremos todas las filas visibles y agrupadas
    //    for (let i = 0; i < rowCount; i++) {
    //        let rowNode = params.api.getDisplayedRowAtIndex(i);  // Obtener la fila visible en el índice i

    //        if (rowNode) {
    //            if (rowNode.aggData) {
    //                visibleRows.push(rowNode.aggData);  // Agregar datos agregados
    //            } else if (rowNode.data) {
    //                visibleRows.push(rowNode.data);  // Agregar datos normales
    //            }
    //        }
    //    }

    //    // Si hay filas visibles, procedemos a calcular min/max
    //    if (visibleRows.length > 0) {
    //        currentColumnDefs.forEach(function (colDef) {
    //            if (colDef.field) {
    //                let aggregatedValues = visibleRows
    //                    .map(row => {
    //                        let value = row[colDef.field] || 0;
    //                        return typeof value === 'string' ? parseFloat(value) : value;
    //                    })
    //                    .filter(value => !isNaN(value));  // Filtrar los valores no numéricos

    //                if (aggregatedValues.length > 0) {
    //                    var minMax = aggregatedValues.reduce(function (acc, value) {
    //                        acc.min = value < acc.min ? value : acc.min;
    //                        acc.max = value > acc.max ? value : acc.max;
    //                        return acc;
    //                    }, { min: Infinity, max: -Infinity });

    //                    colDef.minValue = minMax.min;
    //                    colDef.maxValue = minMax.max;

    //                    // Agregar las reglas de estilo para los valores mínimo y máximo
    //                    colDef.cellClassRules = {
    //                        'green-bold': function (params) {
    //                            let cellValue = parseFloat(params.value);
    //                            return !isNaN(cellValue) && cellValue === colDef.maxValue;
    //                        },
    //                        'red-bold': function (params) {
    //                            let cellValue = parseFloat(params.value);
    //                            return !isNaN(cellValue) && cellValue === colDef.minValue;
    //                        }
    //                    };

    //                    console.log(`Columna: ${colDef.field}, Mínimo: ${colDef.minValue}, Máximo: ${colDef.maxValue}`);
    //                }
    //            }
    //        });

    //        // Actualizamos las definiciones de las columnas en el grid
    //        gridOptions.columnDefs = currentColumnDefs;

    //        // Refrescamos las celdas para aplicar las reglas de estilo
    //        params.api.refreshCells({ force: true });  // Forzar el refresco de todas las celdas
    //        params.api.refreshHeader();  // Refrescar los encabezados
    //    }
    //},
    aggFuncs: {
        derived: function (values) {
            return values.reduce((a, b) => a + b, 0);
        }
    }
};

document.addEventListener('DOMContentLoaded', function () {
    const gridDiv = document.querySelector('#myGrid');
    new agGrid.Grid(gridDiv, gridOptions);

    const sizes = ["compact"];
    const el = document.querySelector('[class*="ag-theme-alpine"]');
    sizes.forEach((size) => el.classList.toggle(size, size === "compact"));
});



gridOptions.aggFuncs = { derived: function (values) { return values.reduce((a, b) => a + b, 0); } };

function generateColumnDefs(operations) {
    let columnDefs = [];
    operations.forEach(function (op) {
        let newColumnDef = {
            headerName: op.columnName,
            field: op.columnName,
            valueGetter: function (params) {
                // If condition is empty or satisfied
                if (!op.condition || eval(op.condition)) {
                    // If operation is provided
                    if (op.operation) {
                        // Calculate operation value
                        return eval(op.operation);
                    } else {
                        // If operation is not provided, return null
                        return null;
                    }
                } else {
                    // If condition is not satisfied, return null
                    return null;
                }
            },
            aggFunc: op.aggFunc
        };
        columnDefs.push(newColumnDef);
    });
    return columnDefs;
}

function generateColumnDefsFromData(rowData) {
    if (!rowData || rowData.length === 0) {
        return [];
    }

    let firstRow = rowData[0];
    let columnDefs = [];

    for (let key in firstRow) {
        if (firstRow.hasOwnProperty(key)) {
            columnDefs.push({ headerName: key, field: key });
        }
    }

    return columnDefs;
}

let accordionCount = 1;


function populateData(listContainerId) {
    let select = document.getElementById(listContainerId);
    if (select) {
        kpi_get_report.forEach(kpi => {
            let el = document.createElement("option");
            el.textContent = kpi.pivot;
            el.value = kpi.original;
            select.appendChild(el);
        });
    }

}

let joinCounter = 1; // Counter for generating unique IDs

function addJoin() {
    joinCounter++;

    let newJoinContainer = document.createElement("div");
    newJoinContainer.className = "join-inputs-container";
    newJoinContainer.id = `joinContainer${joinCounter}`;
    newJoinContainer.setAttribute('data-id', '0');

    let selectHTML = `<div style="margin-left: 2px;" class="col-xs-2">&nbsp;</div><div class="col-xs-10"> <div class="row"><div class="col-xs-3">
                            <select class="joinSelect form-control" id="joinSelect${joinCounter}" placeholder="KPI Type" data-id="0">
                                <option value="left">LEFT JOIN</option>
                                <option value="right">RIGHT JOIN</option>
                                <option value="inner">INNER JOIN</option>
                            </select>
                      </div>`;

    let input1HTML = `<div class="col-xs-3">
                            <input type="text" class="form-control firstJoinKey" id="firstJoinKey_${joinCounter}" placeholder="Enter key1 (e.g. orders.customerId)" />
                      </div>`;
    let input2HTML = `<div class="col-xs-3">
                            <input type="text" class="form-control secondJoinKey" id="secondJoinKey_${joinCounter}" placeholder="Enter key2 (e.g. customers.id)" />
                      </div>`;
    let uniqueHTML = `<div class="col-xs-3 buttons-container">
                            <input type="text" class="form-control uniqueKey" id="uniqueKey_${joinCounter}" placeholder="Unique key (e.g. appointmentId)" />
                            <button  class="btn btn-primary" onclick="removeJoin(this); return false;">X</button>
                            </div>
                            </div>`;
    newJoinContainer.innerHTML = `${selectHTML} ${input1HTML} ${input2HTML} ${uniqueHTML}`;

    // Get the parent element where the new join container should be inserted
    let parentElement = document.querySelector(".right-container");

    // Append the new join container to the parent element
    parentElement.appendChild(newJoinContainer);
}

function removeJoin(button) {
    // Get the parent element of the button (which is the join container)
    let joinContainer = button.parentElement.parentElement.parentElement.parentElement;
    let allJoinContainers = document.querySelectorAll('.join-inputs-container');


    if (joinContainer.getAttribute('data-id') > 0) {
        UnAssignJoinReport(joinContainer.getAttribute('data-id'));
    }
    if (joinContainer === allJoinContainers[0]) {
        joinContainer.querySelectorAll('input').forEach(input => input.value = '');
        joinContainer.querySelectorAll('select').forEach(select => select.selectedIndex = 0);
    }
    else {
        joinContainer.remove();
    }

}
// Update the saveJoins function to return the generated EntityAttributeValueId
async function saveJoins(EntityAttributeValueData) {
    try {
        const response = await fetch('<%= ConfigurationManager.AppSettings["ApiBaseUrl"] %>/saveJoins', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(EntityAttributeValueData),
        });

        if (!response.ok) {
            throw new Error('Error saving JoinsEntityAttributeValue');
        }

        const data = await response.json();
        return data.EntityAttributeValueId;
    } catch (error) {
        console.error('Error saving JoinsEntityAttributeValue:', error);
        throw error;
    }
}

async function saveJoinDefinition(joinDefinitionData) {
    try {
        const response = await fetch('<%= ConfigurationManager.AppSettings["ApiBaseUrl"] %>/saveJoinDefinition', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(joinDefinitionData),
        });

        if (!response.ok) {
            throw new Error('Error saving JoinDefinition');
        }

        const data = await response.json();
        return data; // You can return any relevant data as needed
    } catch (error) {
        console.error('Error saving JoinDefinition:', error);
        throw error;
    }
}

// Function to populate the user select dropdown
function populateUserSelect(userDictionary) {
    const userListContainer = document.getElementById('userListMultiSelect');
    userListContainer.innerHTML = ""; // Clear previous content

    // Loop through the userDictionary and add checkboxes to the container
    userDictionary.forEach(user => {
        const checkbox = document.createElement('input');
        checkbox.type = 'checkbox';
        checkbox.value = user.UserId;
        checkbox.id = `user_${user.UserId}`;
        checkbox.classList.add('user-checkbox'); // Add a class for easier selection
        checkbox.addEventListener('change', function () {
            // When checkbox is checked/unchecked, update the selectedUserIds array
            if (!this.checked) {
                const userId = this.value; // Get the UserId from the checkbox
                UnAssignSelectedUserId(userId); // Pass the UserId to the function
            }
        });

        const label = document.createElement('label');
        label.setAttribute('for', `user_${user.UserId}`);
        label.textContent = user.UserName;

        const checkboxContainer = document.createElement('div');
        checkboxContainer.classList.add('checkbox-item'); // Add a class for styling
        checkboxContainer.appendChild(checkbox);
        checkboxContainer.appendChild(label);

        userListContainer.appendChild(checkboxContainer);
    });
}

// Function to populate the dropdown with report names
function populateReportNamesDropdown(reportNames) {
    const dropdown = document.getElementById('reportNamesDropdown');

    // Clear existing options
    dropdown.innerHTML = '<option value="">Select a Report</option>';

    // Add report names as options
    reportNames.forEach((report) => {
        const option = document.createElement('option');
        option.value = report.ReportId;
        option.text = report.ReportName;
        dropdown.appendChild(option);
    });
}

// Function to refresh the list of reports when the select element is clicked
function refreshReportNames() {
    try {
        fetchReportNames();
    } catch (error) {
        console.error('Error refreshing report names:', error);
    }
}

// Declare global variable
let reportSelectedFromDropdown = false;
let selectedReportId = -1;
let joinDefinitions = [];
let kpiData = [];
let reportDetail = {};

// Function to load user data and populate the select when the document is ready
async function loadUserDataAndPopulateSelect() {
    try {
        const userData = await fetchUserDataFromApi();
        populateUserSelect(userData);
    } catch (error) {
        console.log('Error loading user data:', error);
    }
}

// Function to fetch columns by UserId from the API
async function getReportsUsersMapByUserId(userId) {
    try {
        const apiUrl = request_path + `getReportsUsersMapByUserId/${userId}`;

        const response = await fetch(apiUrl);

        if (!response.ok) {
            throw new Error(`Failed to fetch columns for UserId ${userId}`);
        }

        // Parse the JSON response
        const returnData = await response.json();

        return returnData;
    } catch (error) {
        console.log(`Error fetching columns for UserId ${userId}:`, error);
    }
}

// Function to fetch user data by UserName from the backend API
async function fetchUserDataByUserName(userName) {
    try {
        // Define your API URL within the function
        const apiUrl = request_path + `getUserData/${userName}`;

        // Make a GET request to your custom API URL
        const response = await fetch(apiUrl);

        if (!response.ok) {
            throw new Error(`Failed to fetch user data for ${userName}`);
        }

        // Parse the JSON response
        const userData = await response.json();

        // Use the userData as needed
        return userData;
    } catch (error) {
        console.error(`Error fetching user data for ${userName}:`, error);
    }
}

// Function to fetch user data from the ASP.NET API
async function fetchUserDataFromApi() {
    try {
        const apiUrl = request_path + 'getUsers'; // Replace with the actual API endpoint

        const response = await fetch(apiUrl);

        if (!response.ok) {
            throw new Error('Failed to fetch user data');
        }

        const userData = await response.json();
        return userData;
    } catch (error) {
        console.error('Error fetching user data:', error);
        throw error;
    }
}

async function fetchReportNames(ReportId) {
    try {
        const apiUrl = request_path + `getReportInfo/${ReportId}`;

        const response = await fetch(apiUrl);

        if (!response.ok) {
            throw new Error(`Failed to fetch report names for ReportId ${ReportId}`);
        }

        // Parse the JSON response
        const reportNames = await response.json();

        // Populate the dropdown with the retrieved report names
        populateReportNamesDropdown(reportNames);
    } catch (error) {
        console.error(`Error fetching report names for ReportId ${ReportId}:`, error);
    }
}

// Function to save the state of the inputs
function saveInputState(accordionIndex, columnName) {
    let inputState = {};
    inputState.columnName = columnName.trim();
    inputState.aggFuncValue = document.getElementById('aggFunc').value.trim();
    inputState.operationValues = [];
    inputState.conditionValues = [];

    // Saving values of additional inputs
    let listContainerInput = document.getElementById(`listContainerInput${columnName}${accordionIndex}1`);
    if (listContainerInput) {
        inputState.listContainerValue = listContainerInput.value.trim();
    }

    // Saving values of additional selects
    let mathOperatorsSelect = document.getElementById(`mathOperators${columnName}${accordionIndex}1`);
    if (mathOperatorsSelect) {
        inputState.mathOperatorsValue = mathOperatorsSelect.value.trim();
    }

    // Saving values of additional logical operators select
    let logicalOperatorsSelect = document.getElementById(`logicalOperators${columnName}`);
    if (logicalOperatorsSelect) {
        inputState.logicalOperatorsValue = logicalOperatorsSelect.value.trim();
    }

    return inputState;
}

// Function to restore the state of the inputs
function restoreInputState(inputState) {
    // Restoring values of operation inputs
    let operationInputs = document.querySelectorAll(`#operation${accordionCount}`);
    operationInputs.forEach((input, index) => {
        if (inputState.operationValues[index] !== undefined) {
            input.value = inputState.operationValues[index];
        } else {
            input.value = '';
        }
    });

    // Restoring values of condition inputs
    let conditionInputs = document.querySelectorAll(`#condition${accordionCount}`);
    conditionInputs.forEach((input, index) => {
        if (inputState.conditionValues[index] !== undefined) {
            input.value = inputState.conditionValues[index];
        } else {
            input.value = '';
        }
    });
}

// Initialize accordion with beforeActivate event handler
$("#accordion").accordion({
    beforeActivate: function (event, ui) {
        //if (ui.newHeader.length) {
        //    // Accordion panel is opening
        //    let accordionIndex = ui.newHeader.parent().index();
        //    let inputState = saveInputState(accordionIndex);
        //    restoreInputState(inputState);
        //}
    }
});
function addAccordion(data) {

    let columnName;
    let aggFuncValue;

    if (data == null) {
        columnName = document.getElementById('columnName').value;
        aggFuncValue = document.getElementById('aggFunc').value;    // Save previous input values

        columnName = sanitizeColumnName(columnName);

        // check if the column name is not empty
        if (columnName.trim() === '') {
            // alert('Please enter a column name');
            return;
        }
        else {
            addChildAccordion(columnName, aggFuncValue, 0, true);
            accordionCount++;
        }
    }
    else {
        for (var i = 0; i < data.length; i++) {
            var kpi = kpiData[i];
            addChildAccordion(kpi.name, kpi.type, kpi.kpiId, kpi.Visible);
            accordionCount++;
        }
    }

}
function sanitizeColumnName(columnName) {
    return columnName.replace(/[$]/g, '_d').replace(/[%]/g, '_p');
}

function desanitizeColumnName(columnName) {
    return columnName.replace(/_d/g, '$').replace(/_p/g, '%');
}
async function toggleVisibility(visible, kpiId, columnField) {
    try {
        // Toggle the visibility parameter
        const updatedVisibility = !visible;

        // Update the visibility icon
        const icon = document.getElementById(`visibilityIcon${kpiId}`);
        if (!updatedVisibility) {
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }

        // Send the updated visibility status to the backend
        const apiUrl = `${request_path}saveKpiVisibility/${kpiId}/${updatedVisibility}`;
        const response = await fetch(apiUrl);


        const button = document.getElementById(`toggleVisibilityButton${kpiId}`);
        const newValue = `toggleVisibility(${!visible}, ${kpiId}, '${columnField}')`;
        button.setAttribute('onclick', newValue);

        if (!response.ok) {
            throw new Error('Failed to save Kpi Visibility');
        }

        await response.json(); // If the backend responds with JSON data

        //var columnState = gridApi.getColumnState();
        //var column = columnState.find(col => col.colId === columnField);
        //var isCurrentlyVisible = !column.hide; // Invert the current visibility
        //gridApi.setColumnVisible(columnField, !isCurrentlyVisible);

    } catch (error) {
        console.error('Error in saving Kpi Visibility:', error);
        throw error;
    }

}

//Hover to show tooltip for toggle visibility button
function updateTooltip(reportId, visible) {
    const button = document.getElementById(`toggleVisibilityButton${reportId}`);
    button.title = visible ? 'Hide column' : 'Show column';
}

function clearTooltip(reportId) {
    const button = document.getElementById(`toggleVisibilityButton${reportId}`);
    button.title = '';
}

// Function to update tooltip for remove button
function updateRemoveTooltip(button) {
    button.title = 'Remove';
}

// Function to clear tooltip for remove button
function clearRemoveTooltip(button) {
    button.title = '';
}
function addChildAccordion(columnName, aggFuncValue, reportId, visible) {
    var adhocPageClass = window.location.href.toLowerCase().includes('adhoc.aspx') ? 'hidden' : '';
    if (aggFuncValue == "none")
        aggFuncValue = "String"
    let prevColumnValue = columnName;
    let prevAggFuncValue = aggFuncValue;

    // Restore previous input values
    // Column Name and Aggregation Function
    document.getElementById('columnName').value = prevColumnValue;
    document.getElementById('aggFunc').value = prevAggFuncValue;


    let newAccordion = "";

    if (aggFuncValue != "ifelse" && aggFuncValue != "ifelseo") {
        newAccordion = `
                 <div class="header-container" id="accordian${accordionCount}" data-column-name="${columnName}" data-id="${reportId}">
                   <h4>${desanitizeColumnName(columnName)} - ${aggFuncValue}</h4>
                      <div class="inputs-container">
                          <input type="text" class="form-control operation" id="operation${accordionCount}" placeholder="Enter operation (e.g. data.salesPrice + data.negotiationPrice)" />
                          <input type="text" class="form-control condition" id="condition${accordionCount}" placeholder="Enter condition (e.g. data.salesStatus == 'sold')" />
                      </div>
                      <div class="buttons-container">
                          <button class="btn btn-primary" onclick="addSet('operationsContainerLabel${columnName}', '${columnName}', 0); return false;">Add Operation</button>
                          <button  class="btn btn-primary" onclick="addCondition('conditionContainerLabel${columnName}', '${columnName}', 0); return false;">Add Condition</button>
                         <button class="btn btn-primary ${adhocPageClass}" onclick="removeAccordion(this); return false;"onmouseover="updateRemoveTooltip(this);" onmouseout="clearRemoveTooltip(this);">X </button>
                        <button  class="btn btn-primary visible_row" id="toggleVisibilityButton${reportId}" onclick="toggleVisibility(${visible},${reportId},'${columnName}');"onmouseover="updateTooltip(${reportId}, ${visible});" onmouseout="clearTooltip(${reportId});" ><i id="visibilityIcon${reportId}" class="fa ${visible ? `fa-eye` : `fa-eye-slash`}"></i></button>      
                    </div> 
                </div>

              <div class="flex-container">
                  <fieldset>
                  <legend>Operations</legend>
                      <div id="operationsContainerLabel${columnName}">
                          <div class="operationRow">
                              <div class="my-widget">
                                  <div class="setContainer"  id="set${columnName}1">
                                      <div class="itemContainer">
                                          <div class ="ui-widget operationValue">
                                              <input id="listContainerInput${columnName}1" list="listContainer${columnName}1" placeholder="Available Properties" class="form-control1" style="margin-right:-1px"/>
                                              <datalist id="listContainer${columnName}1"></datalist>
                                              <select id="mathOperators${columnName}1" class="mathOperators form-control1 "  style="margin-right:0px">
                                                  <option value="">Select a Math Operator</option>
                                                  <option value="+">Addition (+)</option>
                                                  <option value="-">Subtraction (-)</option>
                                                  <option value="*">Multiplication (*)</option>
                                                  <option value="/">Division (/)</option>
                                                  <option value="%">Modulus (%)</option>
                                              </select>
                                              <button class="btn btn-primary  ${adhocPageClass}" onclick="removeWidget(this); return false;">X</button>
                                          </div>
                                      </div>
                                  </div>
                      
                              </div>
                          </div>
                      </div>
                  </fieldset>
                  <fieldset>
                    <legend>Format</legend>
                        <div id="formatContainerLabel${columnName}">
                            <div class="operationRow">
                                <div class="my-widget">
                                    <div class="setContainer"  id="formatSet${columnName}1">
                                      <div class="itemContainer">
                                          <div class ="ui-widget typeFormat">
                                              <select id="mathFormat${columnName}1" class="mathFormat form-control1 "  style="margin-right:0px; width:100% !important">
                                                  <option value="none">--</option>
                                                  <option value="percentage">%</option>
                                                  <option value="currency">$</option>
                                              </select>
                                          </div>
                                      </div>
                                  </div>
                                </div>
                            </div>
                        </div>
                  </fieldset>
                  <fieldset>
                  <legend>Conditions</legend>
                      <div id="conditionContainerLabel${columnName}">
                          <div class="operationRow">
                              <div class="my-widget">
                                  <div class="setContainer" id="conditionSet${columnName}1">
                                      <div class="itemContainer">
                                          <div class ="ui-widget conditionValue">
                                              <input id="firstConditionContainerInput${columnName}1" list="firstConditionContainer${columnName}1" placeholder="Available Properties" class="form-control2"  style="margin-right:-4px"/>
                                              <datalist id="firstConditionContainer${columnName}1"></datalist>
                                              <select id="operators${columnName}1" class="mathOperators form-control2 cOperators"  style="margin-right:-4px">
                                                  <option value="">Select an Operator</option>
                                                  <option value="==">== Equal To</option>
                                                  <option value="!=">!= Not Equal</option>
                                                  <option value=">">> Bigger Than</option>
                                                  <option value=">=">>= Bigger or Equal To</option>
                                                  <option value="<">< Smaller Than</option>
                                                  <option value="<="><= Smaller or Equal To</option>
                                              </select>
                                              <input id="secondConditionContainerInput${columnName}1" list="secondConditionContainer${columnName}1" placeholder="Available Properties" class="form-control2"  style="margin-right:-4px"/>
                                              <datalist id="secondConditionContainer${columnName}1"></datalist>
                                              <select id="logicalOperators${columnName}1" class="logicalOperators form-control2 lOperators">
                                                  <option value="">Select a Logical Operator</option>s
                                                  <option value="&&">AND (&&)</option>
                                                  <option value="||">OR (||)</option>
                                              </select>
                                              <button class="btn btn-primary  ${adhocPageClass}" onclick="removeWidget(this); return false;"  style="margin-left:-4px">X</button>
                                          </div>
                                      </div>
                                  </div>
                              </div>
                          </div>
                      </div>
                  </fieldset>
              </div>
    `;
    }
    else {
        newAccordion = `
                 <div class="header-container" id="accordian${accordionCount}" data-column-name="${columnName}" data-id="${reportId}">
                   <h4>${desanitizeColumnName(columnName)} - ${aggFuncValue}</h4>
                      <div class="inputs-container">
                          <input type="text" class="form-control condition" id="condition${accordionCount}" placeholder="Enter condition (e.g. if data.salesStatus == 'sold' then 'others')" />
                      </div>
                      <div class="buttons-container">
                         <button class="btn btn-primary ${adhocPageClass}" onclick="removeAccordion(this); return false;"onmouseover="updateRemoveTooltip(this);" onmouseout="clearRemoveTooltip(this);">X </button>
                        <button  class="btn btn-primary visible_row" id="toggleVisibilityButton${reportId}" onclick="toggleVisibility(${visible},${reportId},'${columnName}');"onmouseover="updateTooltip(${reportId}, ${visible});" onmouseout="clearTooltip(${reportId});" ><i id="visibilityIcon${reportId}" class="fa ${visible ? `fa-eye` : `fa-eye-slash`}"></i></button>      
                    </div> 
                </div>

                <div class="flex-container">
                  <fieldset>
                  <legend>Conditions</legend>
                     <div id="operationsContainerLabel${columnName}">
                        <div class="operationRow">
                              <div class="my-widget">
                                  <div class="setContainer"  id="set${columnName}1">
                                      <div class="itemContainer">
                                          <div class ="col-xs-12">
                                             <div class="row">
                                                <div class="col-xs-4">
                                                    <label for="inputField" class="col-xs-12 control-label" >IF</label>
                                                    <input id="ffirstConditionContainerInput${columnName}1" list="listContainer${columnName}1" placeholder="Available Properties" class="col-xs-12 form-control" />
                                                    <datalist id="listContainer${columnName}1"></datalist>
                                                    <select id="operators${columnName}1" class="mathOperators form-control cOperators" class="col-xs-12 form-control" >
                                                        <option value="">Select an Operator</option>
                                                        <option value="==">== Equal To</option>
                                                        <option value="!=">!= Not Equal</option>
                                                        <option value=">">> Bigger Than</option>
                                                        <option value=">=">>= Bigger or Equal To</option>
                                                        <option value="<">< Smaller Than</option>
                                                        <option value="<="><= Smaller or Equal To</option>
                                                    </select>
                                                    <input id="ssecondConditionContainerInput${columnName}1" list="listContainer${columnName}1" placeholder="Available Properties" class="col-xs-12 form-control" />
                                                    <datalist id="listContainer${columnName}1"></datalist>
                                                </div>
                                                <div class="col-xs-4">
                                                    <label for="inputField" class="col-xs-12 control-label" >THEN</label>
                                                    <input id="ifresultConditionContainerInput${columnName}1" list="listContainer${columnName}1" placeholder="Result" class="col-xs-12 form-control" />
                                                </div>

                                                <div class="col-xs-4">
                                                     <label for="inputField" class="col-xs-12 control-label" >ELSE THEN</label>
                                                    <input id="elseresultConditionContainerInput${columnName}1" list="listContainer${columnName}1" placeholder="Result" class="col-xs-12 form-control" />
                                                    <input id="fourConditionContainerInput${columnName}1" list="listContainer${columnName}1" placeholder="Available Properties" class="col-xs-12 form-control" />
                                                    <datalist id="listContainer${columnName}1"></datalist>
                                                </div>
                                             </div>
                                          </div>
                                      </div>
                                  </div>
                              </div>
                        </div>
                     </div>
                  </fieldset>
                  <fieldset>
                  <legend>Format</legend>
                      <div id="formatContainerLabel${columnName}">
                          <div class="operationRow">
                              <div class="my-widget">
                                  <div class="setContainer"  id="formatSet${columnName}1">
                                    <div class="itemContainer">
                                        <div class ="ui-widget typeFormat">
                                            <select id="mathFormat${columnName}1" class="mathFormat form-control "  style="margin-right:0px; width:100% !important">
                                                <option value="none">--</option>
                                                <option value="percentage">%</option>
                                                <option value="currency">$</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                              </div>
                          </div>
                      </div>
                </fieldset>
                
              </div>
            `;
    }

    // Append newAccordion to the accordion element
    //document.getElementById('accordion').innerHTML += newAccordion;
    document.getElementById('accordion').insertAdjacentHTML('beforeend', newAccordion);
    // Refresh the accordion 
    $('#accordion').accordion('option', 'active', accordionCount - 1);
    $("#accordion").accordion("refresh");

    // Restore previous input values

    // Column Name and Aggregation Function
    document.getElementById('columnName').value = desanitizeColumnName(prevColumnValue);
    document.getElementById('aggFunc').value = prevAggFuncValue;


    // Populate the new datalists
    setTimeout(() => {
        populateData(`listContainer${columnName}1`);
        populateData(`firstConditionContainer${columnName}1`);
        populateData(`secondConditionContainer${columnName}1`);
    }, 0);

}

function removeAccordion(button) {
    const headerContainer = $(button).closest('.header-container');
    const flexContainer = headerContainer.next('.flex-container');
    headerContainer.remove();
    flexContainer.remove();
    $('#accordion').accordion('refresh');
    if (headerContainer.data('id') > 0)
        UnAssignReportKpiId(headerContainer.data('id'));
}

let counter = 1;
function addSet(containerId, columnName, count) {
    var adhocPageClass = window.location.href.toLowerCase().includes('adhoc.aspx') ? 'hidden' : '';


    if (count == 0)
        counter++;
    else
        counter = count;

    // Find the correct container within the given containerId
    var targetContainer = $(`#${containerId} .operationRow .my-widget`);

    let newSet = $('<div>')
        .attr('id', `set${columnName}${counter}`)
        .addClass('setContainer');

    let newItem = $('<div>')
        .addClass('itemContainer')
        .appendTo(newSet);

    let newWidget = $('<div>')
        .addClass('ui-widget')
        .appendTo(newItem);

    // Now include columnName in the ID attribute
    $('<input class="form-control1">')
        .attr('id', `listContainerInput${columnName}${counter}`)
        .attr('list', `listContainer${columnName}${counter}`)
        .attr('placeholder', 'Available Properties')
        .appendTo(newWidget);

    $('<datalist>')
        .attr('id', `listContainer${columnName}${counter}`)
        .appendTo(newWidget);

    $('<select class="form-control1">')
        .attr('id', `mathOperators${columnName}${counter}`)
        .addClass('mathOperators')
        .append('<option value="">Select a Math Operator</option>')
        .append('<option value="+">Addition (+)</option>')
        .append('<option value="-">Subtraction (-)</option>')
        .append('<option value="*">Multiplication (*)</option>')
        .append('<option value="/">Division (/)</option>')
        .append('<option value="%">Modulus (%)</option>')
        .appendTo(newWidget);

    $('<button>')
        .addClass('btn btn-primary ' + adhocPageClass)
        .text('X')
        .attr('onclick', "removeWidget(this); return false;")
        .appendTo(newWidget);

    newSet.appendTo(targetContainer);

    // Populate the new list containers
    populateData(`listContainer${columnName}${counter}`);

    // Open the accordion
    $('#accordion').accordion('option', 'active', accordionCount - 1);
    $("#accordion").accordion("refresh");
}

let conditionCounter = 1;

function addCondition(containerId, columnName, count) {
    var adhocPageClass = window.location.href.toLowerCase().includes('adhoc.aspx') ? 'hidden' : '';

    if (count == 0)
        conditionCounter++;
    else
        conditionCounter = count;

    var targetContainer = $(`#${containerId} .operationRow .my-widget`);

    let newConditionSet = $('<div>')
        .attr('id', `conditionSet${columnName}${conditionCounter}`)
        .addClass('setContainer');

    let newItem = $('<div>')
        .addClass('itemContainer')
        .appendTo(newConditionSet);

    let newWidget = $('<div>')
        .addClass('ui-widget')
        .appendTo(newItem);

    $('<input class="form-control2">')
        .attr('id', `firstConditionContainerInput${columnName}${conditionCounter}`)
        .attr('list', `firstConditionContainer${columnName}${conditionCounter}`)
        .attr('placeholder', 'Available Properties')
        .appendTo(newWidget);

    $('<datalist>')
        .attr('id', `firstConditionContainer${columnName}${conditionCounter}`)
        .appendTo(newWidget);

    $('<select class="form-control2">')
        .attr('id', `operators${columnName}${conditionCounter}`)
        .addClass('mathOperators')
        .append('<option value="">Select an Operator</option>')
        .append('<option value="==">== Equal To</option>')
        .append('<option value="!=">!= Not Equal</option>')
        .append('<option value=">">> Bigger Than</option>')
        .append('<option value=">=">>= Bigger or Equal To</option>')
        .append('<option value="<">< Smaller Than</option>')
        .append('<option value="<="><= Smaller or Equal To</option>')
        .appendTo(newWidget);

    $('<input class="form-control2">')
        .attr('id', `secondConditionContainerInput${columnName}${conditionCounter}`)
        .attr('list', `secondConditionContainer${columnName}${conditionCounter}`)
        .attr('placeholder', 'Available Properties')
        .appendTo(newWidget);

    $('<datalist>')
        .attr('id', `secondConditionContainer${columnName}${conditionCounter}`)
        .appendTo(newWidget);

    $('<select class="form-control2">')
        .attr('id', `logicalOperators${columnName}${conditionCounter}`)
        .addClass('logicalOperators')
        .append('<option value="">Select a Logical Operator</option>')
        .append('<option value="&&">AND (&&)</option>')
        .append('<option value="||">OR (||)</option>')
        .appendTo(newWidget);

    $('<button>')
        .addClass('btn btn-primary ' + adhocPageClass)
        .attr('onclick', 'removeWidget(this); return false;')
        .text('X')
        .appendTo(newWidget);

    //newConditionSet.appendTo(`#${containerId}`);
    targetContainer.append(newConditionSet);

    // Populate the new list container
    populateData(`firstConditionContainer${columnName}${conditionCounter}`);
    populateData(`secondConditionContainer${columnName}${conditionCounter}`);
    // Refresh the accordion to adjust its height
    $('#accordion').accordion('option', 'active', accordionCount - 1);
    $("#accordion").accordion("refresh");
}

function removeWidget(buttonElement) {
    // Find the closest .ui-widget div to the clicked button
    var widget = $(buttonElement).closest('.ui-widget');

    // Remove the widget from the DOM
    widget.remove();
}

function appendOperation(columnName) {
    // Loop through each setContainer inside the operations container
    $(`#operationsContainerLabel${columnName} .setContainer`).each(function () {
        var setContainer = $(this);
        // Get the set ID to identify the correct inputs
        var setId = setContainer.attr('id').replace('set', '');

        // Loop through each itemContainer inside the set container
        setContainer.find('.itemContainer').each(function () {
            var itemContainer = $(this);
            // Get the value from the input list
            var inputValue = itemContainer.find(`input[list="operationsContainer${setId}"], input[list="listContainer${setId}"]`).val();
            // Get the selected math operator
            var operatorValue = itemContainer.find(`#mathOperators${setId}`).val();

            // Check if both values are present
            if (inputValue && operatorValue) {
                // Append the values to the operation input (adjust the ID as needed)
                var existingValue = $("#operation1").val(); // Change this ID if necessary
                $("#operation1").val(existingValue + inputValue + " " + operatorValue + " ");
            }
        });
    });
}

// Fetch the dataset dynamically (this assumes you have a method to get the dataset by its name)
function getDataSet(name) {
    return window[name];
}

function aggregateDataUsingCustomFuncs(data, groupColumn, operationsList) {
    const customAggFuncs = getCustomAggFuncs(operationsList, data, groupColumn);
    const groups = groupDataByKey(data, groupColumn);
    const result = [];

    for (const [group, rows] of Object.entries(groups)) {
        const aggregatedRow = { [groupColumn]: group };

        for (const [columnName, aggFunc] of Object.entries(customAggFuncs)) {
            const valuesToAggregate = rows.map(row => row[columnName]);
            aggregatedRow[columnName] = aggFunc(valuesToAggregate);
        }

        result.push(aggregatedRow);
    }

    return result;
}

function groupDataByKey(data, key) {
    return data.reduce(function (rv, x) {
        (rv[x[key]] = rv[x[key]] || []).push(x);
        return rv;
    }, {});
}

function evalOperation(operationString, row) {
    let operationParts = operationString.split(' ');
    let operation = operationParts[0];

    switch (operation) {
        case 'sum':
        case 'count':
        case 'derived':
            return evalDerivedOperation(operationString.replace(operation + ' ', ''), row);

        default:
            // Here we construct the code for the operation
            let code = 'let result = ' + operationString.replace(/\b[a-zA-Z0-9_]+\.\w+\b|\b\w+\b/g, columnName => {
                // Check if the column name includes a prefix (like 'purchase.')
                if (columnName.includes('.')) {
                    let parts = columnName.split('.');
                    let strippedColumnName = parts[1]; // Use only the column name without the prefix

                    // Convert the column value to a number to perform safe arithmetic operations
                    return `(Number(row["${strippedColumnName.charAt(0).toLowerCase() + strippedColumnName.slice(1)}"] || 0))`;
                } else if (!isNaN(columnName)) {
                    // If it's not a column name but a number, leave it as it is
                    return columnName;
                } else {
                    // Handle the case where there is no prefix and treat it as a direct column
                    return `(Number(row["${columnName.charAt(0).toLowerCase() + columnName.slice(1)}"] || 0))`;
                }
            }) + ';' +
                'return isNaN(result) || !isFinite(result) ? 0 : result;'; // We no longer use .toFixed(2) here

            try {
                // Evaluate the generated code for the operation
                return new Function('row', code)(row);
            } catch (e) {
                
                //console.log('Error process kpi operations');
                console.error('Error evaluating operation:', e);
                return -1;
            }
    }
}


function innerJoin(data1, data2, key1, key2) {
    const result = [];
    data1.forEach(row1 => {
        data2.forEach(row2 => {
            if (row1[key1] === row2[key2]) {
                result.push({ ...row1, ...row2 });
            }
        });
    });
    return result;
}

function filterData(data, condition, operation) {
    return data.filter(row => evalCondition(condition, row, operation));
}

function getDataFromPath(obj, path) {
    const parts = path.split('.');
    let currentObj = obj;

    for (let i = 0; i < parts.length; i++) {
        if (parts[i] === 'join') {
            // Grab the next part as the join key
            const joinKey = parts[i + 1];
            const secondArrayName = parts[i + 2];

            // Assuming both currentObj and obj[secondArrayName] are arrays
            let joinedData = [];
            currentObj.forEach(firstItem => {
                obj[secondArrayName].forEach(secondItem => {
                    if (firstItem[joinKey] === secondItem[joinKey]) {
                        // Joining the two objects. 
                        // here using {...firstItem, ...secondItem} to merge the objects. 
                        // might want a different approach if there are overlapping keys.
                        joinedData.push({ ...firstItem, ...secondItem });
                    }
                });
            });

            currentObj = joinedData;

            // Skip the next two parts as they are processed in this block
            i += 2;

        } else {
            currentObj = currentObj[parts[i]];
        }

        if (!currentObj) return undefined;  // If any part of the path is undefined, return
    }

    return currentObj;
}

function joinDataSets(dataSet1, dataSet2, joinKey) {
    const result = [];

    dataSet1.forEach(item1 => {
        const matchingItem = dataSet2.find(item2 => item2[joinKey] === item1[joinKey]);
        if (matchingItem) {
            result.push(Object.assign({}, item1, matchingItem));
        }
    });

    return result;
}

function extractDataPaths(inputString) {
    const regex = /\b\w+(\.\w+)+\b/g;
    const matches = inputString.match(regex) || [];
    return [...new Set(matches)];  // remove duplicates
}



// Global dictionary to keep track of selected values
let selectedValues = {};

function createListItem(listContainer, key, value) {
    var listItem = document.createElement('option');
    listItem.value = key + "." + value;
    listItem.textContent = key + "." + value;
    listContainer.appendChild(listItem);
}
function addColumn() {
    var conditions = document.getElementsByClassName('condition');
    var conditionStrings = [];
    var newColumnName = document.getElementById('newColumnName').value;

    for (var i = 0; i < conditions.length; i++) {
        var column = conditions[i].getElementsByClassName('column')[0].value;
        var operator = decodeURIComponent(conditions[i].getElementsByClassName('operator')[0].value);
        var value = conditions[i].getElementsByClassName('value')[0].value;
        if (column && operator && value) {
            conditionStrings.push('data.' + column + ' ' + operator + ' ' + value);
        }
    }

    var valueGetter = new Function('data', 'return ' + conditionStrings.join(' && ') + ' ? params.data.' + column + ' : null;');

    var newColumn = { headerName: newColumnName, field: newColumnName, valueGetter: valueGetter };
    //var columnDefs = gridApi.getColumnDefs();
    var columnDefs = gridOptions.columnDefs;
    columnDefs.push(newColumn);
    gridApi.setColumnDefs(columnDefs);

}

function addColumnFormula() {
    //***************************************
    var newColumnField = document.getElementById('ddlColumns'); //document.getElementById('newColumnField');
    //var isDetail = document.getElementById('isDetail').checked;

    var newColumn = { headerName: newColumnField.value, field: newColumnField.value };

    var masterColumnDefs = gridOptions.columnDefs;
    masterColumnDefs.push(newColumn);
    gridApi.setColumnDefs(masterColumnDefs);

    newColumnField.value = '';
}

function filterTrueOnly() {
    var model = {};
    var columnDefs = gridOptions.columnDefs;
    for (let i = 0; i < columnDefs.length; i++) {
        if (columnDefs[i].valueGetter) {
            model[columnDefs[i].field + '_getter'] = {
                type: 'equals',
                filter: true
            };
        }
    }
    gridApi.setFilterModel(model);
}

function createKpi() {
    var formulaColumnName = document.getElementById('complexFormulaName');
    var formulaInput = document.getElementById('formulaInput');

    var formulaFunction = new Function('params', 'return ' + formulaInput.value + ';');

    var newColumnDef = {
        headerName: formulaColumnName.value,
        valueGetter: formulaFunction
    };

    // Getting current column definitions
    var columnDefs = gridOptions.columnDefs;
    columnDefs.push(newColumnDef);

    // Updating column definitions in the grid
    gridApi.setColumnDefs(columnDefs);

    formulaColumnName.value = '';
    formulaInput.value = '';
}

function addDetailColumn() {
    let masterNode = gridApi.getDisplayedRowAtIndex(0); // Assuming you want to add to the first master row's detail grid
    if (masterNode.gridOptionsWrapper) {
        let detailGridOptions = masterNode.gridOptionsWrapper.gridOptions;
        if (detailGridOptions) {
            detailGridOptions.columnApi.setColumnDefs(newColumnDefs);
        } else {
        }
    } else {
    }
}

function updateHeaderName() {
    // Get input values
    var columnField = document.getElementById('columnFieldInput').value;
    var newHeaderName = document.getElementById('newHeaderNameInput').value;

    // Ensure input values are not empty
    if (columnField && newHeaderName) {
        // Clone the existing column definitions and update the headerName for the specified column
        var newColumnDefs = gridOptions.columnDefs.map((colDef) => {
            if (colDef.field === columnField) {
                return { ...colDef, headerName: newHeaderName };
            }
            return colDef;
        });

        // Apply the new column definitions
        gridApi.setColumnDefs(newColumnDefs);
    }
}

function groupColumn() {
    var columnName = document.getElementById('groupColumnInput').value;
    gridOptions.columnApi.applyColumnState({
        state: [{
            colId: columnName,
            rowGroup: true
        }],
        defaultState: { rowGroup: false }
    });
}

let allRowData;
function onFirstDataRendered(params) {
    // arbitrarily expand a row for presentational purposes
    setTimeout(function () {
        params.api.getDisplayedRowAtIndex(0).setExpanded(true);
    }, 0);

    setInterval(function () {
        if (!allRowData) {
            return;
        }

        const data = allRowData[0];

        const newCallRecords = [];
        data.callRecords.forEach(function (record, index) {
            newCallRecords.push({
                name: record.name,
                callId: record.callId,
                duration: record.duration + (index % 2),
                switchCode: record.switchCode,
                direction: record.direction,
                number: record.number,
            });
        });

        data.callRecords = newCallRecords;
        data.calls++;

        const tran = {
            update: [data],
        };

        params.api.applyTransaction(tran);
    }, 2000);
}



var initialKpiData = [];

function toggleAccordion() {
    var accordion = document.getElementById('accordion');
    var buttonIcon = document.getElementById('toggleIcon');
    var buttonText = document.getElementById('toggleText');

    if (accordion.classList.contains('show')) {
        // Accordion is expanded, collapse it
        accordion.classList.remove('show');
        buttonIcon.classList.remove('fa-chevron-up');
        buttonIcon.classList.add('fa-chevron-down');
        buttonText.textContent = 'Expand KPIs';

        //// Collect new KPI data
        //var newKpiData = collectKPIData();

        //// Compare new KPI data with initial KPI data
        //if (JSON.stringify(initialKpiData) !== JSON.stringify(newKpiData)) {
        //    saveReport();
        //}
    } else {
        // Accordion is collapsed, expand it
        accordion.classList.add('show');
        buttonIcon.classList.remove('fa-chevron-down');
        buttonIcon.classList.add('fa-chevron-up');
        buttonText.textContent = 'Collapse KPIs';

        //// Store the initial KPI data
        //initialKpiData = collectKPIData();
    }
}

