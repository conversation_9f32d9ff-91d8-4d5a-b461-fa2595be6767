CREATE TYPE [dbo].[CustomerType] AS TABLE (
    [customerID]               INT            NULL,
    [externalCustomerID]       NVARCHAR (128) NULL,
    [customerTypeID]           INT            NULL,
    [customerStatusTypeID]     INT            NULL,
    [customerDispositionID]    INT            NULL,
    [firstName]                NVARCHAR (64)  NULL,
    [lastName]                 NVARCHAR (64)  NULL,
    [age]                      INT            NULL,
    [sex]                      BIT            NULL,
    [customersPickList1ItemID] INT            NULL,
    [guestFirstName]           NVARCHAR (64)  NULL,
    [guestLastName]            NVARCHAR (64)  NULL,
    [guestTypeID]              INT            NULL,
    [guestAge]                 INT            NULL,
    [guestSex]                 BIT            NULL,
    [customersPickList2ItemID] INT            NULL,
    [incomeTypeID]             INT            NULL,
    [customersPickList3ItemID] INT            NULL,
    [customersText1]           NVARCHAR (512) NULL,
    [customersDecimal1]        DECIMAL (9, 2) NULL,
    [customersBool1]           BIT            NULL,
    [customersDate1]           DATETIME       NULL,
    [customersTime1]           DATETIME       NULL,
    [doNotCall]                BIT            NULL,
    [primaryPhone]             NVARCHAR (32)  NULL,
    [secondaryPhone]           NVARCHAR (32)  NULL,
    [streetAddress]            NVARCHAR (128) NULL,
    [streetAddress2]           NVARCHAR (128) NULL,
    [city]                     NVARCHAR (64)  NULL,
    [stateID]                  INT            NULL,
    [zipcode]                  NVARCHAR (16)  NULL,
    [countryID]                INT            NULL,
    [businessName]             NVARCHAR (64)  NULL,
    [email]                    NVARCHAR (128) NULL,
    [customersPickList4ItemID] INT            NULL,
    [customersText2]           NVARCHAR (512) NULL,
    [customersText3]           NVARCHAR (512) NULL,
    [customersDecimal2]        DECIMAL (9, 2) NULL,
    [customersBool2]           BIT            NULL,
    [customersDate2]           DATETIME       NULL,
    [customersTime2]           DATETIME       NULL,
    [apiConnectionID]          INT            NULL,
    [apiExternalCustomerID]    NVARCHAR (128) NULL,
    [apiExternalConnectionID]  NVARCHAR (128) NULL,
    [insertTimeStamp]          DATETIME       NULL,
    [updateTimeStamp]          DATETIME       NULL,
    [city2]                    NVARCHAR (64)  NULL,
    [stateID2]                 INT            NULL,
    [zipcode2]                 NVARCHAR (16)  NULL,
    [countryID2]               INT            NULL,
    [userAddDoNotCall]         NVARCHAR (50)  NULL,
    [insertDoNotCall]          DATETIME       NULL,
    [streetAddressGuest]       NVARCHAR (128) NULL,
    [streetAddress2Guest]      NVARCHAR (128) NULL,
    [emailGuest]               NVARCHAR (128) NULL);


GO
PRINT N'Creating User-Defined Table Type [dbo].[NotesType]...';


GO
CREATE TYPE [dbo].[NotesType] AS TABLE (
    [noteID]          INT            NULL,
    [externalNoteID]  NVARCHAR (50)  NULL,
    [noteText]        NVARCHAR (MAX) NULL,
    [userID]          INT            NULL,
    [fieldID]         INT            NULL,
    [parentID]        INT            NULL,
    [insertTimeStamp] DATETIME       NULL,
    [updateTimeStamp] DATETIME       NULL);


GO
PRINT N'Creating User-Defined Table Type [dbo].[PurchaseType]...';


GO
CREATE TYPE [dbo].[PurchaseType] AS TABLE (
    [purchaseID]                     INT            NULL,
    [externalPurchaseID]             NVARCHAR (128) NULL,
    [tourID]                         INT            NULL,
    [parentSupersededFromPurchaseID] INT            NULL,
    [supersededFromPurchaseID]       INT            NULL,
    [supersedingFromPender]          BIT            NULL,
    [saleDate]                       DATETIME       NULL,
    [saleTime]                       DATETIME       NULL,
    [productCategoryID]              INT            NULL,
    [productID]                      INT            NULL,
    [subProductID]                   INT            NULL,
    [saleAmount]                     DECIMAL (9, 2) NULL,
    [downPaymentAmount]              DECIMAL (9, 2) NULL,
    [fees1Amount]                    DECIMAL (9, 2) NULL,
    [fees2Amount]                    DECIMAL (9, 2) NULL,
    [fees3Amount]                    DECIMAL (9, 2) NULL,
    [fees4Amount]                    DECIMAL (9, 2) NULL,
    [fees5Amount]                    DECIMAL (9, 2) NULL,
    [saleTypeID]                     INT            NULL,
    [saleStatusTypeID]               INT            NULL,
    [saleDispositionID]              INT            NULL,
    [purchasesPickList1ItemID]       INT            NULL,
    [purchasesPickList2ItemID]       INT            NULL,
    [purchasesPickList3ItemID]       INT            NULL,
    [purchasesText1]                 NVARCHAR (512) NULL,
    [purchasesText2]                 NVARCHAR (512) NULL,
    [purchasesBool1]                 BIT            NULL,
    [purchasesBool2]                 BIT            NULL,
    [purchasesDate1]                 DATETIME       NULL,
    [purchasesTime1]                 DATETIME       NULL,
    [purchasesDate2]                 DATETIME       NULL,
    [purchasesTime2]                 DATETIME       NULL,
    [apiConnectionID]                INT            NULL,
    [apiExternalPurchaseID]          NVARCHAR (128) NULL,
    [apiExternalConnectionID]        NVARCHAR (128) NULL,
    [insertTimeStamp]                DATETIME       NULL,
    [updateTimeStamp]                DATETIME       NULL);


GO
PRINT N'Creating User-Defined Table Type [dbo].[TourType]...';


GO
CREATE TYPE [dbo].[TourType] AS TABLE (
    [tourID]                   INT            NULL,
    [externalTourID]           NVARCHAR (128) NULL,
    [customerID]               INT            NULL,
    [externalLeadID]           NVARCHAR (128) NULL,
    [acquisitionDate]          DATETIME       NULL,
    [acquisitionTime]          DATETIME       NULL,
    [leadSourceID]             INT            NULL,
    [leadTypeID]               INT            NULL,
    [leadStatusTypeID]         INT            NULL,
    [leadDispositionID]        INT            NULL,
    [checkInDate]              DATETIME       NULL,
    [checkInTime]              DATETIME       NULL,
    [checkOutDate]             DATETIME       NULL,
    [checkOutTime]             DATETIME       NULL,
    [leadPickList1ItemID]      INT            NULL,
    [leadText1]                NVARCHAR (512) NULL,
    [leadDecimal1]             DECIMAL (9, 2) NULL,
    [leadBool1]                BIT            NULL,
    [leadPickList2ItemID]      INT            NULL,
    [leadText2]                NVARCHAR (512) NULL,
    [leadDecimal2]             DECIMAL (9, 2) NULL,
    [leadBool2]                BIT            NULL,
    [contactStatusTypeID]      INT            NULL,
    [contactDispositionID]     INT            NULL,
    [callBackDate]             DATETIME       NULL,
    [callBackTime]             DATETIME       NULL,
    [callBackPickList1ItemID]  INT            NULL,
    [callBackText1]            NVARCHAR (512) NULL,
    [callBackDecimal1]         DECIMAL (9, 2) NULL,
    [callBackBool1]            BIT            NULL,
    [tourSourceID]             INT            NULL,
    [tourTypeID]               INT            NULL,
    [tourStatusTypeID]         INT            NULL,
    [tourConcernTypeID]        INT            NULL,
    [tourTypePickList1ItemID]  INT            NULL,
    [locationID]               INT            NULL,
    [regionID]                 INT            NULL,
    [tourDate]                 DATETIME       NULL,
    [tourTime]                 DATETIME       NULL,
    [entryDateTime]            DATETIME       NULL,
    [tourText1]                NVARCHAR (512) NULL,
    [tourDecimal1]             DECIMAL (9, 2) NULL,
    [tourBool1]                BIT            NULL,
    [rescheduledCount]         INT            NULL,
    [rescheduledTourID]        INT            NULL,
    [parentRescheduledTourID]  INT            NULL,
    [marketingTeamID]          INT            NULL,
    [marketingAgentID]         INT            NULL,
    [marketingCloserID]        INT            NULL,
    [confirmerID]              INT            NULL,
    [resetterID]               INT            NULL,
    [venueID]                  INT            NULL,
    [campaignID]               INT            NULL,
    [channelID]                INT            NULL,
    [hotelID]                  INT            NULL,
    [marketingPickList1ItemID] INT            NULL,
    [marketingPickList2ItemID] INT            NULL,
    [marketingText1]           NVARCHAR (512) NULL,
    [marketingDecimal1]        DECIMAL (9, 2) NULL,
    [marketingBool1]           BIT            NULL,
    [marketingDate1]           DATETIME       NULL,
    [marketingTime1]           DATETIME       NULL,
    [depositAmount]            DECIMAL (9, 2) NULL,
    [depositRefundable]        BIT            NULL,
    [salesTeamID]              INT            NULL,
    [podiumID]                 INT            NULL,
    [salesRepID]               INT            NULL,
    [salesCloser1ID]           INT            NULL,
    [salesCloser2ID]           INT            NULL,
    [exitID]                   INT            NULL,
    [verificationOfficerID]    INT            NULL,
    [salesPickList1ItemID]     INT            NULL,
    [salesText1]               NVARCHAR (512) NULL,
    [salesDecimal1]            DECIMAL (9, 2) NULL,
    [salesBool1]               BIT            NULL,
    [salesDate1]               DATETIME       NULL,
    [salesTime1]               DATETIME       NULL,
    [importNumber]             NVARCHAR (64)  NULL,
    [apiConnectionID]          INT            NULL,
    [apiExternalTourID]        NVARCHAR (128) NULL,
    [apiExternalConnectionID]  NVARCHAR (128) NULL,
    [insertTimeStamp]          DATETIME       NULL,
    [updateTimeStamp]          DATETIME       NULL,
    [salesTeamExitID]          INT            NULL,
    [salesRepExit1ID]          INT            NULL,
    [salesRepExit2ID]          INT            NULL,
    [salesRepExit3ID]          INT            NULL);


GO
PRINT N'Altering Table [dbo].[Customers]...';


GO
ALTER TABLE [dbo].[Customers]
    ADD [streetAddressGuest]  NVARCHAR (128) NULL,
        [streetAddress2Guest] NVARCHAR (128) NULL,
        [emailGuest]          NVARCHAR (128) NULL;


GO
PRINT N'Dropping Primary Key [dbo].[PK_Reports]...';


GO
ALTER TABLE [dbo].[Reports] DROP CONSTRAINT [PK_Reports];


GO
PRINT N'Creating Procedure [dbo].[BulkUpdateCustomers]...';


GO
CREATE PROCEDURE [dbo].[BulkUpdateCustomers]
    @DataTableParam dbo.CustomerType READONLY -- Table-Valued Parameter
AS
BEGIN
    SET NOCOUNT ON;

    -- Use MERGE to update existing rows and insert new rows
    MERGE INTO Customers AS target
    USING @DataTableParam AS source
    ON target.customerID = source.customerID

    -- When there is a match, update the customer data
    WHEN MATCHED THEN
        UPDATE SET
            target.externalCustomerID = source.externalCustomerID,
            target.customerTypeID = source.customerTypeID,
            target.customerStatusTypeID = source.customerStatusTypeID,
            target.customerDispositionID = source.customerDispositionID,
            target.firstName = source.firstName,
            target.lastName = source.lastName,
            target.age = source.age,
            target.sex = source.sex,
            target.customersPickList1ItemID = source.customersPickList1ItemID,
            target.guestFirstName = source.guestFirstName,
            target.guestLastName = source.guestLastName,
            target.guestTypeID = source.guestTypeID,
            target.guestAge = source.guestAge,
            target.guestSex = source.guestSex,
            target.customersPickList2ItemID = source.customersPickList2ItemID,
            target.incomeTypeID = source.incomeTypeID,
            target.customersPickList3ItemID = source.customersPickList3ItemID,
            target.customersText1 = source.customersText1,
            target.customersDecimal1 = source.customersDecimal1,
            target.customersBool1 = source.customersBool1,
            target.customersDate1 = source.customersDate1,
            target.customersTime1 = source.customersTime1,
            target.doNotCall = source.doNotCall,
            target.primaryPhone = source.primaryPhone,
            target.secondaryPhone = source.secondaryPhone,
            target.streetAddress = source.streetAddress,
            target.streetAddress2 = source.streetAddress2,
            target.city = source.city,
            target.stateID = source.stateID,
            target.zipcode = source.zipcode,
            target.countryID = source.countryID,
            target.businessName = source.businessName,
            target.email = source.email,
            target.customersPickList4ItemID = source.customersPickList4ItemID,
            target.customersText2 = source.customersText2,
            target.customersText3 = source.customersText3,
            target.customersDecimal2 = source.customersDecimal2,
            target.customersBool2 = source.customersBool2,
            target.customersDate2 = source.customersDate2,
            target.customersTime2 = source.customersTime2,
            target.apiConnectionID = source.apiConnectionID,
            target.apiExternalCustomerID = source.apiExternalCustomerID,
            target.apiExternalConnectionID = source.apiExternalConnectionID,
			target.insertTimeStamp = target.insertTimeStamp,
            target.updateTimeStamp = GETDATE(), 
            target.city2 = source.city2,
            target.stateID2 = source.stateID2,
            target.zipcode2 = source.zipcode2,
            target.countryID2 = source.countryID2,
            target.userAddDoNotCall = source.userAddDoNotCall,
            target.insertDoNotCall = source.insertDoNotCall,
            target.streetAddressGuest = source.streetAddressGuest,
            target.streetAddress2Guest = source.streetAddress2Guest,
            target.emailGuest = source.emailGuest;
END;
GO
PRINT N'Creating Procedure [dbo].[BulkUpdateNotes]...';


GO
CREATE PROCEDURE [dbo].[BulkUpdateNotes]
    @DataTableParam dbo.NotesType READONLY -- Use the TVP defined above
AS
BEGIN
    SET NOCOUNT ON;

    -- Use MERGE to update existing records
    MERGE INTO Notes AS target
    USING @DataTableParam AS source
    ON target.noteID = source.noteID
    
    -- When a matching noteID is found, update the existing row
    WHEN MATCHED THEN
        UPDATE SET
            target.externalNoteID = source.externalNoteID,
            target.noteText = source.noteText,
            target.userID = source.userID,
            target.fieldID = source.fieldID,
            target.parentID = source.parentID,
            target.updateTimeStamp = GETDATE(); -- Automatically update the timestamp


END;
GO
PRINT N'Creating Procedure [dbo].[BulkUpdatePurchases]...';


GO
CREATE PROCEDURE [dbo].[BulkUpdatePurchases]
    @DataTableParam dbo.PurchaseType READONLY -- Use the TVP defined above
AS
BEGIN
    SET NOCOUNT ON;

    -- Use MERGE to update existing records and insert new ones if needed
    MERGE INTO Purchases AS target
    USING @DataTableParam AS source
    ON target.purchaseID = source.purchaseID
    
    -- When a matching purchaseID is found, update the existing row
    WHEN MATCHED THEN
        UPDATE SET
            target.externalPurchaseID = source.externalPurchaseID,
            target.tourID = source.tourID,
            target.parentSupersededFromPurchaseID = source.parentSupersededFromPurchaseID,
            target.supersededFromPurchaseID = source.supersededFromPurchaseID,
            target.supersedingFromPender = source.supersedingFromPender,
            target.saleDate = source.saleDate,
            target.saleTime = source.saleTime,
            target.productCategoryID = source.productCategoryID,
            target.productID = source.productID,
            target.subProductID = source.subProductID,
            target.saleAmount = source.saleAmount,
            target.downPaymentAmount = source.downPaymentAmount,
            target.fees1Amount = source.fees1Amount,
            target.fees2Amount = source.fees2Amount,
            target.fees3Amount = source.fees3Amount,
            target.fees4Amount = source.fees4Amount,
            target.fees5Amount = source.fees5Amount,
            target.saleTypeID = source.saleTypeID,
            target.saleStatusTypeID = source.saleStatusTypeID,
            target.saleDispositionID = source.saleDispositionID,
            target.purchasesPickList1ItemID = source.purchasesPickList1ItemID,
            target.purchasesPickList2ItemID = source.purchasesPickList2ItemID,
            target.purchasesPickList3ItemID = source.purchasesPickList3ItemID,
            target.purchasesText1 = source.purchasesText1,
            target.purchasesText2 = source.purchasesText2,
            target.purchasesBool1 = source.purchasesBool1,
            target.purchasesBool2 = source.purchasesBool2,
            target.purchasesDate1 = source.purchasesDate1,
            target.purchasesTime1 = source.purchasesTime1,
            target.purchasesDate2 = source.purchasesDate2,
            target.purchasesTime2 = source.purchasesTime2,
            target.apiConnectionID = source.apiConnectionID,
            target.apiExternalPurchaseID = source.apiExternalPurchaseID,
            target.apiExternalConnectionID = source.apiExternalConnectionID,
            target.updateTimeStamp = GETDATE(); -- Automatically update the timestamp

END;
GO
PRINT N'Creating Procedure [dbo].[BulkUpdateTours]...';


GO
CREATE PROCEDURE [dbo].[BulkUpdateTours]
    @DataTableParam dbo.TourType READONLY -- Use the TVP defined above
AS
BEGIN
    SET NOCOUNT ON;

    -- Use MERGE to update existing records and insert new ones if needed
    MERGE INTO Tours AS target
    USING @DataTableParam AS source
    ON target.tourID = source.tourID
    
    -- When a matching tourID is found, update the existing row
    WHEN MATCHED THEN
        UPDATE SET
            target.externalTourID = source.externalTourID,
            target.customerID = source.customerID,
            target.externalLeadID = source.externalLeadID,
            target.acquisitionDate = source.acquisitionDate,
            target.acquisitionTime = source.acquisitionTime,
            target.leadSourceID = source.leadSourceID,
            target.leadTypeID = source.leadTypeID,
            target.leadStatusTypeID = source.leadStatusTypeID,
            target.leadDispositionID = source.leadDispositionID,
            target.checkInDate = source.checkInDate,
            target.checkInTime = source.checkInTime,
            target.checkOutDate = source.checkOutDate,
            target.checkOutTime = source.checkOutTime,
            target.leadPickList1ItemID = source.leadPickList1ItemID,
            target.leadText1 = source.leadText1,
            target.leadDecimal1 = source.leadDecimal1,
            target.leadBool1 = source.leadBool1,
            target.leadPickList2ItemID = source.leadPickList2ItemID,
            target.leadText2 = source.leadText2,
            target.leadDecimal2 = source.leadDecimal2,
            target.leadBool2 = source.leadBool2,
            target.contactStatusTypeID = source.contactStatusTypeID,
            target.contactDispositionID = source.contactDispositionID,
            target.callBackDate = source.callBackDate,
            target.callBackTime = source.callBackTime,
            target.callBackPickList1ItemID = source.callBackPickList1ItemID,
            target.callBackText1 = source.callBackText1,
            target.callBackDecimal1 = source.callBackDecimal1,
            target.callBackBool1 = source.callBackBool1,
            target.tourSourceID = source.tourSourceID,
            target.tourTypeID = source.tourTypeID,
            target.tourStatusTypeID = source.tourStatusTypeID,
            target.tourConcernTypeID = source.tourConcernTypeID,
            target.tourTypePickList1ItemID = source.tourTypePickList1ItemID,
            target.locationID = source.locationID,
            target.regionID = source.regionID,
            target.tourDate = source.tourDate,
            target.tourTime = source.tourTime,
            target.entryDateTime = source.entryDateTime,
            target.tourText1 = source.tourText1,
            target.tourDecimal1 = source.tourDecimal1,
            target.tourBool1 = source.tourBool1,
            target.rescheduledCount = source.rescheduledCount,
            target.rescheduledTourID = source.rescheduledTourID,
            target.parentRescheduledTourID = source.parentRescheduledTourID,
            target.marketingTeamID = source.marketingTeamID,
            target.marketingAgentID = source.marketingAgentID,
            target.marketingCloserID = source.marketingCloserID,
            target.confirmerID = source.confirmerID,
            target.resetterID = source.resetterID,
            target.venueID = source.venueID,
            target.campaignID = source.campaignID,
            target.channelID = source.channelID,
            target.hotelID = source.hotelID,
            target.marketingPickList1ItemID = source.marketingPickList1ItemID,
            target.marketingPickList2ItemID = source.marketingPickList2ItemID,
            target.marketingText1 = source.marketingText1,
            target.marketingDecimal1 = source.marketingDecimal1,
            target.marketingBool1 = source.marketingBool1,
            target.marketingDate1 = source.marketingDate1,
            target.marketingTime1 = source.marketingTime1,
            target.depositAmount = source.depositAmount,
            target.depositRefundable = source.depositRefundable,
            target.salesTeamID = source.salesTeamID,
            target.podiumID = source.podiumID,
            target.salesRepID = source.salesRepID,
            target.salesCloser1ID = source.salesCloser1ID,
            target.salesCloser2ID = source.salesCloser2ID,
            target.exitID = source.exitID,
            target.verificationOfficerID = source.verificationOfficerID,
            target.salesPickList1ItemID = source.salesPickList1ItemID,
            target.salesText1 = source.salesText1,
            target.salesDecimal1 = source.salesDecimal1,
            target.salesBool1 = source.salesBool1,
            target.salesDate1 = source.salesDate1,
            target.salesTime1 = source.salesTime1,
            target.importNumber = source.importNumber,
            target.apiConnectionID = source.apiConnectionID,
            target.apiExternalTourID = source.apiExternalTourID,
            target.apiExternalConnectionID = source.apiExternalConnectionID,
			target.insertTimeStamp = target.insertTimeStamp,
            target.updateTimeStamp = GETDATE(), -- Automatically update the timestamp
            target.salesTeamExitID = source.salesTeamExitID,
            target.salesRepExit1ID = source.salesRepExit1ID,
            target.salesRepExit2ID = source.salesRepExit2ID,
            target.salesRepExit3ID = source.salesRepExit3ID;


END;
GO
PRINT N'Creating Procedure [dbo].[DeleteAllTours]...';
