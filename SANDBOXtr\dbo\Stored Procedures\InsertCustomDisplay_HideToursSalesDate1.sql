﻿
CREATE PROCEDURE [dbo].[InsertCustomDisplay_HideToursSalesDate1]

AS
BEGIN

	DECLARE @GroupTypeKey nvarchar(64) SET @GroupTypeKey = 'HideToursSalesDate1'

	DELETE FROM CustomPageControlDisplayRights WHERE (customPageControlDisplayRightGroupTypeKey = @GroupTypeKey)

	DECLARE @Add int SET @Add = 1
	DECLARE @Remove int SET @Remove = 2

	DECLARE @Insert int SET @Insert = 1
	DECLARE @Select int SET @Select = 2
	DECLARE @Update int SET @Update = 3

	DECLARE @Visible int SET @Visible = 1
	DECLARE @Enabled int SET @Enabled = 2
	DECLARE @Text int SET @Text = 3
	DECLARE @Delegate int SET @Delegate = 4
	DECLARE @Authorized int SET @Authorized = 5
	DECLARE @Administrated int SET @Administrated = 6

	EXECUTE InsertCustomDisplay_HideTours<PERSON>alesField @GroupTypeKey, 'date1', 'Date1', 'date1', 'Date1s', 0

	DECLARE @PagePathID [varchar] (256)

	SET @PagePathID = 'TrackResults.Web.Data.Cache.TourDateTypesPickListCache_TourDateTypesPickList'

	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'TourDateTypesPickList',@Delegate,NULL,NULL,'TrackResults.Web.Security.CollectionDisplayRules,TrackResults.Web:RemoveKeyValuePairByIntKey(System.Int32 3)'

END