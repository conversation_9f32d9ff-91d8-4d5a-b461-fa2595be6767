﻿CREATE TABLE [dbo].[CancellationDispositionTypes] (
    [cancellationDispositionTypeID]   INT           IDENTITY (1, 1) NOT NULL,
    [cancellationDispositionTypeName] NVARCHAR (64) NOT NULL,
    [sortOrder]                       INT           NOT NULL,
    [active]                          BIT           NOT NULL,
    CONSTRAINT [PK_CancellationDispositionTypes] PRIMARY KEY CLUSTERED ([cancellationDispositionTypeID] ASC),
    CONSTRAINT [UK_CancellationDispositionTypes_cancellationDispositionTypeName] UNIQUE NONCLUSTERED ([cancellationDispositionTypeName] ASC)
);


GO
CREATE TRIGGER [dbo].[CancellationDispositionTypes.InsertUpdateDelete]
    ON [dbo].[CancellationDispositionTypes]
    FOR INSERT, UPDATE, DELETE
AS 
BEGIN
    SET NOCOUNT ON;

    DECLARE @Activity  NVARCHAR (8), @id INT;

    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'updated'
    END

    IF EXISTS (SELECT * FROM inserted) AND NOT EXISTS(SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'inserted'
    END

    IF EXISTS (SELECT * FROM deleted) AND NOT EXISTS(SELECT * FROM inserted)
    BEGIN
        SET @Activity = 'deleted'
    END

    

    IF (@Activity = 'deleted')
    BEGIN
        DECLARE cur CURSOR FOR SELECT cancellationDispositionTypeID FROM deleted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'CancellationDispositionTypes', @id, 'cancellationDispositionTypeID',
                (
                    SELECT *
                    FROM deleted i 
                    WHERE i.cancellationDispositionTypeID =  @id
                    FOR JSON AUTO
                ),0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
    ELSE
    BEGIN
        DECLARE cur CURSOR
        FOR SELECT cancellationDispositionTypeID
        FROM inserted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'CancellationDispositionTypes', @id, 'cancellationDispositionTypeID',
                (
                    SELECT *
                    FROM inserted i 
                    WHERE i.cancellationDispositionTypeID =  @id
                    FOR JSON AUTO
                ), 0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
END

GO
DISABLE TRIGGER [dbo].[CancellationDispositionTypes.InsertUpdateDelete]
    ON [dbo].[CancellationDispositionTypes];

