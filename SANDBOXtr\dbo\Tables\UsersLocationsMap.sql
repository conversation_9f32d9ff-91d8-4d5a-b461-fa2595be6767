﻿CREATE TABLE [dbo].[UsersLocationsMap] (
    [userLocationMapID] INT IDENTITY (1, 1) NOT NULL,
    [userID]            INT NOT NULL,
    [locationID]        INT NOT NULL,
    CONSTRAINT [PK_UsersLocationsMap] PRIMARY KEY CLUSTERED ([userLocationMapID] ASC),
    FOREIGN KEY ([userID]) REFERENCES [dbo].[Users] ([userID])
);


GO
CREATE TRIGGER [dbo].[UsersLocationsMap.InsertUpdateDelete]
    ON [dbo].[UsersLocationsMap]
    FOR INSERT, UPDATE, DELETE
AS 
BEGIN
    SET NOCOUNT ON;

    DECLARE @Activity  NVARCHAR (8), @id INT;

    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'updated'
    END

    IF EXISTS (SELECT * FROM inserted) AND NOT EXISTS(SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'inserted'
    END

    IF EXISTS (SELECT * FROM deleted) AND NOT EXISTS(SELECT * FROM inserted)
    BEGIN
        SET @Activity = 'deleted'
    END

    

    IF (@Activity = 'deleted')
    BEGIN
        DECLARE cur CURSOR FOR SELECT userLocationMapID FROM deleted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'UsersLocationsMap', @id, 'userLocationMapID',
                (
                    SELECT *
                    FROM deleted i 
                    WHERE i.userLocationMapID =  @id
                    FOR JSON AUTO
                ),0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
    ELSE
    BEGIN
        DECLARE cur CURSOR
        FOR SELECT userLocationMapID
        FROM inserted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'UsersLocationsMap', @id, 'userLocationMapID',
                (
                    SELECT *
                    FROM inserted i 
                    WHERE i.userLocationMapID =  @id
                    FOR JSON AUTO
                ), 0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
END

GO
DISABLE TRIGGER [dbo].[UsersLocationsMap.InsertUpdateDelete]
    ON [dbo].[UsersLocationsMap];

