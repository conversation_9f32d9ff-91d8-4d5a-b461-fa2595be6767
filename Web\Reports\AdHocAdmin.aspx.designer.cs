﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated. 
// </auto-generated>
//------------------------------------------------------------------------------

namespace TrackResults.Web.Reports
{


    public partial class AdHocAdmin
    {

        /// <summary>
        /// DateTimeCriteriaDetails control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::TrackResults.Web.Reports.UserControls.CriteriaUserControls.DateTimeCriteriaDetails DateTimeCriteriaDetails;

        /// <summary>
        /// CustomersCriteriaDetails control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::TrackResults.Web.Reports.UserControls.CriteriaUserControls.CustomersCriteriaDetails CustomersCriteriaDetails;

        /// <summary>
        /// CancellationCriteriaDetails control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::TrackResults.Web.Reports.UserControls.CriteriaUserControls.CancellationCriteriaDetails CancellationCriteriaDetails;

        /// <summary>
        /// LeadCriteria control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::TrackResults.Web.Reports.UserControls.CriteriaUserControls.ToursCriteriaDetailsLeadsCalls LeadCriteria;

        /// <summary>
        /// PurchasessCriteriaDetails control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::TrackResults.Web.Reports.UserControls.CriteriaUserControls.PurchasesCriteriaDetails PurchasessCriteriaDetails;

        /// <summary>
        /// SalesCriteriaDetails control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::TrackResults.Web.Reports.UserControls.CriteriaUserControls.ToursCriteriaDetailsSales SalesCriteriaDetails;

        /// <summary>
        /// TourCriteriaDetails control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::TrackResults.Web.Reports.UserControls.CriteriaUserControls.ToursCriteriaDetailsTours TourCriteriaDetails;

        /// <summary>
        /// MarketingCriteriaDetails control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::TrackResults.Web.Reports.UserControls.CriteriaUserControls.ToursCriteriaDetailsMarketing MarketingCriteriaDetails;

        /// <summary>
        /// BasicCriteria1 control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::TrackResults.Web.Reports.UserControls.CriteriaUserControls.BasicCriteriaDetails BasicCriteria1;
    }
}
