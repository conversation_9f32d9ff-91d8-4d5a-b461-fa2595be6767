﻿CREATE TABLE [dbo].[Dashboards] (
    [dashboardID]            INT            IDENTITY (1, 1) NOT NULL,
    [dashboardKey]           NVARCHAR (64)  NULL,
    [dashboardTypeID]        INT            NOT NULL,
    [name]                   NVARCHAR (128) NOT NULL,
    [isApplicationDashboard] BIT            CONSTRAINT [DF_Dashboards_isApplicationDashboard] DEFAULT ((0)) NOT NULL,
    [userID]                 INT            NULL,
    [sortOrder]              INT            NOT NULL,
    [insertTimeStamp]        DATETIME       CONSTRAINT [DF_Dashboards_insertTimeStamp] DEFAULT (getdate()) NOT NULL,
    CONSTRAINT [PK_Dashboards] PRIMARY KEY CLUSTERED ([dashboardID] ASC),
    CONSTRAINT [FK_Dashboards_dashboardTypeID] FOREIGN KEY ([dashboardTypeID]) REFERENCES [dbo].[DashboardTypes] ([dashboardTypeID]),
    CONSTRAINT [FK_Dashboards_userID] FOREIGN KEY ([userID]) REFERENCES [dbo].[Users] ([userID])
);


GO
CREATE TRIGGER [dbo].[Dashboards.InsertUpdateDelete]
    ON [dbo].[Dashboards]
    FOR INSERT, UPDATE, DELETE
AS 
BEGIN
    SET NOCOUNT ON;

    DECLARE @Activity  NVARCHAR (8), @id INT;

    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'updated'
    END

    IF EXISTS (SELECT * FROM inserted) AND NOT EXISTS(SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'inserted'
    END

    IF EXISTS (SELECT * FROM deleted) AND NOT EXISTS(SELECT * FROM inserted)
    BEGIN
        SET @Activity = 'deleted'
    END

    

    IF (@Activity = 'deleted')
    BEGIN
        DECLARE cur CURSOR FOR SELECT dashboardID FROM deleted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'Dashboards', @id, 'dashboardID',
                (
                    SELECT *
                    FROM deleted i 
                    WHERE i.dashboardID =  @id
                    FOR JSON AUTO
                ),0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
    ELSE
    BEGIN
        DECLARE cur CURSOR
        FOR SELECT dashboardID
        FROM inserted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'Dashboards', @id, 'dashboardID',
                (
                    SELECT *
                    FROM inserted i 
                    WHERE i.dashboardID =  @id
                    FOR JSON AUTO
                ), 0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
END

GO
DISABLE TRIGGER [dbo].[Dashboards.InsertUpdateDelete]
    ON [dbo].[Dashboards];

