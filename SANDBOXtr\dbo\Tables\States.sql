﻿CREATE TABLE [dbo].[States] (
    [stateID]           INT           IDENTITY (1, 51) NOT NULL,
    [stateGroupTypeID]  INT           CONSTRAINT [DF_States_stateGroupTypeID] DEFAULT ((6)) NOT NULL,
    [stateAbbreviation] NVARCHAR (8)  NOT NULL,
    [stateName]         NVARCHAR (64) NOT NULL,
    [active]            BIT           NOT NULL,
    CONSTRAINT [PK_States] PRIMARY KEY CLUSTERED ([stateID] ASC),
    CONSTRAINT [FK_States_StateGroupTypes] FOREIGN KEY ([stateGroupTypeID]) REFERENCES [dbo].[StateGroupTypes] ([stateGroupTypeID]),
    CONSTRAINT [UK_States_stateName] UNIQUE NONCLUSTERED ([stateName] ASC)
);


GO
CREATE TRIGGER [dbo].[States.InsertUpdateDelete]
    ON [dbo].[States]
    FOR INSERT, UPDATE, DELETE
AS 
BEGIN
    SET NOCOUNT ON;

    DECLARE @Activity  NVARCHAR (8), @id INT;

    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'updated'
    END

    IF EXISTS (SELECT * FROM inserted) AND NOT EXISTS(SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'inserted'
    END

    IF EXISTS (SELECT * FROM deleted) AND NOT EXISTS(SELECT * FROM inserted)
    BEGIN
        SET @Activity = 'deleted'
    END

    

    IF (@Activity = 'deleted')
    BEGIN
        DECLARE cur CURSOR FOR SELECT stateID FROM deleted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'States', @id, 'stateID',
                (
                    SELECT *
                    FROM deleted i 
                    WHERE i.stateID =  @id
                    FOR JSON AUTO
                ),0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
    ELSE
    BEGIN
        DECLARE cur CURSOR
        FOR SELECT stateID
        FROM inserted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'States', @id, 'stateID',
                (
                    SELECT *
                    FROM inserted i 
                    WHERE i.stateID =  @id
                    FOR JSON AUTO
                ), 0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
END

GO
DISABLE TRIGGER [dbo].[States.InsertUpdateDelete]
    ON [dbo].[States];

