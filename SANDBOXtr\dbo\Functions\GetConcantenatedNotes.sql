﻿CREATE FUNCTION [dbo].[GetConcantenatedNotes]
(
	@ReferenceID int,
	@Delimeter nvarchar(8),
	@<PERSON><PERSON>ey nvarchar(250)
)
RETURNS nvarchar(max)
AS
BEGIN

	DECLARE @ConcantenatedList nvarchar(max)
	SET @ConcantenatedList = ''

	SELECT @ConcantenatedList = @ConcantenatedList + @Delimeter + noteText + ' -' + CONVERT(char(5), NOTES.insertTimeStamp, 101) +
		' ' + ISNULL(Users.firstName,'') + ' ' + ISNULL(Users.lastName,'')
	FROM NOTES
	INNER JOIN CustomFields ON NOTES.fieldID = CustomFields.fieldID
	LEFT OUTER JOIN Users ON NOTES.userID = Users.userID
	WHERE NOTES.parentID = @ReferenceID AND CustomFields.featureKey = @FeatureKey
	ORDER BY NOTES.noteid

	IF DATALENGTH(@ConcantenatedList) > 0
	BEGIN
		SET @ConcantenatedList = RIGHT(@ConcantenatedList, ((DATALENGTH(@ConcantenatedList)/2) - (DATALENGTH(@Delimeter)/2)))
	END

	RETURN @ConcantenatedList

END
