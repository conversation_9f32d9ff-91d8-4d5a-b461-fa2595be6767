﻿using DocumentFormat.OpenXml.Wordprocessing;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using System.Transactions;

using TrackResults.BES.Data.Business;
using TrackResults.BES.Data.Types;
using TrackResults.BES.DataAccess;
using TrackResults.BES.DataAccess.Mappings;
using TrackResults.BES.DataAccess.Notes;
using TrackResults.BES.DataAccess.Secure;
using TrackResults.Common.Core.Data.SqlClient.MSSql;
using TrackResults.Common.Core.Extensions;
using TrackResults.Common.Exceptions;
using static TrackResults.BES.Services.TourReferencesService;

namespace TrackResults.BES.Services
{
	public static class TourReferencesService
	{
		public delegate bool ValidateTourCommandBlock(Tour tour, List<string> errorMessages);

		public static bool UpsertUpdatedTour(TourReference tourReference, List<string> errorMessages, bool useTypeIDs, bool ignoreUpdated,
			int? apiConnectionID, ValidateTourCommandBlock validateTourCommandBlock)
		{
			Tour upsertedTour;

			return UpsertUpdatedTour(tourReference, errorMessages, useTypeIDs, ignoreUpdated, apiConnectionID, validateTourCommandBlock,
				out upsertedTour);
		}

        

        public static bool UpsertUpdatedTour(TourReference tourReference, List<string> errorMessages, bool useTypeIDs, bool ignoreUpdated,
			int? apiConnectionID, ValidateTourCommandBlock validateTourCommandBlock, out Tour upsertedTour)
		{
			bool isValid = true;
			upsertedTour = null;

			bool complete = false;
			Tour updateTour = null;
			MSSqlConnectionScope.ExecuteTransactionWithComplete(ConnectionStrings.Default, delegate ()
			{
				ReferencesConversionService importService = new ReferencesConversionService();
				Dictionary<string, string> columnKeyDisplayNames = ImportExportService.GetColumnKeyDisplayNames();

				if (!importService.ConvertTourReferenceToTour(tourReference, columnKeyDisplayNames, errorMessages, null, null, useTypeIDs,
					out updateTour))
				{
					isValid = false;
				}

				if (isValid)
				{
					if (validateTourCommandBlock != null)
					{
						isValid = validateTourCommandBlock(updateTour, errorMessages);
					}

					if (isValid)
					{
						if(updateTour.Notes != null && tourReference.NotesReference.noteID != null)
						{
							Notes updateNotes = updateTour.Notes;
                            if (ApplicationSettingsService.EnableApiExternalIDConnection && apiConnectionID != null)
                            {
                                if (updateNotes.NoteID == 0)
                                    updateNotes.NoteID = ApiConnectionService.GetExternalTourID((int)apiConnectionID, updateNotes.ExternalNoteID);

                                if (tourReference.Action.ToUpper() == "PUT")
                                    updateNotes.NoteID = NotesDataAccess.Insert(updateNotes, updateNotes.ParentID);
                                else
                                    NotesDataAccess.Update(updateNotes);
                            }
                        }

                        if (updateTour.Customer != null && updateTour.Customer.FirstName == "Kristy" && updateTour.Customer.LastName == "Lee")
                        {
                            string c = updateTour.Customer.FirstName;
                        }

                        if (updateTour.Customer != null && (updateTour.Customer.CustomerUpdated || ignoreUpdated) && tourReference.CustomerReference.CustomerID != null)
						{
							Customer updateCustomer = updateTour.Customer;
                            

							if (ApplicationSettingsService.EnableApiExternalIDConnection && apiConnectionID != null)
							{
								if(updateCustomer.CustomerID == 0)
									updateCustomer.CustomerID = ApiConnectionService.GetExternalCustomerID((int)apiConnectionID, updateCustomer.ApiExternalCustomerID);
						 
							}
                            if (tourReference.Action.ToUpper() == "PUT")
                                updateCustomer.CustomerID = CustomersDataAccess.Upsert(updateCustomer, ignoreUpdated, true, false);
                            else
                                updateCustomer.CustomerID = CustomersDataAccess.Upsert(updateCustomer, ignoreUpdated, false, updateTime:false);
							tourReference.CustomerReference.CustomerID = updateCustomer.CustomerID;
						}

						 
						if (updateTour != null && tourReference.TourID != null)
						{
							if (ApplicationSettingsService.EnableApiExternalIDConnection && apiConnectionID != null)
							{
								if(updateTour.TourID == 0)
									updateTour.TourID = ApiConnectionService.GetExternalTourID((int)apiConnectionID, updateTour.ExternalTourID);
							}

							updateTour.CustomerID = updateTour.Customer.CustomerID;
                            if (tourReference.Action.ToUpper() == "PUT")
                                updateTour.TourID = ToursDataAccess.Upsert(updateTour, ignoreUpdated, true, false);
                            else
                                updateTour.TourID = ToursDataAccess.Upsert(updateTour, ignoreUpdated, updateTime: false);

							tourReference.TourID = updateTour.TourID;
							tourReference.CustomerReference.CustomerID = updateTour.CustomerID;
						}

						if (!updateTour.Purchases.IsNullOrEmpty())
						{
							Purchase purchase;
							for (int index = 0; index < updateTour.Purchases.Count; index++)
							{
                                purchase = updateTour.Purchases[index];
                                if (ApplicationSettingsService.EnableApiExternalIDConnection && apiConnectionID != null)
                                {
                                    purchase.PurchaseID = ApiConnectionService.GetExternalPurchaseID((int)apiConnectionID, purchase.ApiExternalPurchaseID);
                                }
								if(purchase.TourID == 0)
									purchase.TourID = updateTour.TourID;
								//if (tourReference.Action.ToUpper() == "PUT")
								//    purchase.PurchaseID = PurchasesDataAccess.Upsert(purchase, ignoreUpdated, true);
								//else
								//    purchase.PurchaseID = PurchasesDataAccess.Upsert(purchase, ignoreUpdated);
								purchase.PurchaseID = PurchasesDataAccess.Upsert(purchase, ignoreUpdated, updateTime: false);

								tourReference.PurchaseReferences[index].PurchaseID = purchase.PurchaseID;
                                tourReference.PurchaseReferences[index].TourID = purchase.TourID;
                            }
						}
						complete = true;
					}
				}

				return complete;
			});

			upsertedTour = updateTour;

			return isValid;
		}


        public static bool CreateOrUpdateTour(TourReference tourReference, List<string> errorMessages, bool useTypeIDs, bool useExternalIDs)
		{
			bool isValid = true;
			Tour convertedTour = null;

			// Validate external IDs are provided
			if (useExternalIDs == true)
			{
				if (tourReference.ApiConnectionID == null || tourReference.ApiConnectionID == 0)
				{
					errorMessages.Add("Error: Cannot call CreateOrUpdateTour with ExternalIDs without setting ApiConnectionID on the Tour.");
					isValid = false;
				}
				if (string.IsNullOrEmpty(tourReference.ApiExternalTourID))
				{
					errorMessages.Add("Error: Cannot call CreateOrUpdateTour with ExternalIDs without setting ApiExternalTourID on the Tour.");
					isValid = false;
				}
				if (tourReference.CustomerReference != null && (tourReference.CustomerReference.ApiConnectionID == null || tourReference.CustomerReference.ApiConnectionID == 0))
				{
					errorMessages.Add("Error: Cannot call CreateOrUpdateTour with ExternalIDs without setting ApiConnectionID on the Tour.Customer.");
					isValid = false;
				}
				if (tourReference.CustomerReference != null && string.IsNullOrEmpty(tourReference.CustomerReference.ApiExternalCustomerID))
				{
					errorMessages.Add("Error: Cannot call CreateOrUpdateTour with ExternalIDs without setting ApiExternalCustomerID on the Tour.Customer.");
					isValid = false;
				}
				if (!tourReference.PurchaseReferences.IsNullOrEmpty())
				{
					for (int index = 0; index < tourReference.PurchaseReferences.Count; index++)
					{
						if (tourReference.PurchaseReferences[index].ApiConnectionID == null || tourReference.PurchaseReferences[index].ApiConnectionID == 0)
						{
							errorMessages.Add(string.Format("Error: Cannot call CreateOrUpdateTour with ExternalIDs without setting ApiConnectionID on all Tour.Purchases. (TourPurchase Index = {0})", index));
							isValid = false;
						}
						if (string.IsNullOrEmpty(tourReference.PurchaseReferences[index].ApiExternalPurchaseID))
						{
							errorMessages.Add(string.Format("Error: Cannot call CreateOrUpdateTour with ExternalIDs without setting ApiExternalPurchaseID on all Tour.Purchases. (TourPurchase Index = {0})", index));
							isValid = false;
						}
					}
				}

				if (isValid == false)
				{
					return isValid;
				}
			}

			MSSqlConnectionScope.ExecuteTransactionWithComplete(ConnectionStrings.Default, delegate ()
			{
				ReferencesConversionService importService = new ReferencesConversionService();
				Dictionary<string, string> columnKeyDisplayNames = ImportExportService.GetColumnKeyDisplayNames();

				// Convert TourReference to Tour
				isValid = importService.ConvertTourReferenceToTour(tourReference, columnKeyDisplayNames, errorMessages, null, null, useTypeIDs, out convertedTour);

				if (isValid)
				{
					if (convertedTour.Customer != null)
					{
						// Check for external Tour Customer Update
						if (useExternalIDs == true)
						{
							// Retrieve Customer using APIExternalCustomerID
							int? customerID = CustomersDataAccess.SelectCustomerIDByApiConnectionIDApiExternalCustomerID((int)convertedTour.Customer.ApiConnectionID, convertedTour.Customer.ApiExternalCustomerID);
							if (customerID != null && customerID > 0)
							{
								convertedTour.Customer.CustomerID = (int)customerID;
							}
						}

						// Add or Update Tour Customer
						int? customerId = CustomersDataAccess.Upsert(convertedTour.Customer);
						tourReference.CustomerReference.CustomerID = customerId;
						convertedTour.Customer.CustomerID = System.Convert.ToInt32(customerId);
						convertedTour.CustomerID = System.Convert.ToInt32(customerId);
					}

					// Check for external Tour update
					if (useExternalIDs == true)
					{
						// Retrieve Tour using APIExternalTourID
						int? retrievedTourID = ToursDataAccess.SelectTourIDByApiConnectionIDApiExternalTourID((int)convertedTour.ApiConnectionID, convertedTour.ApiExternalTourID);
						if (retrievedTourID != null && retrievedTourID > 0)
						{
							convertedTour.TourID = (int)retrievedTourID;
						}
					}

					// Add or update the Tour
					int? tourID = ToursDataAccess.Upsert(convertedTour);
					tourReference.TourID = tourID;
					convertedTour.TourID = System.Convert.ToInt32(tourID);


					// Add or update Tour Purchases if they exist
					if (!convertedTour.Purchases.IsNullOrEmpty())
					{
						Purchase convertedPurchase;
						for (int index = 0; index < convertedTour.Purchases.Count; index++)
						{
							convertedPurchase = convertedTour.Purchases[index];
							if (useExternalIDs == true)
							{
								// Retrieve Purchase using APIExternalPurchaseID
								int? purchaseID = PurchasesDataAccess.SelectPurchaseIDByApiConnectionIDApiExternalPurchaseID((int)convertedPurchase.ApiConnectionID, convertedPurchase.ApiExternalPurchaseID);
								if (purchaseID != null && purchaseID > 0)
								{
									convertedPurchase.PurchaseID = (int)purchaseID;
								}
							}

							// Add or update the Purchase
							convertedPurchase.TourID = convertedTour.TourID;
							convertedPurchase.ApiExternalTourID = convertedTour.ApiExternalTourID;
							convertedPurchase.PurchaseID = PurchasesDataAccess.Upsert(convertedPurchase);

							tourReference.PurchaseReferences[index].PurchaseID = convertedPurchase.PurchaseID;
							tourReference.PurchaseReferences[index].ExternalPurchaseID = convertedPurchase.ExternalPurchaseID;
							tourReference.PurchaseReferences[index].TourID = convertedPurchase.TourID;
						}
					}
				}

				return isValid;
			});

			return isValid;
		}

		public static bool CreateOrUpdateCustomer(CustomerReference customerReference, List<string> errorMessages, bool useExternalIDs)
		{
			bool isValid = true;
			Customer convertedCustomer = null;

			// Validate external IDs are provided
			if (useExternalIDs == true)
			{
				if (customerReference != null && (customerReference.ApiConnectionID == null || customerReference.ApiConnectionID == 0))
				{
					errorMessages.Add("Error: Cannot call CreateOrUpdateCustomer using ExternalIDs without setting ApiConnectionID on the Customer.");
					isValid = false;
				}
				if (customerReference != null && string.IsNullOrEmpty(customerReference.ApiExternalCustomerID))
				{
					errorMessages.Add("Error: Cannot call CreateOrUpdateCustomer using ExternalIDs without setting ApiExternalCustomerID on the Customer.");
					isValid = false;
				}

				if (isValid == false)
				{
					return isValid;
				}
			}

			MSSqlConnectionScope.ExecuteTransactionWithComplete(ConnectionStrings.Default, delegate ()
			{
				ReferencesConversionService importService = new ReferencesConversionService();
				Dictionary<string, string> columnKeyDisplayNames = ImportExportService.GetColumnKeyDisplayNames();

				// Convert CustomerReference to Customer
				isValid = importService.ConvertCustomerReferenceToCustomer(customerReference, columnKeyDisplayNames, errorMessages, null, null, out convertedCustomer);

				if (isValid)
				{
					if (convertedCustomer != null)
					{
						// Check for external Customer Update
						if (useExternalIDs == true)
						{
							// Retrieve Customer using APIExternalCustomerID
							int? customerID = CustomersDataAccess.SelectCustomerIDByApiConnectionIDApiExternalCustomerID((int)convertedCustomer.ApiConnectionID, convertedCustomer.ApiExternalCustomerID);
							if (customerID != null && customerID > 0)
							{
								convertedCustomer.CustomerID = (int)customerID;
							}
						}

						// Add or Update Customer
						int? customerId = CustomersDataAccess.Upsert(convertedCustomer);
						customerReference.CustomerID = customerId;
						convertedCustomer.CustomerID = System.Convert.ToInt32(customerId);
					}
				}

				return isValid;
			});

			return isValid;
		}

		public static bool CreateOrUpdatePurchase(PurchaseReference purchaseReference, List<string> errorMessages, bool useTypeIDs, bool useExternalIDs)
		{
			bool isValid = true;
			Purchase convertedPurchase = null;

			// Validate external IDs are provided
			if (useExternalIDs == true)
			{
				if (purchaseReference.ApiConnectionID == null || purchaseReference.ApiConnectionID == 0)
				{
					errorMessages.Add("Error: Cannot call CreateOrUpdatePurchase with ExternalIDs without setting ApiConnectionID on the Purchase.");
					isValid = false;
				}
				if (string.IsNullOrEmpty(purchaseReference.ApiExternalPurchaseID))
				{
					errorMessages.Add("Error: Cannot call CreateOrUpdatePurchase with ExternalIDs without setting ApixternalPurchaseID on the Purchase.");
					isValid = false;
				}
				if (string.IsNullOrEmpty(purchaseReference.ApiExternalTourID))
				{
					errorMessages.Add("Error: Cannot call CreateOrUpdatePurchase with ExternalIDs without setting APIExternalTourID on the Purchase.");
					isValid = false;
				}
			}
			else
			{
				// TourID is a foreign key and is required when adding
				if ((purchaseReference.PurchaseID == null || purchaseReference.PurchaseID == 0) && (purchaseReference.TourID == null || purchaseReference.TourID == 0))
				{
					errorMessages.Add("Error: Cannot call CreateOrUpdatePurchase to add a new Purchase without setting TourID on the Purchase.");
					isValid = false;
				}
			}

			if (isValid == false)
			{
				return isValid;
			}

			MSSqlConnectionScope.ExecuteTransactionWithComplete(ConnectionStrings.Default, delegate ()
			{
				ReferencesConversionService importService = new ReferencesConversionService();
				Dictionary<string, string> columnKeyDisplayNames = ImportExportService.GetColumnKeyDisplayNames();

				// Convert PurchaseReference to Purchase
				isValid = importService.ConvertPurchaseReferenceToPurchase(purchaseReference, columnKeyDisplayNames, errorMessages, null, null, useTypeIDs, out convertedPurchase);

				if (isValid)
				{
					// Add or update Purchase
					if (useExternalIDs == true)
					{
						// Retrieve Purchase using APIExternalPurchaseID
						int? purchaseID = PurchasesDataAccess.SelectPurchaseIDByApiConnectionIDApiExternalPurchaseID((int)convertedPurchase.ApiConnectionID, convertedPurchase.ApiExternalPurchaseID);
						if (purchaseID != null && purchaseID > 0)
						{
							convertedPurchase.PurchaseID = (int)purchaseID;
							convertedPurchase.TourID = System.Convert.ToInt32(ToursDataAccess.SelectTourIDByApiConnectionIDApiExternalTourID((int)convertedPurchase.ApiConnectionID, purchaseReference.ApiExternalTourID));
						}
						else
						{
							// This scenario is adding a new Purchase using external IDs so try to retrieve the required TourID
							if (convertedPurchase.TourID <= 0)
							{
								int? retrievedTourID = ToursDataAccess.SelectTourIDByApiConnectionIDApiExternalTourID((int)convertedPurchase.ApiConnectionID, purchaseReference.ApiExternalTourID);
								if (retrievedTourID != null && retrievedTourID > 0)
								{
									convertedPurchase.TourID = (int)retrievedTourID;
									purchaseReference.TourID = retrievedTourID;
								}
								else
								{
									errorMessages.Add("Error: Cannot reference a valid Tour using the provided APIConnectionID and ApiExternalTourID.");
									isValid = false;
									return isValid;
								}
							}
						}
					}

					// Add or update the Purchase
					convertedPurchase.PurchaseID = PurchasesDataAccess.Upsert(convertedPurchase);

					purchaseReference.PurchaseID = convertedPurchase.PurchaseID;
					purchaseReference.ExternalPurchaseID = convertedPurchase.ExternalPurchaseID;
					purchaseReference.TourID = convertedPurchase.TourID;
				}

				return isValid;
			});

			return isValid;
		}

        #region [Bulk CreateOrUpdate Services]

		public class MapsReferences
		{
			public string ExternalCustomerID { get; set; }
			public int CustomerID { get; set; }
            public string ExternalTourID { get; set; }
            public int TourID { get; set; }
            public List<string> ExternalPurchaseIDs { get; set; }
        }
        public static bool CreateOrUpdateTourReferences(List<TourReference> references, List<string> errorMessages, bool useTypeIDs, bool ignoreUpdated,
            int? apiConnectionID, ValidateTourCommandBlock validateTourCommandBlock)
        {
            ReferencesConversionService importService = new ReferencesConversionService();
            Dictionary<string, string> columnKeyDisplayNames = ImportExportService.GetColumnKeyDisplayNames();
			List<Tour> tours = new List<Tour>();
            List<Customer> customers = new List<Customer>();
            List<Purchase> purchases = new List<Purchase>();
            List<Notes> notes = new List<Notes>();
            Tour updateTour = null;
			List<MapsReferences> mapsReferences = new List<MapsReferences>();
            List<(string ColumnName, Type ColumnType, string PropertyName)> customerTypeColumns = new List<(string, Type, string)>
            {
                ("customerID", typeof(int), "CustomerID"),
                ("externalCustomerID", typeof(string), "ExternalCustomerID"),
                ("customerTypeID", typeof(int), "CustomerTypeID"),
                ("customerStatusTypeID", typeof(int), "CustomerStatusTypeID"),
                ("customerDispositionID", typeof(int), "CustomerDispositionID"),
                ("firstName", typeof(string), "FirstName"),
                ("lastName", typeof(string), "LastName"),
                ("age", typeof(int), "Age"),
                ("sex", typeof(bool), "Sex"),
                ("customersPickList1ItemID", typeof(int), "CustomersPickList1ItemID"),
                ("guestFirstName", typeof(string), "GuestFirstName"),
                ("guestLastName", typeof(string), "GuestLastName"),
                ("guestTypeID", typeof(int), "GuestTypeID"),
                ("guestAge", typeof(int), "GuestAge"),
                ("guestSex", typeof(bool), "GuestSex"),
                ("customersPickList2ItemID", typeof(int), "CustomersPickList2ItemID"),
                ("incomeTypeID", typeof(int), "IncomeTypeID"),
                ("customersPickList3ItemID", typeof(int), "CustomersPickList3ItemID"),
                ("customersText1", typeof(string), "CustomersText1"),
                ("customersDecimal1", typeof(decimal), "CustomersDecimal1"),
                ("customersBool1", typeof(bool), "CustomersBool1"),
                ("customersDate1", typeof(DateTime), "CustomersDate1"),
                ("customersTime1", typeof(DateTime), "CustomersTime1"),
                ("doNotCall", typeof(bool), "DoNotCall"),
                ("primaryPhone", typeof(string), "PrimaryPhone"),
                ("secondaryPhone", typeof(string), "SecondaryPhone"),
                ("streetAddress", typeof(string), "StreetAddress"),
                ("streetAddress2", typeof(string), "StreetAddress2"),
                ("city", typeof(string), "City"),
                ("stateID", typeof(int), "StateID"),
                ("zipcode", typeof(string), "Zipcode"),
                ("countryID", typeof(int), "CountryID"),
                ("businessName", typeof(string), "BusinessName"),
                ("email", typeof(string), "Email"),
                ("customersPickList4ItemID", typeof(int), "CustomersPickList4ItemID"),
                ("customersText2", typeof(string), "CustomersText2"),
                ("customersText3", typeof(string), "CustomersText3"),
                ("customersDecimal2", typeof(decimal), "CustomersDecimal2"),
                ("customersBool2", typeof(bool), "CustomersBool2"),
                ("customersDate2", typeof(DateTime), "CustomersDate2"),
                ("customersTime2", typeof(DateTime), "CustomersTime2"),
                ("apiConnectionID", typeof(int), "ApiConnectionID"),
                ("apiExternalCustomerID", typeof(string), "ApiExternalCustomerID"),
                ("apiExternalConnectionID", typeof(string), "ApiExternalConnectionID"),
                ("insertTimeStamp", typeof(DateTime), "InsertTimeStamp"),
                ("updateTimeStamp", typeof(DateTime), "UpdateTimeStamp"),
                ("city2", typeof(string), "City2"),
                ("stateID2", typeof(int), "StateID2"),
                ("zipcode2", typeof(string), "Zipcode2"),
                ("countryID2", typeof(int), "CountryID2"),
                ("userAddDoNotCall", typeof(string), "UserAddDoNotCall"),
                ("insertDoNotCall", typeof(DateTime), "InsertDoNotCall"),
                ("streetAddressGuest", typeof(string), "StreetAddressGuest"),
                ("streetAddress2Guest", typeof(string), "StreetAddress2Guest"),
                ("emailGuest", typeof(string), "Email2")
            };
            List<(string ColumnName, Type ColumnType, string PropertyName)> tourTypeColumns = new List<(string, Type, string)>
            {
                ("tourID", typeof(int), "TourID"),
                ("externalTourID", typeof(string), "ExternalTourID"),
                ("customerID", typeof(int), "CustomerID"),
                ("externalLeadID", typeof(string), "ExternalLeadID"),
                ("acquisitionDate", typeof(DateTime), "AcquisitionDate"),
                ("acquisitionTime", typeof(DateTime), "AcquisitionTime"),
                ("leadSourceID", typeof(int), "LeadSourceID"),
                ("leadTypeID", typeof(int), "LeadTypeID"),
                ("leadStatusTypeID", typeof(int), "LeadStatusTypeID"),
                ("leadDispositionID", typeof(int), "LeadDispositionID"),
                ("checkInDate", typeof(DateTime), "CheckInDate"),
                ("checkInTime", typeof(DateTime), "CheckInTime"),
                ("checkOutDate", typeof(DateTime), "CheckOutDate"),
                ("checkOutTime", typeof(DateTime), "CheckOutTime"),
                ("leadPickList1ItemID", typeof(int), "LeadPickList1ItemID"),
                ("leadText1", typeof(string), "LeadText1"),
                ("leadDecimal1", typeof(decimal), "LeadDecimal1"),
                ("leadBool1", typeof(bool), "LeadBool1"),
                ("leadPickList2ItemID", typeof(int), "LeadPickList2ItemID"),
                ("leadText2", typeof(string), "LeadText2"),
                ("leadDecimal2", typeof(decimal), "LeadDecimal2"),
                ("leadBool2", typeof(bool), "LeadBool2"),
                ("contactStatusTypeID", typeof(int), "ContactStatusTypeID"),
                ("contactDispositionID", typeof(int), "ContactDispositionID"),
                ("callBackDate", typeof(DateTime), "CallBackDate"),
                ("callBackTime", typeof(DateTime), "CallBackTime"),
                ("callBackPickList1ItemID", typeof(int), "CallBackPickList1ItemID"),
                ("callBackText1", typeof(string), "CallBackText1"),
                ("callBackDecimal1", typeof(decimal), "CallBackDecimal1"),
                ("callBackBool1", typeof(bool), "CallBackBool1"),
                ("tourSourceID", typeof(int), "TourSourceID"),
                ("tourTypeID", typeof(int), "TourTypeID"),
                ("tourStatusTypeID", typeof(int), "TourStatusTypeID"),
                ("tourConcernTypeID", typeof(int), "TourConcernTypeID"),
                ("tourTypePickList1ItemID", typeof(int), "TourTypePickList1ItemID"),
                ("locationID", typeof(int), "LocationID"),
                ("regionID", typeof(int), "RegionID"),
                ("tourDate", typeof(DateTime), "TourDate"),
                ("tourTime", typeof(DateTime), "TourTime"),
                ("entryDateTime", typeof(DateTime), "EntryDateTime"),
                ("tourText1", typeof(string), "TourText1"),
                ("tourDecimal1", typeof(decimal), "TourDecimal1"),
                ("tourBool1", typeof(bool), "TourBool1"),
                ("rescheduledCount", typeof(int), "RescheduledCount"),
                ("rescheduledTourID", typeof(int), "RescheduledTourID"),
                ("parentRescheduledTourID", typeof(int), "ParentRescheduledTourID"),
                ("marketingTeamID", typeof(int), "MarketingTeamID"),
                ("marketingAgentID", typeof(int), "MarketingAgentID"),
                ("marketingCloserID", typeof(int), "MarketingCloserID"),
                ("confirmerID", typeof(int), "ConfirmerID"),
                ("resetterID", typeof(int), "ResetterID"),
                ("venueID", typeof(int), "VenueID"),
                ("campaignID", typeof(int), "CampaignID"),
                ("channelID", typeof(int), "ChannelID"),
                ("hotelID", typeof(int), "HotelID"),
                ("marketingPickList1ItemID", typeof(int), "MarketingPickList1ItemID"),
                ("marketingPickList2ItemID", typeof(int), "MarketingPickList2ItemID"),
                ("marketingText1", typeof(string), "MarketingText1"),
                ("marketingDecimal1", typeof(decimal), "MarketingDecimal1"),
                ("marketingBool1", typeof(bool), "MarketingBool1"),
                ("marketingDate1", typeof(DateTime), "MarketingDate1"),
                ("marketingTime1", typeof(DateTime), "MarketingTime1"),
                ("depositAmount", typeof(decimal), "DepositAmount"),
                ("depositRefundable", typeof(bool), "DepositRefundable"),
                ("salesTeamID", typeof(int), "SalesTeamID"),
                ("podiumID", typeof(int), "PodiumID"),
                ("salesRepID", typeof(int), "SalesRepID"),
                ("salesCloser1ID", typeof(int), "SalesCloser1ID"),
                ("salesCloser2ID", typeof(int), "SalesCloser2ID"),
                ("exitID", typeof(int), "ExitID"),
                ("verificationOfficerID", typeof(int), "VerificationOfficerID"),
                ("salesPickList1ItemID", typeof(int), "SalesPickList1ItemID"),
                ("salesText1", typeof(string), "SalesText1"),
                ("salesDecimal1", typeof(decimal), "SalesDecimal1"),
                ("salesBool1", typeof(bool), "SalesBool1"),
                ("salesDate1", typeof(DateTime), "SalesDate1"),
                ("salesTime1", typeof(DateTime), "SalesTime1"),
                ("importNumber", typeof(string), "ImportNumber"),
                ("apiConnectionID", typeof(int), "ApiConnectionID"),
                ("apiExternalTourID", typeof(string), "ApiExternalTourID"),
                ("apiExternalConnectionID", typeof(string), "ApiExternalConnectionID"),
                ("insertTimeStamp", typeof(DateTime), "InsertTimeStamp"),
                ("updateTimeStamp", typeof(DateTime), "UpdateTimeStamp"),
                ("salesTeamExitID", typeof(int), "SalesTeamExitID"),
                ("salesRepExit1ID", typeof(int), "SalesRepExit1ID"),
                ("salesRepExit2ID", typeof(int), "SalesRepExit2ID"),
                ("salesRepExit3ID", typeof(int), "SalesRepExit3ID")
            };
            List<(string ColumnName, Type ColumnType, string PropertyName)> purchaseTypeColumns = new List<(string, Type, string)>
            {
                ("purchaseID", typeof(int), "PurchaseID"),
                ("externalPurchaseID", typeof(string), "ExternalPurchaseID"),
                ("tourID", typeof(int), "TourID"),
                ("parentSupersededFromPurchaseID", typeof(int), "ParentSupersededFromPurchaseID"),
                ("supersededFromPurchaseID", typeof(int), "SupersededFromPurchaseID"),
                ("supersedingFromPender", typeof(bool), "SupersedingFromPender"),
                ("saleDate", typeof(DateTime), "SaleDate"),
                ("saleTime", typeof(DateTime), "SaleTime"),
                ("productCategoryID", typeof(int), "ProductCategoryID"),
                ("productID", typeof(int), "ProductID"),
                ("subProductID", typeof(int), "SubProductID"),
                ("saleAmount", typeof(decimal), "SaleAmount"),
                ("downPaymentAmount", typeof(decimal), "DownPaymentAmount"),
                ("fees1Amount", typeof(decimal), "Fees1Amount"),
                ("fees2Amount", typeof(decimal), "Fees2Amount"),
                ("fees3Amount", typeof(decimal), "Fees3Amount"),
                ("fees4Amount", typeof(decimal), "Fees4Amount"),
                ("fees5Amount", typeof(decimal), "Fees5Amount"),
                ("saleTypeID", typeof(int), "SaleTypeID"),
                ("saleStatusTypeID", typeof(int), "SaleStatusTypeID"),
                ("saleDispositionID", typeof(int), "SaleDispositionID"),
                ("purchasesPickList1ItemID", typeof(int), "PurchasesPickList1ItemID"),
                ("purchasesPickList2ItemID", typeof(int), "PurchasesPickList2ItemID"),
                ("purchasesPickList3ItemID", typeof(int), "PurchasesPickList3ItemID"),
                ("purchasesText1", typeof(string), "PurchasesText1"),
                ("purchasesText2", typeof(string), "PurchasesText2"),
                ("purchasesBool1", typeof(bool), "PurchasesBool1"),
                ("purchasesBool2", typeof(bool), "PurchasesBool2"),
                ("purchasesDate1", typeof(DateTime), "PurchasesDate1"),
                ("purchasesTime1", typeof(DateTime), "PurchasesTime1"),
                ("purchasesDate2", typeof(DateTime), "PurchasesDate2"),
                ("purchasesTime2", typeof(DateTime), "PurchasesTime2"),
                ("apiConnectionID", typeof(int), "ApiConnectionID"),
                ("apiExternalPurchaseID", typeof(string), "ApiExternalPurchaseID"),
                ("apiExternalConnectionID", typeof(string), "ApiExternalConnectionID"),
                ("insertTimeStamp", typeof(DateTime), "InsertTimeStamp"),
                ("updateTimeStamp", typeof(DateTime), "UpdateTimeStamp")
            };
            List<(string ColumnName, Type ColumnType, string PropertyName)> noteTypeColumns = new List<(string, Type, string)>
            {
                ("noteID", typeof(int), "NoteID"),
                ("externalNoteID", typeof(string), "ExternalNoteID"),
                ("noteText", typeof(string), "NoteText"),
                ("userID", typeof(int), "UserID"),
                ("fieldID", typeof(int), "FieldID"),
                ("parentID", typeof(int), "ParentID"),
                ("insertTimeStamp", typeof(DateTime), "InsertTimeStamp"),
                ("updateTimeStamp", typeof(DateTime), "UpdateTimeStamp")
            };

            //// Llamar al método con la lista de columnas permitidas para Purchases
            //DataTable dataTablePurchases = ConvertToDataTableForBulk(Convert.ToInt32(apiConnectionID), purchases, purchaseTypeColumns);

            //// Llamar al método con la lista de columnas permitidas para Notes
            //DataTable dataTableNotes = ConvertToDataTableForBulk(Convert.ToInt32(apiConnectionID), notes, noteTypeColumns);

            try
            {
                foreach (TourReference reference in references)
                {
                    // Attempt to convert the reference to a Tour object
                    if (importService.ConvertTourReferenceToTour(reference, columnKeyDisplayNames, errorMessages, null, null, useTypeIDs, out updateTour))
                    {
                        // Handle Customer addition
                        if (updateTour.Customer != null &&
                           (updateTour.Customer.CustomerUpdated || ignoreUpdated) &&
                            reference.CustomerReference?.CustomerID != null)
                            customers.Add(updateTour.Customer);

                        // Handle Tour addition and mapping
                        if (updateTour != null && reference.TourID != null)
                        {
                            tours.Add(updateTour);

                            // Create a MapsReferences object to track the relationship
                            MapsReferences maps = new MapsReferences();
                            maps.ExternalTourID = updateTour.ExternalTourID;
                            maps.ExternalPurchaseIDs = new List<string>();

                            // Handle the mapping of Customer to Tour
                            if (updateTour.Customer != null &&
                               (updateTour.Customer.CustomerUpdated || ignoreUpdated) &&
                                reference.CustomerReference?.CustomerID != null)
                                maps.ExternalCustomerID = updateTour.Customer.ExternalCustomerID;

                            // Handle the mapping of Purchases
                            if (!updateTour.Purchases.IsNullOrEmpty())
                            {
                                foreach (var purchase in updateTour.Purchases)
                                    maps.ExternalPurchaseIDs.Add(purchase.ExternalPurchaseID);
                            }

                            // Add the maps object to the list after it's fully populated
                            mapsReferences.Add(maps);
                        }

                        // Handle Purchases addition
                        if (!updateTour.Purchases.IsNullOrEmpty())
                            purchases.AddRange(updateTour.Purchases);

                        // Handle Notes addition
                        if (updateTour.Notes != null && reference.NotesReference?.noteID != null)
                            notes.Add(updateTour.Notes);

                    }
                }
            }
            catch (Exception exception)
            {
                MiddlewareErrorsDataAccess.Insert(MiddlewareErrorType.ImportTourReferenceException, Convert.ToInt32(apiConnectionID),
                           null, null, exception.ToString(), true, null, null);
                return false;
            }

            try
            {
                try
                {
                    if (customers.Count > 0)
                    {
                        // Obtén la lista de IDs de clientes existentes con su externalCustomerID
                        var existingCustomerIds = GetExistingCustomerIds();

                        // Separa los clientes únicos para insertar
                        var customersToInsert = customers
                            .Where(c => !existingCustomerIds.Any(e => e.externalCustomerID == c.ExternalCustomerID))
                            .GroupBy(c => c.ExternalCustomerID)
                            .Select(g => g.First())
                            .ToList();

                        // Separa los clientes únicos para actualizar
                        var customersToUpdate = customers
                            .Where(c => existingCustomerIds.Any(e => e.externalCustomerID == c.ExternalCustomerID))
                            .GroupBy(c => c.ExternalCustomerID)
                            .Select(g => g.First())
                            .ToList();


                        if (customersToInsert.Count > 0)
                            BulkInsertCustomers(Convert.ToInt32(apiConnectionID), customersToInsert, customerTypeColumns);
                        if (customersToUpdate.Count > 0)
                        {
                            // Assign customerID to customersToUpdate
                            foreach (var customer in customersToUpdate)
                            {
                                // Find the corresponding customerID from existingCustomerIds
                                var matchingCustomer = existingCustomerIds
                                    .FirstOrDefault(e => e.externalCustomerID == customer.ExternalCustomerID);
                                if (matchingCustomer != default)
                                    customer.CustomerID = matchingCustomer.customerID;
                            }

                            BulkUpdateCustomers(Convert.ToInt32(apiConnectionID), customersToUpdate, customerTypeColumns);
                        }

                        mapsReferences = GetCustomersIds(mapsReferences);
                    }
                    if (tours.Count > 0)
                    {
                        // Obtén la lista de IDs de tours existentes con su externalTourID
                        var existingToursIds = GetExistingToursIds();

                        var toursToInsert = tours
                            .Where(c => !existingToursIds.Any(e => e.externalTourID == c.ExternalTourID))
                            .GroupBy(c => c.ExternalTourID)
                            .Select(g => g.First())
                            .ToList();

                        var toursToUpdate = tours
                            .Where(c => existingToursIds.Any(e => e.externalTourID == c.ExternalTourID))
                            .GroupBy(c => c.ExternalTourID) // Agrupa por externalTourID para evitar duplicados
                            .Select(g => g.First())
                            .ToList();

                        if (toursToInsert.Count > 0)
                            BulkInsertTours(Convert.ToInt32(apiConnectionID), toursToInsert, mapsReferences, tourTypeColumns);
                        if (toursToUpdate.Count > 0)
                        {
                            // Assign tourID to toursToUpdate
                            foreach (var tour in toursToUpdate)
                            {
                                var matchingTour = existingToursIds
                                    .FirstOrDefault(e => e.externalTourID == tour.ExternalTourID);
                                if (matchingTour != default)
                                    tour.TourID = matchingTour.tourID;
                            }
                            BulkUpdateTours(Convert.ToInt32(apiConnectionID), toursToUpdate, mapsReferences, tourTypeColumns);
                        }
                        mapsReferences = GetToursIds(mapsReferences);
                    }
                    if (purchases.Count > 0)
                    {
                        // Obtén la lista de IDs de compras existentes con su externalPurchaseID
                        var existingPurchasesIds = GetExistingPurchasesIds();

                        var purchasesToInsert = purchases
                            .Where(c => !existingPurchasesIds.Any(e => e.externalPurchaseID == c.ExternalPurchaseID))
                            .GroupBy(c => c.ExternalPurchaseID) // Agrupa por ExternalPurchaseID
                            .Select(g => g.First())
                            .ToList();

                        var purchasesToUpdate = purchases
                            .Where(c => existingPurchasesIds.Any(e => e.externalPurchaseID == c.ExternalPurchaseID))
                            .GroupBy(c => c.ExternalPurchaseID) // Agrupa por externalPurchaseID para evitar duplicados
                            .Select(g => g.First())
                            .ToList();

                        if (purchasesToInsert.Count > 0)
                            BulkInsertPurchases(Convert.ToInt32(apiConnectionID), purchasesToInsert, mapsReferences, purchaseTypeColumns);
                        if (purchasesToUpdate.Count > 0)
                        {
                            foreach (var purchase in purchasesToUpdate)
                            {
                                var matchingPurchase = existingPurchasesIds
                                    .FirstOrDefault(e => e.externalPurchaseID == purchase.ExternalPurchaseID);
                                if (matchingPurchase != default)
                                    purchase.PurchaseID = matchingPurchase.purchaseID;
                            }

                            BulkUpdatePurchases(Convert.ToInt32(apiConnectionID), purchasesToUpdate, mapsReferences, purchaseTypeColumns);
                        }
                    }
                    if (notes.Count > 0)
                        BulkInsertNotes(Convert.ToInt32(apiConnectionID), notes, noteTypeColumns);
                }
                catch (Exception ex)
                {
                    // Manejo de errores
                    MiddlewareErrorsDataAccess.Insert(
                        MiddlewareErrorType.ImportTourReferenceException,
                        Convert.ToInt32(apiConnectionID),
                        null,
                        null,
                        ex.ToString(),
                        true,
                        null,
                        null
                    );
                    throw; 
                }

                return true;
            }
            catch (Exception exception)
            {
                MiddlewareErrorsDataAccess.Insert(MiddlewareErrorType.ImportTourReferenceException, Convert.ToInt32(apiConnectionID),
                           null, null, exception.ToString(), true, null, null);
                return false;
            }

        }

        private static void BulkInsertCustomers(int apiConnectionID, List<Customer> customers, List<(string ColumnName, Type ColumnType, string PropertyName)> columnMappings)
        {
            // Convertimos la lista de clientes a un DataTable
            DataTable data = ConvertToDataTableForBulk(apiConnectionID, customers, columnMappings);
            ValidateDataTableColumns(data);

            // Configuramos los mapeos de columnas como un diccionario para pasarlo a ExecuteBulkInsert
            var columnMappingDictionary = columnMappings.ToDictionary(
                mapping => mapping.PropertyName == "Email2" ? "EmailGuest" : mapping.PropertyName,
                mapping => mapping.ColumnName == "Email2" ? "emailGuest" : mapping.ColumnName
            );


            try
            {
                // Llamamos a ExecuteBulkInsert
                MSSqlDataAccess.ExecutedBulkInsert(
                    ConnectionStrings.Default, "Customers", columnMappings, customers
                );
            }
            catch (Exception exception)
            {
                MiddlewareErrorsDataAccess.Insert(
                        MiddlewareErrorType.ImportTourReferenceException,
                        apiConnectionID,
                        null,
                        null,
                        exception.ToString(),
                        true,
                        null,
                        null
                    );

                throw;
            }
        }

        private static void BulkInsertTours(int apiConnectionID, List<Tour> tours, List<MapsReferences> mapsReferences, List<(string ColumnName, Type ColumnType, string PropertyName)> columnMappings)
        {
            // Actualizamos los CustomerID en la lista de tours basado en los mapsReferences
            foreach (var tour in tours)
            {
                var mapReference = mapsReferences.FirstOrDefault(m => m.ExternalTourID == tour.ExternalTourID);
                if (mapReference != null && !string.IsNullOrEmpty(mapReference.ExternalCustomerID))
                {
                    tour.CustomerID = mapReference.CustomerID;
                }
            }

            try
            {
                // Llamamos a ExecuteBulkInsert
                MSSqlDataAccess.ExecutedBulkInsert(
                    ConnectionStrings.Default, "Tours", columnMappings, tours
                );
            }
            catch (Exception exception)
            {
                // Manejo de errores
                MiddlewareErrorsDataAccess.Insert(
                        MiddlewareErrorType.ImportTourReferenceException,
                        apiConnectionID,
                        null,
                        null,
                        exception.ToString(),
                        true,
                        null,
                        null
                    );

                throw;
            }
        }

        private static void BulkInsertPurchases(int apiConnectionID, List<Purchase> purchases, List<MapsReferences> mapsReferences, List<(string ColumnName, Type ColumnType, string PropertyName)> columnMappings)
        {
            // Actualizamos los TourID en la lista de purchases basado en los mapsReferences
            foreach (var purchase in purchases)
            {
                var mapReference = mapsReferences.FirstOrDefault(m => m.ExternalPurchaseIDs.Contains(purchase.ExternalPurchaseID));
                if (mapReference != null && !string.IsNullOrEmpty(mapReference.ExternalTourID))
                {
                    purchase.TourID = mapReference.TourID;
                }
            }

            // Convertimos la lista de purchases a un DataTable
            DataTable data = ConvertToDataTableForBulk(apiConnectionID, purchases, columnMappings);
            ValidateDataTableColumns(data);

            // Configuramos los mapeos de columnas como un diccionario para pasarlo a ExecuteBulkInsert
            var columnMappingDictionary = columnMappings.ToDictionary(mapping => mapping.PropertyName, mapping => mapping.ColumnName);

            try
            {
                // Llamamos a ExecuteBulkInsert
                MSSqlDataAccess.ExecutedBulkInsert(
                    ConnectionStrings.Default, "Purchases", columnMappings, purchases
                );
            }
            catch (Exception exception)
            {
                // Manejo de errores
                MiddlewareErrorsDataAccess.Insert(
                        MiddlewareErrorType.ImportTourReferenceException,
                        apiConnectionID,
                        null,
                        null,
                        exception.ToString(),
                        true,
                        null,
                        null
                    );

                throw;
            }
        }

        private static void BulkInsertNotes(int apiConnectionID, List<Notes> notes, List<(string ColumnName, Type ColumnType, string PropertyName)> columnMappings)
        {
            // Convertimos la lista de notes a un DataTable
            DataTable data = ConvertToDataTableForBulk(apiConnectionID, notes, columnMappings);
            ValidateDataTableColumns(data);

            // Configuramos los mapeos de columnas como un diccionario para pasarlo a ExecuteBulkInsert
            var columnMappingDictionary = columnMappings.ToDictionary(mapping => mapping.PropertyName, mapping => mapping.ColumnName);

            try
            {
                // Llamamos a ExecuteBulkInsert
                MSSqlDataAccess.ExecutedBulkInsert(
                    ConnectionStrings.Default, "Notes", columnMappings, notes
                );
            }
            catch (Exception exception)
            {
                // Manejo de errores
                MiddlewareErrorsDataAccess.Insert(
                        MiddlewareErrorType.ImportTourReferenceException,
                        apiConnectionID,
                        null,
                        null,
                        exception.ToString(),
                        true,
                        null,
                        null
                    );
                throw;
            }
        }

        private static void BulkUpdateCustomers(int apiConnectionID, List<Customer> customers, List<(string ColumnName, Type ColumnType, string PropertyName)> columnMappings)
        {
            string storedProcedureName = "BulkUpdateCustomers";
            string tableTypeName = "dbo.CustomerType";
            int batchSize = 1000;

            // Convertimos la lista de customers a un DataTable
            DataTable customerTable = ConvertToDataTableForBulk(apiConnectionID, customers, columnMappings);
            ValidateDataTableColumns(customerTable);

            try
            {
                // Llamamos al método ExecuteBulkUpdate para procesar la actualización en lotes
                MSSqlDataAccess.ExecuteBulkUpdate(
                    connectionString: ConnectionStrings.Default,
                    storedProcedureName: storedProcedureName,
                    data: customers,
                    tableTypeName: tableTypeName,
                    columnMappings,
                    batchSize: batchSize
                );
            }
            catch (Exception exception)
            {
                // Manejo de errores
                MiddlewareErrorsDataAccess.Insert(
                        MiddlewareErrorType.ImportTourReferenceException,
                        apiConnectionID,
                        null,
                        null,
                        exception.ToString(),
                        true,
                        null,
                        null
                    );

                throw;
            }
        }

        private static void BulkUpdateTours(int apiConnectionID, List<Tour> tours, List<MapsReferences> mapsReferences, List<(string ColumnName, Type ColumnType, string PropertyName)> columnMappings)
        {
            // Actualizamos el CustomerID en la lista de tours basado en los mapsReferences
            foreach (var tour in tours)
            {
                var mapReference = mapsReferences.FirstOrDefault(m => m.ExternalTourID == tour.ExternalTourID);
                if (mapReference != null && !string.IsNullOrEmpty(mapReference.ExternalCustomerID))
                {
                    tour.CustomerID = mapReference.CustomerID;
                }
            }

            // Convertimos la lista de tours a un DataTable
            DataTable tourTable = ConvertToDataTableForBulk(apiConnectionID, tours, columnMappings);
            ValidateDataTableColumns(tourTable);

            try
            {
                // Llamamos al método ExecuteBulkUpdate para procesar la actualización en lotes
                MSSqlDataAccess.ExecuteBulkUpdate(
                    connectionString: ConnectionStrings.Default,
                    storedProcedureName: "BulkUpdateTours",
                    data: tours,
                    tableTypeName: "dbo.TourType",
                    columnMappings,
                    batchSize: 1000
                );
            }
            catch (Exception exception)
            {
                // Manejo de errores
                MiddlewareErrorsDataAccess.Insert(
                        MiddlewareErrorType.ImportTourReferenceException,
                        apiConnectionID,
                        null,
                        null,
                        exception.ToString(),
                        true,
                        null,
                        null
                    );
                throw;
            }
            
        }

        private static void BulkUpdatePurchases(int apiConnectionID, List<Purchase> purchases, List<MapsReferences> mapsReferences, List<(string ColumnName, Type ColumnType, string PropertyName)> columnMappings)
        {
            // Actualizamos el TourID en la lista de purchases basado en los mapsReferences
            foreach (var purchase in purchases)
            {
                var mapReference = mapsReferences.FirstOrDefault(m => m.ExternalPurchaseIDs != null && m.ExternalPurchaseIDs.Contains(purchase.ExternalPurchaseID));
                if (mapReference != null && !string.IsNullOrEmpty(mapReference.ExternalCustomerID))
                {
                    purchase.TourID = mapReference.TourID;
                }
            }

            // Convertimos la lista de purchases a un DataTable
            DataTable purchaseTable = ConvertToDataTableForBulk(apiConnectionID, purchases, columnMappings);
            ValidateDataTableColumns(purchaseTable);

            try
            {
                // Llamamos al método ExecuteBulkUpdate para procesar la actualización en lotes
                MSSqlDataAccess.ExecuteBulkUpdate(
                    connectionString: ConnectionStrings.Default,
                    storedProcedureName: "BulkUpdatePurchases",
                    data: purchases,
                    tableTypeName: "dbo.PurchaseType",
                    columnMappings,
                    batchSize: 1000
                );
            }
            catch (Exception exception)
            {
                // Manejo de errores
                MiddlewareErrorsDataAccess.Insert(
                        MiddlewareErrorType.ImportTourReferenceException,
                        apiConnectionID,
                        null,
                        null,
                        exception.ToString(),
                        true,
                        null,
                        null
                    );

                throw;
            }
        }

        private static void BulkUpdateNotes(int apiConnectionID, List<Notes> notes, List<(string ColumnName, Type ColumnType, string PropertyName)> columnMappings)
        {
            // Convertimos la lista de notes a un DataTable
            DataTable notesTable = ConvertToDataTableForBulk(apiConnectionID, notes, columnMappings);
            ValidateDataTableColumns(notesTable);

            // Llamamos a ExecuteBulkUpdate
            try
            {
                // Llamamos al método ExecuteBulkUpdate para procesar la actualización en lotes
                MSSqlDataAccess.ExecuteBulkUpdate(
                    connectionString: ConnectionStrings.Default,
                    storedProcedureName: "BulkUpdateNotes",
                    data: notes,
                    tableTypeName: "dbo.NotesType",
                    columnMappings,
                    batchSize: 1000
                );
            }
            catch (Exception exception)
            {
                // Manejo de errores
                MiddlewareErrorsDataAccess.Insert(
                        MiddlewareErrorType.ImportTourReferenceException,
                        apiConnectionID,
                        null,
                        null,
                        exception.ToString(),
                        true,
                        null,
                        null
                    );
                throw;
            }
        }


        //private static void BulkUpdateCustomers(List<Customer> customers, List<(string ColumnName, Type ColumnType, string PropertyName)> columnMappings, SqlConnection connection, SqlTransaction transaction)
        //{
        //    int batchSize = 1000; // Adjust this value as needed
        //    int totalBatches = (int)Math.Ceiling((double)customers.Count / batchSize);

        //    try
        //    {
        //        for (int i = 0; i < totalBatches; i++)
        //        {
        //            List<Customer> batch = customers.Skip(i * batchSize).Take(batchSize).ToList();

        //            using (var command = new SqlCommand("BulkUpdateCustomers", connection, transaction))
        //            {
        //                command.CommandType = CommandType.StoredProcedure;

        //                command.CommandTimeout = 300; // Increase timeout

        //                // Create a DataTable for the current batch
        //                DataTable customerTable = ConvertToDataTableForBulk(batch, columnMappings);

        //                ValidateDataTableColumns(customerTable);

        //                // Create a SqlParameter for the DataTable
        //                SqlParameter customerParameter = new SqlParameter
        //                {
        //                    ParameterName = "@CustomerTable",
        //                    SqlDbType = SqlDbType.Structured,
        //                    Value = customerTable,
        //                    TypeName = "dbo.CustomerType"
        //                };

        //                command.Parameters.Add(customerParameter);

        //                try
        //                {
        //                    command.ExecuteNonQuery();
        //                }
        //                catch (SqlException sqlEx) when (sqlEx.Number == 1205) // Error de deadlock
        //                {
        //                    // Implementa retry o log específico para deadlocks si es necesario
        //                    MiddlewareErrorsDataAccess.Insert(MiddlewareErrorType.ImportTourReferenceException, 0,
        //                       null, null, sqlEx.ToString(), true, null, null);
        //                    throw; // Puedes decidir si hacer retry o lanzar la excepción
        //                }
        //            }
        //        }
        //    }
        //    catch (Exception exception)
        //    {
        //        MiddlewareErrorsDataAccess.Insert(MiddlewareErrorType.ImportTourReferenceException, 0,
        //                   null, null, exception.ToString(), true, null, null);
        //    }
        //}

        //private static void BulkUpdateTours(List<Tour> tours, List<MapsReferences> mapsReferences, List<(string ColumnName, Type ColumnType, string PropertyName)> columnMappings, SqlConnection connection, SqlTransaction transaction)
        //{
        //    foreach (var tour in tours)
        //    {
        //        var mapReference = mapsReferences.FirstOrDefault(m => m.ExternalTourID == tour.ExternalTourID);
        //        if (mapReference != null && !string.IsNullOrEmpty(mapReference.ExternalCustomerID))
        //            tour.CustomerID = mapReference.CustomerID;
        //    }

        //    int batchSize = 1000; // Adjust this value as needed
        //    int totalBatches = (int)Math.Ceiling((double)tours.Count / batchSize);

        //    try
        //    {
        //        for (int i = 0; i < totalBatches; i++)
        //        {
        //            List<Tour> batch = tours.Skip(i * batchSize).Take(batchSize).ToList();

        //            using (var command = new SqlCommand("BulkUpdateTours", connection, transaction))
        //            {
        //                command.CommandType = CommandType.StoredProcedure;

        //                command.CommandTimeout = 300; // Increase timeout

        //                // Create a DataTable for the current batch
        //                DataTable tourTable = ConvertToDataTableForBulk(batch, columnMappings);

        //                ValidateDataTableColumns(tourTable);

        //                // Add the DataTable as a parameter to the stored procedure
        //                SqlParameter tourParameter = new SqlParameter
        //                {
        //                    ParameterName = "@TourTable",
        //                    SqlDbType = SqlDbType.Structured,
        //                    Value = tourTable,
        //                    TypeName = "dbo.TourType" // The table type defined in SQL Server
        //                };

        //                command.Parameters.Add(tourParameter);

        //                // Execute the stored procedure for this batch
        //                try
        //                {
        //                    command.ExecuteNonQuery();
        //                }
        //                catch (SqlException sqlEx) when (sqlEx.Number == 1205) // Error de deadlock
        //                {
        //                    // Implementa retry o log específico para deadlocks si es necesario
        //                    MiddlewareErrorsDataAccess.Insert(MiddlewareErrorType.ImportTourReferenceException, 0,
        //                       null, null, sqlEx.ToString(), true, null, null);
        //                    throw; // Puedes decidir si hacer retry o lanzar la excepción
        //                }
        //            }
        //        }
        //    }
        //    catch (Exception exception)
        //    {
        //        MiddlewareErrorsDataAccess.Insert(MiddlewareErrorType.ImportTourReferenceException, 0,
        //                   null, null, exception.ToString(), true, null, null);
        //    }
        //}

        //private static void BulkUpdatePurchases(List<Purchase> purchases, List<MapsReferences> mapsReferences, List<(string ColumnName, Type ColumnType, string PropertyName)> columnMappings, SqlConnection connection, SqlTransaction transaction)
        //{
        //    foreach (var purchase in purchases)
        //    {
        //        // Aquí buscamos si hay un mapReference que contenga el ExternalPurchaseID en su lista de ExternalPurchaseIDs
        //        var mapReference = mapsReferences.FirstOrDefault(m =>
        //            m.ExternalPurchaseIDs != null && m.ExternalPurchaseIDs.Contains(purchase.ExternalPurchaseID));

        //        // Si encontramos una referencia válida y el ExternalCustomerID no es nulo o vacío, actualizamos el TourID
        //        if (mapReference != null && !string.IsNullOrEmpty(mapReference.ExternalCustomerID))
        //        {
        //            purchase.TourID = mapReference.TourID;
        //        }
        //    }

        //    int batchSize = 1000; // Adjust this value as needed
        //    int totalBatches = (int)Math.Ceiling((double)purchases.Count / batchSize);

        //    try
        //    {
        //        for (int i = 0; i < totalBatches; i++)
        //        {
        //            List<Purchase> batch = purchases.Skip(i * batchSize).Take(batchSize).ToList();

        //            using (var command = new SqlCommand("BulkUpdatePurchases", connection, transaction))
        //            {
        //                command.CommandType = CommandType.StoredProcedure;

        //                command.CommandTimeout = 300; // Increase timeout

        //                // Create a DataTable from the purchases list
        //                DataTable purchaseTable = ConvertToDataTableForBulk(purchases, columnMappings);

        //                ValidateDataTableColumns(purchaseTable);
        //                // Add the DataTable as a parameter to the stored procedure
        //                SqlParameter purchaseParameter = new SqlParameter
        //                {
        //                    ParameterName = "@PurchaseTable",
        //                    SqlDbType = SqlDbType.Structured,
        //                    Value = purchaseTable,
        //                    TypeName = "dbo.PurchaseType" // The table type defined in SQL Server
        //                };

        //                command.Parameters.Add(purchaseParameter);

        //                // Execute the stored procedure for this batch
        //                try
        //                {
        //                    command.ExecuteNonQuery();
        //                }
        //                catch (SqlException sqlEx) when (sqlEx.Number == 1205) // Error de deadlock
        //                {
        //                    // Implementa retry o log específico para deadlocks si es necesario
        //                    MiddlewareErrorsDataAccess.Insert(MiddlewareErrorType.ImportTourReferenceException, 0,
        //                       null, null, sqlEx.ToString(), true, null, null);
        //                    throw; // Puedes decidir si hacer retry o lanzar la excepción
        //                }
        //            }
        //        }
        //    }
        //    catch (Exception exception)
        //    {
        //        MiddlewareErrorsDataAccess.Insert(MiddlewareErrorType.ImportTourReferenceException, 0,
        //                   null, null, exception.ToString(), true, null, null);
        //    }
        //}

        //private static void BulkUpdateNotes(List<Notes> notes, List<(string ColumnName, Type ColumnType, string PropertyName)> columnMappings, SqlConnection connection, SqlTransaction transaction)
        //{
        //    int batchSize = 1000; // Adjust this value as needed
        //    int totalBatches = (int)Math.Ceiling((double)notes.Count / batchSize);

        //    try
        //    {
        //        for (int i = 0; i < totalBatches; i++)
        //        {
        //            List<Notes> batch = notes.Skip(i * batchSize).Take(batchSize).ToList();

        //            using (var command = new SqlCommand("BulkUpdateNotes", connection, transaction))
        //            {
        //                command.CommandType = CommandType.StoredProcedure;

        //                command.CommandTimeout = 300; // Increase timeout

        //                // Create a DataTable from the note list
        //                DataTable notesTable = ConvertToDataTableForBulk(notes, columnMappings);

        //                // Add the DataTable as a parameter to the stored procedure
        //                SqlParameter notesParameter = new SqlParameter
        //                {
        //                    ParameterName = "@NotesTable",
        //                    SqlDbType = SqlDbType.Structured,
        //                    Value = notesTable,
        //                    TypeName = "dbo.NotesType" // The table type defined in SQL Server
        //                };

        //                command.Parameters.Add(notesParameter);

        //                // Execute the stored procedure for this batch
        //                try
        //                {
        //                    command.ExecuteNonQuery();
        //                }
        //                catch (SqlException sqlEx) when (sqlEx.Number == 1205) // Error de deadlock
        //                {
        //                    // Implementa retry o log específico para deadlocks si es necesario
        //                    MiddlewareErrorsDataAccess.Insert(MiddlewareErrorType.ImportTourReferenceException, 0,
        //                       null, null, sqlEx.ToString(), true, null, null);
        //                    throw; // Puedes decidir si hacer retry o lanzar la excepción
        //                }
        //            }
        //        }
        //    }
        //    catch (Exception exception)
        //    {
        //        MiddlewareErrorsDataAccess.Insert(MiddlewareErrorType.ImportTourReferenceException, 0,
        //                   null, null, exception.ToString(), true, null, null);
        //    }
        //}

        public static DataTable ConvertToDataTableForBulk<T>(int apiConnectionID, List<T> data, List<(string ColumnName, Type ColumnType, string PropertyName)> columnMappings)
        {
            try
            {
                // Create the DataTable to hold the data
                DataTable table = new DataTable();

                // Add columns to the DataTable based on the provided column mappings
                foreach (var columnMapping in columnMappings)
                {
                    table.Columns.Add(columnMapping.ColumnName, columnMapping.ColumnType);
                }

                // Populate the DataTable with data from the list of objects
                foreach (var item in data)
                {
                    DataRow row = table.NewRow();

                    // Populate each column in the DataRow using reflection to get property values
                    foreach (var columnMapping in columnMappings)
                    {
                        // Get the property value using reflection
                        var propertyValue = typeof(T).GetProperty(columnMapping.PropertyName)?.GetValue(item, null);

                        // Handle null values by assigning DBNull if the property value is null
                        row[columnMapping.ColumnName] = propertyValue ?? DBNull.Value;
                    }

                    // Add the populated row to the DataTable
                    table.Rows.Add(row);
                }

                return table;
            }
            catch (Exception exception)
            {
                MiddlewareErrorsDataAccess.Insert(
                         MiddlewareErrorType.ImportTourReferenceException,
                         apiConnectionID,
                         null,
                         null,
                         exception.ToString(),
                         true,
                         null,
                         null
                     );
                return null;
            }
        }

        public static void ValidateDataTableColumns(DataTable dataTable)
        {
            foreach (DataRow row in dataTable.Rows)
            {
                foreach (DataColumn column in dataTable.Columns)
                {
                    // Validar si las columnas son de tipo DateTime
                    if (column.DataType == typeof(DateTime))
                    {
                        if (column.ColumnName == "updateTimeStamp" || column.ColumnName == "insertTimeStamp" || column.ColumnName == "entryDateTime")
                        {
                            // Si la columna es updateTimeStamp, asignar la fecha actual
                            row[column] = DateTime.Now;
                        }
                        else if(row[column] != DBNull.Value)
                        {
                            DateTime dateValue = (DateTime)row[column];

                            // Si la fecha está fuera del rango de SQL Server, convertirla a DBNull
                            if (dateValue < (DateTime)System.Data.SqlTypes.SqlDateTime.MinValue ||
                                dateValue > (DateTime)System.Data.SqlTypes.SqlDateTime.MaxValue)
                            {
                                row[column] = DBNull.Value;
                            }
                        }
                    }

                    // Si algún valor es nulo y no es compatible con la base de datos, convertirlo a DBNull
                    if (row[column] == null)
                    {
                        row[column] = DBNull.Value;
                    }
                }
            }
        }

        private static List<MapsReferences> GetCustomersIds(List<MapsReferences> mapsReferences)
        {
            // Crear un diccionario para almacenar el mapeo de ExternalCustomerID con customerID
            var customerIdsDictionary = new Dictionary<string, int>();

            var command = new SqlCommand("SELECT ExternalCustomerID, customerID FROM Customers WITH (NOLOCK)");

            MSSqlDataAccess.ExecuteReader(ConnectionStrings.Default, command, delegate (SqlDataReader sqlDataReader)
            {
                while (sqlDataReader.Read())
                {
                    var externalId = sqlDataReader.GetString(0);
                    var customerId = sqlDataReader.GetInt32(1);
                    customerIdsDictionary[externalId] = customerId;
                }
            });

            // Asignar los customerID correspondientes a cada MapsReferences
            foreach (var reference in mapsReferences)
            {
                if (customerIdsDictionary.TryGetValue(reference.ExternalCustomerID, out int customerId))
                {
                    reference.CustomerID = customerId;
                }
            }

            return mapsReferences;
        }


        private static List<MapsReferences> GetToursIds(List<MapsReferences> mapsReferences)
        {
            // Diccionario para mapear ExternalTourID con TourID
            var tourIdsDictionary = new Dictionary<string, int>();

            // Consulta para obtener los TourID correspondientes a cada ExternalTourID
            var command = new SqlCommand("SELECT ExternalTourID, TourID FROM Tours WITH (NOLOCK)");

            MSSqlDataAccess.ExecuteReader(ConnectionStrings.Default, command, delegate (SqlDataReader sqlDataReader)
            {
                while (sqlDataReader.Read())
                {
                    var externalTourID = sqlDataReader.GetString(0);
                    var tourID = sqlDataReader.GetInt32(1);
                    tourIdsDictionary[externalTourID] = tourID;
                }
            });


            // Asignar los TourID correspondientes a cada MapsReferences
            foreach (var reference in mapsReferences)
            {
                if (tourIdsDictionary.TryGetValue(reference.ExternalTourID, out int tourId))
                {
                    reference.TourID = tourId;
                }
            }

            return mapsReferences;
        }


        private static List<(int customerID, string externalCustomerID)> GetExistingCustomerIds()
        {
            var existingIds = new List<(int customerID, string externalCustomerID)>();
            var command = new SqlCommand("SELECT customerID, externalCustomerID FROM Customers WITH (NOLOCK)");

            MSSqlDataAccess.ExecuteReader(ConnectionStrings.Default, command, delegate (SqlDataReader sqlDataReader)
            {
                while (sqlDataReader.Read())
                {
                    int customerID = sqlDataReader.GetInt32(0);                // Get customerID
                    string externalCustomerID = sqlDataReader.GetString(1);    // Get externalCustomerID

                    // Add both values to the list as a tuple
                    existingIds.Add((customerID, externalCustomerID));
                }
            });

            return existingIds;
        }

        private static List<(int tourID, string externalTourID)> GetExistingToursIds()
        {
            var existingIds = new List<(int tourID, string externalTourID)>();
            var command = new SqlCommand("SELECT tourID, externalTourID FROM Tours WITH (NOLOCK)");

            MSSqlDataAccess.ExecuteReader(ConnectionStrings.Default, command, delegate (SqlDataReader sqlDataReader)
            {
                while (sqlDataReader.Read())
                {
                    int tourID = sqlDataReader.GetInt32(0);                // Obtener tourID
                    string externalTourID = sqlDataReader.GetString(1);    // Obtener externalTourID

                    // Agrega ambos valores a la lista como una tupla
                    existingIds.Add((tourID, externalTourID));
                }
            });
            return existingIds;
        }

        private static List<(int purchaseID, string externalPurchaseID)> GetExistingPurchasesIds()
        {
            var existingIds = new List<(int purchaseID, string externalPurchaseID)>();
            var command = new SqlCommand("SELECT purchaseID, externalPurchaseID FROM Purchases WITH (NOLOCK)");

            MSSqlDataAccess.ExecuteReader(ConnectionStrings.Default, command, delegate (SqlDataReader sqlDataReader)
            {
                while (sqlDataReader.Read())
                {
                    int purchaseID = sqlDataReader.GetInt32(0);                 // Obtener purchaseID
                    string externalPurchaseID = sqlDataReader.GetString(1);     // Obtener externalPurchaseID

                    // Agrega ambos valores a la lista como una tupla
                    existingIds.Add((purchaseID, externalPurchaseID));
                }
            });
            return existingIds;
        }

        private static List<int> GetExistingNotesIds()
        {
            var existingIds = new List<int>();
            var command = new SqlCommand("SELECT noteID FROM Notes WITH (NOLOCK)");

            MSSqlDataAccess.ExecuteReader(ConnectionStrings.Default, command, delegate (SqlDataReader sqlDataReader)
            {
                while (sqlDataReader.Read())
                {
                    existingIds.Add(sqlDataReader.GetInt32(0));  // Obtener noteID y añadir a la lista
                }
            });
            return existingIds;
        }

        #endregion
    }
}
