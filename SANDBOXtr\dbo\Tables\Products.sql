﻿CREATE TABLE [dbo].[Products] (
    [productID]   INT           IDENTITY (1, 1) NOT NULL,
    [productName] NVARCHAR (64) NOT NULL,
    [productCode] NVARCHAR (64) NULL,
    [active]      BIT           NOT NULL,
    CONSTRAINT [PK_Products] PRIMARY KEY CLUSTERED ([productID] ASC),
    CONSTRAINT [UK_Products_productName] UNIQUE NONCLUSTERED ([productName] ASC)
);


GO
CREATE TRIGGER [dbo].[Products.InsertUpdateDelete]
    ON [dbo].[Products]
    FOR INSERT, UPDATE, DELETE
AS 
BEGIN
    SET NOCOUNT ON;

    DECLARE @Activity  NVARCHAR (8), @id INT;

    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'updated'
    END

    IF EXISTS (SELECT * FROM inserted) AND NOT EXISTS(SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'inserted'
    END

    IF EXISTS (SELECT * FROM deleted) AND NOT EXISTS(SELECT * FROM inserted)
    BEGIN
        SET @Activity = 'deleted'
    END

    

    IF (@Activity = 'deleted')
    BEGIN
        DECLARE cur CURSOR FOR SELECT productID FROM deleted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'Products', @id, 'productID',
                (
                    SELECT *
                    FROM deleted i 
                    WHERE i.productID =  @id
                    FOR JSON AUTO
                ),0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
    ELSE
    BEGIN
        DECLARE cur CURSOR
        FOR SELECT productID
        FROM inserted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'Products', @id, 'productID',
                (
                    SELECT *
                    FROM inserted i 
                    WHERE i.productID =  @id
                    FOR JSON AUTO
                ), 0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
END

GO
DISABLE TRIGGER [dbo].[Products.InsertUpdateDelete]
    ON [dbo].[Products];

