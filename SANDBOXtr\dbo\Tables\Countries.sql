﻿CREATE TABLE [dbo].[Countries] (
    [countryID]   INT           IDENTITY (1, 1) NOT NULL,
    [countryName] NVARCHAR (64) NOT NULL,
    [temp]        NCHAR (10)    NULL,
    [active]      BIT           NOT NULL,
    CONSTRAINT [PK_Countries] PRIMARY KEY CLUSTERED ([countryID] ASC),
    CONSTRAINT [UK_Countries_countryName] UNIQUE NONCLUSTERED ([countryName] ASC)
);


GO
CREATE TRIGGER [dbo].[Countries.InsertUpdateDelete]
    ON [dbo].[Countries]
    FOR INSERT, UPDATE, DELETE
AS 
BEGIN
    SET NOCOUNT ON;

    DECLARE @Activity  NVARCHAR (8), @id INT;

    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'updated'
    END

    IF EXISTS (SELECT * FROM inserted) AND NOT EXISTS(SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'inserted'
    END

    IF EXISTS (SELECT * FROM deleted) AND NOT EXISTS(SELECT * FROM inserted)
    BEGIN
        SET @Activity = 'deleted'
    END

    

    IF (@Activity = 'deleted')
    BEGIN
        DECLARE cur CURSOR FOR SELECT countryID FROM deleted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'Countries', @id, 'countryID',
                (
                    SELECT *
                    FROM deleted i 
                    WHERE i.countryID =  @id
                    FOR JSON AUTO
                ),0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
    ELSE
    BEGIN
        DECLARE cur CURSOR
        FOR SELECT countryID
        FROM inserted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'Countries', @id, 'countryID',
                (
                    SELECT *
                    FROM inserted i 
                    WHERE i.countryID =  @id
                    FOR JSON AUTO
                ), 0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
END

GO
DISABLE TRIGGER [dbo].[Countries.InsertUpdateDelete]
    ON [dbo].[Countries];

