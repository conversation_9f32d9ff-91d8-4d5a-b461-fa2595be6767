﻿CREATE TABLE [dbo].[ToursManyPickList2ItemsMap] (
    [toursManyPickList2ItemMapID] INT IDENTITY (1, 1) NOT NULL,
    [tourID]                      INT NOT NULL,
    [toursManyPickList2ItemID]    INT NOT NULL,
    CONSTRAINT [PK_ToursManyPickList2ItemsMap] PRIMARY KEY CLUSTERED ([toursManyPickList2ItemMapID] ASC),
    CONSTRAINT [FK_ToursManyPickList2ItemsMap_tourID] FOREIGN KEY ([tourID]) REFERENCES [dbo].[Tours] ([tourID]),
    CONSTRAINT [FK_ToursManyPickList2ItemsMap_toursManyPickList2ItemID] FOREIGN KEY ([toursManyPickList2ItemID]) REFERENCES [dbo].[ToursManyPickList2Items] ([toursManyPickList2ItemID])
);

