﻿CREATE TABLE [dbo].[CancellationsSupersededPurchases] (
    [cancellationsSupersededPurchaseID] INT IDENTITY (1, 1) NOT NULL,
    [cancellationID]                    INT NOT NULL,
    [supersededPurchaseID]              INT NOT NULL,
    CONSTRAINT [PK_CancellationsSupersededToursSales] PRIMARY KEY CLUSTERED ([cancellationsSupersededPurchaseID] ASC),
    CONSTRAINT [FK_CancellationsSupersededPurchases_Purchases] FOREIGN KEY ([supersededPurchaseID]) REFERENCES [dbo].[Purchases] ([purchaseID]),
    CONSTRAINT [FK_CancellationsSupersededToursSales_Cancellations] FOREIGN KEY ([cancellationID]) REFERENCES [dbo].[Cancellations] ([cancellationID])
);


GO
CREATE TRIGGER [dbo].[CancellationsSupersededPurchases.InsertUpdateDelete]
    ON [dbo].[CancellationsSupersededPurchases]
    FOR INSERT, UPDATE, DELETE
AS 
BEGIN
    SET NOCOUNT ON;

    DECLARE @Activity  NVARCHAR (8), @id INT;

    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'updated'
    END

    IF EXISTS (SELECT * FROM inserted) AND NOT EXISTS(SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'inserted'
    END

    IF EXISTS (SELECT * FROM deleted) AND NOT EXISTS(SELECT * FROM inserted)
    BEGIN
        SET @Activity = 'deleted'
    END

    

    IF (@Activity = 'deleted')
    BEGIN
        DECLARE cur CURSOR FOR SELECT cancellationsSupersededPurchaseID FROM deleted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'CancellationsSupersededPurchases', @id, 'cancellationsSupersededPurchaseID',
                (
                    SELECT *
                    FROM deleted i 
                    WHERE i.cancellationsSupersededPurchaseID =  @id
                    FOR JSON AUTO
                ),0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
    ELSE
    BEGIN
        DECLARE cur CURSOR
        FOR SELECT cancellationsSupersededPurchaseID
        FROM inserted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'CancellationsSupersededPurchases', @id, 'cancellationsSupersededPurchaseID',
                (
                    SELECT *
                    FROM inserted i 
                    WHERE i.cancellationsSupersededPurchaseID =  @id
                    FOR JSON AUTO
                ), 0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
END

GO
DISABLE TRIGGER [dbo].[CancellationsSupersededPurchases.InsertUpdateDelete]
    ON [dbo].[CancellationsSupersededPurchases];

