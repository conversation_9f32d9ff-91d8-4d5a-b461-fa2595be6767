﻿CREATE TABLE [dbo].[CancellationsPickupsAudits] (
    [cancellationsPickupsAuditID]     INT            IDENTITY (1, 1) NOT NULL,
    [cancellationsPickupsAuditTypeID] INT            NOT NULL,
    [cancellationsPickupID]           INT            NOT NULL,
    [userID]                          INT            NULL,
    [information]                     NVARCHAR (256) NULL,
    [insertTimeStamp]                 DATETIME       CONSTRAINT [DF_CancellationsPickupsAudits_insertTimeStamp] DEFAULT ((0)) NOT NULL,
    CONSTRAINT [PK_CancellationsPickupsAudits] PRIMARY KEY CLUSTERED ([cancellationsPickupsAuditID] ASC),
    CONSTRAINT [FK_CancellationsPickupsAudits_cancellationsPickupID] FOREIGN KEY ([cancellationsPickupID]) REFERENCES [dbo].[CancellationsPickups] ([cancellationsPickupID]),
    CONSTRAINT [FK_CancellationsPickupsAudits_cancellationsPickupsAuditTypeID] FOREIGN KEY ([cancellationsPickupsAuditTypeID]) REFERENCES [dbo].[CancellationsPickupsAuditTypes] ([cancellationsPickupsAuditTypeID]),
    CONSTRAINT [FK_CancellationsPickupsAudits_userID] FOREIGN KEY ([userID]) REFERENCES [dbo].[Users] ([userID])
);

