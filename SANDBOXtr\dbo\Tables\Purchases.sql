﻿CREATE TABLE [dbo].[Purchases] (
    [purchaseID]                     INT            IDENTITY (1, 1) NOT NULL,
    [externalPurchaseID]             NVARCHAR (128) NULL,
    [tourID]                         INT            NOT NULL,
    [parentSuperseded<PERSON>romPurchaseID] INT            NULL,
    [supersededFrom<PERSON>urchaseID]       INT            NULL,
    [superseding<PERSON><PERSON>Pender]          BIT            CONSTRAINT [DF_ToursSales_supersedingFromPender] DEFAULT ((0)) NOT NULL,
    [saleDate]                       DATETIME       NULL,
    [saleTime]                       DATETIME       NULL,
    [productCategoryID]              INT            NULL,
    [productID]                      INT            NULL,
    [subProductID]                   INT            NULL,
    [saleAmount]                     DECIMAL (9, 2) NULL,
    [downPaymentAmount]              DECIMAL (9, 2) NULL,
    [fees1Amount]                    DECIMAL (9, 2) NULL,
    [fees2Amount]                    DECIMAL (9, 2) NULL,
    [fees3Amount]                    DECIMAL (9, 2) NULL,
    [fees4Amount]                    DECIMAL (9, 2) NULL,
    [fees5Amount]                    DECIMAL (9, 2) NULL,
    [saleTypeID]                     INT            NULL,
    [saleStatusTypeID]               INT            NULL,
    [saleDispositionID]              INT            NULL,
    [purchasesPickList1ItemID]       INT            NULL,
    [purchasesPickList2ItemID]       INT            NULL,
    [purchasesPickList3ItemID]       INT            NULL,
    [purchasesText1]                 NVARCHAR (512) NULL,
    [purchasesText2]                 NVARCHAR (512) NULL,
    [purchasesBool1]                 BIT            NULL,
    [purchasesBool2]                 BIT            NULL,
    [purchasesDate1]                 DATETIME       NULL,
    [purchasesTime1]                 DATETIME       NULL,
    [purchasesDate2]                 DATETIME       NULL,
    [purchasesTime2]                 DATETIME       NULL,
    [apiConnectionID]                INT            NULL,
    [apiExternalPurchaseID]          NVARCHAR (128) NULL,
    [apiExternalConnectionID]        NVARCHAR (128) NULL,
    [insertTimeStamp]                DATETIME       CONSTRAINT [DF_ToursSales_insertTimeStamp] DEFAULT (getdate()) NOT NULL,
    [updateTimeStamp]                DATETIME       CONSTRAINT [DF_ToursSales_updateTimeStamp] DEFAULT (getdate()) NOT NULL,
    CONSTRAINT [PK_Purchases] PRIMARY KEY CLUSTERED ([purchaseID] ASC),
    CONSTRAINT [FK_Purchases_apiConnectionID] FOREIGN KEY ([apiConnectionID]) REFERENCES [dbo].[ApiConnections] ([apiConnectionID]),
    CONSTRAINT [FK_Purchases_parentSupersededFromPurchaseID] FOREIGN KEY ([parentSupersededFromPurchaseID]) REFERENCES [dbo].[Purchases] ([purchaseID]),
    CONSTRAINT [FK_Purchases_Product] FOREIGN KEY ([productID]) REFERENCES [dbo].[Products] ([productID]),
    CONSTRAINT [FK_Purchases_productCategoryID] FOREIGN KEY ([productCategoryID]) REFERENCES [dbo].[ProductCategories] ([productCategoryID]),
    CONSTRAINT [FK_Purchases_purchasesPickList1ItemID] FOREIGN KEY ([purchasesPickList1ItemID]) REFERENCES [dbo].[PurchasesPickList1Items] ([purchasesPickList1ItemID]),
    CONSTRAINT [FK_Purchases_purchasesPickList2ItemID] FOREIGN KEY ([purchasesPickList2ItemID]) REFERENCES [dbo].[PurchasesPickList2Items] ([purchasesPickList2ItemID]),
    CONSTRAINT [FK_Purchases_purchasesPickList3ItemID] FOREIGN KEY ([purchasesPickList3ItemID]) REFERENCES [dbo].[PurchasesPickList3Items] ([purchasesPickList3ItemID]),
    CONSTRAINT [FK_Purchases_saleDispositionID] FOREIGN KEY ([saleDispositionID]) REFERENCES [dbo].[SaleDispositions] ([saleDispositionID]),
    CONSTRAINT [FK_Purchases_SaleStatusType] FOREIGN KEY ([saleStatusTypeID]) REFERENCES [dbo].[SaleStatusTypes] ([saleStatusTypeID]),
    CONSTRAINT [FK_Purchases_SaleType] FOREIGN KEY ([saleTypeID]) REFERENCES [dbo].[SaleTypes] ([saleTypeID]),
    CONSTRAINT [FK_Purchases_subProductID] FOREIGN KEY ([subProductID]) REFERENCES [dbo].[SubProducts] ([subProductID]),
    CONSTRAINT [FK_Purchases_supersededFromPurchaseID] FOREIGN KEY ([supersededFromPurchaseID]) REFERENCES [dbo].[Purchases] ([purchaseID]),
    CONSTRAINT [FK_Purchases_Tours] FOREIGN KEY ([tourID]) REFERENCES [dbo].[Tours] ([tourID])
);


GO
CREATE NONCLUSTERED INDEX [IX_Purchases_apiConnectionID]
    ON [dbo].[Purchases]([apiConnectionID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_Purchases_apiExternalTourID]
    ON [dbo].[Purchases]([apiExternalPurchaseID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_Purchases_saledate]
    ON [dbo].[Purchases]([saleDate] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_Purchases_tourID]
    ON [dbo].[Purchases]([tourID] ASC);


GO
CREATE TRIGGER [dbo].[Purchases.InsertUpdateDelete]
    ON [dbo].[Purchases]
    FOR INSERT, UPDATE, DELETE
AS 
BEGIN
    SET NOCOUNT ON;

    DECLARE @Activity  NVARCHAR (8), @id INT;

    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'updated'
    END

    IF EXISTS (SELECT * FROM inserted) AND NOT EXISTS(SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'inserted'
    END

    IF EXISTS (SELECT * FROM deleted) AND NOT EXISTS(SELECT * FROM inserted)
    BEGIN
        SET @Activity = 'deleted'
    END

    

    IF (@Activity = 'deleted')
    BEGIN
        DECLARE cur CURSOR FOR SELECT purchaseID FROM deleted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'Purchases', @id, 'purchaseID',
                (
                    SELECT *
                    FROM deleted i 
                    WHERE i.purchaseID =  @id
                    FOR JSON AUTO
                ),0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
    ELSE
    BEGIN
        DECLARE cur CURSOR
        FOR SELECT purchaseID
        FROM inserted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'Purchases', @id, 'purchaseID',
                (
                    SELECT *
                    FROM inserted i 
                    WHERE i.purchaseID =  @id
                    FOR JSON AUTO
                ), 0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
END

GO
DISABLE TRIGGER [dbo].[Purchases.InsertUpdateDelete]
    ON [dbo].[Purchases];

