var lodash = window._, options = [], typeReports = [], kpi_get_report = []; $(function () { $("#accordion").accordion({ active: !1, collapsible: !0 }), $(".header-container").click(function () { $(this).next(".inputs-container1").slideToggle(), $(this).css("background-color", "#f5f5f5") }) }), $(document).ready(async function () { var t = $.ui.accordion.prototype._keydown; $.ui.accordion.prototype._keydown = function (e) { return !!$(e.target).is("input") || 32 !== e.which && t.apply(this, arguments) }, $("#accordion").accordion({ heightStyle: "content", activate: function (t, e) { var o = 0 !== e.newHeader.length ? e.newHeader : e.oldHeader, n = o.data("column-name"), i = o.find("input.operation"), a = o.find("input.condition"); i.val(""), a.val(""); var r = o.next(); r.find(".my-widget .setContainer").each(function () { var t = $(this), e = t.find(`input[id^="listContainerInput${n}"]`).val(), o = t.find(`select[id^="mathOperators${n}"]`).val(); if (e && "" !== e.trim()) { var a = i.val(); i.val(a + e + " "), o && "" !== o.trim() && i.val(a + e + " " + o + " ") } }), r.find(".my-widget .setContainer").each(function () { var t = $(this), e = t.find('input[id^="firstConditionContainer"]').first().val(), o = t.find('select[id^="operators"]').val(), n = t.find('input[id^="secondConditionContainer"]').first().val(), i = t.find('select[id^="logicalOperators"]').val(); if (e && o && n) { var r = a.val(); a.val(r + e + " " + o + " " + n + " " + i + " ") } else { var l = t.find('input[id^="ffirstConditionContainer"]').first().val(), s = t.find('select[id^="operators"]').val(), d = t.find('input[id^="ssecondConditionContainer"]').first().val(), c = t.find('input[id^="ifresultConditionContainer"]').first().val(), u = t.find('input[id^="elseresultConditionContainer"]').first().val(), p = t.find('input[id^="fourConditionContainer"]').first().val(); if (l && o && d && c && (u || p)) { var r = a.val(); a.val(r + "IF " + l + " " + s + " " + d + " THEN " + c + " ELSE THEN " + u + " " + p) } } }) } }), $(".ag-tool-panel-wrapper").attr("aria-hidden", "true").addClass("ag-hidden") }), document.addEventListener("DOMContentLoaded", loadUserDataAndPopulateSelect); var checkboxSelection = function (t) { return 0 === t.api.getRowGroupColumns().length }, headerCheckboxSelection = function (t) { return 0 === t.api.getRowGroupColumns().length }; let gridApi; const gridOptions = { defaultColDef: { flex: 1, enableValue: !0, enableRowGroup: !0, enablePivot: !0, resizable: !0, sortable: !0, filter: !1, cellRenderer: "agGroupCellRenderer", suppressMenu: !0 }, rowData: [], domLayout: "autoHeight", columnDefs: [], groupDefaultExpanded: -1, autoGroupColumnDef: { headerName: "Group", cellRendererParams: { checkbox: !1, footerValueGetter(t) { let e = -1 === t.node.level; return e ? "Summary" : `Sub Total(${t.value})` } } }, groupIncludeTotalFooter: !1, groupUseEntireRow: !0, sideBar: !0, pivotMode: !0, suppressAggFuncInHeader: !0, onGridReady: function (t) { gridApi = t.api, t.api.sizeColumnsToFit() }, onRowClicked: function (t) { t.node.setExpanded(!t.node.expanded) }, getRowClass: function (t) { if (t.node.rowPinned) return "bold-row" }, pagination: !0, paginationPageSize: 50, paginationPageSizeSelector: [50, 100, 200, 500, 1e3], onFirstDataRendered: onFirstDataRendered, paginateChildRows: !0, paginationNumberFormatter: t => "[" + t.value.toLocaleString() + "]", aggFuncs: { derived: function (t) { return t.reduce((t, e) => t + e, 0) } } }; function generateColumnDefs(operations) { let columnDefs = []; return operations.forEach(function (op) { let newColumnDef = { headerName: op.columnName, field: op.columnName, valueGetter: function (params) { return (!op.condition || eval(op.condition)) && op.operation ? eval(op.operation) : null }, aggFunc: op.aggFunc }; columnDefs.push(newColumnDef) }), columnDefs } function generateColumnDefsFromData(t) { if (!t || 0 === t.length) return []; let e = t[0], o = []; for (let n in e) e.hasOwnProperty(n) && o.push({ headerName: n, field: n }); return o } document.addEventListener("DOMContentLoaded", function () { let t = document.querySelector("#myGrid"); new agGrid.Grid(t, gridOptions); let e = ["compact"], o = document.querySelector('[class*="ag-theme-alpine"]'); e.forEach(t => o.classList.toggle(t, "compact" === t)) }), gridOptions.aggFuncs = { derived: function (t) { return t.reduce((t, e) => t + e, 0) } }; let accordionCount = 1; function populateData(t) { let e = document.getElementById(t); e && kpi_get_report.forEach(t => { let o = document.createElement("option"); o.textContent = t.pivot, o.value = t.original, e.appendChild(o) }) } let joinCounter = 1; function addJoin() {
    joinCounter++; let t = document.createElement("div"); t.className = "join-inputs-container", t.id = `joinContainer${joinCounter}`, t.setAttribute("data-id", "0"); let e = `<div style="margin-left: 2px;" class="col-xs-2">&nbsp;</div><div class="col-xs-10"> <div class="row"><div class="col-xs-3">
                            <select class="joinSelect form-control" id="joinSelect${joinCounter}" placeholder="KPI Type" data-id="0">
                                <option value="left">LEFT JOIN</option>
                                <option value="right">RIGHT JOIN</option>
                                <option value="inner">INNER JOIN</option>
                            </select>
                      </div>`, o = `<div class="col-xs-3">
                            <input type="text" class="form-control firstJoinKey" id="firstJoinKey_${joinCounter}" placeholder="Enter key1 (e.g. orders.customerId)" />
                      </div>`, n = `<div class="col-xs-3">
                            <input type="text" class="form-control secondJoinKey" id="secondJoinKey_${joinCounter}" placeholder="Enter key2 (e.g. customers.id)" />
                      </div>`, i = `<div class="col-xs-3 buttons-container">
                            <input type="text" class="form-control uniqueKey" id="uniqueKey_${joinCounter}" placeholder="Unique key (e.g. appointmentId)" />
                            <button  class="btn btn-primary" onclick="removeJoin(this); return false;">X</button>
                            </div>
                            </div>`; t.innerHTML = `${e} ${o} ${n} ${i}`; document.querySelector(".right-container").appendChild(t)
} function removeJoin(t) { let e = t.parentElement.parentElement.parentElement.parentElement, o = document.querySelectorAll(".join-inputs-container"); e.getAttribute("data-id") > 0 && UnAssignJoinReport(e.getAttribute("data-id")), e === o[0] ? (e.querySelectorAll("input").forEach(t => t.value = ""), e.querySelectorAll("select").forEach(t => t.selectedIndex = 0)) : e.remove() } async function saveJoins(t) { try { let e = await fetch('<%= ConfigurationManager.AppSettings["ApiBaseUrl"] %>/saveJoins', { method: "POST", headers: { "Content-Type": "application/json" }, body: JSON.stringify(t) }); if (!e.ok) throw Error("Error saving JoinsEntityAttributeValue"); let o = await e.json(); return o.EntityAttributeValueId } catch (n) { throw console.error("Error saving JoinsEntityAttributeValue:", n), n } } async function saveJoinDefinition(t) { try { let e = await fetch('<%= ConfigurationManager.AppSettings["ApiBaseUrl"] %>/saveJoinDefinition', { method: "POST", headers: { "Content-Type": "application/json" }, body: JSON.stringify(t) }); if (!e.ok) throw Error("Error saving JoinDefinition"); let o = await e.json(); return o } catch (n) { throw console.error("Error saving JoinDefinition:", n), n } } function populateUserSelect(t) { let e = document.getElementById("userListMultiSelect"); e.innerHTML = "", t.forEach(t => { let o = document.createElement("input"); o.type = "checkbox", o.value = t.UserId, o.id = `user_${t.UserId}`, o.classList.add("user-checkbox"), o.addEventListener("change", function () { if (!this.checked) { let t = this.value; UnAssignSelectedUserId(t) } }); let n = document.createElement("label"); n.setAttribute("for", `user_${t.UserId}`), n.textContent = t.UserName; let i = document.createElement("div"); i.classList.add("checkbox-item"), i.appendChild(o), i.appendChild(n), e.appendChild(i) }) } function populateReportNamesDropdown(t) { let e = document.getElementById("reportNamesDropdown"); e.innerHTML = '<option value="">Select a Report</option>', t.forEach(t => { let o = document.createElement("option"); o.value = t.ReportId, o.text = t.ReportName, e.appendChild(o) }) } function refreshReportNames() { try { fetchReportNames() } catch (t) { console.error("Error refreshing report names:", t) } } let reportSelectedFromDropdown = !1, selectedReportId = -1, joinDefinitions = [], kpiData = [], reportDetail = {}; async function loadUserDataAndPopulateSelect() { try { let t = await fetchUserDataFromApi(); populateUserSelect(t) } catch (e) { console.log("Error loading user data:", e) } } async function getReportsUsersMapByUserId(t) { try { let e = request_path + `getReportsUsersMapByUserId/${t}`, o = await fetch(e); if (!o.ok) throw Error(`Failed to fetch columns for UserId ${t}`); let n = await o.json(); return n } catch (i) { console.log(`Error fetching columns for UserId ${t}:`, i) } } async function fetchUserDataByUserName(t) { try { let e = request_path + `getUserData/${t}`, o = await fetch(e); if (!o.ok) throw Error(`Failed to fetch user data for ${t}`); let n = await o.json(); return n } catch (i) { console.error(`Error fetching user data for ${t}:`, i) } } async function fetchUserDataFromApi() { try { let t = request_path + "getUsers", e = await fetch(t); if (!e.ok) throw Error("Failed to fetch user data"); let o = await e.json(); return o } catch (n) { throw console.error("Error fetching user data:", n), n } } async function fetchReportNames(t) { try { let e = request_path + `getReportInfo/${t}`, o = await fetch(e); if (!o.ok) throw Error(`Failed to fetch report names for ReportId ${t}`); let n = await o.json(); populateReportNamesDropdown(n) } catch (i) { console.error(`Error fetching report names for ReportId ${t}:`, i) } } function saveInputState(t, e) { let o = {}; o.columnName = e.trim(), o.aggFuncValue = document.getElementById("aggFunc").value.trim(), o.operationValues = [], o.conditionValues = []; let n = document.getElementById(`listContainerInput${e}${t}1`); n && (o.listContainerValue = n.value.trim()); let i = document.getElementById(`mathOperators${e}${t}1`); i && (o.mathOperatorsValue = i.value.trim()); let a = document.getElementById(`logicalOperators${e}`); return a && (o.logicalOperatorsValue = a.value.trim()), o } function restoreInputState(t) { document.querySelectorAll(`#operation${accordionCount}`).forEach((e, o) => { void 0 !== t.operationValues[o] ? e.value = t.operationValues[o] : e.value = "" }); document.querySelectorAll(`#condition${accordionCount}`).forEach((e, o) => { void 0 !== t.conditionValues[o] ? e.value = t.conditionValues[o] : e.value = "" }) } function addAccordion(t) { let e, o; if (null == t) { if (e = document.getElementById("columnName").value, o = document.getElementById("aggFunc").value, "" === (e = sanitizeColumnName(e)).trim()) return; addChildAccordion(e, o, 0, !0), accordionCount++ } else for (var n = 0; n < t.length; n++) { var i = kpiData[n]; addChildAccordion(i.name, i.type, i.kpiId, i.Visible), accordionCount++ } } function sanitizeColumnName(t) { return t.replace(/[$]/g, "_d").replace(/[%]/g, "_p") } function desanitizeColumnName(t) { return t.replace(/_d/g, "$").replace(/_p/g, "%") } async function toggleVisibility(t, e, o) { try { let n = !t, i = document.getElementById(`visibilityIcon${e}`); n ? (i.classList.remove("fa-eye-slash"), i.classList.add("fa-eye")) : (i.classList.remove("fa-eye"), i.classList.add("fa-eye-slash")); let a = `${request_path}saveKpiVisibility/${e}/${n}`, r = await fetch(a), l = document.getElementById(`toggleVisibilityButton${e}`), s = `toggleVisibility(${!t}, ${e}, '${o}')`; if (l.setAttribute("onclick", s), !r.ok) throw Error("Failed to save Kpi Visibility"); await r.json() } catch (d) { throw console.error("Error in saving Kpi Visibility:", d), d } } function updateTooltip(t, e) { let o = document.getElementById(`toggleVisibilityButton${t}`); o.title = e ? "Hide column" : "Show column" } function clearTooltip(t) { let e = document.getElementById(`toggleVisibilityButton${t}`); e.title = "" } function updateRemoveTooltip(t) { t.title = "Remove" } function clearRemoveTooltip(t) { t.title = "" } function addChildAccordion(t, e, o, n) {
    var i = window.location.href.toLowerCase().includes("adhoc.aspx") ? "hidden" : ""; "none" == e && (e = "String"); let a = t, r = e; document.getElementById("columnName").value = a, document.getElementById("aggFunc").value = r; let l = ""; l = "ifelse" != e && "ifelseo" != e ? `
                 <div class="header-container" id="accordian${accordionCount}" data-column-name="${t}" data-id="${o}">
                   <h4>${desanitizeColumnName(t)} - ${e}</h4>
                      <div class="inputs-container">
                          <input type="text" class="form-control operation" id="operation${accordionCount}" placeholder="Enter operation (e.g. data.salesPrice + data.negotiationPrice)" />
                          <input type="text" class="form-control condition" id="condition${accordionCount}" placeholder="Enter condition (e.g. data.salesStatus == 'sold')" />
                      </div>
                      <div class="buttons-container">
                          <button class="btn btn-primary" onclick="addSet('operationsContainerLabel${t}', '${t}', 0); return false;">Add Operation</button>
                          <button  class="btn btn-primary" onclick="addCondition('conditionContainerLabel${t}', '${t}', 0); return false;">Add Condition</button>
                         <button class="btn btn-primary ${i}" onclick="removeAccordion(this); return false;"onmouseover="updateRemoveTooltip(this);" onmouseout="clearRemoveTooltip(this);">X </button>
                        <button  class="btn btn-primary visible_row" id="toggleVisibilityButton${o}" onclick="toggleVisibility(${n},${o},'${t}');"onmouseover="updateTooltip(${o}, ${n});" onmouseout="clearTooltip(${o});" ><i id="visibilityIcon${o}" class="fa ${n ? "fa-eye" : "fa-eye-slash"}"></i></button>      
                    </div> 
                </div>

              <div class="flex-container">
                  <fieldset>
                  <legend>Operations</legend>
                      <div id="operationsContainerLabel${t}">
                          <div class="operationRow">
                              <div class="my-widget">
                                  <div class="setContainer"  id="set${t}1">
                                      <div class="itemContainer">
                                          <div class ="ui-widget operationValue">
                                              <input id="listContainerInput${t}1" list="listContainer${t}1" placeholder="Available Properties" class="form-control1" style="margin-right:-1px"/>
                                              <datalist id="listContainer${t}1"></datalist>
                                              <select id="mathOperators${t}1" class="mathOperators form-control1 "  style="margin-right:0px">
                                                  <option value="">Select a Math Operator</option>
                                                  <option value="+">Addition (+)</option>
                                                  <option value="-">Subtraction (-)</option>
                                                  <option value="*">Multiplication (*)</option>
                                                  <option value="/">Division (/)</option>
                                                  <option value="%">Modulus (%)</option>
                                              </select>
                                              <button class="btn btn-primary  ${i}" onclick="removeWidget(this); return false;">X</button>
                                          </div>
                                      </div>
                                  </div>
                      
                              </div>
                          </div>
                      </div>
                  </fieldset>
                  <fieldset>
                    <legend>Format</legend>
                        <div id="formatContainerLabel${t}">
                            <div class="operationRow">
                                <div class="my-widget">
                                    <div class="setContainer"  id="formatSet${t}1">
                                      <div class="itemContainer">
                                          <div class ="ui-widget typeFormat">
                                              <select id="mathFormat${t}1" class="mathFormat form-control1 "  style="margin-right:0px; width:100% !important">
                                                  <option value="none">--</option>
                                                  <option value="percentage">%</option>
                                                  <option value="currency">$</option>
                                              </select>
                                          </div>
                                      </div>
                                  </div>
                                </div>
                            </div>
                        </div>
                  </fieldset>
                  <fieldset>
                  <legend>Conditions</legend>
                      <div id="conditionContainerLabel${t}">
                          <div class="operationRow">
                              <div class="my-widget">
                                  <div class="setContainer" id="conditionSet${t}1">
                                      <div class="itemContainer">
                                          <div class ="ui-widget conditionValue">
                                              <input id="firstConditionContainerInput${t}1" list="firstConditionContainer${t}1" placeholder="Available Properties" class="form-control2"  style="margin-right:-4px"/>
                                              <datalist id="firstConditionContainer${t}1"></datalist>
                                              <select id="operators${t}1" class="mathOperators form-control2 cOperators"  style="margin-right:-4px">
                                                  <option value="">Select an Operator</option>
                                                  <option value="==">== Equal To</option>
                                                  <option value="!=">!= Not Equal</option>
                                                  <option value=">">> Bigger Than</option>
                                                  <option value=">=">>= Bigger or Equal To</option>
                                                  <option value="<">< Smaller Than</option>
                                                  <option value="<="><= Smaller or Equal To</option>
                                              </select>
                                              <input id="secondConditionContainerInput${t}1" list="secondConditionContainer${t}1" placeholder="Available Properties" class="form-control2"  style="margin-right:-4px"/>
                                              <datalist id="secondConditionContainer${t}1"></datalist>
                                              <select id="logicalOperators${t}1" class="logicalOperators form-control2 lOperators">
                                                  <option value="">Select a Logical Operator</option>s
                                                  <option value="&&">AND (&&)</option>
                                                  <option value="||">OR (||)</option>
                                              </select>
                                              <button class="btn btn-primary  ${i}" onclick="removeWidget(this); return false;"  style="margin-left:-4px">X</button>
                                          </div>
                                      </div>
                                  </div>
                              </div>
                          </div>
                      </div>
                  </fieldset>
              </div>
    `: `
                 <div class="header-container" id="accordian${accordionCount}" data-column-name="${t}" data-id="${o}">
                   <h4>${desanitizeColumnName(t)} - ${e}</h4>
                      <div class="inputs-container">
                          <input type="text" class="form-control condition" id="condition${accordionCount}" placeholder="Enter condition (e.g. if data.salesStatus == 'sold' then 'others')" />
                      </div>
                      <div class="buttons-container">
                         <button class="btn btn-primary ${i}" onclick="removeAccordion(this); return false;"onmouseover="updateRemoveTooltip(this);" onmouseout="clearRemoveTooltip(this);">X </button>
                        <button  class="btn btn-primary visible_row" id="toggleVisibilityButton${o}" onclick="toggleVisibility(${n},${o},'${t}');"onmouseover="updateTooltip(${o}, ${n});" onmouseout="clearTooltip(${o});" ><i id="visibilityIcon${o}" class="fa ${n ? "fa-eye" : "fa-eye-slash"}"></i></button>      
                    </div> 
                </div>

                <div class="flex-container">
                  <fieldset>
                  <legend>Conditions</legend>
                     <div id="operationsContainerLabel${t}">
                        <div class="operationRow">
                              <div class="my-widget">
                                  <div class="setContainer"  id="set${t}1">
                                      <div class="itemContainer">
                                          <div class ="col-xs-12">
                                             <div class="row">
                                                <div class="col-xs-4">
                                                    <label for="inputField" class="col-xs-12 control-label" >IF</label>
                                                    <input id="ffirstConditionContainerInput${t}1" list="listContainer${t}1" placeholder="Available Properties" class="col-xs-12 form-control" />
                                                    <datalist id="listContainer${t}1"></datalist>
                                                    <select id="operators${t}1" class="mathOperators form-control cOperators" class="col-xs-12 form-control" >
                                                        <option value="">Select an Operator</option>
                                                        <option value="==">== Equal To</option>
                                                        <option value="!=">!= Not Equal</option>
                                                        <option value=">">> Bigger Than</option>
                                                        <option value=">=">>= Bigger or Equal To</option>
                                                        <option value="<">< Smaller Than</option>
                                                        <option value="<="><= Smaller or Equal To</option>
                                                    </select>
                                                    <input id="ssecondConditionContainerInput${t}1" list="listContainer${t}1" placeholder="Available Properties" class="col-xs-12 form-control" />
                                                    <datalist id="listContainer${t}1"></datalist>
                                                </div>
                                                <div class="col-xs-4">
                                                    <label for="inputField" class="col-xs-12 control-label" >THEN</label>
                                                    <input id="ifresultConditionContainerInput${t}1" list="listContainer${t}1" placeholder="Result" class="col-xs-12 form-control" />
                                                </div>

                                                <div class="col-xs-4">
                                                     <label for="inputField" class="col-xs-12 control-label" >ELSE THEN</label>
                                                    <input id="elseresultConditionContainerInput${t}1" list="listContainer${t}1" placeholder="Result" class="col-xs-12 form-control" />
                                                    <input id="fourConditionContainerInput${t}1" list="listContainer${t}1" placeholder="Available Properties" class="col-xs-12 form-control" />
                                                    <datalist id="listContainer${t}1"></datalist>
                                                </div>
                                             </div>
                                          </div>
                                      </div>
                                  </div>
                              </div>
                        </div>
                     </div>
                  </fieldset>
                  <fieldset>
                  <legend>Format</legend>
                      <div id="formatContainerLabel${t}">
                          <div class="operationRow">
                              <div class="my-widget">
                                  <div class="setContainer"  id="formatSet${t}1">
                                    <div class="itemContainer">
                                        <div class ="ui-widget typeFormat">
                                            <select id="mathFormat${t}1" class="mathFormat form-control "  style="margin-right:0px; width:100% !important">
                                                <option value="none">--</option>
                                                <option value="percentage">%</option>
                                                <option value="currency">$</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                              </div>
                          </div>
                      </div>
                </fieldset>
                
              </div>
            `, document.getElementById("accordion").insertAdjacentHTML("beforeend", l), $("#accordion").accordion("option", "active", accordionCount - 1), $("#accordion").accordion("refresh"), document.getElementById("columnName").value = desanitizeColumnName(a), document.getElementById("aggFunc").value = r, setTimeout(() => { populateData(`listContainer${t}1`), populateData(`firstConditionContainer${t}1`), populateData(`secondConditionContainer${t}1`) }, 0)
} function removeAccordion(t) { let e = $(t).closest(".header-container"), o = e.next(".flex-container"); e.remove(), o.remove(), $("#accordion").accordion("refresh"), e.data("id") > 0 && UnAssignReportKpiId(e.data("id")) } $("#accordion").accordion({ beforeActivate: function (t, e) { } }); let counter = 1; function addSet(t, e, o) { var n = window.location.href.toLowerCase().includes("adhoc.aspx") ? "hidden" : ""; 0 == o ? counter++ : counter = o; var i = $(`#${t} .operationRow .my-widget`); let a = $("<div>").attr("id", `set${e}${counter}`).addClass("setContainer"), r = $("<div>").addClass("itemContainer").appendTo(a), l = $("<div>").addClass("ui-widget").appendTo(r); $('<input class="form-control1">').attr("id", `listContainerInput${e}${counter}`).attr("list", `listContainer${e}${counter}`).attr("placeholder", "Available Properties").appendTo(l), $("<datalist>").attr("id", `listContainer${e}${counter}`).appendTo(l), $('<select class="form-control1">').attr("id", `mathOperators${e}${counter}`).addClass("mathOperators").append('<option value="">Select a Math Operator</option>').append('<option value="+">Addition (+)</option>').append('<option value="-">Subtraction (-)</option>').append('<option value="*">Multiplication (*)</option>').append('<option value="/">Division (/)</option>').append('<option value="%">Modulus (%)</option>').appendTo(l), $("<button>").addClass("btn btn-primary " + n).text("X").attr("onclick", "removeWidget(this); return false;").appendTo(l), a.appendTo(i), populateData(`listContainer${e}${counter}`), $("#accordion").accordion("option", "active", accordionCount - 1), $("#accordion").accordion("refresh") } let conditionCounter = 1; function addCondition(t, e, o) { var n = window.location.href.toLowerCase().includes("adhoc.aspx") ? "hidden" : ""; 0 == o ? conditionCounter++ : conditionCounter = o; var i = $(`#${t} .operationRow .my-widget`); let a = $("<div>").attr("id", `conditionSet${e}${conditionCounter}`).addClass("setContainer"), r = $("<div>").addClass("itemContainer").appendTo(a), l = $("<div>").addClass("ui-widget").appendTo(r); $('<input class="form-control2">').attr("id", `firstConditionContainerInput${e}${conditionCounter}`).attr("list", `firstConditionContainer${e}${conditionCounter}`).attr("placeholder", "Available Properties").appendTo(l), $("<datalist>").attr("id", `firstConditionContainer${e}${conditionCounter}`).appendTo(l), $('<select class="form-control2">').attr("id", `operators${e}${conditionCounter}`).addClass("mathOperators").append('<option value="">Select an Operator</option>').append('<option value="==">== Equal To</option>').append('<option value="!=">!= Not Equal</option>').append('<option value=">">> Bigger Than</option>').append('<option value=">=">>= Bigger or Equal To</option>').append('<option value="<">< Smaller Than</option>').append('<option value="<="><= Smaller or Equal To</option>').appendTo(l), $('<input class="form-control2">').attr("id", `secondConditionContainerInput${e}${conditionCounter}`).attr("list", `secondConditionContainer${e}${conditionCounter}`).attr("placeholder", "Available Properties").appendTo(l), $("<datalist>").attr("id", `secondConditionContainer${e}${conditionCounter}`).appendTo(l), $('<select class="form-control2">').attr("id", `logicalOperators${e}${conditionCounter}`).addClass("logicalOperators").append('<option value="">Select a Logical Operator</option>').append('<option value="&&">AND (&&)</option>').append('<option value="||">OR (||)</option>').appendTo(l), $("<button>").addClass("btn btn-primary " + n).attr("onclick", "removeWidget(this); return false;").text("X").appendTo(l), i.append(a), populateData(`firstConditionContainer${e}${conditionCounter}`), populateData(`secondConditionContainer${e}${conditionCounter}`), $("#accordion").accordion("option", "active", accordionCount - 1), $("#accordion").accordion("refresh") } function removeWidget(t) { $(t).closest(".ui-widget").remove() } function appendOperation(t) { $(`#operationsContainerLabel${t} .setContainer`).each(function () { var t = $(this), e = t.attr("id").replace("set", ""); t.find(".itemContainer").each(function () { var t = $(this), o = t.find(`input[list="operationsContainer${e}"], input[list="listContainer${e}"]`).val(), n = t.find(`#mathOperators${e}`).val(); if (o && n) { var i = $("#operation1").val(); $("#operation1").val(i + o + " " + n + " ") } }) }) } function getDataSet(t) { return window[t] } function aggregateDataUsingCustomFuncs(t, e, o) { let n = getCustomAggFuncs(o, t, e), i = groupDataByKey(t, e), a = []; for (let [r, l] of Object.entries(i)) { let s = { [e]: r }; for (let [d, c] of Object.entries(n)) { let u = l.map(t => t[d]); s[d] = c(u) } a.push(s) } return a } function groupDataByKey(t, e) { return t.reduce(function (t, o) { return (t[o[e]] = t[o[e]] || []).push(o), t }, {}) } function evalOperation(t, e) { let o = t.split(" ")[0]; switch (o) { case "sum": case "count": case "derived": return evalDerivedOperation(t.replace(o + " ", ""), e); default: let n = "let result = " + t.replace(/\b[a-zA-Z0-9_]+\.\w+\b|\b\w+\b/g, t => { if (t.includes(".")) { let e = t.split(".")[1]; return `(Number(row["${e.charAt(0).toLowerCase() + e.slice(1)}"] || 0))` } return isNaN(t) ? `(Number(row["${t.charAt(0).toLowerCase() + t.slice(1)}"] || 0))` : t }) + ";return isNaN(result) || !isFinite(result) ? 0 : result;"; try { return Function("row", n)(e) } catch (i) { return console.error("Error evaluating operation:", i), -1 } } } function innerJoin(t, e, o, n) { let i = []; return t.forEach(t => { e.forEach(e => { t[o] === e[n] && i.push({ ...t, ...e }) }) }), i } function filterData(t, e, o) { return t.filter(t => evalCondition(e, t, o)) } function getDataFromPath(t, e) { let o = e.split("."), n = t; for (let i = 0; i < o.length; i++) { if ("join" === o[i]) { let a = o[i + 1], r = o[i + 2], l = []; n.forEach(e => { t[r].forEach(t => { e[a] === t[a] && l.push({ ...e, ...t }) }) }), n = l, i += 2 } else n = n[o[i]]; if (!n) return } return n } function joinDataSets(t, e, o) { let n = []; return t.forEach(t => { let i = e.find(e => e[o] === t[o]); i && n.push(Object.assign({}, t, i)) }), n } function extractDataPaths(t) { let e = /\b\w+(\.\w+)+\b/g, o = t.match(e) || []; return [...new Set(o)] } let selectedValues = {}; function createListItem(t, e, o) { var n = document.createElement("option"); n.value = e + "." + o, n.textContent = e + "." + o, t.appendChild(n) } function addColumn() { for (var t = document.getElementsByClassName("condition"), e = [], o = document.getElementById("newColumnName").value, n = 0; n < t.length; n++) { var i = t[n].getElementsByClassName("column")[0].value, a = decodeURIComponent(t[n].getElementsByClassName("operator")[0].value), r = t[n].getElementsByClassName("value")[0].value; i && a && r && e.push("data." + i + " " + a + " " + r) } var l = Function("data", "return " + e.join(" && ") + " ? params.data." + i + " : null;"), s = { headerName: o, field: o, valueGetter: l }, d = gridOptions.columnDefs; d.push(s), gridApi.setColumnDefs(d) } function addColumnFormula() { var t = document.getElementById("ddlColumns"), e = { headerName: t.value, field: t.value }, o = gridOptions.columnDefs; o.push(e), gridApi.setColumnDefs(o), t.value = "" } function filterTrueOnly() { var t = {}, e = gridOptions.columnDefs; for (let o = 0; o < e.length; o++)e[o].valueGetter && (t[e[o].field + "_getter"] = { type: "equals", filter: !0 }); gridApi.setFilterModel(t) } function createKpi() { var t = document.getElementById("complexFormulaName"), e = document.getElementById("formulaInput"), o = Function("params", "return " + e.value + ";"), n = { headerName: t.value, valueGetter: o }, i = gridOptions.columnDefs; i.push(n), gridApi.setColumnDefs(i), t.value = "", e.value = "" } function addDetailColumn() { let t = gridApi.getDisplayedRowAtIndex(0); if (t.gridOptionsWrapper) { let e = t.gridOptionsWrapper.gridOptions; e && e.columnApi.setColumnDefs(newColumnDefs) } } function updateHeaderName() { var t = document.getElementById("columnFieldInput").value, e = document.getElementById("newHeaderNameInput").value; if (t && e) { var o = gridOptions.columnDefs.map(o => o.field === t ? { ...o, headerName: e } : o); gridApi.setColumnDefs(o) } } function groupColumn() { var t = document.getElementById("groupColumnInput").value; gridOptions.columnApi.applyColumnState({ state: [{ colId: t, rowGroup: !0 }], defaultState: { rowGroup: !1 } }) } let allRowData; function onFirstDataRendered(t) { setTimeout(function () { t.api.getDisplayedRowAtIndex(0).setExpanded(!0) }, 0), setInterval(function () { if (!allRowData) return; let e = allRowData[0], o = []; e.callRecords.forEach(function (t, e) { o.push({ name: t.name, callId: t.callId, duration: t.duration + e % 2, switchCode: t.switchCode, direction: t.direction, number: t.number }) }), e.callRecords = o, e.calls++; let n = { update: [e] }; t.api.applyTransaction(n) }, 2e3) } var initialKpiData = []; function toggleAccordion() { var t = document.getElementById("accordion"), e = document.getElementById("toggleIcon"), o = document.getElementById("toggleText"); t.classList.contains("show") ? (t.classList.remove("show"), e.classList.remove("fa-chevron-up"), e.classList.add("fa-chevron-down"), o.textContent = "Expand KPIs") : (t.classList.add("show"), e.classList.remove("fa-chevron-down"), e.classList.add("fa-chevron-up"), o.textContent = "Collapse KPIs") }