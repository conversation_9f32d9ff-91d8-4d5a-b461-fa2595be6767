﻿CREATE TABLE [dbo].[WebhookWorkFlows] (
    [WebhookWorkFlowId] INT            IDENTITY (1, 1) NOT NULL,
    [WebhookTargetURL]  NVARCHAR (255) NULL,
    [WebhookParameters] NVARCHAR (MAX) NULL,
    [WebhookId]         INT            NOT NULL,
    [Retries]           INT            NOT NULL,
    [Completed]         BIT            NOT NULL,
    [HttpApiResponse]   NVARCHAR (MAX) NULL,
    [ErrorMessage]      NVARCHAR (MAX) NULL,
    [WorkFlowId]        INT            NOT NULL,
    CONSTRAINT [FK_WebhookWorkFlow_WorkFlows_WorkFlowId] FOREIGN KEY ([WorkFlowId]) REFERENCES [dbo].[WorkFlows] ([WorkFlowId])
);

