﻿

CREATE FUNCTION dbo.GetConcantenatedMilestonesTourStatusTypes
(
	@TourID int,
	@Delimeter nvarchar(8)
)
RETURNS nvarchar(max)
AS
BEGIN

	DECLARE @ConcantenatedList nvarchar(max)
	SET @ConcantenatedList = ''

	SELECT @ConcantenatedList = @ConcantenatedList + @Delimeter + TourStatusTypes.tourStatusTypeName
	FROM MilestonesTourStatusTypes INNER JOIN
		TourStatusTypes ON MilestonesTourStatusTypes.tourStatusTypeID = TourStatusTypes.tourStatusTypeID
	WHERE tourID = @TourID
	ORDER BY MilestonesTourStatusTypes.insertTimeStamp

	IF DATALENGTH(@ConcantenatedList) > 0
	BEGIN
		SET @ConcantenatedList = RIGHT(@ConcantenatedList, ((DATALENGTH(@ConcantenatedList)/2) - (DATALENGTH(@Delimeter)/2)))
	END

	RETURN @ConcantenatedList

END