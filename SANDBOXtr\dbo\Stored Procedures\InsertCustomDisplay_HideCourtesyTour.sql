﻿
CREATE PROCEDURE [dbo].[InsertCustomDisplay_HideCourtesyTour]

AS
BEGIN

	DECLARE @GroupType<PERSON>ey nvarchar(64) SET @GroupTypeKey = 'HideCourtesyTour'
	DECLARE @controlDelegates nvarchar(4000)

	DELETE FROM CustomPageControlDisplayRights WHERE (customPageControlDisplayRightGroupTypeKey = @GroupTypeKey)

	DECLARE @Add int SET @Add = 1
	DECLARE @Remove int SET @Remove = 2

	DECLARE @Insert int SET @Insert = 1
	DECLARE @Select int SET @Select = 2
	DECLARE @Update int SET @Update = 3

	DECLARE @Visible int SET @Visible = 1
	DECLARE @Enabled int SET @Enabled = 2
	DECLARE @Text int SET @Text = 3
	DECLARE @Delegate int SET @Delegate = 4
	DECLARE @Authorized int SET @Authorized = 5
	DECLARE @Administrated int SET @Administrated = 6
	DECLARE @GridColumnVisible int SET @GridColumnVisible = 7

	DECLARE @PagePathID [varchar] (256)

	
	SET @PagePathID = 'ASP.default_aspx_gridViewTours'

	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'courtesyTourColumn',@GridColumnVisible,0,'id_courtesyTour',NULL
	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'courtesyTourPercent',@GridColumnVisible,0,'id_courtesyTourPercent',NULL
		
	SET @PagePathID = 'ASP.reports_usercontrols_toursearchcriteria_ascx'

	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'trWasCourtesyTourTypes',@Visible,0,NULL,NULL
		
	SET @PagePathID = 'ASP.reports_callcenterdetailreport_aspx_gridviewsummary'

	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'courtesyTourColumn',@GridColumnVisible,0,'id_courtesyTour',NULL
	
	SET @PagePathID = 'ASP.reports_efficiencymarketingreport_aspx'

	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'courtesyTourReportSettings',@Visible,0,'NULL',NULL

	SET @PagePathID = 'ASP.reports_efficiencymarketingreport_aspx_gridViewSummary'

	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'courtesyTourColumn',@GridColumnVisible,0,'id_courtesyTour',NULL
	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'courtesyTourPercent',@GridColumnVisible,0,'id_courtesyTourPercent',NULL
	
	SET @PagePathID = 'ASP.reports_efficiencymarketingreport_aspx_gridView'

	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'courtesyTourColumn',@GridColumnVisible,0,'id_courtesyTour',NULL
	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'courtesyTourPercent',@GridColumnVisible,0,'id_courtesyTourPercent',NULL
	
	SET @PagePathID = 'ASP.reports_efficiencysalesreport_aspx'

	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'courtesyTourReportSettings',@Visible,0,'NULL',NULL

	SET @PagePathID = 'ASP.reports_efficiencysalesreport_aspx_gridViewSummary'

	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'courtesyTourColumn',@GridColumnVisible,0,'id_courtesyTour',NULL
	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'notQualifiedColumn',@GridColumnVisible,1,'id_notQualified',NULL
	
	SET @PagePathID = 'ASP.reports_efficiencysalesreport_aspx_gridView'

	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'courtesyTourColumn',@GridColumnVisible,0,'id_courtesyTour',NULL
	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'notQualifiedColumn',@GridColumnVisible,1,'id_notQualified',NULL
		
	SET @PagePathID = 'ASP.reports_tourreport_aspx_gridviewsummary'

	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'courtesyTourColumn',@GridColumnVisible,0,'id_courtesyTour',NULL
	
	SET @PagePathID = 'ASP.reports_tourstatuseffeciencyreport_aspx_gridviewsummary'

	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'courtesyTourColumn',@GridColumnVisible,0,'id_courtesyTour',NULL
	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'courtesyTourPercentColumn',@GridColumnVisible,0,'id_courtesyTourPercent',NULL
	
	SET @PagePathID = 'ASP.reports_tourstatuseffeciencyreport_aspx_gridview'

	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'courtesyTourColumn',@GridColumnVisible,0,'id_courtesyTour',NULL
	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'courtesyTourPercentColumn',@GridColumnVisible,0,'id_courtesyTourPercent',NULL

	SET @PagePathID = 'TrackResults.BES.Data.Cache.IDNames.IIDNamesCache'
	
	SET @controlDelegates = 'TrackResults.BES.Security.CollectionRules:RemoveKeyValuePairByIntKey(System.Int32 11)'
	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'c_tstin',@Delegate,NULL,NULL,@controlDelegates
		
	SET @controlDelegates = 'TrackResults.BES.Security.CollectionRules:RemoveKeyValuePairByIntKey(System.Int32 11)'
	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'c_tstcin',@Delegate,NULL,NULL,@controlDelegates

END