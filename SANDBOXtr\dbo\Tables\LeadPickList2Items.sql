﻿CREATE TABLE [dbo].[LeadPickList2Items] (
    [leadPickList2ItemID]   INT           IDENTITY (1, 1) NOT NULL,
    [leadPickList2ItemName] NVARCHAR (64) NOT NULL,
    [leadPickList2ItemCode] NVARCHAR (64) NOT NULL,
    [sortOrder]             INT           NOT NULL,
    [active]                BIT           NOT NULL,
    CONSTRAINT [PK_LeadPickList2Items] PRIMARY KEY CLUSTERED ([leadPickList2ItemID] ASC),
    CONSTRAINT [UK_LeadPickList2Items_leadPickList2ItemName] UNIQUE NONCLUSTERED ([leadPickList2ItemName] ASC)
);


GO
CREATE TRIGGER [dbo].[LeadPickList2Items.InsertUpdateDelete]
    ON [dbo].[LeadPickList2Items]
    FOR INSERT, UPDATE, DELETE
AS 
BEGIN
    SET NOCOUNT ON;

    DECLARE @Activity  NVARCHAR (8), @id INT;

    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'updated'
    END

    IF EXISTS (SELECT * FROM inserted) AND NOT EXISTS(SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'inserted'
    END

    IF EXISTS (SELECT * FROM deleted) AND NOT EXISTS(SELECT * FROM inserted)
    BEGIN
        SET @Activity = 'deleted'
    END

    

    IF (@Activity = 'deleted')
    BEGIN
        DECLARE cur CURSOR FOR SELECT leadPickList2ItemID FROM deleted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'LeadPickList2Items', @id, 'leadPickList2ItemID',
                (
                    SELECT *
                    FROM deleted i 
                    WHERE i.leadPickList2ItemID =  @id
                    FOR JSON AUTO
                ),0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
    ELSE
    BEGIN
        DECLARE cur CURSOR
        FOR SELECT leadPickList2ItemID
        FROM inserted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'LeadPickList2Items', @id, 'leadPickList2ItemID',
                (
                    SELECT *
                    FROM inserted i 
                    WHERE i.leadPickList2ItemID =  @id
                    FOR JSON AUTO
                ), 0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
END

GO
DISABLE TRIGGER [dbo].[LeadPickList2Items.InsertUpdateDelete]
    ON [dbo].[LeadPickList2Items];

