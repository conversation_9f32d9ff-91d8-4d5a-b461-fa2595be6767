﻿

--drop table dbo.CustomerMetaData
--create table dbo.DeploymentMetaData (id int primary key identity,  settingName nvarchar(100),settingValue nvarchar(100), isActive bit)
--insert into dbo.DeploymentMetaData values ('timeZone', 'PST', 1)
--select * from dbo.DeploymentMetaData
--UPDATE dbo.deploymentmetadata set settingvalue = 'Pacific Standard Time'

--select dbo.GetDeploymentDateTime()

CREATE function dbo.GetDeploymentDateTime()
returns datetimeoffset
begin
	declare @zone nvarchar(100) 
	declare @timeOffset datetimeoffset
	declare @timeReturn datetime
	declare @offset int

	select @zone = settingValue from dbo.DeploymentMetaData where settingName = 'timeZone' and isActive = 1
	select @timeOffset = 
		case @zone
			WHEN 'Dateline Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Dateline Standard Time'
		WHEN 'UTC-11' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'UTC-11'
		WHEN 'Aleutian Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Aleutian Standard Time'
		WHEN 'Hawaiian Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Hawaiian Standard Time'
		WHEN 'Marquesas Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Marquesas Standard Time'
		WHEN 'Alaskan Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Alaskan Standard Time'
		WHEN 'UTC-09' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'UTC-09'
		WHEN 'Pacific Standard Time (Mexico)' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Pacific Standard Time (Mexico)'
		WHEN 'UTC-08' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'UTC-08'
		WHEN 'Pacific Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Pacific Standard Time'
		WHEN 'US Mountain Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'US Mountain Standard Time'
		WHEN 'Mountain Standard Time (Mexico)' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Mountain Standard Time (Mexico)'
		WHEN 'Mountain Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Mountain Standard Time'
		WHEN 'Central America Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Central America Standard Time'
		WHEN 'Central Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Central Standard Time'
		WHEN 'Easter Island Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Easter Island Standard Time'
		WHEN 'Central Standard Time (Mexico)' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Central Standard Time (Mexico)'
		WHEN 'Canada Central Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Canada Central Standard Time'
		WHEN 'SA Pacific Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'SA Pacific Standard Time'
		WHEN 'Eastern Standard Time (Mexico)' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Eastern Standard Time (Mexico)'
		WHEN 'Eastern Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Eastern Standard Time'
		WHEN 'Haiti Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Haiti Standard Time'
		WHEN 'Cuba Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Cuba Standard Time'
		WHEN 'US Eastern Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'US Eastern Standard Time'
		WHEN 'Paraguay Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Paraguay Standard Time'
		WHEN 'Atlantic Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Atlantic Standard Time'
		WHEN 'Venezuela Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Venezuela Standard Time'
		WHEN 'Central Brazilian Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Central Brazilian Standard Time'
		WHEN 'SA Western Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'SA Western Standard Time'
		WHEN 'Pacific SA Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Pacific SA Standard Time'
		WHEN 'Turks And Caicos Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Turks And Caicos Standard Time'
		WHEN 'Newfoundland Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Newfoundland Standard Time'
		WHEN 'Tocantins Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Tocantins Standard Time'
		WHEN 'E. South America Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'E. South America Standard Time'
		WHEN 'SA Eastern Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'SA Eastern Standard Time'
		WHEN 'Argentina Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Argentina Standard Time'
		WHEN 'Greenland Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Greenland Standard Time'
		WHEN 'Montevideo Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Montevideo Standard Time'
		WHEN 'Magallanes Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Magallanes Standard Time'
		WHEN 'Saint Pierre Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Saint Pierre Standard Time'
		WHEN 'Bahia Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Bahia Standard Time'
		WHEN 'UTC-02' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'UTC-02'
		WHEN 'Mid-Atlantic Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Mid-Atlantic Standard Time'
		WHEN 'Azores Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Azores Standard Time'
		WHEN 'Cape Verde Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Cape Verde Standard Time'
		WHEN 'UTC' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'UTC'
		WHEN 'Morocco Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Morocco Standard Time'
		WHEN 'GMT Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'GMT Standard Time'
		WHEN 'Greenwich Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Greenwich Standard Time'
		WHEN 'W. Europe Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'W. Europe Standard Time'
		WHEN 'Central Europe Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Central Europe Standard Time'
		WHEN 'Romance Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Romance Standard Time'
		WHEN 'Central European Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Central European Standard Time'
		WHEN 'W. Central Africa Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'W. Central Africa Standard Time'
		WHEN 'Namibia Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Namibia Standard Time'
		WHEN 'Jordan Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Jordan Standard Time'
		WHEN 'GTB Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'GTB Standard Time'
		WHEN 'Middle East Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Middle East Standard Time'
		WHEN 'Egypt Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Egypt Standard Time'
		WHEN 'E. Europe Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'E. Europe Standard Time'
		WHEN 'Syria Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Syria Standard Time'
		WHEN 'West Bank Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'West Bank Standard Time'
		WHEN 'South Africa Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'South Africa Standard Time'
		WHEN 'FLE Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'FLE Standard Time'
		WHEN 'Israel Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Israel Standard Time'
		WHEN 'Kaliningrad Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Kaliningrad Standard Time'
		WHEN 'Libya Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Libya Standard Time'
		WHEN 'Arabic Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Arabic Standard Time'
		WHEN 'Turkey Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Turkey Standard Time'
		WHEN 'Arab Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Arab Standard Time'
		WHEN 'Belarus Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Belarus Standard Time'
		WHEN 'Russian Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Russian Standard Time'
		WHEN 'E. Africa Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'E. Africa Standard Time'
		WHEN 'Iran Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Iran Standard Time'
		WHEN 'Arabian Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Arabian Standard Time'
		WHEN 'Astrakhan Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Astrakhan Standard Time'
		WHEN 'Azerbaijan Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Azerbaijan Standard Time'
		WHEN 'Russia Time Zone 3' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Russia Time Zone 3'
		WHEN 'Mauritius Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Mauritius Standard Time'
		WHEN 'Saratov Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Saratov Standard Time'
		WHEN 'Georgian Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Georgian Standard Time'
		WHEN 'Caucasus Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Caucasus Standard Time'
		WHEN 'Afghanistan Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Afghanistan Standard Time'
		WHEN 'West Asia Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'West Asia Standard Time'
		WHEN 'Ekaterinburg Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Ekaterinburg Standard Time'
		WHEN 'Pakistan Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Pakistan Standard Time'
		WHEN 'India Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'India Standard Time'
		WHEN 'Sri Lanka Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Sri Lanka Standard Time'
		WHEN 'Nepal Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Nepal Standard Time'
		WHEN 'Central Asia Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Central Asia Standard Time'
		WHEN 'Bangladesh Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Bangladesh Standard Time'
		WHEN 'Omsk Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Omsk Standard Time'
		WHEN 'Myanmar Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Myanmar Standard Time'
		WHEN 'SE Asia Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'SE Asia Standard Time'
		WHEN 'Altai Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Altai Standard Time'
		WHEN 'W. Mongolia Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'W. Mongolia Standard Time'
		WHEN 'North Asia Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'North Asia Standard Time'
		WHEN 'N. Central Asia Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'N. Central Asia Standard Time'
		WHEN 'Tomsk Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Tomsk Standard Time'
		WHEN 'China Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'China Standard Time'
		WHEN 'North Asia East Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'North Asia East Standard Time'
		WHEN 'Singapore Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Singapore Standard Time'
		WHEN 'W. Australia Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'W. Australia Standard Time'
		WHEN 'Taipei Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Taipei Standard Time'
		WHEN 'Ulaanbaatar Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Ulaanbaatar Standard Time'
		WHEN 'North Korea Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'North Korea Standard Time'
		WHEN 'Aus Central W. Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Aus Central W. Standard Time'
		WHEN 'Transbaikal Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Transbaikal Standard Time'
		WHEN 'Tokyo Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Tokyo Standard Time'
		WHEN 'Korea Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Korea Standard Time'
		WHEN 'Yakutsk Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Yakutsk Standard Time'
		WHEN 'Cen. Australia Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Cen. Australia Standard Time'
		WHEN 'AUS Central Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'AUS Central Standard Time'
		WHEN 'E. Australia Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'E. Australia Standard Time'
		WHEN 'AUS Eastern Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'AUS Eastern Standard Time'
		WHEN 'West Pacific Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'West Pacific Standard Time'
		WHEN 'Tasmania Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Tasmania Standard Time'
		WHEN 'Vladivostok Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Vladivostok Standard Time'
		WHEN 'Lord Howe Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Lord Howe Standard Time'
		WHEN 'Bougainville Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Bougainville Standard Time'
		WHEN 'Russia Time Zone 10' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Russia Time Zone 10'
		WHEN 'Magadan Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Magadan Standard Time'
		WHEN 'Norfolk Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Norfolk Standard Time'
		WHEN 'Sakhalin Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Sakhalin Standard Time'
		WHEN 'Central Pacific Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Central Pacific Standard Time'
		WHEN 'Russia Time Zone 11' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Russia Time Zone 11'
		WHEN 'New Zealand Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'New Zealand Standard Time'
		WHEN 'UTC+12' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'UTC+12'
		WHEN 'Fiji Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Fiji Standard Time'
		WHEN 'Kamchatka Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Kamchatka Standard Time'
		WHEN 'Chatham Islands Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Chatham Islands Standard Time'
		WHEN 'UTC+13' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'UTC+13'
		WHEN 'Tonga Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Tonga Standard Time'
		WHEN 'Samoa Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Samoa Standard Time'
		WHEN 'Line Islands Standard Time' then CONVERT(DATETIME, getdate(),120) AT TIME ZONE 'Line Islands Standard Time'
		--
		else getdate()
		end
		set @timeReturn = Convert(datetime, @timeOffset)
		select @offset = datepart(tzoffset, @timeOffset)
		set @timeReturn = DATEADD(mi, @offset, @timeReturn)
	return @timeReturn
end

--SELECT CONVERT(DATETIME2(0), getdate(), 126)     
--AT TIME ZONE 'US Mountain Standard Time';  
		
--SELECT CONVERT(DATETIME2(0), getdate(), 126)     
--AT TIME ZONE 'Pacific Standard Time'; 
