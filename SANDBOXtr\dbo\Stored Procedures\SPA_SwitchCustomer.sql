﻿CREATE PROCEDURE [dbo].[<PERSON><PERSON>_SwitchCustomer]
(
    @customerID int, @status INT OUTPUT
)
AS
BEGIN

	DECLARE @FirstName NVARCHAR(MAX) = NULL, @LastName NVARCHAR(MAX) = NULL, @Age NVARCHAR(MAX) = NULL, 
	@GuestFirstName NVARCHAR(MAX) = NULL, @GuestLastName NVARCHAR(MAX) = NULL, @GuestAge NVARCHAR(MAX) = NULL, 
	@PrimaryPhone  NVARCHAR(MAX) = NULL, @StreetAddres  NVARCHAR(MAX) = NULL, @City NVARCHAR(MAX) = NULL, @State NVARCHAR(MAX) = NULL, @ZipCode NVARCHAR(MAX) = NULL, 
	@SecondaryPhone  NVARCHAR(MAX) = NULL, @StreetAddres2 NVARCHAR(MAX) = NULL, @City2 NVARCHAR(MAX) = NULL, @State2 NVARCHAR(MAX) = NULL, @ZipCode2 NVARCHAR(MAX) = NULL,
	@CountryID NVARCHAR(MAX) = NULL, @CountryID2 NVARCHAR(MAX) = NULL, @Sex NVARCHAR(MAX) = NULL, @Sex2 NVARCHAR(MAX) = NULL, @Email NVARCHAR(MAX) = NULL, @Email2 NVARCHAR(MAX) = NULL, 
	@StreetAddresGuest NVARCHAR(MAX) = NULL, @StreetAddres2Guest NVARCHAR(MAX) = NULL

	SELECT
	@FirstName = firstName, 
	@LastName = lastName, 
	@Age = age,
	@PrimaryPhone = primaryPhone, 
	@StreetAddres = streetAddress, 
	@City = city, 
	@State = stateID, 
	@ZipCode = zipcode,
	@Sex = sex,
	@CountryID = countryID,
	@GuestFirstName = guestFirstName, 
	@GuestLastName = guestLastName, 
	@GuestAge = guestAge,
	@SecondaryPhone = secondaryPhone, 
	@StreetAddres2 = streetAddress2, 
	@City2 = city2, 
	@State2 = stateID2, 
	@ZipCode2 = zipcode2,
	@CountryID2 = countryID2,
	@Sex2 = guestSex, 
	@Email = email, 
	@Email2 = emailGuest,
	@StreetAddresGuest = streetAddressGuest,
	@StreetAddres2Guest = streetAddress2Guest
	FROM Customers WHERE CustomerID = @customerID;


	IF (@FirstName IS NOT NULL OR @LastName IS NOT NULL) AND (@GuestFirstName IS NOT NULL OR @GuestLastName IS NOT NULL)
	BEGIN
		UPDATE Customers SET
		firstName = @GuestFirstName,
		lastName = @GuestLastName,
		age = @GuestAge,
		primaryPhone = @SecondaryPhone,
		streetAddress = @StreetAddresGuest,
		streetAddress2 = @StreetAddres2Guest,
		city = @City2,
		stateID = @State2,
		zipcode = @ZipCode2,
		countryID = @CountryID2,
		sex = @Sex2,
		guestFirstName = @FirstName,
		guestLastName = @LastName,
		guestAge = @Age,
		secondaryPhone = @PrimaryPhone,
		city2 = @City,
		stateID2 = @State,
		zipcode2 = @ZipCode,
		countryID2 = @CountryID,
		guestSex = @Sex,
		email = @Email2,
		emailGuest = @Email,
		streetAddressGuest = @StreetAddres,
		streetAddress2Guest = @StreetAddres2
		WHERE customerID = @customerID;

		SET @status = 200
	END
	ELSE
	BEGIN
		SET @status = 500
	END
	
END
