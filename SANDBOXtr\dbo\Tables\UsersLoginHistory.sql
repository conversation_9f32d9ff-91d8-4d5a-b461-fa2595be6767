﻿CREATE TABLE [dbo].[UsersLoginHistory] (
    [users<PERSON>og<PERSON><PERSON>istory<PERSON>] INT            IDENTITY (1, 1) NOT NULL,
    [userName]            NVARCHAR (256) NOT NULL,
    [roleName]            NVARCHAR (256) NULL,
    [ipAddressBase]       NVARCHAR (50)  NOT NULL,
    [loginDate]           DATETIME       NOT NULL,
    CONSTRAINT [PK_UsersLoginHistory] PRIMARY KEY CLUSTERED ([usersLoginHistoryID] ASC)
);


GO
CREATE TRIGGER [dbo].[UsersLoginHistory.InsertUpdateDelete]
    ON [dbo].[UsersLoginHistory]
    FOR INSERT, UPDATE, DELETE
AS 
BEGIN
    SET NOCOUNT ON;

    DECLARE @Activity  NVARCHAR (8), @id INT;

    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'updated'
    END

    IF EXISTS (SELECT * FROM inserted) AND NOT EXISTS(SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'inserted'
    END

    IF EXISTS (SELECT * FROM deleted) AND NOT EXISTS(SELECT * FROM inserted)
    BEGIN
        SET @Activity = 'deleted'
    END

    

    IF (@Activity = 'deleted')
    BEGIN
        DECLARE cur CURSOR FOR SELECT usersLoginHistoryID FROM deleted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'UsersLoginHistory', @id, 'usersLoginHistoryID',
                (
                    SELECT *
                    FROM deleted i 
                    WHERE i.usersLoginHistoryID =  @id
                    FOR JSON AUTO
                ),0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
    ELSE
    BEGIN
        DECLARE cur CURSOR
        FOR SELECT usersLoginHistoryID
        FROM inserted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'UsersLoginHistory', @id, 'usersLoginHistoryID',
                (
                    SELECT *
                    FROM inserted i 
                    WHERE i.usersLoginHistoryID =  @id
                    FOR JSON AUTO
                ), 0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
END

GO
DISABLE TRIGGER [dbo].[UsersLoginHistory.InsertUpdateDelete]
    ON [dbo].[UsersLoginHistory];

