﻿CREATE TABLE [dbo].[LeadPickList1Items] (
    [leadPickList1ItemID]   INT           IDENTITY (1, 1) NOT NULL,
    [leadPickList1ItemName] NVARCHAR (64) NOT NULL,
    [leadPickList1ItemCode] NVARCHAR (64) NOT NULL,
    [sortOrder]             INT           NOT NULL,
    [active]                BIT           NOT NULL,
    CONSTRAINT [PK_LeadPickList1Items] PRIMARY KEY CLUSTERED ([leadPickList1ItemID] ASC),
    CONSTRAINT [UK_LeadPickList1Items_leadPickList1ItemName] UNIQUE NONCLUSTERED ([leadPickList1ItemName] ASC)
);


GO
CREATE TRIGGER [dbo].[LeadPickList1Items.InsertUpdateDelete]
    ON [dbo].[LeadPickList1Items]
    FOR INSERT, UPDATE, DELETE
AS 
BEGIN
    SET NOCOUNT ON;

    DECLARE @Activity  NVARCHAR (8), @id INT;

    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'updated'
    END

    IF EXISTS (SELECT * FROM inserted) AND NOT EXISTS(SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'inserted'
    END

    IF EXISTS (SELECT * FROM deleted) AND NOT EXISTS(SELECT * FROM inserted)
    BEGIN
        SET @Activity = 'deleted'
    END

    

    IF (@Activity = 'deleted')
    BEGIN
        DECLARE cur CURSOR FOR SELECT leadPickList1ItemID FROM deleted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'LeadPickList1Items', @id, 'leadPickList1ItemID',
                (
                    SELECT *
                    FROM deleted i 
                    WHERE i.leadPickList1ItemID =  @id
                    FOR JSON AUTO
                ),0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
    ELSE
    BEGIN
        DECLARE cur CURSOR
        FOR SELECT leadPickList1ItemID
        FROM inserted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'LeadPickList1Items', @id, 'leadPickList1ItemID',
                (
                    SELECT *
                    FROM inserted i 
                    WHERE i.leadPickList1ItemID =  @id
                    FOR JSON AUTO
                ), 0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
END

GO
DISABLE TRIGGER [dbo].[LeadPickList1Items.InsertUpdateDelete]
    ON [dbo].[LeadPickList1Items];

