﻿CREATE TABLE [dbo].[Hotels] (
    [hotelID]   INT           IDENTITY (1, 1) NOT NULL,
    [hotelName] NVARCHAR (64) NOT NULL,
    [hotelCode] NVARCHAR (64) NULL,
    [active]    BIT           NOT NULL,
    CONSTRAINT [PK_Hotels] PRIMARY KEY CLUSTERED ([hotelID] ASC),
    CONSTRAINT [UK_Hotels_hotelName] UNIQUE NONCLUSTERED ([hotelName] ASC)
);


GO
CREATE TRIGGER [dbo].[Hotels.InsertUpdateDelete]
    ON [dbo].[Hotels]
    FOR INSERT, UPDATE, DELETE
AS 
BEGIN
    SET NOCOUNT ON;

    DECLARE @Activity  NVARCHAR (8), @id INT;

    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'updated'
    END

    IF EXISTS (SELECT * FROM inserted) AND NOT EXISTS(SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'inserted'
    END

    IF EXISTS (SELECT * FROM deleted) AND NOT EXISTS(SELECT * FROM inserted)
    BEGIN
        SET @Activity = 'deleted'
    END

    

    IF (@Activity = 'deleted')
    BEGIN
        DECLARE cur CURSOR FOR SELECT hotelID FROM deleted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'Hotels', @id, 'hotelID',
                (
                    SELECT *
                    FROM deleted i 
                    WHERE i.hotelID =  @id
                    FOR JSON AUTO
                ),0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
    ELSE
    BEGIN
        DECLARE cur CURSOR
        FOR SELECT hotelID
        FROM inserted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'Hotels', @id, 'hotelID',
                (
                    SELECT *
                    FROM inserted i 
                    WHERE i.hotelID =  @id
                    FOR JSON AUTO
                ), 0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
END

GO
DISABLE TRIGGER [dbo].[Hotels.InsertUpdateDelete]
    ON [dbo].[Hotels];

