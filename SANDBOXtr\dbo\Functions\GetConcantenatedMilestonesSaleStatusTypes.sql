﻿

CREATE FUNCTION dbo.GetConcantenatedMilestonesSaleStatusTypes
(
	@PurchaseID int,
	@Delimeter nvarchar(8)
)
RETURNS nvarchar(max)
AS
BEGIN

	DECLARE @ConcantenatedList nvarchar(max)
	SET @ConcantenatedList = ''

	SELECT @ConcantenatedList = @ConcantenatedList + @Delimeter + SaleStatusTypes.saleStatusTypeName
	FROM MilestonesSaleStatusTypes INNER JOIN
		SaleStatusTypes ON MilestonesSaleStatusTypes.saleStatusTypeID = SaleStatusTypes.saleStatusTypeID
	WHERE purchaseID = @PurchaseID
	ORDER BY MilestonesSaleStatusTypes.insertTimeStamp

	IF DATALENGTH(@ConcantenatedList) > 0
	BEGIN
		SET @ConcantenatedList = RIGHT(@ConcantenatedList, ((DATALENGTH(@ConcantenatedList)/2) - (DATALENGTH(@Delimeter)/2)))
	END

	RETURN @ConcantenatedList

END