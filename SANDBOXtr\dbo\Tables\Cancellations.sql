﻿CREATE TABLE [dbo].[Cancellations] (
    [cancellationID]                INT      IDENTITY (1, 1) NOT NULL,
    [cancellationPurchaseID]        INT      NOT NULL,
    [cancellationDate]              DATETIME NULL,
    [cancellationReasonTypeID]      INT      NULL,
    [cancellationReceivedTypeID]    INT      NULL,
    [cancellationDispositionTypeID] INT      NULL,
    [withinRescission]              BIT      CONSTRAINT [DF_Cancellations_withinRescission] DEFAULT ((0)) NULL,
    [pender]                        BIT      NOT NULL,
    CONSTRAINT [PK_Cancellations] PRIMARY KEY CLUSTERED ([cancellationID] ASC),
    CONSTRAINT [FK_Cancellations_CancellationDispositionTypes] FOREIGN KEY ([cancellationDispositionTypeID]) REFERENCES [dbo].[CancellationDispositionTypes] ([cancellationDispositionTypeID]),
    CONSTRAINT [FK_Cancellations_CancellationReasonTypes] FOREIGN KEY ([cancellationReasonTypeID]) REFERENCES [dbo].[CancellationReasonTypes] ([cancellationReasonTypeID]),
    CONSTRAINT [FK_Cancellations_CancellationReceivedTypes] FOREIGN KEY ([cancellationReceivedTypeID]) REFERENCES [dbo].[CancellationReceivedTypes] ([cancellationReceivedTypeID]),
    CONSTRAINT [FK_Cancellations_Purchases] FOREIGN KEY ([cancellationPurchaseID]) REFERENCES [dbo].[Purchases] ([purchaseID]),
    CONSTRAINT [UK_Cancellations] UNIQUE NONCLUSTERED ([cancellationPurchaseID] ASC)
);


GO
CREATE TRIGGER [dbo].[Cancellations.InsertUpdateDelete]
    ON [dbo].[Cancellations]
    FOR INSERT, UPDATE, DELETE
AS 
BEGIN
    SET NOCOUNT ON;

    DECLARE @Activity  NVARCHAR (8), @id INT;

    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'updated'
    END

    IF EXISTS (SELECT * FROM inserted) AND NOT EXISTS(SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'inserted'
    END

    IF EXISTS (SELECT * FROM deleted) AND NOT EXISTS(SELECT * FROM inserted)
    BEGIN
        SET @Activity = 'deleted'
    END

    

    IF (@Activity = 'deleted')
    BEGIN
        DECLARE cur CURSOR FOR SELECT cancellationID FROM deleted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'Cancellations', @id, 'cancellationID',
                (
                    SELECT *
                    FROM deleted i 
                    WHERE i.cancellationID =  @id
                    FOR JSON AUTO
                ),0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
    ELSE
    BEGIN
        DECLARE cur CURSOR
        FOR SELECT cancellationID
        FROM inserted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'Cancellations', @id, 'cancellationID',
                (
                    SELECT *
                    FROM inserted i 
                    WHERE i.cancellationID =  @id
                    FOR JSON AUTO
                ), 0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
END

GO
DISABLE TRIGGER [dbo].[Cancellations.InsertUpdateDelete]
    ON [dbo].[Cancellations];

