﻿CREATE TABLE [dbo].[TourSources] (
    [tourSourceID]   INT           IDENTITY (1, 1) NOT NULL,
    [tourSourceName] NVARCHAR (64) NOT NULL,
    [tourSourceCode] NVARCHAR (64) NOT NULL,
    [sortOrder]      INT           NOT NULL,
    [active]         BIT           NOT NULL,
    CONSTRAINT [PK_TourSources] PRIMARY KEY CLUSTERED ([tourSourceID] ASC),
    CONSTRAINT [UK_TourSources_tourSourceName] UNIQUE NONCLUSTERED ([tourSourceName] ASC)
);


GO
CREATE TRIGGER [dbo].[TourSources.InsertUpdateDelete]
    ON [dbo].[TourSources]
    FOR INSERT, UPDATE, DELETE
AS 
BEGIN
    SET NOCOUNT ON;

    DECLARE @Activity  NVARCHAR (8), @id INT;

    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'updated'
    END

    IF EXISTS (SELECT * FROM inserted) AND NOT EXISTS(SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'inserted'
    END

    IF EXISTS (SELECT * FROM deleted) AND NOT EXISTS(SELECT * FROM inserted)
    BEGIN
        SET @Activity = 'deleted'
    END

    

    IF (@Activity = 'deleted')
    BEGIN
        DECLARE cur CURSOR FOR SELECT tourSourceID FROM deleted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'TourSources', @id, 'tourSourceID',
                (
                    SELECT *
                    FROM deleted i 
                    WHERE i.tourSourceID =  @id
                    FOR JSON AUTO
                ),0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
    ELSE
    BEGIN
        DECLARE cur CURSOR
        FOR SELECT tourSourceID
        FROM inserted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'TourSources', @id, 'tourSourceID',
                (
                    SELECT *
                    FROM inserted i 
                    WHERE i.tourSourceID =  @id
                    FOR JSON AUTO
                ), 0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
END

GO
DISABLE TRIGGER [dbo].[TourSources.InsertUpdateDelete]
    ON [dbo].[TourSources];

