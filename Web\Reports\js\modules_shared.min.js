var allRowsGrid, customKpis = [], typeReports = [], get_reports_list = [], file_local_active = !1, reportFilterID = 0, listKpis = [], ReportFilters = null; let runCount = 0; const intervalId = setInterval(updateDropdownOptions, 3e3); var initialFilters = [], reportSelectedName = ""; let newColumnDefs, previusly_reportSelected, reportIdSelected = 0, initial_date, last_date; function populateKpi(e) { var t = document.getElementById("ddlKPIsGroupBy"); t.innerHTML = "", getKPIsDataByReportId(i => { for (var a in i) for (var s = i[a], r = 0; r < s.length; r++)if ("" != s[r].headerText) { var l = document.createElement("option"); l.text = s[r].headerText, l.value = void 0 !== s[r].pivot ? s[r].pivot : s[r].headerText, t.add(l) } if (null != e) { for (var r = 0; r < t.options.length; r++)if (t.options[r].value == e) { t.options[r].selected = !0; break } } }) } async function onReportTypeOnSelected(e) { let t; var i = document.getElementById("ddlKPIsGroupBy"); if ("--" != e.options[e.selectedIndex].text) { "Customer Name" != e.options[e.selectedIndex].text && "Users Name" != e.options[e.selectedIndex].text && "User FullName" != e.options[e.selectedIndex].text ? t = e.options[e.selectedIndex].text + ".name" : "Customer Name" == e.options[e.selectedIndex].text ? t = e.options[e.selectedIndex].text + ".customerName" : "Users Name" == e.options[e.selectedIndex].text ? t = e.options[e.selectedIndex].text + ".name" : "User FullName" == e.options[e.selectedIndex].text && (t = e.options[e.selectedIndex].text + ".userFullName"); var a = Array.from(i.options).find(e => e.text === t); if (a) a.selected = !0; else { var s = document.createElement("option"); s.text = t, s.value = t; for (var r = 0; r < i.options.length; r++)i.options[r].selected = !1; i.add(s), s.selected = !0 } i.disabled = !0, i.style.setProperty("background-color", "#ffffff", "important") } else i.disabled = !1, i.selectedIndex = 0 } async function onReportSelected(e) { var t = !1; if ("--" != e.options[e.selectedIndex].text) { await clearReportTool(); let i = document.getElementById("btnDeleteReport"); i.style.display = "inline", t = !0 } else if ($("#reportName").val() == previusly_reportSelected && "--" == e.options[e.selectedIndex].text) { await clearReportTool(); let a = document.getElementById("btnDeleteReport"); a.style.display = "none" } if (t) { await clearDateTimeCriterial(), await clearCancellationCriteriaFilters(), await clearCustomersCriteriaFilters(), await clearBasicCriterial(), await clearMarketingCriteriaFilters(), await clearPurchaseCriteriaFilters(), await clearSalesCriteriaFilters(), await clearTourCriteriaFilters(), await clearLeadCriteriaFilters(), reportFilterID = 0, selectedReportId = e.value, reportSelectedName = e.options[e.selectedIndex].text, previusly_reportSelected = e.options[e.selectedIndex].text; let s = /^local_.*$/; if (s.test(selectedReportId)) file_local_active = !0, document.querySelector(".fileInputData").style.display = "block", document.querySelectorAll(".options").forEach(e => e.disabled = !0); else if (file_local_active = !1, document.querySelector(".fileInputData").style.display = "none", document.querySelectorAll(".options").forEach(e => e.disabled = !1), selectedReportId > 0) try { if ($("#loader").show(), selectedReportId = parseInt(selectedReportId), reportSelectedFromDropdown = !0, reportDetail = await fetchReportById(selectedReportId), fetchReportUsersById(selectedReportId), [joinDefinitions, reportKPIRelationship] = await Promise.all([fetchJoinDefinitions(selectedReportId), fetchReportKPIRelationship(selectedReportId),]), reportFilters = reportDetail.filters, null !== reportFilters && reportFilters.length > 0) { let r = JSON.parse(reportFilters); function l(e, t) { return e[t] } function n(e, t) { t.forEach(function (t) { var i = l(e, t), a = document.getElementById(i); if (a && !a.checked) { a.checked = !0; var s = new Event("click", { bubbles: !0, cancelable: !0 }); a.dispatchEvent(s); var r = a.parentElement; if (r) { var n = new Event("click", { bubbles: !0, cancelable: !0 }); r.dispatchEvent(n) } } }) } function o(e, t, i, a) { if ("All" !== e[t] && null != e[t]) { var s = e[t]; if (null != s) { var r = s.split(",").map(function (e) { return e.trim() }); n(i, r); var l = document.getElementById(a); l && (l.value = s, l.text = s) } } } void 0 != r.dateCriteriaFilters && setDateTimeCriteriaFilters(r.dateCriteriaFilters), void 0 != r.customersCriteriaFilters && setCustomersCriteriaFilters(r.customersCriteriaFilters), void 0 != r.cancellationFilters && setCancellationCriteriaFilters(r.cancellationFilters), void 0 != r.purchaseFilters && setPurchaseCriteriaFilters(r.purchaseFilters), void 0 != r.salesFilters && setSalesCriteriaFilters(r.salesFilters), void 0 != r.marketingFilters && setMarketingCriteriaFilters(r.marketingFilters), void 0 != r.tourFilters && setTourCriteriaFilters(r.tourFilters), void 0 != r.basicFilters && setBasicCriteriaFilters(r.basicFilters), void 0 != r.leadFilters && setLeadCriteriaFilters(r.leadFilters), void 0 !== r.dateTypeLogic && (document.getElementById("dropDownDateTypeLogic").value = r.dateTypeLogic); for (var c = { Sunday: "ctl00_c1_DateTimeCriteriaDetails_dropDownDaysOfWeek_i0_checkBox", Monday: "ctl00_c1_DateTimeCriteriaDetails_dropDownDaysOfWeek_i1_checkBox", Tuesday: "ctl00_c1_DateTimeCriteriaDetails_dropDownDaysOfWeek_i2_checkBox", Wednesday: "ctl00_c1_DateTimeCriteriaDetails_dropDownDaysOfWeek_i3_checkBox", Thursday: "ctl00_c1_DateTimeCriteriaDetails_dropDownDaysOfWeek_i4_checkBox", Friday: "ctl00_c1_DateTimeCriteriaDetails_dropDownDaysOfWeek_i5_checkBox", Saturday: "ctl00_c1_DateTimeCriteriaDetails_dropDownDaysOfWeek_i6_checkBox" }, u = {}, p = 1; p <= 52; p++)u[p.toString()] = "ctl00_c1_DateTimeCriteriaDetails_dropDownWeeks_i" + (p - 1) + "_checkBox"; for (var D = { January: "ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i0_checkBox", February: "ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i1_checkBox", March: "ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i2_checkBox", April: "ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i3_checkBox", May: "ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i4_checkBox", June: "ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i5_checkBox", July: "ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i6_checkBox", August: "ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i7_checkBox", September: "ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i8_checkBox", October: "ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i9_checkBox", November: "ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i10_checkBox", December: "ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i11_checkBox" }, d = {}, m = 0; m <= 24; m++)d[(2023 - m).toString()] = "ctl00_c1_DateTimeCriteriaDetails_dropDownYears_i" + m + "_checkBox"; var y = { "8:00 AM": "ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_i0_checkBox", "8:40 AM": "ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_i1_checkBox", "10:00 AM": "ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_i2_checkBox", "11:30 AM": "ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_i3_checkBox", "12:00 PM": "ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_i4_checkBox", "1:00 PM": "ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_i5_checkBox", "2:00 PM": "ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_i6_checkBox", "3:45 PM": "ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_i7_checkBox", "4:00 PM": "ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_i8_checkBox", "6:00 PM": "ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_i9_checkBox", "8:00 PM": "ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_i10_checkBox" }; updateDropdownOptions(), r.dateCriteriaFilters && (void 0 !== r.dateCriteriaFilters.daysOfWeek && o(r.dateCriteriaFilters, "daysOfWeek", c, "ctl00_c1_DateTimeCriteriaDetails_dropDownDaysOfWeek_Input"), void 0 !== r.dateCriteriaFilters.weeks && o(r.dateCriteriaFilters, "weeks", u, "ctl00_c1_DateTimeCriteriaDetails_dropDownWeeks_Input"), void 0 !== r.dateCriteriaFilters.months && o(r.dateCriteriaFilters, "months", D, "ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_Input"), void 0 !== r.dateCriteriaFilters.years && o(r.dateCriteriaFilters, "years", d, "ctl00_c1_DateTimeCriteriaDetails_dropDownYears_Input"), void 0 !== r.dateCriteriaFilters.times && o(r.dateCriteriaFilters, "times", y, "ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_Input"), r.dateCriteriaFilters.endDate && "All" !== r.dateCriteriaFilters.endDate && (document.getElementById("ctl00_c1_DateTimeCriteriaDetails_endDateRadDatePicker_dateInput").value = last_date), r.dateCriteriaFilters.startDate && "All" !== r.dateCriteriaFilters.startDate && (document.getElementById("ctl00_c1_DateTimeCriteriaDetails_startDateRadDatePicker_dateInput").value = initial_date), r.dateCriteriaFilters.dateRange && "All" !== r.dateCriteriaFilters.dateRange && (document.getElementById("dropDownDateRanges").value = r.dateCriteriaFilters.dateRange.toString()), r.dateCriteriaFilters.yearRange && "All" !== r.dateCriteriaFilters.yearRange && (document.getElementById("dropDownYearRanges").value = r.dateCriteriaFilters.yearRange.toString())), ReportFilters = r } $("[id$=reportName]").val(reportDetail.name), $("[id$=initialReportName]").val(reportDetail.name), reportIdSelected = reportDetail.reportId; for (var k = document.getElementById("preJoinsDropdown"), p = 0; p < k.options.length; p++) { var C = k.options[p]; if (C.value.toLowerCase() == reportDetail.preJoin.toLowerCase()) { C.selected = !0; break } } populateKpi(reportDetail.groupBy); var P = document.getElementById("reportTypeOnDropdown"); if (P.selectedIndex = 0, reportDetail.reportOn > 0) for (var p = 0; p <= P.options.length; p++) { var C = P.options[p]; if (C.value == reportDetail.reportOn) { C.selected = !0, 1e3 != reportDetail.reportOn && 2e3 != reportDetail.reportOn ? g = C.text + ".name" : 1e3 == reportDetail.reportOn ? g = C.text + ".customerName" : 2e3 == reportDetail.reportOn && (g = C.text + ".userFullName"); var g, I = document.getElementById("ddlKPIsGroupBy"), h = Array.from(I.options).find(e => e.text === g); if (h) h.selected = !0; else { var w = document.createElement("option"); w.text = g, w.value = g; for (var p = 0; p < I.options.length; p++)I.options[p].selected = !1; I.add(w), w.selected = !0 } break } } if (kpiData = await fetchKPIs(selectedReportId), document.getElementById("accordion").innerHTML = "", addAccordion(kpiData), joinDefinitions.length > 1) for (var p = 0; p < joinDefinitions.length - 1; p++)addJoin(); if (displayRowsJoin(joinDefinitions), displayRowsKpi(kpiData), kpiData.length > 0) loadGrid(); else { var f = $("#reportNamesDropdown option:selected").text(); $("#loader").hide(), showSwal("success", f + " loaded successfully.") } } catch (_) { $("#loader").hide() } } } function loadGrid() { if (void 0 != gridApi && (gridApi.setGridOption("rowData", []), gridApi.setGridOption("columnDefs", []), gridApi.setGridOption("pinnedTopRowData", []), gridApi.setGridOption("pinnedBottomRowData", [])), file_local_active) readLocalFile(); else { let e = [], t = document.querySelectorAll(".header-container"), i = ""; for (let a of t) try { let [s, r] = a.querySelector("h4").textContent.trim().split(" - ").map(e => e.trim()), l = a.querySelector(".inputs-container"), n = a.querySelector(".buttons-container"), o = "", c = ""; if ("ifelse" != r) for (var u = l.querySelector(".operation").value.trim().split(" "), p = 0; p < u.length; p++)o += findKpiByCode(u[p]) + (p < u.length - 1 ? " " : ""); for (var D = l.querySelector(".condition").value.trim().split(" "), p = 0; p < D.length; p++)c += findKpiByCode(D[p]) + (p < D.length - 1 ? " " : ""); var d = n.querySelector(".visible_row").querySelector("i"); let m = !1; "fa fa-eye" == d.className && (m = !0), e.push({ columnName: s, operation: o, condition: c, aggFunc: r, original: l.querySelector(".condition").value, format: a.nextElementSibling.querySelector(".mathFormat").value, kpiData: kpiData, Visible: m }) } catch (y) { } loadGridData(e, i = document.getElementById("ddlKPIsGroupBy").value.trim()) } } async function assignUsers(e) { let t = getSelectedUserIds(); if (0 === e && (e = $("#reportNamesDropdown").val()), e > 0) { let i = await saveReportUsers(e, t), a = i.success ? "Users have been successfully assigned to the selected report." : "Unable to assign users to the selected report.", s = i.success ? "success" : "error"; showSwal(s, a) } else showSwal("error", "Please select a valid report before assigning users.") } async function saveReport() { try { $("#loader").show(); let e = document.getElementById("reportName").value; if (!e) { showSwal("error", "Please Select Report from dropdown!"); return } let t = collectFiltersData2(), i = { reportName: e, reportType: document.getElementById("aggFunc").value, groupBy: document.getElementById("ddlKPIsGroupBy").value, dropDownDateRanges: document.getElementById("dropDownDateRanges").value, dropDownYearRanges: document.getElementById("dropDownYearRanges").value, preJoin: document.getElementById("preJoinsDropdown").value, reportId: "--" === document.getElementById("reportNamesDropdown").value ? 0 : parseInt(document.getElementById("reportNamesDropdown").value), reportOn: "--" === document.getElementById("reportTypeOnDropdown").value ? 0 : parseInt(document.getElementById("reportTypeOnDropdown").value), filters: JSON.stringify(t) }; var a = !1; let s = document.getElementById("initialReportName").value; document.getElementById("reportName").value !== s && "--" === document.getElementById("reportNamesDropdown").value && (i.reportId = 0, a = !0); let r = await saveReportToApi(i); if (assignUsers(r), get_reports_list = [], r) { if (t) { let l = await saveFiltersToApi(r, t); if (!l.success) return } let n = retrieveJoinData(r); if (n.length > 0) { let o = await saveJoinsToApi(n); if (!o.success) return } let c = collectKPIData(); if (c.length > 0) { let u = c.map(e => e.kpiId), p = await saveKPIsToApi(r, c); if (!p) return; let D = p.kpiIds, d = a ? [...u, ...D] : D; a || (reportIdSelected = 0); let m = await updateReportKPIRelationship(r, { success: !0, kpiIds: d }, reportIdSelected); if (!m.success) return } showSwal("success", "Report saved successfully!") } var y = document.getElementById("reportNamesDropdown"); y.innerHTML = "", getReports(e => { for (var t in e) { var i = e[t]; get_reports_list = i; for (var a = 0; a < i.length; a++)typeReports.push(i[a]), (option_select = document.createElement("option")).text = i[a].Item1, option_select.value = i[a].Item2, y.add(option_select) } $("#reportNamesDropdown").val(r).change() }) } catch (k) { $("#loader").hide() } finally { $("#loader").hide() } } function loadGridData(e, t) { try { document.getElementById("reportNamesDropdown").value; for (var i, a = [], s = document.getElementById("locationsUser").selectedOptions, r = 0; r < s.length; r++)"--" != s[r].value && a.push(s[r].value); i = document.getElementById("reportTypeOnDropdown").value; var l = document.getElementById("dropDownDateRanges").value; document.getElementById("dropDownYearRanges").value; var n = document.getElementById("preJoinsDropdown").value; let o = retrieveJoinData(i); var c = JSON.stringify(collectDateCriteriaFilters()), u = JSON.stringify(collectCustomersCriteriaFilters()), p = JSON.stringify(collectCancellationCriteriaFilters()), D = JSON.stringify(collectPurchaseCriteriaFilters()), d = JSON.stringify(collectSalesCriteriaFilters()), m = JSON.stringify(collectMarketingCriteriaFilters()), y = JSON.stringify(collectTourCriteriaFilters()), k = JSON.stringify(collectBasicCriteriaFilters()), C = JSON.stringify(collectLeadCriteriaFilters()), P = JSON.stringify({ dateTimeCriteriaObject: c, customerCriteriaObject: u, cancellationCriteriaObject: p, purchasesCriteriaObject: D, salesCriteriaObject: d, marketingCriteriaObject: m, tourCriteriaObject: y, basicCriteriaObject: k, leadCriteriaObject: C }), g = !1; e.length > 0 && (g = !0); var I = JSON.stringify({ avk: n, reportonId: i, dropDownDateRanges: l, kpis: g, preJoin: n, joins: JSON.stringify({ joinData: o }), locationId: JSON.stringify({ locationsUser: a }), filters: P }); $.ajax({ type: "POST", url: "ApiReportsWebService.aspx/getDataForGrid", data: I, contentType: "application/json; charset=utf-8", dataType: "json", success: function (i) { return handleGridDataResponse(i, e, t) }, failure: function (e) { $("#loader").hide() } }) } catch (h) { $("#loader").hide(), showSwal("error", "Data not loaded correctly.") } } function handleGridDataResponse(e, t, i) { let a = JSON.parse(e.d), s = processJoinsBasedOnSelection(a); if (!s) return; let r = combineRowData(s), l = generateColumnDefs(t, s); i && "--" !== i && addGroupColumn(i, a, l); let n = sortOperations(t); processOperations(n, r, s), l = finalizeColumnDefs(t, l, a, n), populateDropdown(l, r), updateGrid(l, r), l = l.filter(e => !n.some(t => !1 === t.Visible && t.columnName === e.headerName)), getAllRowsGrid(gridApi), setTimeout(() => { let e = getVisibleRows(gridApi); calculateMinMaxAndApplyStyles(l, e), gridApi.refreshCells({ force: !0 }), gridApi.setGridOption("columnDefs", l) }, 50), $("#loader").hide(), showSwal("success", "Data loaded successfully.") } function processJoinsBasedOnSelection(e) { try { return reportSelectedFromDropdown ? processJoinsBySavedData(joinDefinitions, e) : processJoins(e) } catch (t) { return $("#loader").hide(), showSwal("error", "Error detected while processing joins."), null } } function combineRowData(e) { try { let t = ["rowData"]; return Array.from(new Set(t.filter(t => Array.isArray(e?.[t])).flatMap(t => e[t]))) } catch (i) { return $("#loader").hide(), showSwal("error", "Error combine row data"), null } } function generateColumnDefs(e, t) { try { let i = e.length > 0 ? e : t.kpiCodesList; return i.map(t => ({ headerName: e.length > 0 ? t.columnName : t.headerText, field: e.length > 0 ? t.columnName : t.pivot, cellClassRules: {} })) } catch (a) { return $("#loader").hide(), showSwal("error", "Error generate column defs"), null } } function addGroupColumn(e, t, i) { try { let a = e.split(".")[1].replace(/^./, e => e.toLowerCase()); if (t?.rowData?.length > 0 && a in t.rowData[0]) { let s = i.find(e => e.field === a) || { headerName: a, field: a, rowGroup: !0 }; i.includes(s) || i.push(s) } } catch (r) { return $("#loader").hide(), showSwal("error", "Error add group column"), null } } function processOperations(e, t, i) { try { let a = {}; for (let s of e) { let { columnName: r, operation: l, condition: n, aggFunc: o } = s; a[r] = 0; let c = l.split(".")[1]?.replace(/^./, e => e.toLowerCase()), u = new Set; for (let p of i.rowData ?? []) { let D = evalCondition(s.condition, p, s); switch (o) { case "count": u.has(p.tourID) || (a[r] += D, p[r] = D, u.add(p.tourID)); break; case "ifelse": let d = regexConditionValue(n, p, D); a[r] += d, p[r] = d; break; case "String": p[r] = p[c]; break; case "derived": if (s.operation && D) { let m = evalDerivedOperation(s.operation, p); -1 != m && (a[r] += m, p[r] = m) } break; default: if (s.operation && D) { let y = evalOperation(s.operation, p); -1 != y && (a[r] += y, p[r] = y) } else a[r] += 0, p[r] = 0 } } } return a } catch (k) { return $("#loader").hide(), showSwal("error", "Error process kpi operations"), null } } function finalizeColumnDefs(e, t, i, a) { try { if (0 === e.length) return configureKPIDefs(t, i); return configureOperationDefs(a, t) } catch (s) { return $("#loader").hide(), showSwal("error", "Error finalize columns defs in grid"), null } } function updateGrid(e, t) { try { gridApi.setGridOption("columnDefs", e), gridApi.setGridOption("rowData", t), updateTotals(e, t) } catch (i) { return $("#loader").hide(), showSwal("error", "Error update grid"), null } } function updateTotals(e, t) { let i = [{}]; e.forEach(e => { if (e.aggFunc && "first" !== e.aggFunc) { let a = t.reduce((t, i) => t + (i[e.field] || 0), 0); i[0][e.field] = isNaN(a) ? "--" : a } else i[0][e.field] = "--" }), gridApi.setGridOption("pinnedTopRowData", i), gridApi.setGridOption("pinnedBottomRowData", i) } function configureKPIDefs(e, t) { return e.map(e => { let i = t.rowData.find(t => e.field in t); if (!i) return null; let a = typeof i[e.field], s = "number" === a ? "sum" : "countNonEmpty"; return { ...e, aggFunc: e => "countNonEmpty" === s ? e.values.filter(e => null != e && "" !== e).length : e.values.reduce((e, t) => e + (t || 0), 0), valueFormatter: e => formatValue(e, s) } }).filter(Boolean) } function configureOperationDefs(e, t) { return e.forEach(e => { let i = t.find(t => t.field === e.columnName); i && configureColumnDef(i, e) }), t } function configureColumnDef(e, t) { e.headerName = t.columnName, e.cellClassRules = {}, e.aggFunc = "String" === t.aggFunc || "ifelse" === t.aggFunc ? "first" : "sum", e.cellClass = "none" !== t.aggFunc && "ifelse" !== t.aggFunc ? "number-cell" : void 0, e.cellRenderer = "none" !== t.aggFunc && "ifelse" !== t.aggFunc ? "agAnimateShowChangeCellRenderer" : void 0, e.valueGetter = "derived" === t.aggFunc ? createValueGetter(t.operation) : void 0, e.valueFormatter = createValueFormatter(t), e.sortable = !0, "derived" === t.aggFunc ? e.comparator = (e, i, a, s) => { let r = e => { let i = allRowsGrid.find(t => t.key === e.key); try { var a = i.data[t.columnName]; return /[%$,]/.test(a) && (a = a.replace(/[%$,]/g, "")), isNaN(a) || !isFinite(a) ? 0 : a } catch (s) { return console.error("Error evaluating the formula:", formula, s), 0 } }, l = r(a), n = r(s); return l - n } : e.comparator = (e, t) => e - t } function createValueGetter(e) { return e.replace(/[\w$%]+/g, e => isNaN(e) ? `getValue("${e}")` : e) } function createValueFormatter(e) { let t = {}; return function (i) { let a = `${i.node.id}-${i.colDef.field}`; if (t[a]) return t[a]; let s = i.value; if (/[%$,]/.test(s) && (s = isNaN(Number(s = s.replace(/[%$,]/g, ""))) ? 0 : Number(s)), s === i.colDef.headerName) return t[a] = "--"; if (null == s) return t[a] = "string" == typeof s ? "" : 0; if ("ifelse" === e.aggFunc) return e.condition.includes(".") || (s = evalConditionValue(e.condition, i)), t[a] = "none" === e.format ? s : ""; if ("String" === e.aggFunc && ("number" != typeof s || isNaN(s))) return t[a] = s || ""; if ("derived" === e.aggFunc && (s = processDerivedOperation(i, s, e)), (isNaN(s) || !isFinite(s)) && ["ifelse", "none"].includes(e.aggFunc)) return i.node.aggData && (i.node.aggData[e.columnName] = "--"), t[a] = "--"; Number.isInteger(s) || (s = parseFloat(s).toFixed(2)); let r = formatByType(s, e.format); return i.node.aggData ? i.node.aggData[e.columnName] = r : i.node.data[e.columnName] = r, t[a] = r } } function getVisibleRows(e) { let t = [], i = e.getDisplayedRowCount(); for (let a = 0; a < i; a++) { let s = e.getDisplayedRowAtIndex(a); s && (s.aggData ? t.push(s.aggData) : s.data && t.push(s.data)) } return t } function getAllRowsGrid(e) { allRowsGrid = []; let t = e.getDisplayedRowCount(); for (let i = 0; i < t; i++) { let a = e.getDisplayedRowAtIndex(i); a && allRowsGrid.push({ key: a.key, data: a.aggData || a.data || null }) } } function calculateMinMaxAndApplyStyles(e, t) { try { e.forEach(e => { if (e.field || e.valueGetter) { let i = t.map(t => { if ("string" == typeof e.valueGetter) return processValueGetterExpression(e, t) || 0; let i = t[e.field] || 0; return i = cleanCellValue(i) }).filter(e => !isNaN(e)); i.length > 0 && (e.minValue = Math.min(...i), e.maxValue = Math.max(...i), e.cellClassRules = { "green-bold"(i) { let a = t[i.rowIndex]?.[e.headerName]; return isValidValue(a) && !i.node.footer && i.node.allLeafChildren && calculateMax(a, e.maxValue) }, "red-bold"(i) { let a = t[i.rowIndex]?.[e.headerName]; return isValidValue(a) && !i.node.footer && i.node.allLeafChildren && calculateMin(a, e.minValue) } }) } }) } catch (i) { return $("#loader").hide(), showSwal("error", "Error calculated max and min data"), null } } function isValidValue(e) { return "string" == typeof e && (e = e.replace(/[%$,]/g, "").trim()), !isNaN(e = Number(e)) } function calculateMax(e, t) { let i = e; return (i = cleanCellValue(i)) === t } function calculateMin(e, t) { let i = e; return (i = cleanCellValue(i)) === t } function cleanCellValue(e) { return "string" == typeof e && (e = e.replace(/[%$,]/g, "").trim()), Number(e) } function processDerivedOperation(params, formattedValue, operation) { let operationString = operation.operation, combinedData = { ...params.node.aggData, ...params.data }; if (Object.keys(combinedData).forEach(e => { let t = combinedData[e]; if ("string" == typeof t) { let i = t.replace(/[%$,]/g, "").trim(); combinedData[e] = isNaN(Number(i)) ? 0 : Number(i), t = combinedData[e] } let a; (a = /[%$,]/.test(e) ? RegExp(`(^|[^a-zA-Z0-9_])${escapeRegExp(e)}(?=$|[^a-zA-Z0-9_])`, "g") : RegExp(`\\b${escapeRegExp(e)}\\b`, "g")).test(operationString) && (operationString = operationString.replace(a, t)) }), /[%$,]/.test(operationString) && (operationString = operationString.replace(/[%$,]/g, "")), operation.operation === operationString) return 0; try { return formattedValue = eval(operationString), isNaN(formattedValue) || !isFinite(formattedValue) ? 0 : formattedValue } catch { return 0 } } function nodeDerivedOperation(combinedData, formattedValue, operation) { let operationString = operation; if (Object.keys(combinedData).forEach(e => { let t = combinedData[e], i; (i = /[%$,]/.test(e) ? RegExp(`(^|[^a-zA-Z0-9_])${escapeRegExp(e)}(?=$|[^a-zA-Z0-9_])`, "g") : RegExp(`\\b${escapeRegExp(e)}\\b`, "g")).test(operationString) && (operationString = operationString.replace(i, t)) }), /[%$,]/.test(operationString) && (operationString = operationString.replace(/[%$,]/g, "")), operation.operation === operationString) return 0; try { return formattedValue = eval(operationString), isNaN(formattedValue) || !isFinite(formattedValue) ? 0 : formattedValue } catch { return 0 } } function escapeRegExp(e) { return e.replace(/[.*+?^${}()|[\]\\]/g, "\\$&").replace(/[%4#]/g, "") } function formatByType(e, t) { return "currency" === t ? "$" + Math.round(e).toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",") : "percentage" === t ? Math.round(1 > Math.abs(e) ? 100 * e : e) + "%" : e } function formatByTypeColumn(e, t) { return "currency" === t ? Math.round(e).toString().replace(/\B(?=(\d{3})+(?!\d))/g, "") : "percentage" === t ? Math.round(1 > Math.abs(e) ? 100 * e : e) : e } function loadDataGrid(e) { let t = Object.keys(e[0]).map(e => ({ headerName: e, field: e })); var i = document.getElementById("ddlKPIsGroupBy"); i.innerHTML = ""; for (var a = 0; a < t.length; a++) { var s = document.createElement("option"); s.text = t[a].headerName, s.value = t[a].headerName, i.add(s) } populateDropdown(t, e), gridApi.setGridOption("columnDefs", t), gridApi.setGridOption("rowData", e) } function readLocalFile() { let e = document.getElementById("fileInputData"), t = e.files[0], i = new FileReader; i.onload = function (e) { let i = getFileType(t), a; "json" === i ? a = JSON.parse(e.target.result) : "csv" === i && (a = csvJSON(e.target.result)), loadDataGrid(a) }, i.readAsText(t), $("#loader").hide() } function getKPIsDataByReportId(e) { var t, i; i = JSON.stringify({ avk: t = document.getElementById("preJoinsDropdown").value }), kpi_get_report = [], $.ajax({ type: "POST", url: "ApiReportsWebService.aspx/getKPIsDataByReportId", contentType: "application/json; charset=utf-8", data: i, dataType: "json", success: function (t) { var i = JSON.parse(t.d); kpi_get_report.push(...i.kpiCodesList), e(i) }, error: function (e) { } }) } function getReports(e) { $.ajax({ type: "POST", url: "ApiReportsWebService.aspx/getReports", contentType: "application/json; charset=utf-8", dataType: "json", success: function (t) { e(JSON.parse(t.d)) }, error: function (e) { } }) } function getLocations(e) { $.ajax({ type: "POST", url: "ApiReportsWebService.aspx/getLocations", contentType: "application/json; charset=utf-8", dataType: "json", success: function (t) { e(JSON.parse(t.d)) }, error: function (e) { } }) } function getReportsOnType(e) { $.ajax({ type: "POST", url: "ApiReportsWebService.aspx/getReportsOnType", contentType: "application/json; charset=utf-8", dataType: "json", success: function (t) { e(JSON.parse(t.d)) }, error: function (e) { } }) } function getDateRange(e) { $.ajax({ type: "POST", url: "ApiReportsWebService.aspx/getDateRange", contentType: "application/json; charset=utf-8", dataType: "json", success: function (t) { e(JSON.parse(t.d)) }, error: function (e) { } }) } async function UnAssignSelectedUserId(e) { try { let t = request_path + "unAssignReportUser"; var i = selectedReportId; let a = JSON.stringify({ ReportId: i, UserId: e }), s = await fetch(t, { method: "POST", headers: { "Content-Type": "application/json", "Cache-Control": "no-cache, no-store, must-revalidate", Pragma: "no-cache", Expires: "0" }, body: a }); s.ok ? showSwal("success", "User unassigned from report successfully") : showSwal("error", "Failed to unassign user from report"); let r = await s.json(); return r } catch (l) { throw showSwal("error", "Error unassigning user from report"), l } } async function UnAssignReportKpiId(e) { try { let t = request_path + "unAssignKpiReport"; var i = selectedReportId; let a = JSON.stringify({ ReportId: i, KpiId: e }), s = await fetch(t, { method: "POST", headers: { "Content-Type": "application/json", "Cache-Control": "no-cache, no-store, must-revalidate", Pragma: "no-cache", Expires: "0" }, body: a }); s.ok ? showSwal("success", "KPI unassigned from report successfully") : showSwal("error", "Failed to unassign kpi from report"); let r = await s.json(); return r } catch (l) { throw showSwal("error", "Error unassigning KPI from report"), l } } async function UnAssignJoinReport(e) { try { let t = request_path + "unAssignJoinReport", i = JSON.stringify({ JoinId: e }), a = await fetch(t, { method: "POST", headers: { "Content-Type": "application/json", "Cache-Control": "no-cache, no-store, must-revalidate", Pragma: "no-cache", Expires: "0" }, body: i }); a.ok ? showSwal("success", "Join unassigned from report successfully") : showSwal("error", "Failed to unassign join from report"); let s = await a.json(); return s } catch (r) { throw showSwal("error", "Error unassigning join from report"), r } } async function unAssignReport(e) { try { let t = request_path + "unAssignReport", i = JSON.stringify({ ReportId: e }), a = await fetch(t, { method: "POST", headers: { "Content-Type": "application/json", "Cache-Control": "no-cache, no-store, must-revalidate", Pragma: "no-cache", Expires: "0" }, body: i }); if (a.ok) { showSwal("success", "Report unassigned successfully"), gridApi.setGridOption("rowData", []), gridApi.setGridOption("columnDefs", []), gridApi.setGridOption("pinnedTopRowData", []), gridApi.setGridOption("pinnedBottomRowData", []); var s = document.querySelectorAll(".join-inputs-container"); joinCounter = 1, s.forEach(function (e) { "joinContainer1" !== e.id ? e.parentNode.removeChild(e) : (document.getElementById("joinSelect1").selectedIndex = 0, document.getElementById("firstJoinKey_1").value = "", document.getElementById("secondJoinKey_1").value = "", document.getElementById("uniqueKey_1").value = "") }); let r = document.getElementById("accordion"); r.innerHTML = "", $("#columnName").val(""); let l = document.getElementById("reportTypeOnDropdown"); l.selectedIndex = 0, $("#reportName").val(""); let n = document.getElementById("preJoinsDropdown"); n.selectedIndex = 0, await clearDateTimeCriterial(), await clearCancellationCriteriaFilters(), await clearCustomersCriteriaFilters(), await clearBasicCriterial(), await clearMarketingCriteriaFilters(), await clearPurchaseCriteriaFilters(), await clearSalesCriteriaFilters(), await clearTourCriteriaFilters(), await clearLeadCriteriaFilters(); let o = document.getElementById("btnDeleteReport"); o.style.display = "none"; var c = document.getElementById("reportNamesDropdown"); c.innerHTML = "", getReports(e => { for (var t in e) for (var i = e[t], a = 0; a < i.length; a++) { typeReports.push(i[a]); var s = document.createElement("option"); s.text = i[a].Item1, "" != i[a].Item2 ? s.value = i[a].Item2 : s.value = i[a].Item6, c.add(s) } }) } else showSwal("error", "Failed to unassign report"); let u = await a.json(); return u } catch (p) { throw showSwal("error", "Error unassigning report"), p } } async function fetchReportById(e) { try { let t = request_path + `getReportById/${e}`, i = await fetch(t, { method: "GET", headers: { "Cache-Control": "no-cache, no-store, must-revalidate", Pragma: "no-cache", Expires: "0" } }); if (!i.ok) throw Error(`Failed to fetch report with ID ${e}: ${i.statusText}`); let a = await i.json(); return a } catch (s) { throw $("#loader").hide(), console.error(`Error fetching report by ID ${e}:`, s), s } } async function fetchReportFiltersById(e) { try { let t = request_path + `getFilters/${e}`, i = await fetch(t, { method: "GET", headers: { "Cache-Control": "no-cache, no-store, must-revalidate", Pragma: "no-cache", Expires: "0" } }); if (!i.ok) throw Error(`Failed to fetch report filters with ID ${e}`); let a = await i.json(); return a } catch (s) { $("#loader").hide(), console.error(s) } } async function fetchReportUsersById(e) { try { let t = request_path + `getSavedUserByReportId/${e}`, i = await fetch(t, { method: "GET", headers: { "Cache-Control": "no-cache, no-store, must-revalidate", Pragma: "no-cache", Expires: "0" } }); if (!i.ok) throw Error(`Failed to fetch report with ID ${e}`); let a = await i.json(); checkUsersInList(a) } catch (s) { $("#loader").hide(), console.error(s) } } async function fetchJoinDefinitions(e) { try { let t = request_path + `getJoinDefinitions/${e}`, i = await fetch(t, { method: "GET", headers: { "Cache-Control": "no-cache, no-store, must-revalidate", Pragma: "no-cache", Expires: "0" } }); if (!i.ok) throw Error("Failed to fetch join definitions"); let a = await i.json(); return a } catch (s) { throw s } } async function fetchReportKPIRelationship(e) { try { let t = request_path + `getReportKPIRelationship/${e}`, i = await fetch(t, { method: "GET", headers: { "Cache-Control": "no-cache, no-store, must-revalidate", Pragma: "no-cache", Expires: "0" } }); if (!i.ok) throw Error("Failed to fetch ReportKPIRelationship"); let a = await i.json(); return a } catch (s) { throw s } } async function fetchKPIs(e) { try { let t = request_path + `getKPIs?reportId=${e}`, i = await fetch(t, { method: "GET", headers: { "Cache-Control": "no-cache, no-store, must-revalidate", Pragma: "no-cache", Expires: "0" } }); if (!i.ok) throw Error("Error fetching KPIs"); let a = await i.json(); return a } catch (s) { throw s } } async function saveReportUsers(e, t) { try { let i = request_path + "saveReportUsers", a = JSON.stringify({ ReportId: e, UserIds: t }), s = await fetch(i, { method: "POST", headers: { "Content-Type": "application/json", "Cache-Control": "no-cache, no-store, must-revalidate", Pragma: "no-cache", Expires: "0" }, body: a }); if (!s.ok) throw Error("Failed to save data into ReportUser"); let r = await s.json(); return r } catch (l) { throw l } } async function saveReportToApi(e) { try { let t = request_path + "saveReport", i = await fetch(t, { method: "POST", headers: { "Content-Type": "application/json", "Cache-Control": "no-cache, no-store, must-revalidate", Pragma: "no-cache", Expires: "0" }, body: JSON.stringify(e) }); if (!i.ok) throw Error("Error saving report"); let a = await i.json(); return a.reportId } catch (s) { throw s } } async function saveFiltersToApi(e, t) { try { let i = request_path + "saveFilters", a = await fetch(i, { method: "POST", headers: { "Content-Type": "application/json", "Cache-Control": "no-cache, no-store, must-revalidate", Pragma: "no-cache", Expires: "0" }, body: JSON.stringify({ reportId: e, filters: JSON.stringify(t) }) }); if (!a.ok) throw Error("Error saving filters to the API"); let s = await a.json(); return s } catch (r) { throw r } } async function saveJoinsToApi(e) { try { let t = request_path + "saveJoins", i = await fetch(t, { method: "POST", headers: { "Content-Type": "application/json", "Cache-Control": "no-cache, no-store, must-revalidate", Pragma: "no-cache", Expires: "0" }, body: JSON.stringify(e) }); if (!i.ok) throw Error("Error saving joins to the API"); let a = await i.json(); return a } catch (s) { throw s } } async function saveReportsToApi(e) { try { let t = request_path + "saveJoins", i = await fetch(t, { method: "POST", headers: { "Content-Type": "application/json", "Cache-Control": "no-cache, no-store, must-revalidate", Pragma: "no-cache", Expires: "0" }, body: JSON.stringify(e) }); if (!i.ok) throw Error("Error saving joins to the API"); let a = await i.json(); return a } catch (s) { throw s } } async function updateReportWithJoinId(e, t) { try { let i = request_path + "updateReportWithJoinId", a = { reportId: e, joinIds: t }, s = await fetch(i, { method: "POST", headers: { "Content-Type": "application/json", "Cache-Control": "no-cache, no-store, must-revalidate", Pragma: "no-cache", Expires: "0" }, body: JSON.stringify(a) }); if (!s.ok) throw Error("Error updating report with join IDs"); let r = await s.json(); return r } catch (l) { throw l } } async function saveKPIsToApi(e, t) { try { let i = request_path + "saveKPIs", a = await fetch(i, { method: "POST", headers: { "Content-Type": "application/json", "Cache-Control": "no-cache, no-store, must-revalidate", Pragma: "no-cache", Expires: "0" }, body: JSON.stringify({ reportId: e, kpiData: t }) }); if (!a.ok) throw Error("Error saving KPIs"); let s = await a.json(); return s } catch (r) { throw r } } async function updateReportKPIRelationship(e, t, i) { try { let a = request_path + "updateReportKPIRelationship", s = { reportId: e, kpiIds: t, reportIdOld: i }, r = await fetch(a, { method: "POST", headers: { "Content-Type": "application/json", "Cache-Control": "no-cache, no-store, must-revalidate", Pragma: "no-cache", Expires: "0" }, body: JSON.stringify(s) }); if (!r.ok) throw Error("Error updating ReportKPIRelationship"); let l = await r.json(); return l } catch (n) { throw n } } async function fetchUserDataByUserName(e) { try { let t = request_path + `getUserData/${e}`, i = await fetch(t, { method: "GET", headers: { "Cache-Control": "no-cache, no-store, must-revalidate", Pragma: "no-cache", Expires: "0" } }); if (!i.ok) throw Error(`Failed to fetch user data for ${e}`); let a = await i.json(); return a } catch (s) { $("#loader").hide(), console.error(s) } } function collectKPIData() { let e = [], t = document.querySelectorAll(".header-container"); for (let i of t) { let a = i.getAttribute("data-column-name"); if (null != a) { let s = parseInt(i.getAttribute("data-id")), r = i.querySelector(".operation") ? i.querySelector(".operation").value : null, l = i.querySelector(".condition") ? i.querySelector(".condition").value : null, n = i.nextElementSibling ? i.nextElementSibling.querySelector(".mathFormat") : null, o = n ? n.value : null, c = i.querySelector("h4"), u = c ? c.textContent : "", p = u.match(/ - (.+)/), D = p ? p[1] : "", d = { columnName: a, operation: r, condition: l, aggFunc: D, kpiId: s, typeFormat: o }; e.push(d) } } return e } function collectFiltersData2() { var e = collectDateCriteriaFilters(), t = collectLocationFilters(), i = collectCustomersCriteriaFilters2(), a = collectCancellationCriteriaFilters2(), s = collectPurchaseCriteriaFilters2(), r = collectSalesCriteriaFilters2(), l = collectMarketingCriteriaFilters2(), n = collectTourCriteriaFilters2(), o = collectBasicCriteriaFilters2(), c = collectLeadCriteriaFilters2(); let u = { dateCriteriaFilters: e, loctionFilters: t, customersCriteriaFilters: i, cancellationFilters: a, purchaseFilters: s, salesFilters: r, marketingFilters: l, tourFilters: n, basicFilters: o, leadFilters: c }; return u } function collectFiltersData() { var e = collectDateCriteriaFilters(), t = collectLocationFilters(), i = collectCustomersCriteriaFilters(), a = collectCancellationCriteriaFilters(), s = collectPurchaseCriteriaFilters(), r = collectSalesCriteriaFilters(), l = collectMarketingCriteriaFilters(), n = collectTourCriteriaFilters(), o = collectBasicCriteriaFilters(), c = collectLeadCriteriaFilters(); let u = { dateCriteriaFilters: e, loctionFilters: t, customersCriteriaFilters: i, cancellationFilters: a, purchaseFilters: s, salesFilters: r, marketingFilters: l, tourFilters: n, basicFilters: o, leadFilters: c }; return u } function collectLocationFilters() { for (var e = [], t = document.getElementById("locationsUser").selectedOptions, i = 0; i < t.length; i++)e.push(t[i].value); let a = { locationsUser: e }; return a } function collectDateCriteriaFilters() { var e = document.getElementById("dropDownDateTypeLogic").value, t = document.getElementById("ctl00_c1_DateTimeCriteriaDetails_dropDownDaysOfWeek_Input").value, i = document.getElementById("ctl00_c1_DateTimeCriteriaDetails_dropDownWeeks_Input").value, a = document.getElementById("ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_Input").value, s = document.getElementById("ctl00_c1_DateTimeCriteriaDetails_dropDownYears_Input").value, r = document.getElementById("ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_Input").value, l = document.getElementById("ctl00_c1_DateTimeCriteriaDetails_endDateRadDatePicker_dateInput").value, n = document.getElementById("ctl00_c1_DateTimeCriteriaDetails_startDateRadDatePicker_dateInput").value, o = parseInt(document.getElementById("dropDownDateRanges").value), c = parseInt(document.getElementById("dropDownYearRanges").value), u = document.getElementById("dropDownTierOneDateTypes").value, p = document.getElementById("ctl00_c1_DateTimeCriteriaDetails_dropDownTierOneDateTypesMultiSelect_Input").value; let D = { dateTypeLogic: e, daysOfWeek: t, weeks: i, months: a, years: s, times: r, endDate: l, startDate: n, dateRange: o, yearRange: c, tierOneDateTypes: u, multipleDateTypes: p }; return D } function collectBasicCriteriaFilters() { var e = getElementValue("customerIDTextBox"), t = getElementValue("externalCustomerIDTextBox"), i = getElementValue("externalLeadIDTextBox"), a = getElementValue("tourIDTextBox"), s = getElementValue("externalTourIDTextBox"), r = getElementValue("dropDownPurchasesCount"), l = getElementValue("purchaseIDTextBox"), n = getElementValue("externalPurchaseIDTextBox"), o = getElementValue("textBoxNotes"), c = getParsedElementValue("ctl00_c1_BasicCriteria1_dropDownUser_ClientState"), u = getElementValue("textBoxImportNumber"); let p = { customerId: e, extId: t, extLeadId: i, tourId: a, extTourId: s, purchase: r, purchaseId: l, extPurchaseId: n, notes: o, user: c, importNmbr: u }; return p } function collectCustomersCriteriaFilters() { var e = getParsedElementValue("ctl00_c1_CustomersCriteriaDetails_customerDispositionIDManyPickLists_manyPickListsDropDown_ClientState"), t = getElementValue("textBoxName"), i = getElementValue("dropDownSex"), a = getParsedElementValue("ctl00_c1_CustomersCriteriaDetails_customersPickList1ItemIDManyPickLists_manyPickListsDropDown_ClientState"), s = getParsedElementValue("ctl00_c1_CustomersCriteriaDetails_guestTypeIDManyPickLists_manyPickListsDropDown_ClientState"), r = getElementValue("dropDownGuestSex"), l = getParsedElementValue("ctl00_c1_CustomersCriteriaDetails_customersPickList2ItemIDManyPickLists_manyPickListsDropDown_ClientState"), n = getParsedElementValue("ctl00_c1_CustomersCriteriaDetails_incomeTypeIDManyPickLists_manyPickListsDropDown_ClientState"), o = getParsedElementValue("ctl00_c1_CustomersCriteriaDetails_customersPickList3ItemIDManyPickLists_manyPickListsDropDown_ClientState"), c = getElementValue("dropDownCustomersDecimal1"), u = getElementValue("dropDownCustomersBool1"), p = getElementValue("textBoxPhone"), D = getElementValue("TextBoxStreetAddress"), d = getElementValue("TextBoxStreetAddress2"), m = getElementValue("textBoxCity"), y = getParsedElementValue("ctl00_c1_CustomersCriteriaDetails_stateIDManyPickLists_manyPickListsDropDown_ClientState"), k = getElementValue("textBoxZipcode"), C = getParsedElementValue("ctl00_c1_CustomersCriteriaDetails_countryIDManyPickLists_manyPickListsDropDown_ClientState"), P = getElementValue("TextBoxBusiness"), g = getElementValue("TextBoxEmail"), I = getParsedElementValue("ctl00_c1_CustomersCriteriaDetails_customersPickList4ItemIDManyPickLists_manyPickListsDropDown_ClientState"), h = getElementValue("dropDownCustomersBool2"), w = getElementValue("TextBoxCustomersText1"), f = getElementValue("TextBoxCustomersText2"), _ = getElementValue("TextBoxCustomersText3"); let L = { customerDisposition: e, name: t, sex: i, customersPickList1Item: a, guestType: s, guestSex: r, customersPickList2Item: l, incomeType: n, customersPickList3Item: o, customersDecimal1: c, customersBool1: u, phone: p, streetAddress: D, streetAddress2: d, city: m, state: y, zipcode: k, country: C, businessName: P, email: g, customersPickList4Item: I, customersBool2: h, customersText: w, customersText2: f, customersText3: _ }; return L } function collectCancellationCriteriaFilters() { var e = getParsedElementValue("ctl00_c1_CancellationCriteriaDetails_dropDownCancellationReasonTypes_ClientState"), t = getParsedElementValue("ctl00_c1_CancellationCriteriaDetails_dropDownCancellationReceivedTypes_ClientState"), i = getElementValue("dropDownWithinRescission"), a = getParsedElementValue("ctl00_c1_CancellationCriteriaDetails_dropDownCancellationDispositionTypes_ClientState"), s = getParsedElementValue("ctl00_c1_CancellationCriteriaDetails_dropDownCancellationStatusTypes_ClientState"), r = getParsedElementValue("ctl00_c1_CancellationCriteriaDetails_dropDownCancellationUser_manyPickListsDropDown_ClientState"); let l = { cxlRequestReason: e, cxlRequestReceived: t, withinRescission: i, cxlRequestDisposition: a, cxlRequestStatus: s, cxlRequestUser: r }; return l } function collectPurchaseCriteriaFilters() { var e = getParsedElementValue("ctl00_c1_PurchasessCriteriaDetails_productCategoryIDManyPickLists_manyPickListsDropDown_ClientState"), t = getParsedElementValue("ctl00_c1_PurchasessCriteriaDetails_productIDManyPickLists_manyPickListsDropDown_ClientState"), i = getParsedElementValue("ctl00_c1_PurchasessCriteriaDetails_subProductIDManyPickLists_manyPickListsDropDown_ClientState"), a = getElementValue("dropDownSaleAmount"), s = getElementValue("dropDownDownPaymentAmount"), r = getElementValue("dropDownDownFees1Amount"), l = getElementValue("dropDownDownFees2Amount"), n = getElementValue("dropDownDownFees3Amount"), o = getElementValue("dropDownDownFees4Amount"), c = getElementValue("dropDownDownFees5Amount"), u = getParsedElementValue("ctl00_c1_PurchasessCriteriaDetails_dropDownSaleTypes_ClientState"), p = getParsedElementValue("ctl00_c1_PurchasessCriteriaDetails_dropDownSaleStatusTypes_ClientState"), D = getElementValue("milestoneStatesDropDown"), d = getParsedElementValue("ctl00_c1_PurchasessCriteriaDetails_saleDispositionIDManyPickLists_manyPickListsDropDown_ClientState"), m = getParsedElementValue("ctl00_c1_PurchasessCriteriaDetails_purchasesPickList1ItemIDManyPickLists_manyPickListsDropDown_ClientState"), y = getParsedElementValue("ctl00_c1_PurchasessCriteriaDetails_purchasesPickList2ItemIDManyPickLists_manyPickListsDropDown_ClientState"), k = getParsedElementValue("ctl00_c1_PurchasessCriteriaDetails_purchasesPickList3ItemIDManyPickLists_manyPickListsDropDown_ClientState"), C = getElementValue("textBoxPurchasesText1"), P = getElementValue("textBoxPurchasesText2"), g = getElementValue("dropDownPurchasesBool1"), I = getElementValue("dropDownPurchasesBool2"); let h = { productCategory: e, product: t, subProduct: i, saleAmount: a, downPaymentAmount: s, fees1: r, fees2: l, fees3: n, fees4: o, proposalValue: c, saleTypes: u, saleStatus: p, milestones: D, saleDisposition: d, purchasesPickList1Item: m, purchasesPickList2Item: y, purchasesPickList3Item: k, purchasesText1: C, purchasesText2: P, purchasesBool1: g, purchasesBool2: I }; return h } function collectSalesCriteriaFilters() { var e = getParsedElementValue("ctl00_c1_SalesCriteriaDetails_salesTeamIDManyPickLists_manyPickListsDropDown_ClientState"), t = getParsedElementValue("ctl00_c1_SalesCriteriaDetails_podiumIDManyPickLists_manyPickListsDropDown_ClientState"), i = getParsedElementValue("ctl00_c1_SalesCriteriaDetails_salesRepIDManyPickLists_manyPickListsDropDown_ClientState"), a = getParsedElementValue("ctl00_c1_SalesCriteriaDetails_salesCloser1IDManyPickLists_manyPickListsDropDown_ClientState"), s = getParsedElementValue("ctl00_c1_SalesCriteriaDetails_salesCloser2IDManyPickLists_manyPickListsDropDown_ClientState"), r = getParsedElementValue("ctl00_c1_SalesCriteriaDetails_exitIDManyPickLists_manyPickListsDropDown_ClientState"), l = getParsedElementValue("ctl00_c1_SalesCriteriaDetails_verificationOfficerIDManyPickLists_manyPickListsDropDown_ClientState"), n = getParsedElementValue("ctl00_c1_SalesCriteriaDetails_salesPickList1ItemIDManyPickLists_manyPickListsDropDown_ClientState"), o = getElementValue("textBoxSalesText1"), c = getElementValue("dropDownSalesDecimal1"), u = getElementValue("dropDownSalesBool1"), p = getParsedElementValue("ctl00_c1_SalesCriteriaDetails_exitTeamIDManyPickLists_manyPickListsDropDown_ClientState"), D = getParsedElementValue("ctl00_c1_SalesCriteriaDetails_exitRep1IDManyPickLists_manyPickListsDropDown_ClientState"), d = getParsedElementValue("ctl00_c1_SalesCriteriaDetails_exitRep2IDManyPickLists_manyPickListsDropDown_ClientState"), m = getParsedElementValue("ctl00_c1_SalesCriteriaDetails_exitRep3IDManyPickLists_manyPickListsDropDown_ClientState"); let y = { salesTeam: e, podium: t, salesAgent: i, salesCloser1: a, salesCloser2: s, extRep: r, verificationOfficer: l, salesPickList1Item: n, salesText: o, salesDecimal: c, salesBool: u, salesExitTeam: p, extRep1: D, extRep2: d, extRep3: m }; return y } function collectMarketingCriteriaFilters() { var e = getParsedElementValue("ctl00_c1_MarketingCriteriaDetails_marketingTeamIDManyPickLists_manyPickListsDropDown_ClientState"), t = getParsedElementValue("ctl00_c1_MarketingCriteriaDetails_marketingAgentIDManyPickLists_manyPickListsDropDown_ClientState"), i = getParsedElementValue("ctl00_c1_MarketingCriteriaDetails_marketingCloserIDManyPickLists_manyPickListsDropDown_ClientState"), a = getParsedElementValue("ctl00_c1_MarketingCriteriaDetails_confirmerIDManyPickLists_manyPickListsDropDown_ClientState"), s = getParsedElementValue("ctl00_c1_MarketingCriteriaDetails_resetterIDManyPickLists_manyPickListsDropDown_ClientState"), r = getParsedElementValue("ctl00_c1_MarketingCriteriaDetails_venueIDManyPickLists_manyPickListsDropDown_ClientState"), l = getParsedElementValue("ctl00_c1_MarketingCriteriaDetails_campaignIDManyPickLists_manyPickListsDropDown_ClientState"), n = getParsedElementValue("ctl00_c1_MarketingCriteriaDetails_channelIDManyPickLists_manyPickListsDropDown_ClientState"), o = getParsedElementValue("ctl00_c1_MarketingCriteriaDetails_hotelIDManyPickLists_manyPickListsDropDown_ClientState"), c = getParsedElementValue("ctl00_c1_MarketingCriteriaDetails_giftIDsManyPickLists_manyPickListsDropDown_ClientState"), u = getParsedElementValue("ctl00_c1_MarketingCriteriaDetails_marketingPickList1ItemIDManyPickLists_manyPickListsDropDown_ClientState"), p = getParsedElementValue("ctl00_c1_MarketingCriteriaDetails_marketingPickList2ItemIDManyPickLists_manyPickListsDropDown_ClientState"), D = getElementValue("textBoxMarketingText1"), d = getElementValue("dropDownMarketingBool1"), m = getElementValue("dropDownDepositAmount"), y = getElementValue("dropDownDepositRefundable"); let k = { marketingTeam: e, marketingAgent: t, marketingCloser: i, confirmer: a, resetter: s, venue: r, campaign: l, channel: n, hotel: o, giftIDs: c, marketingPickList1Item1: u, marketingPickList1Item2: p, marketingText: D, marketingBool: d, depositAmount: m, depositRefundable: y }; return k } function collectTourCriteriaFilters() { var e = getParsedElementValue("ctl00_c1_TourCriteriaDetails_tourSourceIDManyPickLists_manyPickListsDropDown_ClientState"), t = getParsedElementValue("ctl00_c1_TourCriteriaDetails_tourTypeIDManyPickLists_manyPickListsDropDown_ClientState"), i = document.getElementById("ctl00_c1_TourCriteriaDetails_dropDownTourStatusTypes_Input").value, a = getParsedElementValue("ctl00_c1_TourCriteriaDetails_tourConcernTypeIDManyPickLists_manyPickListsDropDown_ClientState"), s = getParsedElementValue("ctl00_c1_TourCriteriaDetails_tourTypePickList1ItemIDManyPickLists_manyPickListsDropDown_ClientState"), r = getParsedElementValue("ctl00_c1_TourCriteriaDetails_locationIDManyPickLists_manyPickListsDropDown_ClientState"), l = getParsedElementValue("ctl00_c1_TourCriteriaDetails_regionIDManyPickLists_manyPickListsDropDown_ClientState"), n = null; null != document.getElementById("dropDownTourBool1") && (n = document.getElementById("dropDownTourBool1").value); var o = getParsedElementValue("ctl00_c1_TourCriteriaDetails_toursManyPickList1ItemIDsManyPickLists_manyPickListsDropDown_ClientState"); let c = { tourSource: e, office: t, tourStatus: i, tourDisposition: a, importMethod: s, businessPartner: r, businessGroup: l, isShow: n, saleBonusGifts: o }; return c } function collectLeadCriteriaFilters() { var e = getParsedElementValue("ctl00_c1_LeadCriteria_leadSourceIDManyPickLists_manyPickListsDropDown_ClientState"), t = getParsedElementValue("ctl00_c1_LeadCriteria_leadDispositionIDManyPickLists_manyPickListsDropDown_ClientState"), i = getParsedElementValue("ctl00_c1_LeadCriteria_leadPickList1ItemIDManyPickLists_manyPickListsDropDown_ClientState"), a = getParsedElementValue("ctl00_c1_LeadCriteria_leadPickList2ItemIDManyPickLists_manyPickListsDropDown_ClientState"); let s = { leadSourceFile: e, leadDisposition: t, calendarName: i, leadSource: a }; return s } function collectCustomersCriteriaFilters2() { var e = getParsedElementJson("ctl00_c1_CustomersCriteriaDetails_customerDispositionIDManyPickLists_manyPickListsDropDown_ClientState"), t = getElementValue("textBoxName"), i = getElementValue("dropDownSex"), a = getParsedElementJson("ctl00_c1_CustomersCriteriaDetails_customersPickList1ItemIDManyPickLists_manyPickListsDropDown_ClientState"), s = getParsedElementJson("ctl00_c1_CustomersCriteriaDetails_guestTypeIDManyPickLists_manyPickListsDropDown_ClientState"), r = getElementValue("dropDownGuestSex"), l = getParsedElementJson("ctl00_c1_CustomersCriteriaDetails_customersPickList2ItemIDManyPickLists_manyPickListsDropDown_ClientState"), n = getParsedElementJson("ctl00_c1_CustomersCriteriaDetails_incomeTypeIDManyPickLists_manyPickListsDropDown_ClientState"), o = getParsedElementJson("ctl00_c1_CustomersCriteriaDetails_customersPickList3ItemIDManyPickLists_manyPickListsDropDown_ClientState"), c = getElementValue("dropDownCustomersDecimal1"), u = getElementValue("dropDownCustomersBool1"), p = getElementValue("textBoxPhone"), D = getElementValue("TextBoxStreetAddress"), d = getElementValue("TextBoxStreetAddress2"), m = getElementValue("textBoxCity"), y = getParsedElementJson("ctl00_c1_CustomersCriteriaDetails_stateIDManyPickLists_manyPickListsDropDown_ClientState"), k = getElementValue("textBoxZipcode"), C = getParsedElementJson("ctl00_c1_CustomersCriteriaDetails_countryIDManyPickLists_manyPickListsDropDown_ClientState"), P = getElementValue("TextBoxBusiness"), g = getElementValue("TextBoxEmail"), I = getParsedElementJson("ctl00_c1_CustomersCriteriaDetails_customersPickList4ItemIDManyPickLists_manyPickListsDropDown_ClientState"), h = getElementValue("dropDownCustomersBool2"), w = getElementValue("TextBoxCustomersText1"), f = getElementValue("TextBoxCustomersText2"), _ = getElementValue("TextBoxCustomersText3"); let L = { customerDisposition: e, name: t, sex: i, customersPickList1Item: a, guestType: s, guestSex: r, customersPickList2Item: l, incomeType: n, customersPickList3Item: o, customersDecimal1: c, customersBool1: u, phone: p, streetAddress: D, streetAddress2: d, city: m, state: y, zipcode: k, country: C, businessName: P, email: g, customersPickList4Item: I, customersBool2: h, customersText: w, customersText2: f, customersText3: _ }; return L } function collectBasicCriteriaFilters2() { var e = getElementValue("customerIDTextBox"), t = getElementValue("externalCustomerIDTextBox"), i = getElementValue("externalLeadIDTextBox"), a = getElementValue("tourIDTextBox"), s = getElementValue("externalTourIDTextBox"), r = getElementValue("dropDownPurchasesCount"), l = getElementValue("purchaseIDTextBox"), n = getElementValue("externalPurchaseIDTextBox"), o = getElementValue("textBoxNotes"), c = getParsedElementJson("ctl00_c1_BasicCriteria1_dropDownUser_ClientState"), u = getElementValue("textBoxImportNumber"); let p = { customerId: e, extId: t, extLeadId: i, tourId: a, extTourId: s, purchase: r, purchaseId: l, extPurchaseId: n, notes: o, user: c, importNmbr: u }; return p } function collectCancellationCriteriaFilters2() { var e = getParsedElementJson("ctl00_c1_CancellationCriteriaDetails_dropDownCancellationReasonTypes_ClientState"), t = getParsedElementJson("ctl00_c1_CancellationCriteriaDetails_dropDownCancellationReceivedTypes_ClientState"), i = getElementValue("dropDownWithinRescission"), a = getParsedElementJson("ctl00_c1_CancellationCriteriaDetails_dropDownCancellationDispositionTypes_ClientState"), s = getParsedElementJson("ctl00_c1_CancellationCriteriaDetails_dropDownCancellationStatusTypes_ClientState"), r = getParsedElementJson("ctl00_c1_CancellationCriteriaDetails_dropDownCancellationUser_manyPickListsDropDown_ClientState"); let l = { cxlRequestReason: e, cxlRequestReceived: t, withinRescission: i, cxlRequestDisposition: a, cxlRequestStatus: s, cxlRequestUser: r }; return l } function collectPurchaseCriteriaFilters2() { var e = getParsedElementJson("ctl00_c1_PurchasessCriteriaDetails_productCategoryIDManyPickLists_manyPickListsDropDown_ClientState"), t = getParsedElementJson("ctl00_c1_PurchasessCriteriaDetails_productIDManyPickLists_manyPickListsDropDown_ClientState"), i = getParsedElementJson("ctl00_c1_PurchasessCriteriaDetails_subProductIDManyPickLists_manyPickListsDropDown_ClientState"), a = getElementValue("dropDownSaleAmount"), s = getElementValue("dropDownDownPaymentAmount"), r = getElementValue("dropDownDownFees1Amount"), l = getElementValue("dropDownDownFees2Amount"), n = getElementValue("dropDownDownFees3Amount"), o = getElementValue("dropDownDownFees4Amount"), c = getElementValue("dropDownDownFees5Amount"), u = getParsedElementJson("ctl00_c1_PurchasessCriteriaDetails_dropDownSaleTypes_ClientState"), p = getParsedElementJson("ctl00_c1_PurchasessCriteriaDetails_dropDownSaleStatusTypes_ClientState"), D = getElementValue("milestoneStatesDropDown"), d = getParsedElementJson("ctl00_c1_PurchasessCriteriaDetails_saleDispositionIDManyPickLists_manyPickListsDropDown_ClientState"), m = getParsedElementJson("ctl00_c1_PurchasessCriteriaDetails_purchasesPickList1ItemIDManyPickLists_manyPickListsDropDown_ClientState"), y = getParsedElementJson("ctl00_c1_PurchasessCriteriaDetails_purchasesPickList2ItemIDManyPickLists_manyPickListsDropDown_ClientState"), k = getParsedElementJson("ctl00_c1_PurchasessCriteriaDetails_purchasesPickList3ItemIDManyPickLists_manyPickListsDropDown_ClientState"), C = getElementValue("textBoxPurchasesText1"), P = getElementValue("textBoxPurchasesText2"), g = getElementValue("dropDownPurchasesBool1"), I = getElementValue("dropDownPurchasesBool2"); let h = { productCategory: e, product: t, subProduct: i, saleAmount: a, downPaymentAmount: s, fees1: r, fees2: l, fees3: n, fees4: o, proposalValue: c, saleTypes: u, saleStatus: p, milestones: D, saleDisposition: d, purchasesPickList1Item: m, purchasesPickList2Item: y, purchasesPickList3Item: k, purchasesText1: C, purchasesText2: P, purchasesBool1: g, purchasesBool2: I }; return h } function collectSalesCriteriaFilters2() { var e = getParsedElementJson("ctl00_c1_SalesCriteriaDetails_salesTeamIDManyPickLists_manyPickListsDropDown_ClientState"), t = getParsedElementJson("ctl00_c1_SalesCriteriaDetails_podiumIDManyPickLists_manyPickListsDropDown_ClientState"), i = getParsedElementJson("ctl00_c1_SalesCriteriaDetails_salesRepIDManyPickLists_manyPickListsDropDown_ClientState"), a = getParsedElementJson("ctl00_c1_SalesCriteriaDetails_salesCloser1IDManyPickLists_manyPickListsDropDown_ClientState"), s = getParsedElementJson("ctl00_c1_SalesCriteriaDetails_salesCloser2IDManyPickLists_manyPickListsDropDown_ClientState"), r = getParsedElementJson("ctl00_c1_SalesCriteriaDetails_exitIDManyPickLists_manyPickListsDropDown_ClientState"), l = getParsedElementJson("ctl00_c1_SalesCriteriaDetails_verificationOfficerIDManyPickLists_manyPickListsDropDown_ClientState"), n = getParsedElementJson("ctl00_c1_SalesCriteriaDetails_salesPickList1ItemIDManyPickLists_manyPickListsDropDown_ClientState"), o = getElementValue("textBoxSalesText1"), c = getElementValue("dropDownSalesDecimal1"), u = getElementValue("dropDownSalesBool1"), p = getParsedElementJson("ctl00_c1_SalesCriteriaDetails_exitTeamIDManyPickLists_manyPickListsDropDown_ClientState"), D = getParsedElementJson("ctl00_c1_SalesCriteriaDetails_exitRep1IDManyPickLists_manyPickListsDropDown_ClientState"), d = getParsedElementJson("ctl00_c1_SalesCriteriaDetails_exitRep2IDManyPickLists_manyPickListsDropDown_ClientState"), m = getParsedElementJson("ctl00_c1_SalesCriteriaDetails_exitRep3IDManyPickLists_manyPickListsDropDown_ClientState"); let y = { salesTeam: e, podium: t, salesAgent: i, salesCloser1: a, salesCloser2: s, extRep: r, verificationOfficer: l, salesPickList1Item: n, salesText: o, salesDecimal: c, salesBool: u, salesExitTeam: p, extRep1: D, extRep2: d, extRep3: m }; return y } function collectSalesCriteriaFilters2() { var e = getParsedElementJson("ctl00_c1_SalesCriteriaDetails_salesTeamIDManyPickLists_manyPickListsDropDown_ClientState"), t = getParsedElementJson("ctl00_c1_SalesCriteriaDetails_podiumIDManyPickLists_manyPickListsDropDown_ClientState"), i = getParsedElementJson("ctl00_c1_SalesCriteriaDetails_salesRepIDManyPickLists_manyPickListsDropDown_ClientState"), a = getParsedElementJson("ctl00_c1_SalesCriteriaDetails_salesCloser1IDManyPickLists_manyPickListsDropDown_ClientState"), s = getParsedElementJson("ctl00_c1_SalesCriteriaDetails_salesCloser2IDManyPickLists_manyPickListsDropDown_ClientState"), r = getParsedElementJson("ctl00_c1_SalesCriteriaDetails_exitIDManyPickLists_manyPickListsDropDown_ClientState"), l = getParsedElementJson("ctl00_c1_SalesCriteriaDetails_verificationOfficerIDManyPickLists_manyPickListsDropDown_ClientState"), n = getParsedElementJson("ctl00_c1_SalesCriteriaDetails_salesPickList1ItemIDManyPickLists_manyPickListsDropDown_ClientState"), o = getElementValue("textBoxSalesText1"), c = getElementValue("dropDownSalesDecimal1"), u = getElementValue("dropDownSalesBool1"), p = getParsedElementJson("ctl00_c1_SalesCriteriaDetails_exitTeamIDManyPickLists_manyPickListsDropDown_ClientState"), D = getParsedElementJson("ctl00_c1_SalesCriteriaDetails_exitRep1IDManyPickLists_manyPickListsDropDown_ClientState"), d = getParsedElementJson("ctl00_c1_SalesCriteriaDetails_exitRep2IDManyPickLists_manyPickListsDropDown_ClientState"), m = getParsedElementJson("ctl00_c1_SalesCriteriaDetails_exitRep3IDManyPickLists_manyPickListsDropDown_ClientState"); let y = { salesTeam: e, podium: t, salesAgent: i, salesCloser1: a, salesCloser2: s, extRep: r, verificationOfficer: l, salesPickList1Item: n, salesText: o, salesDecimal: c, salesBool: u, salesExitTeam: p, extRep1: D, extRep2: d, extRep3: m }; return y } function collectMarketingCriteriaFilters2() { var e = getParsedElementJson("ctl00_c1_MarketingCriteriaDetails_marketingTeamIDManyPickLists_manyPickListsDropDown_ClientState"), t = getParsedElementJson("ctl00_c1_MarketingCriteriaDetails_marketingAgentIDManyPickLists_manyPickListsDropDown_ClientState"), i = getParsedElementJson("ctl00_c1_MarketingCriteriaDetails_marketingCloserIDManyPickLists_manyPickListsDropDown_ClientState"), a = getParsedElementJson("ctl00_c1_MarketingCriteriaDetails_confirmerIDManyPickLists_manyPickListsDropDown_ClientState"), s = getParsedElementJson("ctl00_c1_MarketingCriteriaDetails_resetterIDManyPickLists_manyPickListsDropDown_ClientState"), r = getParsedElementJson("ctl00_c1_MarketingCriteriaDetails_venueIDManyPickLists_manyPickListsDropDown_ClientState"), l = getParsedElementJson("ctl00_c1_MarketingCriteriaDetails_campaignIDManyPickLists_manyPickListsDropDown_ClientState"), n = getParsedElementJson("ctl00_c1_MarketingCriteriaDetails_channelIDManyPickLists_manyPickListsDropDown_ClientState"), o = getParsedElementJson("ctl00_c1_MarketingCriteriaDetails_hotelIDManyPickLists_manyPickListsDropDown_ClientState"), c = getParsedElementJson("ctl00_c1_MarketingCriteriaDetails_giftIDsManyPickLists_manyPickListsDropDown_ClientState"), u = getParsedElementJson("ctl00_c1_MarketingCriteriaDetails_marketingPickList1ItemIDManyPickLists_manyPickListsDropDown_ClientState"), p = getParsedElementJson("ctl00_c1_MarketingCriteriaDetails_marketingPickList2ItemIDManyPickLists_manyPickListsDropDown_ClientState"), D = getElementValue("textBoxMarketingText1"), d = getElementValue("dropDownMarketingBool1"), m = getElementValue("dropDownDepositAmount"), y = getElementValue("dropDownDepositRefundable"); let k = { marketingTeam: e, marketingAgent: t, marketingCloser: i, confirmer: a, resetter: s, venue: r, campaign: l, channel: n, hotel: o, giftIDs: c, marketingPickList1Item1: u, marketingPickList1Item2: p, marketingText: D, marketingBool: d, depositAmount: m, depositRefundable: y }; return k } function collectTourCriteriaFilters2() { var e = getParsedElementJson("ctl00_c1_TourCriteriaDetails_tourSourceIDManyPickLists_manyPickListsDropDown_ClientState"), t = getParsedElementJson("ctl00_c1_TourCriteriaDetails_tourTypeIDManyPickLists_manyPickListsDropDown_ClientState"), i = document.getElementById("ctl00_c1_TourCriteriaDetails_dropDownTourStatusTypes_Input").value, a = getParsedElementJson("ctl00_c1_TourCriteriaDetails_tourConcernTypeIDManyPickLists_manyPickListsDropDown_ClientState"), s = getParsedElementJson("ctl00_c1_TourCriteriaDetails_tourTypePickList1ItemIDManyPickLists_manyPickListsDropDown_ClientState"), r = getParsedElementJson("ctl00_c1_TourCriteriaDetails_locationIDManyPickLists_manyPickListsDropDown_ClientState"), l = getParsedElementJson("ctl00_c1_TourCriteriaDetails_regionIDManyPickLists_manyPickListsDropDown_ClientState"), n = null; null != document.getElementById("dropDownTourBool1") && (n = document.getElementById("dropDownTourBool1").value); var o = getParsedElementJson("ctl00_c1_TourCriteriaDetails_toursManyPickList1ItemIDsManyPickLists_manyPickListsDropDown_ClientState"); let c = { tourSource: e, office: t, tourStatus: i, tourDisposition: a, importMethod: s, businessPartner: r, businessGroup: l, isShow: n, saleBonusGifts: o }; return c } function collectLeadCriteriaFilters2() { var e = getParsedElementJson("ctl00_c1_LeadCriteria_leadSourceIDManyPickLists_manyPickListsDropDown_ClientState"), t = getParsedElementJson("ctl00_c1_LeadCriteria_leadDispositionIDManyPickLists_manyPickListsDropDown_ClientState"), i = getParsedElementJson("ctl00_c1_LeadCriteria_leadPickList1ItemIDManyPickLists_manyPickListsDropDown_ClientState"), a = getParsedElementJson("ctl00_c1_LeadCriteria_leadPickList2ItemIDManyPickLists_manyPickListsDropDown_ClientState"); let s = { leadSourceFile: e, leadDisposition: t, calendarName: i, leadSource: a }; return s } async function clearDateTimeCriterial() { document.getElementById("dropDownDateRanges").selectedIndex = 0, document.getElementById("dropDownYearRanges").selectedIndex = 0, setParsedElementValue("ctl00_c1_DateTimeCriteriaDetails_startDateRadDatePicker_dateInput", ""), setParsedElementValue("ctl00_c1_DateTimeCriteriaDetails_startDateRadDatePicker_dateInput_ClientState", '{"enabled":true,"emptyMessage":"","validationText":"","valueAsString":"","minDateStr":"1900-01-01-00-00-00","maxDateStr":"2099-12-31-00-00-00","lastSetTextBoxValue":""}'), setParsedElementValue("ctl00_c1_DateTimeCriteriaDetails_endDateRadDatePicker_dateInput", ""), setParsedElementValue("ctl00_c1_DateTimeCriteriaDetails_endDateRadDatePicker_dateInput_ClientState", '{"enabled":true,"emptyMessage":"","validationText":"","valueAsString":"","minDateStr":"1900-01-01-00-00-00","maxDateStr":"2099-12-31-00-00-00","lastSetTextBoxValue":""}'), setElementValue("dropDownDateTypeLogic", ""), setElementValue("dropDownTierOneDateTypes", ""); for (var e = { Sunday: "ctl00_c1_DateTimeCriteriaDetails_dropDownDaysOfWeek_i0_checkBox", Monday: "ctl00_c1_DateTimeCriteriaDetails_dropDownDaysOfWeek_i1_checkBox", Tuesday: "ctl00_c1_DateTimeCriteriaDetails_dropDownDaysOfWeek_i2_checkBox", Wednesday: "ctl00_c1_DateTimeCriteriaDetails_dropDownDaysOfWeek_i3_checkBox", Thursday: "ctl00_c1_DateTimeCriteriaDetails_dropDownDaysOfWeek_i4_checkBox", Friday: "ctl00_c1_DateTimeCriteriaDetails_dropDownDaysOfWeek_i5_checkBox", Saturday: "ctl00_c1_DateTimeCriteriaDetails_dropDownDaysOfWeek_i6_checkBox" }, t = {}, i = 1; i <= 52; i++)t[i.toString()] = "ctl00_c1_DateTimeCriteriaDetails_dropDownWeeks_i" + (i - 1) + "_checkBox"; for (var a = { January: "ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i0_checkBox", February: "ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i1_checkBox", March: "ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i2_checkBox", April: "ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i3_checkBox", May: "ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i4_checkBox", June: "ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i5_checkBox", July: "ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i6_checkBox", August: "ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i7_checkBox", September: "ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i8_checkBox", October: "ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i9_checkBox", November: "ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i10_checkBox", December: "ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i11_checkBox" }, s = {}, r = 0; r <= 24; r++)s[(2023 - r).toString()] = "ctl00_c1_DateTimeCriteriaDetails_dropDownYears_i" + r + "_checkBox"; var l = { "8:00 AM": "ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_i0_checkBox", "8:40 AM": "ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_i1_checkBox", "10:00 AM": "ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_i2_checkBox", "11:30 AM": "ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_i3_checkBox", "12:00 PM": "ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_i4_checkBox", "1:00 PM": "ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_i5_checkBox", "2:00 PM": "ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_i6_checkBox", "3:45 PM": "ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_i7_checkBox", "4:00 PM": "ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_i8_checkBox", "6:00 PM": "ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_i9_checkBox", "8:00 PM": "ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_i10_checkBox" }; clearCheckboxes(e), clearCheckboxes(t), clearCheckboxes(a), clearCheckboxes(s), clearCheckboxes(l), setParsedElementValue("ctl00_c1_DateTimeCriteriaDetails_dropDownDaysOfWeek_ClientState", '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}'), $("input[id$='ctl00_c1_DateTimeCriteriaDetails_dropDownDaysOfWeek_Input']").val("All"), setParsedElementValue("ctl00_c1_DateTimeCriteriaDetails_dropDownWeeks_ClientState", '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}'), $("input[id$='ctl00_c1_DateTimeCriteriaDetails_dropDownWeeks_Input']").val("All"), setParsedElementValue("ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_ClientState", '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}'), $("input[id$='ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_Input']").val("All"), setParsedElementValue("ctl00_c1_DateTimeCriteriaDetails_dropDownYears_ClientState", '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}'), $("input[id$='ctl00_c1_DateTimeCriteriaDetails_dropDownYears_Input']").val("All"), setParsedElementValue("ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_ClientState", '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}'), $("input[id$='ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_Input']").val("All") } async function clearBasicCriterial() { setElementValue("customerIDTextBox", ""), setElementValue("externalCustomerIDTextBox", ""), setElementValue("externalLeadIDTextBox", ""), setElementValue("tourIDTextBox", ""), setElementValue("externalTourIDTextBox", ""), setElementValue("dropDownPurchasesCount", ""), setElementValue("purchaseIDTextBox", ""), setElementValue("externalPurchaseIDTextBox", ""), setElementValue("textBoxNotes", ""), setParsedElementValue("ctl00_c1_BasicCriteriaDetails_dropDownUser_ClientState", '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}'), $("input[id$='ctl00_c1_BasicCriteriaDetails_dropDownUser_Input']").val("All (select or type to search)"), setElementValue("textBoxImportNumber", "") } async function clearCustomersCriteriaFilters() { setParsedElementValue("ctl00_c1_CustomersCriteriaDetails_customerDispositionIDManyPickLists_manyPickListsDropDown_ClientState", '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}'), $("input[id$='ctl00_c1_CustomersCriteriaDetails_customerDispositionIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)"), setParsedElementValue("ctl00_c1_CustomersCriteriaDetails_customersPickList1ItemIDManyPickLists_manyPickListsDropDown_ClientState", '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}'), $("input[id$='ctl00_c1_CustomersCriteriaDetails_customersPickList1ItemIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)"), setElementValue("dropDownSex", ""), setElementValue("dropDownGuestSex", ""), setElementValue("locationUser", "--"), setParsedElementValue("ctl00_c1_CustomersCriteriaDetails_customersPickList2ItemIDManyPickLists_manyPickListsDropDown_ClientState", '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}'), $("input[id$='ctl00_c1_CustomersCriteriaDetails_customersPickList2ItemIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)"), setParsedElementValue("ctl00_c1_CustomersCriteriaDetails_guestTypeIDManyPickLists_manyPickListsDropDown_ClientState", '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}'), $("input[id$='ctl00_c1_CustomersCriteriaDetails_guestTypeIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)"), setElementValue("dropDownCustomersDecimal1", ""), setElementValue("dropDownCustomersBool1", ""), setElementValue("textBoxPhone", ""), setElementValue("TextBoxStreetAddress", ""), setElementValue("TextBoxStreetAddress2", ""), setElementValue("textBoxCity", ""), setParsedElementValue("ctl00_c1_CustomersCriteriaDetails_stateIDManyPickLists_manyPickListsDropDown_ClientState", '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}'), $("input[id$='ctl00_c1_CustomersCriteriaDetails_stateIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)"), setElementValue("textBoxZipcode", ""), setParsedElementValue("ctl00_c1_CustomersCriteriaDetails_countryIDManyPickLists_manyPickListsDropDown_ClientState", '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}'), $("input[id$='ctl00_c1_CustomersCriteriaDetails_countryIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)"), setElementValue("TextBoxBusiness", ""), setElementValue("TextBoxEmail", ""), setParsedElementValue("ctl00_c1_CustomersCriteriaDetails_customersPickList4ItemIDManyPickLists_manyPickListsDropDown_ClientState", '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}'), $("input[id$='ctl00_c1_CustomersCriteriaDetails_customersPickList4ItemIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)"), setElementValue("dropDownCustomersBool2", ""), setElementValue("TextBoxCustomersText1", ""), setElementValue("TextBoxCustomersText2", ""), setElementValue("TextBoxCustomersText3", "") } async function clearCancellationCriteriaFilters() { setParsedElementValue("ctl00_c1_CancellationCriteriaDetails_dropDownCancellationReasonTypes_ClientState", '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}'), $("input[id$='ctl00_c1_CancellationCriteriaDetails_dropDownCancellationReasonTypes_Input']").val("All (select or type to search)"), setParsedElementValue("ctl00_c1_CancellationCriteriaDetails_dropDownCancellationReceivedTypes_ClientState", '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}'), $("input[id$='ctl00_c1_CancellationCriteriaDetails_dropDownCancellationReceivedTypes_Input']").val("All (select or type to search)"), setElementValue("dropDownWithinRescission", ""), setParsedElementValue("ctl00_c1_CancellationCriteriaDetails_dropDownCancellationDispositionTypes_ClientState", '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}'), $("input[id$='ctl00_c1_CancellationCriteriaDetails_dropDownCancellationDispositionTypes_Input']").val("All (select or type to search)"), setParsedElementValue("ctl00_c1_CancellationCriteriaDetails_dropDownCancellationStatusTypes_ClientState", '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}'), $("input[id$='ctl00_c1_CancellationCriteriaDetails_dropDownCancellationStatusTypes_Input']").val("All (select or type to search)"), setParsedElementValue("ctl00_c1_CancellationCriteriaDetails_dropDownCancellationUser_manyPickListsDropDown_ClientState", '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}'), $("input[id$='ctl00_c1_CancellationCriteriaDetails_dropDownCancellationUser_manyPickListsDropDown_Input']").val("All (select or type to search)") } async function clearPurchaseCriteriaFilters() { setParsedElementValue("ctl00_c1_PurchasessCriteriaDetails_productCategoryIDManyPickLists_manyPickListsDropDown_ClientState", '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}'), $("input[id$='ctl00_c1_PurchasessCriteriaDetails_productCategoryIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)"), setParsedElementValue("ctl00_c1_PurchasessCriteriaDetails_productIDManyPickLists_manyPickListsDropDown_ClientState", '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}'), $("input[id$='ctl00_c1_PurchasessCriteriaDetails_productIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)"), setParsedElementValue("ctl00_c1_PurchasessCriteriaDetails_subProductIDManyPickLists_manyPickListsDropDown_ClientState", '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}'), $("input[id$='ctl00_c1_PurchasessCriteriaDetails_subProductIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)"), setElementValue("dropDownSaleAmount", ""), setElementValue("dropDownDownPaymentAmount", ""), setElementValue("dropDownDownFees1Amount", ""), setElementValue("dropDownDownFees2Amount", ""), setElementValue("dropDownDownFees3Amount", ""), setElementValue("dropDownDownFees4Amount", ""), setElementValue("dropDownDownFees5Amount", ""), setParsedElementValue("ctl00_c1_PurchasessCriteriaDetails_dropDownSaleTypes_ClientState", '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}'), $("input[id$='ctl00_c1_PurchasessCriteriaDetails_dropDownSaleTypes_Input']").val("All (select or type to search)"), setParsedElementValue("ctl00_c1_PurchasessCriteriaDetails_dropDownSaleStatusTypes_ClientState", '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}'), $("input[id$='ctl00_c1_PurchasessCriteriaDetails_dropDownSaleStatusTypes_Input']").val("All (select or type to search)"), setElementValue("milestoneStatesDropDown", ""), setParsedElementValue("ctl00_c1_PurchasessCriteriaDetails_saleDispositionIDManyPickLists_manyPickListsDropDown_ClientState", '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}'), $("input[id$='ctl00_c1_PurchasessCriteriaDetails_saleDispositionIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)"), setParsedElementValue("ctl00_c1_PurchasessCriteriaDetails_purchasesPickList1ItemIDManyPickLists_manyPickListsDropDown_ClientState", '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}'), $("input[id$='ctl00_c1_PurchasessCriteriaDetails_purchasesPickList1ItemIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)"), setParsedElementValue("ctl00_c1_PurchasessCriteriaDetails_purchasesPickList2ItemIDManyPickLists_manyPickListsDropDown_ClientState", '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}'), $("input[id$='ctl00_c1_PurchasessCriteriaDetails_purchasesPickList2ItemIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)"), setParsedElementValue("ctl00_c1_PurchasessCriteriaDetails_purchasesPickList3ItemIDManyPickLists_manyPickListsDropDown_ClientState", '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}'), $("input[id$='ctl00_c1_PurchasessCriteriaDetails_purchasesPickList3ItemIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)"), setElementValue("textBoxPurchasesText1", ""), setElementValue("textBoxPurchasesText2", ""), setElementValue("dropDownPurchasesBool1", ""), setElementValue("dropDownPurchasesBool2", "") } async function clearSalesCriteriaFilters() { setParsedElementValue("ctl00_c1_SalesCriteriaDetails_salesTeamIDManyPickLists_manyPickListsDropDown_ClientState", '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}'), $("input[id$='ctl00_c1_SalesCriteriaDetails_salesTeamIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)"), setParsedElementValue("ctl00_c1_SalesCriteriaDetails_podiumIDManyPickLists_manyPickListsDropDown_ClientState", '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}'), $("input[id$='ctl00_c1_SalesCriteriaDetails_podiumIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)"), setParsedElementValue("ctl00_c1_SalesCriteriaDetails_salesRepIDManyPickLists_manyPickListsDropDown_ClientState", '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}'), $("input[id$='ctl00_c1_SalesCriteriaDetails_salesRepIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)"), setParsedElementValue("ctl00_c1_SalesCriteriaDetails_salesCloser1IDManyPickLists_manyPickListsDropDown_ClientState", '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}'), $("input[id$='ctl00_c1_SalesCriteriaDetails_salesCloser1IDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)"), setParsedElementValue("ctl00_c1_SalesCriteriaDetails_salesCloser2IDManyPickLists_manyPickListsDropDown_ClientState", '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}'), $("input[id$='ctl00_c1_SalesCriteriaDetails_salesCloser2IDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)"), setParsedElementValue("ctl00_c1_SalesCriteriaDetails_exitIDManyPickLists_manyPickListsDropDown_ClientState", '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}'), $("input[id$='ctl00_c1_SalesCriteriaDetails_exitIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)"), setParsedElementValue("ctl00_c1_SalesCriteriaDetails_verificationOfficerIDManyPickLists_manyPickListsDropDown_ClientState", '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}'), $("input[id$='ctl00_c1_SalesCriteriaDetails_verificationOfficerIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)"), setElementValue("ctl00_c1_SalesCriteriaDetails_dropDownSaleTypes_ClientState", ""), $("input[id$='ctl00_c1_SalesCriteriaDetails_dropDownSaleTypes_Input']").val("All"), setElementValue("ctl00_c1_SalesCriteriaDetails_dropDownSaleStatusTypes_ClientState", ""), $("input[id$='ctl00_c1_SalesCriteriaDetails_dropDownSaleStatusTypes_Input']").val("All"), setElementValue("milestoneStatesDropDown", ""), setElementValue("textBoxSalesText1", ""), setElementValue("textBoxSalesText2", ""), setElementValue("dropDownSalesDecimal1", ""), setElementValue("dropDownSalesBool1", "") } async function clearMarketingCriteriaFilters() { setParsedElementValue("ctl00_c1_MarketingCriteriaDetails_marketingTeamIDManyPickLists_manyPickListsDropDown_ClientState", '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}'), $("input[id$='ctl00_c1_MarketingCriteriaDetails_marketingTeamIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)"), setParsedElementValue("ctl00_c1_MarketingCriteriaDetails_marketingAgentIDManyPickLists_manyPickListsDropDown_ClientState", '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}'), $("input[id$='ctl00_c1_MarketingCriteriaDetails_marketingAgentIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)"), setParsedElementValue("ctl00_c1_MarketingCriteriaDetails_marketingCloserIDManyPickLists_manyPickListsDropDown_ClientState", '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}'), $("input[id$='ctl00_c1_MarketingCriteriaDetails_marketingCloserIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)"), setParsedElementValue("ctl00_c1_MarketingCriteriaDetails_confirmerIDManyPickLists_manyPickListsDropDown_ClientState", '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}'), $("input[id$='ctl00_c1_MarketingCriteriaDetails_confirmerIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)"), setParsedElementValue("ctl00_c1_MarketingCriteriaDetails_resetterIDManyPickLists_manyPickListsDropDown_ClientState", '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}'), $("input[id$='ctl00_c1_MarketingCriteriaDetails_resetterIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)"), setParsedElementValue("ctl00_c1_MarketingCriteriaDetails_venueIDManyPickLists_manyPickListsDropDown_ClientState", '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}'), $("input[id$='ctl00_c1_MarketingCriteriaDetails_venueIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)"), setParsedElementValue("ctl00_c1_MarketingCriteriaDetails_campaignIDManyPickLists_manyPickListsDropDown_ClientState", '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}'), $("input[id$='ctl00_c1_MarketingCriteriaDetails_campaignIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)"), setParsedElementValue("ctl00_c1_MarketingCriteriaDetails_channelIDManyPickLists_manyPickListsDropDown_ClientState", '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}'), $("input[id$='ctl00_c1_MarketingCriteriaDetails_channelIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)"), setParsedElementValue("ctl00_c1_MarketingCriteriaDetails_hotelIDManyPickLists_manyPickListsDropDown_ClientState", '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}'), $("input[id$='ctl00_c1_MarketingCriteriaDetails_hotelIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)"), setParsedElementValue("ctl00_c1_MarketingCriteriaDetails_giftIDsManyPickLists_manyPickListsDropDown_ClientState", '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}'), $("input[id$='ctl00_c1_MarketingCriteriaDetails_giftIDsManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)"), setParsedElementValue("ctl00_c1_MarketingCriteriaDetails_marketingPickList1ItemIDManyPickLists_manyPickListsDropDown_ClientState", '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}'), $("input[id$='ctl00_c1_MarketingCriteriaDetails_marketingPickList1ItemIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)"), setParsedElementValue("ctl00_c1_MarketingCriteriaDetails_marketingPickList2ItemIDManyPickLists_manyPickListsDropDown_ClientState", '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}'), $("input[id$='ctl00_c1_MarketingCriteriaDetails_marketingPickList2ItemIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)"), setElementValue("textBoxMarketingText1", ""), setElementValue("dropDownMarketingBool1", ""), setElementValue("dropDownDepositAmount", ""), setElementValue("dropDownDepositRefundable", "") } async function clearTourCriteriaFilters() { setParsedElementValue("ctl00_c1_TourCriteriaDetails_tourSourceIDManyPickLists_manyPickListsDropDown_ClientState", '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}'), $("input[id$='ctl00_c1_TourCriteriaDetails_tourSourceIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)"), setParsedElementValue("ctl00_c1_TourCriteriaDetails_tourTypeIDManyPickLists_manyPickListsDropDown_ClientState", '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}'), $("input[id$='ctl00_c1_TourCriteriaDetails_tourTypeIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)"), setParsedElementValue("ctl00_c1_TourCriteriaDetails_dropDownTourStatusTypes_ClientState", '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}'), $("input[id$='ctl00_c1_TourCriteriaDetails_dropDownTourStatusTypes_Input']").val("All"), setParsedElementValue("ctl00_c1_TourCriteriaDetails_tourConcernTypeIDManyPickLists_manyPickListsDropDown_ClientState", '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}'), $("input[id$='ctl00_c1_TourCriteriaDetails_tourConcernTypeIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)"), setParsedElementValue("ctl00_c1_TourCriteriaDetails_tourTypePickList1ItemIDManyPickLists_manyPickListsDropDown_ClientState", '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}'), $("input[id$='ctl00_c1_TourCriteriaDetails_tourTypePickList1ItemIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)"), setParsedElementValue("ctl00_c1_TourCriteriaDetails_locationIDManyPickLists_manyPickListsDropDown_ClientState", '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}'), $("input[id$='ctl00_c1_TourCriteriaDetails_locationIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)"), setParsedElementValue("ctl00_c1_TourCriteriaDetails_regionIDManyPickLists_manyPickListsDropDown_ClientState", '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}'), $("input[id$='ctl00_c1_TourCriteriaDetails_regionIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)"); var e = document.getElementById("dropDownTourBool1"); null != e && (e.selectedIndex = 0), setParsedElementValue("ctl00_c1_TourCriteriaDetails_toursManyPickList1ItemIDsManyPickLists_manyPickListsDropDown_ClientState", '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}'), $("input[id$='ctl00_c1_TourCriteriaDetails_toursManyPickList1ItemIDsManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)") } async function clearLeadCriteriaFilters() { setParsedElementValue("ctl00_c1_LeadCriteria_leadSourceIDManyPickLists_manyPickListsDropDown_ClientState", '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}'), $("input[id$='ctl00_c1_LeadCriteria_leadSourceIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)"), setParsedElementValue("ctl00_c1_LeadCriteria_leadDispositionIDManyPickLists_manyPickListsDropDown_ClientState", '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}'), $("input[id$='ctl00_c1_LeadCriteria_leadDispositionIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)"), setParsedElementValue("ctl00_c1_LeadCriteria_leadPickList1ItemIDManyPickLists_manyPickListsDropDown_ClientState", '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}'), $("input[id$='ctl00_c1_LeadCriteria_leadPickList1ItemIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)"), setParsedElementValue("ctl00_c1_LeadCriteria_leadPickList2ItemIDManyPickLists_manyPickListsDropDown_ClientState", '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}'), $("input[id$='ctl00_c1_LeadCriteria_leadPickList2ItemIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)") } function setDateTimeCriteriaFilters(e) { null != e.dateRange && "null" != e.dateRange && (document.getElementById("dropDownDateRanges").selectedIndex = e.dateRange), null != e.yearRange && "null" != e.yearRange && (document.getElementById("dropDownYearRanges").selectedIndex = e.yearRange), 2 != e.dateRange && null != e.dateRange && "null" != e.dateRange ? updateDateRange(String(e.dateRange)) : 2 == e.dateRange && (initial_date = e.startDate, last_date = e.endDate), null != e.dateTypeLogic && "" != e.dateTypeLogic && setElementValue("dropDownDateTypeLogic", e.dateTypeLogic), null != e.tierOneDateTypes && "" != e.tierOneDateTypes ? setElementValue("dropDownTierOneDateTypes", e.tierOneDateTypes) : setElementValue("dropDownTierOneDateTypes", "0"); for (var t = { Sunday: "ctl00_c1_DateTimeCriteriaDetails_dropDownDaysOfWeek_i0_checkBox", Monday: "ctl00_c1_DateTimeCriteriaDetails_dropDownDaysOfWeek_i1_checkBox", Tuesday: "ctl00_c1_DateTimeCriteriaDetails_dropDownDaysOfWeek_i2_checkBox", Wednesday: "ctl00_c1_DateTimeCriteriaDetails_dropDownDaysOfWeek_i3_checkBox", Thursday: "ctl00_c1_DateTimeCriteriaDetails_dropDownDaysOfWeek_i4_checkBox", Friday: "ctl00_c1_DateTimeCriteriaDetails_dropDownDaysOfWeek_i5_checkBox", Saturday: "ctl00_c1_DateTimeCriteriaDetails_dropDownDaysOfWeek_i6_checkBox" }, i = {}, a = 1; a <= 52; a++)i[a.toString()] = "ctl00_c1_DateTimeCriteriaDetails_dropDownWeeks_i" + (a - 1) + "_checkBox"; for (var s = { January: "ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i0_checkBox", February: "ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i1_checkBox", March: "ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i2_checkBox", April: "ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i3_checkBox", May: "ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i4_checkBox", June: "ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i5_checkBox", July: "ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i6_checkBox", August: "ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i7_checkBox", September: "ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i8_checkBox", October: "ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i9_checkBox", November: "ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i10_checkBox", December: "ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i11_checkBox" }, r = {}, l = 0; l <= 24; l++)r[(2023 - l).toString()] = "ctl00_c1_DateTimeCriteriaDetails_dropDownYears_i" + l + "_checkBox"; var n = { "8:00 AM": "ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_i0_checkBox", "8:40 AM": "ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_i1_checkBox", "10:00 AM": "ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_i2_checkBox", "11:30 AM": "ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_i3_checkBox", "12:00 PM": "ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_i4_checkBox", "1:00 PM": "ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_i5_checkBox", "2:00 PM": "ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_i6_checkBox", "3:45 PM": "ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_i7_checkBox", "4:00 PM": "ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_i8_checkBox", "6:00 PM": "ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_i9_checkBox", "8:00 PM": "ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_i10_checkBox" }; "All" != e.daysOfWeek && setCheckboxes(t, e.daysOfWeek), "All" != e.weeks && setCheckboxes(i, e.weeks), "All" != e.months && setCheckboxes(s, e.months), "All" != e.years && setCheckboxes(r, e.years), "All" != e.times && setCheckboxes(n, e.times) } function setBasicCriteriaFilters(e) { null != e.customerId && "null" != e.customerId && setElementValue("customerIDTextBox", e.customerId), null != e.extId && "null" != e.extId && setElementValue("externalCustomerIDTextBox", e.extId), null != e.extLeadId && "null" != e.extLeadId && setElementValue("externalLeadIDTextBox", e.extLeadId), null != e.tourId && "null" != e.tourId && setElementValue("tourIDTextBox", e.tourId), null != e.extTourId && "null" != e.extTourId && setElementValue("externalTourIDTextBox", e.extTourId), null != e.purchaseId && "null" != e.purchaseId && setElementValue("purchaseIDTextBox", e.purchaseId), null != e.extPurchaseId && "null" != e.extPurchaseId && setElementValue("externalPurchaseIDTextBox", e.extPurchaseId), null != e.notes && "null" != e.notes && setElementValue("textBoxNotes", e.notes), null != e.user && "null" != e.user && (setParsedElementValue("ctl00_c1_BasicCriteria1_dropDownUser_ClientState", e.user), $("input[id$='ctl00_c1_BasicCriteria1_dropDownUser_Input']").val(JSON.parse(e.user).text)), null != e.importNmbr && "null" != e.importNmbr && setElementValue("textBoxImportNumber", e.importNmbr), null != e.purchase && "null" != e.purchase && setElementValue("dropDownPurchasesCount", e.purchase) } function setCustomersCriteriaFilters(e) { null != e.customerDisposition && "null" != e.customerDisposition && "" !== e.customerDisposition.trim() && (setParsedElementValue("ctl00_c1_CustomersCriteriaDetails_customerDispositionIDManyPickLists_manyPickListsDropDown_ClientState", e.customerDisposition), $("input[id$='ctl00_c1_CustomersCriteriaDetails_customerDispositionIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(e.customerDisposition).text)), null != e.name && "null" != e.name && "" !== e.name.trim() && setElementValue("textBoxName", e.name), null != e.sex && "null" != e.sex && "" !== e.sex.trim() && setElementValue("dropDownSex", e.sex), null != e.customersPickList1Item && "null" != e.customersPickList1Item && "" !== e.customersPickList1Item.trim() && (setParsedElementValue("ctl00_c1_CustomersCriteriaDetails_customersPickList1ItemIDManyPickLists_manyPickListsDropDown_ClientState", e.customersPickList1Item), $("input[id$='ctl00_c1_CustomersCriteriaDetails_customersPickList1ItemIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(e.customersPickList1Item).text)), null != e.guestType && "null" != e.guestType && "" !== e.guestType.trim() && (setParsedElementValue("ctl00_c1_CustomersCriteriaDetails_guestTypeIDManyPickLists_manyPickListsDropDown_ClientState", e.guestType), $("input[id$='ctl00_c1_CustomersCriteriaDetails_guestTypeIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(e.guestType).text)), null != e.guestSex && "null" != e.guestSex && "" !== e.guestSex.trim() && setElementValue("dropDownGuestSex", e.guestSex), null != e.customersPickList2Item && "null" != e.customersPickList2Item && "" !== e.customersPickList2Item.trim() && (setParsedElementValue("ctl00_c1_CustomersCriteriaDetails_customersPickList2ItemIDManyPickLists_manyPickListsDropDown_ClientState", e.customersPickList2Item), $("input[id$='ctl00_c1_CustomersCriteriaDetails_customersPickList2ItemIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(e.customersPickList2Item).text)), null != e.incomeType && "null" != e.incomeType && "" !== e.incomeType.trim() && (setParsedElementValue("ctl00_c1_CustomersCriteriaDetails_incomeTypeIDManyPickLists_manyPickListsDropDown_ClientState", e.incomeType), $("input[id$='ctl00_c1_CustomersCriteriaDetails_incomeTypeIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(e.incomeType).text)), null != e.customersPickList3Item && "null" != e.customersPickList3Item && "" !== e.customersPickList3Item.trim() && (setParsedElementValue("ctl00_c1_CustomersCriteriaDetails_customersPickList3ItemIDManyPickLists_manyPickListsDropDown_ClientState", e.customersPickList3Item), $("input[id$='ctl00_c1_CustomersCriteriaDetails_customersPickList3ItemIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(e.customersPickList3Item).text)), null != e.customersDecimal1 && "null" != e.customersDecimal1 && "" !== e.customersDecimal1.trim() && setElementValue("dropDownCustomersDecimal1", e.customersDecimal1), null != e.customersBool1 && "null" != e.customersBool1 && "" !== e.customersBool1.trim() && setElementValue("dropDownCustomersBool1", e.customersBool1), null != e.phone && "null" != e.phone && "" !== e.phone.trim() && setElementValue("textBoxPhone", e.phone), null != e.streetAddress && "null" != e.streetAddress && "" !== e.streetAddress.trim() && setElementValue("TextBoxStreetAddress", e.streetAddress), null != e.streetAddress2 && "null" != e.streetAddress2 && "" !== e.streetAddress2.trim() && setElementValue("TextBoxStreetAddress2", e.streetAddress2), null != e.city && "null" != e.city && "" !== e.city.trim() && setElementValue("textBoxCity", e.city), null != e.state && "null" != e.state && "" !== e.state.trim() && (setParsedElementValue("ctl00_c1_CustomersCriteriaDetails_stateIDManyPickLists_manyPickListsDropDown_ClientState", e.state), $("input[id$='ctl00_c1_CustomersCriteriaDetails_stateIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(e.state).text)), null != e.zipcode && "null" != e.zipcode && "" !== e.zipcode.trim() && setElementValue("textBoxZipcode", e.zipcode), null != e.country && "null" != e.country && "" !== e.country.trim() && (setParsedElementValue("ctl00_c1_CustomersCriteriaDetails_countryIDManyPickLists_manyPickListsDropDown_ClientState", e.country), $("input[id$='ctl00_c1_CustomersCriteriaDetails_countryIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(e.country).text)), null != e.businessName && "null" != e.businessName && "" !== e.businessName.trim() && setElementValue("TextBoxBusiness", e.businessName), null != e.email && "null" != e.email && "" !== e.email.trim() && setElementValue("TextBoxEmail", e.email), null != e.customersPickList4Item && "null" != e.customersPickList4Item && "" !== e.customersPickList4Item.trim() && (setParsedElementValue("ctl00_c1_CustomersCriteriaDetails_customersPickList4ItemIDManyPickLists_manyPickListsDropDown_ClientState", e.customersPickList4Item), $("input[id$='ctl00_c1_CustomersCriteriaDetails_customersPickList4ItemIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(e.customersPickList4Item).text)), null != e.customersBool2 && "null" != e.customersBool2 && "" !== e.customersBool2.trim() && setElementValue("dropDownCustomersBool2", e.customersBool2), null != e.customersText && "null" != e.customersText && "" !== e.customersText.trim() && setElementValue("TextBoxCustomersText1", e.customersText), null != e.customersText2 && "null" != e.customersText2 && "" !== e.customersText2.trim() && setElementValue("TextBoxCustomersText2", e.customersText2), null != e.customersText3 && "null" != e.customersText3 && "" !== e.customersText3.trim() && setElementValue("TextBoxCustomersText3", e.customersText3) } function setCancellationCriteriaFilters(e) { null != e.cxlRequestReason && "null" != e.cxlRequestReason && (setParsedElementValue("ctl00_c1_CancellationCriteriaDetails_dropDownCancellationReasonTypes_ClientState", e.cxlRequestReason), $("input[id$='ctl00_c1_CancellationCriteriaDetails_dropDownCancellationReasonTypes_Input']").val(JSON.parse(e.cxlRequestReason).text)), null != e.cxlRequestReceived && "null" != e.cxlRequestReceived && (setParsedElementValue("ctl00_c1_CancellationCriteriaDetails_dropDownCancellationReceivedTypes_ClientState", e.cxlRequestReceived), $("input[id$='ctl00_c1_CancellationCriteriaDetails_dropDownCancellationReceivedTypes_Input']").val(JSON.parse(e.cxlRequestReceived).text)), null != e.withinRescission && "null" != e.withinRescission && setElementValue("dropDownWithinRescission", e.withinRescission), null != e.cxlRequestDisposition && "null" != e.cxlRequestDisposition && (setParsedElementValue("ctl00_c1_CancellationCriteriaDetails_dropDownCancellationDispositionTypes_ClientState", e.cxlRequestDisposition), $("input[id$='ctl00_c1_CancellationCriteriaDetails_dropDownCancellationDispositionTypes_Input']").val(JSON.parse(e.cxlRequestDisposition).text)), null != e.cxlRequestStatus && "null" != e.cxlRequestStatus && (setParsedElementValue("ctl00_c1_CancellationCriteriaDetails_dropDownCancellationStatusTypes_ClientState", e.cxlRequestStatus), $("input[id$='ctl00_c1_CancellationCriteriaDetails_dropDownCancellationStatusTypes_Input']").val(JSON.parse(e.cxlRequestStatus).text)), null != e.cxlRequestUser && "null" != e.cxlRequestUser && (setParsedElementValue("ctl00_c1_CancellationCriteriaDetails_dropDownCancellationUser_manyPickListsDropDown_ClientState", e.cxlRequestUser), $("input[id$='ctl00_c1_CancellationCriteriaDetails_dropDownCancellationUser_manyPickListsDropDown_Input']").val(JSON.parse(e.cxlRequestUser).text)) } function setPurchaseCriteriaFilters(e) { null != e.productCategory && "null" != e.productCategory && "" !== e.productCategory.trim() && (setParsedElementValue("ctl00_c1_PurchasessCriteriaDetails_productCategoryIDManyPickLists_manyPickListsDropDown_ClientState", e.productCategory), $("input[id$='ctl00_c1_PurchasessCriteriaDetails_productCategoryIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(e.productCategory).text)), null != e.product && "null" != e.product && "" !== e.product.trim() && (setParsedElementValue("ctl00_c1_PurchasessCriteriaDetails_productIDManyPickLists_manyPickListsDropDown_ClientState", e.product), $("input[id$='ctl00_c1_PurchasessCriteriaDetails_productIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(e.product).text)), null != e.subProduct && "null" != e.subProduct && "" !== e.subProduct.trim() && (setParsedElementValue("ctl00_c1_PurchasessCriteriaDetails_subProductIDManyPickLists_manyPickListsDropDown_ClientState", e.subProduct), $("input[id$='ctl00_c1_PurchasessCriteriaDetails_subProductIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(e.subProduct).text)), null != e.saleAmount && "null" != e.saleAmount && "" !== e.saleAmount.trim() && setElementValue("dropDownSaleAmount", e.saleAmount), null != e.downPaymentAmount && "null" != e.downPaymentAmount && "" !== e.downPaymentAmount.trim() && setElementValue("dropDownDownPaymentAmount", e.downPaymentAmount), null != e.fees1 && "null" != e.fees1 && "" !== e.fees1.trim() && setElementValue("dropDownDownFees1Amount", e.fees1), null != e.fees2 && "null" != e.fees2 && "" !== e.fees2.trim() && setElementValue("dropDownDownFees2Amount", e.fees2), null != e.fees3 && "null" != e.fees3 && "" !== e.fees3.trim() && setElementValue("dropDownDownFees3Amount", e.fees3), null != e.fees4 && "null" != e.fees4 && "" !== e.fees4.trim() && setElementValue("dropDownDownFees4Amount", e.fees4), null != e.proposalValue && "null" != e.proposalValue && "" !== e.proposalValue.trim() && setElementValue("dropDownDownFees5Amount", e.proposalValue), null != e.saleTypes && "null" != e.saleTypes && "" !== e.saleTypes.trim() && (setParsedElementValue("ctl00_c1_PurchasessCriteriaDetails_dropDownSaleTypes_ClientState", e.saleTypes), $("input[id$='ctl00_c1_PurchasessCriteriaDetails_dropDownSaleTypes_Input']").val(JSON.parse(e.saleTypes).text)), null != e.saleStatus && "null" != e.saleStatus && "" !== e.saleStatus.trim() && (setParsedElementValue("ctl00_c1_PurchasessCriteriaDetails_dropDownSaleStatusTypes_ClientState", e.saleStatus), $("input[id$='ctl00_c1_PurchasessCriteriaDetails_dropDownSaleStatusTypes_Input']").val(JSON.parse(e.saleStatus).text)), null != e.milestones && "null" != e.milestones && "" !== e.milestones.trim() && setElementValue("milestoneStatesDropDown", e.milestones), null != e.saleDisposition && "null" != e.saleDisposition && "" !== e.saleDisposition.trim() && (setParsedElementValue("ctl00_c1_PurchasessCriteriaDetails_saleDispositionIDManyPickLists_manyPickListsDropDown_ClientState", e.saleDisposition), $("input[id$='ctl00_c1_PurchasessCriteriaDetails_saleDispositionIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(e.saleDisposition).text)), null != e.purchasesPickList1Item && "null" != e.purchasesPickList1Item && "" !== e.purchasesPickList1Item.trim() && (setParsedElementValue("ctl00_c1_PurchasessCriteriaDetails_purchasesPickList1ItemIDManyPickLists_manyPickListsDropDown_ClientState", e.purchasesPickList1Item), $("input[id$='ctl00_c1_PurchasessCriteriaDetails_purchasesPickList1ItemIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(e.purchasesPickList1Item).text)), null != e.purchasesPickList2Item && "null" != e.purchasesPickList2Item && "" !== e.purchasesPickList2Item.trim() && (setParsedElementValue("ctl00_c1_PurchasessCriteriaDetails_purchasesPickList2ItemIDManyPickLists_manyPickListsDropDown_ClientState", e.purchasesPickList2Item), $("input[id$='ctl00_c1_PurchasessCriteriaDetails_purchasesPickList2ItemIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(e.purchasesPickList2Item).text)), null != e.purchasesPickList3Item && "null" != e.purchasesPickList3Item && "" !== e.purchasesPickList3Item.trim() && (setParsedElementValue("ctl00_c1_PurchasessCriteriaDetails_purchasesPickList3ItemIDManyPickLists_manyPickListsDropDown_ClientState", e.purchasesPickList3Item), $("input[id$='ctl00_c1_PurchasessCriteriaDetails_purchasesPickList3ItemIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(e.purchasesPickList3Item).text)), null != e.purchasesText1 && "null" != e.purchasesText1 && "" !== e.purchasesText1.trim() && setElementValue("textBoxPurchasesText1", e.purchasesText1), null != e.purchasesText2 && "null" != e.purchasesText2 && "" !== e.purchasesText2.trim() && setElementValue("textBoxPurchasesText2", e.purchasesText2), null != e.purchasesBool1 && "null" != e.purchasesBool1 && "" !== e.purchasesBool1.trim() && setElementValue("dropDownPurchasesBool1", e.purchasesBool1), null != e.purchasesBool2 && "null" != e.purchasesBool2 && "" !== e.purchasesBool2.trim() && setElementValue("dropDownPurchasesBool2", e.purchasesBool2) } function setSalesCriteriaFilters(e) { null != e.salesTeam && "null" != e.salesTeam && "" !== e.salesTeam.trim() && (setParsedElementValue("ctl00_c1_SalesCriteriaDetails_salesTeamIDManyPickLists_manyPickListsDropDown_ClientState", e.salesTeam), $("input[id$='ctl00_c1_SalesCriteriaDetails_salesTeamIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(e.salesTeam).text)), null != e.podium && "null" != e.podium && "" !== e.podium.trim() && (setParsedElementValue("ctl00_c1_SalesCriteriaDetails_podiumIDManyPickLists_manyPickListsDropDown_ClientState", e.podium), $("input[id$='ctl00_c1_SalesCriteriaDetails_podiumIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(e.podium).text)), null != e.salesAgent && "null" != e.salesAgent && "" !== e.salesAgent.trim() && (setParsedElementValue("ctl00_c1_SalesCriteriaDetails_salesRepIDManyPickLists_manyPickListsDropDown_ClientState", e.salesAgent), $("input[id$='ctl00_c1_SalesCriteriaDetails_salesRepIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(e.salesAgent).text)), null != e.salesCloser1 && "null" != e.salesCloser1 && "" !== e.salesCloser1.trim() && (setParsedElementValue("ctl00_c1_SalesCriteriaDetails_salesCloser1IDManyPickLists_manyPickListsDropDown_ClientState", e.salesCloser1), $("input[id$='ctl00_c1_SalesCriteriaDetails_salesCloser1IDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(e.salesCloser1).text)), null != e.salesCloser2 && "null" != e.salesCloser2 && "" !== e.salesCloser2.trim() && (setParsedElementValue("ctl00_c1_SalesCriteriaDetails_salesCloser2IDManyPickLists_manyPickListsDropDown_ClientState", e.salesCloser2), $("input[id$='ctl00_c1_SalesCriteriaDetails_salesCloser2IDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(e.salesCloser2).text)), null != e.extRep && "null" != e.extRep && "" !== e.extRep.trim() && (setParsedElementValue("ctl00_c1_SalesCriteriaDetails_exitIDManyPickLists_manyPickListsDropDown_ClientState", e.extRep), $("input[id$='ctl00_c1_SalesCriteriaDetails_exitIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(e.extRep).text)), null != e.verificationOfficer && "null" != e.verificationOfficer && "" !== e.verificationOfficer.trim() && (setParsedElementValue("ctl00_c1_SalesCriteriaDetails_verificationOfficerIDManyPickLists_manyPickListsDropDown_ClientState", e.verificationOfficer), $("input[id$='ctl00_c1_SalesCriteriaDetails_verificationOfficerIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(e.verificationOfficer).text)), null != e.salesPickList1Item && "null" != e.salesPickList1Item && "" !== e.salesPickList1Item.trim() && (setParsedElementValue("ctl00_c1_SalesCriteriaDetails_salesPickList1ItemIDManyPickLists_manyPickListsDropDown_ClientState", e.salesPickList1Item), $("input[id$='ctl00_c1_SalesCriteriaDetails_salesPickList1ItemIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(e.salesPickList1Item).text)), null != e.salesExitTeam && "null" != e.salesExitTeam && "" !== e.salesExitTeam.trim() && (setParsedElementValue("ctl00_c1_SalesCriteriaDetails_exitTeamIDManyPickLists_manyPickListsDropDown_ClientState", e.salesExitTeam), $("input[id$='ctl00_c1_SalesCriteriaDetails_exitTeamIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(e.salesExitTeam).text)), null != e.extRep1 && "null" != e.extRep1 && "" !== e.extRep1.trim() && (setParsedElementValue("ctl00_c1_SalesCriteriaDetails_exitRep1IDManyPickLists_manyPickListsDropDown_ClientState", e.extRep1), $("input[id$='ctl00_c1_SalesCriteriaDetails_exitRep1IDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(e.extRep1).text)), null != e.extRep2 && "null" != e.extRep2 && "" !== e.extRep2.trim() && (setParsedElementValue("ctl00_c1_SalesCriteriaDetails_exitRep2IDManyPickLists_manyPickListsDropDown_ClientState", e.extRep2), $("input[id$='ctl00_c1_SalesCriteriaDetails_exitRep2IDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(e.extRep2).text)), null != e.extRep3 && "null" != e.extRep3 && "" !== e.extRep3.trim() && (setParsedElementValue("ctl00_c1_SalesCriteriaDetails_exitRep3IDManyPickLists_manyPickListsDropDown_ClientState", e.extRep3), $("input[id$='ctl00_c1_SalesCriteriaDetails_exitRep3IDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(e.extRep3).text)), null != e.salesText && "null" != e.salesText && "" !== e.salesText.trim() && setElementValue("textBoxSalesText1", e.salesText), null != e.salesDecimal && "null" != e.salesDecimal && "" !== e.salesDecimal.trim() && setElementValue("dropDownSalesDecimal1", e.salesDecimal), null != e.salesBool && "null" != e.salesBool && "" !== e.salesBool.trim() && setElementValue("dropDownSalesBool1", e.salesBool) } function setMarketingCriteriaFilters(e) { null != e.marketingTeam && "null" != e.marketingTeam && "" !== e.marketingTeam.trim() && (setParsedElementValue("ctl00_c1_MarketingCriteriaDetails_marketingTeamIDManyPickLists_manyPickListsDropDown_ClientState", e.marketingTeam), $("input[id$='ctl00_c1_MarketingCriteriaDetails_marketingTeamIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(e.marketingTeam).text)), null != e.marketingAgent && "null" != e.marketingAgent && "" !== e.marketingAgent.trim() && (setParsedElementValue("ctl00_c1_MarketingCriteriaDetails_marketingAgentIDManyPickLists_manyPickListsDropDown_ClientState", e.marketingAgent), $("input[id$='ctl00_c1_MarketingCriteriaDetails_marketingAgentIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(e.marketingAgent).text)), null != e.marketingCloser && "null" != e.marketingCloser && "" !== e.marketingCloser.trim() && (setParsedElementValue("ctl00_c1_MarketingCriteriaDetails_marketingCloserIDManyPickLists_manyPickListsDropDown_ClientState", e.marketingCloser), $("input[id$='ctl00_c1_MarketingCriteriaDetails_marketingCloserIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(e.marketingCloser).text)), null != e.confirmer && "null" != e.confirmer && "" !== e.confirmer.trim() && (setParsedElementValue("ctl00_c1_MarketingCriteriaDetails_confirmerIDManyPickLists_manyPickListsDropDown_ClientState", e.confirmer), $("input[id$='ctl00_c1_MarketingCriteriaDetails_confirmerIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(e.confirmer).text)), null != e.resetter && "null" != e.resetter && "" !== e.resetter.trim() && (setParsedElementValue("ctl00_c1_MarketingCriteriaDetails_resetterIDManyPickLists_manyPickListsDropDown_ClientState", e.resetter), $("input[id$='ctl00_c1_MarketingCriteriaDetails_resetterIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(e.resetter).text)), null != e.venue && "null" != e.venue && "" !== e.venue.trim() && (setParsedElementValue("ctl00_c1_MarketingCriteriaDetails_venueIDManyPickLists_manyPickListsDropDown_ClientState", e.venue), $("input[id$='ctl00_c1_MarketingCriteriaDetails_venueIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(e.venue).text)), null != e.campaign && "null" != e.campaign && "" !== e.campaign.trim() && (setParsedElementValue("ctl00_c1_MarketingCriteriaDetails_campaignIDManyPickLists_manyPickListsDropDown_ClientState", e.campaign), $("input[id$='ctl00_c1_MarketingCriteriaDetails_campaignIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(e.campaign).text)), null != e.channel && "null" != e.channel && "" !== e.channel.trim() && (setParsedElementValue("ctl00_c1_MarketingCriteriaDetails_channelIDManyPickLists_manyPickListsDropDown_ClientState", e.channel), $("input[id$='ctl00_c1_MarketingCriteriaDetails_channelIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(e.channel).text)), null != e.hotel && "null" != e.hotel && "" !== e.hotel.trim() && (setParsedElementValue("ctl00_c1_MarketingCriteriaDetails_hotelIDManyPickLists_manyPickListsDropDown_ClientState", e.hotel), $("input[id$='ctl00_c1_MarketingCriteriaDetails_hotelIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(e.hotel).text)), null != e.giftIDs && "null" != e.giftIDs && "" !== e.giftIDs.trim() && (setParsedElementValue("ctl00_c1_MarketingCriteriaDetails_giftIDsManyPickLists_manyPickListsDropDown_ClientState", e.giftIDs), $("input[id$='ctl00_c1_MarketingCriteriaDetails_giftIDsManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(e.giftIDs).text)), null != e.marketingPickList1Item1 && "null" != e.marketingPickList1Item1 && "" !== e.marketingPickList1Item1.trim() && (setParsedElementValue("ctl00_c1_MarketingCriteriaDetails_marketingPickList1ItemIDManyPickLists_manyPickListsDropDown_ClientState", e.marketingPickList1Item1), $("input[id$='ctl00_c1_MarketingCriteriaDetails_marketingPickList1ItemIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(e.marketingPickList1Item1).text)), null != e.marketingPickList1Item2 && "null" != e.marketingPickList1Item2 && "" !== e.marketingPickList1Item2.trim() && (setParsedElementValue("ctl00_c1_MarketingCriteriaDetails_marketingPickList2ItemIDManyPickLists_manyPickListsDropDown_ClientState", e.marketingPickList1Item2), $("input[id$='ctl00_c1_MarketingCriteriaDetails_marketingPickList2ItemIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(e.marketingPickList1Item2).text)), null != e.marketingText && "null" != e.marketingText && "" !== e.marketingText.trim() && setElementValue("textBoxMarketingText1", e.marketingText), null != e.marketingBool && "null" != e.marketingBool && "" !== e.marketingBool.trim() && setElementValue("dropDownMarketingBool1", e.marketingBool), null != e.depositAmount && "null" != e.depositAmount && "" !== e.depositAmount.trim() && setElementValue("dropDownDepositAmount", e.depositAmount), null != e.depositRefundable && "null" != e.depositRefundable && "" !== e.depositRefundable.trim() && setElementValue("dropDownDepositRefundable", e.depositRefundable) } function setTourCriteriaFilters(e) { null != e.tourSource && "null" != e.tourSource && "" !== e.tourSource.trim() && (setParsedElementValue("ctl00_c1_TourCriteriaDetails_tourSourceIDManyPickLists_manyPickListsDropDown_ClientState", e.tourSource), $("input[id$='ctl00_c1_TourCriteriaDetails_tourSourceIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(e.tourSource).text)), null != e.office && "null" != e.office && "" !== e.office.trim() && (setParsedElementValue("ctl00_c1_TourCriteriaDetails_tourTypeIDManyPickLists_manyPickListsDropDown_ClientState", e.office), $("input[id$='ctl00_c1_TourCriteriaDetails_tourTypeIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(e.office).text)); var t = { "-Assigned": "ctl00_c1_TourCriteriaDetails_dropDownTourStatusTypes_i0_checkBox", "-Unassigned": "ctl00_c1_TourCriteriaDetails_dropDownTourStatusTypes_i1_checkBox", "-Exclude": "ctl00_c1_TourCriteriaDetails_dropDownTourStatusTypes_i2_checkBox", Booked: "ctl00_c1_TourCriteriaDetails_dropDownTourStatusTypes_i3_checkBox", Cancelled: "ctl00_c1_TourCriteriaDetails_dropDownTourStatusTypes_i4_checkBox", "No Show": "ctl00_c1_TourCriteriaDetails_dropDownTourStatusTypes_i5_checkBox", "Show Qualified": "ctl00_c1_TourCriteriaDetails_dropDownTourStatusTypes_i6_checkBox", "Not Qualified": "ctl00_c1_TourCriteriaDetails_dropDownTourStatusTypes_i7_checkBox", "LO-NotContacted": "ctl00_c1_TourCriteriaDetails_dropDownTourStatusTypes_i8_checkBox", "LO-Unscheduled": "ctl00_c1_TourCriteriaDetails_dropDownTourStatusTypes_i9_checkBox", "LC-NotInterested": "ctl00_c1_TourCriteriaDetails_dropDownTourStatusTypes_i10_checkBox", "LC-Ineligible": "ctl00_c1_TourCriteriaDetails_dropDownTourStatusTypes_i11_checkBox", "BO-Confirmed": "ctl00_c1_TourCriteriaDetails_dropDownTourStatusTypes_i12_checkBox", "BC-CustomMarketing": "ctl00_c1_TourCriteriaDetails_dropDownTourStatusTypes_i13_checkBox", "TC-CourtesyTour": "ctl00_c1_TourCriteriaDetails_dropDownTourStatusTypes_i14_checkBox", "TC-CustomSales": "ctl00_c1_TourCriteriaDetails_dropDownTourStatusTypes_i15_checkBox", Ignore: "ctl00_c1_TourCriteriaDetails_dropDownTourStatusTypes_i16_checkBox" }; "All" != e.tourStatus && "" !== e.tourStatus.trim() && (setCheckboxes(t, e.tourStatus), $("input[id$='ctl00_c1_TourCriteriaDetails_dropDownTourStatusTypes_Input']").val(JSON.parse('{"logEntries": [], "value": "' + e.tourStatus + '", "text": "' + e.tourStatus + '", "enabled": true, "checkedIndices": [], "checkedItemsTextOverflows": false }').text)), null != e.tourDisposition && "null" != e.tourDisposition && "" !== e.tourDisposition.trim() && (setParsedElementValue("ctl00_c1_TourCriteriaDetails_tourConcernTypeIDManyPickLists_manyPickListsDropDown_ClientState", e.tourDisposition), $("input[id$='ctl00_c1_TourCriteriaDetails_tourConcernTypeIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(e.tourDisposition).text)), null != e.importMethod && "null" != e.importMethod && "" !== e.importMethod.trim() && (setParsedElementValue("ctl00_c1_TourCriteriaDetails_tourTypePickList1ItemIDManyPickLists_manyPickListsDropDown_ClientState", e.importMethod), $("input[id$='ctl00_c1_TourCriteriaDetails_tourTypePickList1ItemIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(e.importMethod).text)), null != e.businessPartner && "null" != e.businessPartner && "" !== e.businessPartner.trim() && (setParsedElementValue("ctl00_c1_TourCriteriaDetails_locationIDManyPickLists_manyPickListsDropDown_ClientState", e.businessPartner), $("input[id$='ctl00_c1_TourCriteriaDetails_locationIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(e.businessPartner).text)), null != e.businessGroup && "null" != e.businessGroup && "" !== e.businessGroup.trim() && (setParsedElementValue("ctl00_c1_TourCriteriaDetails_regionIDManyPickLists_manyPickListsDropDown_ClientState", e.businessGroup), $("input[id$='ctl00_c1_TourCriteriaDetails_regionIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(e.businessGroup).text)), null != e.isShow && "" !== e.isShow.trim() && null != document.getElementById("dropDownTourBool1") && setElementValue("dropDownTourBool1", e.isShow), null != e.saleBonusGifts && "null" != e.saleBonusGifts && "" !== e.saleBonusGifts.trim() && (setParsedElementValue("ctl00_c1_TourCriteriaDetails_toursManyPickList1ItemIDsManyPickLists_manyPickListsDropDown_ClientState", e.saleBonusGifts), $("input[id$='ctl00_c1_TourCriteriaDetails_toursManyPickList1ItemIDsManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(e.saleBonusGifts).text)) } function setLeadCriteriaFilters(e) { null != e.leadSourceFile && "null" != e.leadSourceFile && "" !== e.leadSourceFile.trim() && (setParsedElementValue("ctl00_c1_LeadCriteria_leadSourceIDManyPickLists_manyPickListsDropDown_ClientState", e.leadSourceFile), $("input[id$='ctl00_c1_LeadCriteria_leadSourceIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(e.leadSourceFile).text)), null != e.leadDisposition && "null" != e.leadDisposition && "" !== e.leadDisposition.trim() && (setParsedElementValue("ctl00_c1_LeadCriteria_leadDispositionIDManyPickLists_manyPickListsDropDown_ClientState", e.leadDisposition), $("input[id$='ctl00_c1_LeadCriteria_leadDispositionIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(e.leadDisposition).text)), null != e.calendarName && "null" != e.calendarName && "" !== e.calendarName.trim() && (setParsedElementValue("ctl00_c1_LeadCriteria_leadPickList1ItemIDManyPickLists_manyPickListsDropDown_ClientState", e.calendarName), $("input[id$='ctl00_c1_LeadCriteria_leadPickList1ItemIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(e.calendarName).text)), null != e.leadSource && "null" != e.leadSource && "" !== e.leadSource.trim() && (setParsedElementValue("ctl00_c1_LeadCriteria_leadPickList2ItemIDManyPickLists_manyPickListsDropDown_ClientState", e.leadSource), $("input[id$='ctl00_c1_LeadCriteria_leadPickList2ItemIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(e.leadSource).text)) } function escapeRegExp(e) { return e.replace(/[.*+?^${}()|[\]\\]/g, "\\$&") } function displayRowsJoin(e) { for (var t = 0; t < e.length; t++) { var i = e[t], a = document.querySelectorAll(".join-inputs-container"), s = t + 1; a.forEach(function (e) { if (e.getAttribute("id") === "joinContainer" + s) { e.setAttribute("data-id", i.reportJoinId); var t = e.querySelector(".firstJoinKey"), a = e.querySelector(".secondJoinKey"), r = e.querySelector(".uniqueKey"), l = e.querySelector(".joinSelect"); if (t.value = i.leftKey, a.value = i.rightKey, r.value = i.uniqueKey, l) { l.setAttribute("data-id", i.reportJoinId); for (var n = 0; n <= l.options.length; n++) { var o = l.options[n]; if (o.value == i.type) { o.selected = !0; break } } } } }) } } function displayRowsKpi(e) { for (var t = document.getElementById("accordion"), i = 0; i < e.length; i++)if (t) { var a = t.querySelectorAll(".header-container"), s = e[i]; a.forEach(function (e) { if (s.name = sanitizeColumnName(s.name), s && e.getAttribute("data-column-name") === s.name) { var t = e.nextElementSibling, i = e.querySelector('[id^="operation"]'), a = e.querySelector('[id^="condition"]'), r = t.querySelector("#mathFormat" + s.name + "1"); if ("" != s.typeFormat && r) for (var l = 0; l <= r.options.length; l++) { var n = r.options[l]; n && n.value == s.typeFormat && (n.selected = !0) } if (i && (i.value = s.operation), a && (a.value = s.condition), "" != s.condition) { var o = s.condition.split(/\s*(&&|\|\|)\s*/); let c = /IF\s+(.*?)\s+THEN\s+(.*?)\s+ELSE\s+THEN\s+((?:\S+\s+\S+|\S+))/, u = s.condition.match(c); var p = 1; if (u) { let D = u[1], d = u[2], m = u[3] ? u[3].split(" ") : [], y = m[0] || "", k = m[1] || ""; var C = t.querySelector("#ffirstConditionContainerInput" + s.name + p), P = t.querySelector("#ssecondConditionContainerInput" + s.name + p), g = t.querySelector("#fourConditionContainerInput" + s.name + p), I = t.querySelector("#ifresultConditionContainerInput" + s.name + p), h = t.querySelector("#elseresultConditionContainerInput" + s.name + p), w = t.querySelector("#operators" + s.name + p); let f = D.split(/\s*(==|!=|>=|<=|>|<)\s*/); if (C && (C.value = f[0]), P && (P.value = f[2]), g && (g.value = k), I && (I.value = d), h && (h.value = y), w && "" != f[1]) for (var l = 0; l <= w.options.length; l++) { var n = w.options[l]; n && n.value == f[1] && (n.selected = !0) } } else for (var _ = 0; _ < o.length; _++)if (_ > 0 && ("&&" == o[_] || "||" == o[_])) { addCondition("conditionContainerLabel" + s.name, s.name, p + 1); var L = t.querySelector("#logicalOperators" + s.name + p); if (L) for (var l = 0; l <= L.options.length; l++) { var n = L.options[l]; n && n.value == o[_] && (n.selected = !0) } p++ } else if ("&&" != o[_] || "||" != o[_]) { var x = t.querySelector("#firstConditionContainerInput" + s.name + p), T = t.querySelector("#secondConditionContainerInput" + s.name + p), w = t.querySelector("#operators" + s.name + p); let v = o[_].split(/\s*(==|!=|>=|<=|>|<)\s*/); if (x && (x.value = v[0]), T && (T.value = v[2]), w && "" != v[1]) for (var l = 0; l <= w.options.length; l++) { var n = w.options[l]; n && n.value == v[1] && (n.selected = !0) } } } if (s.operation) { for (var E = s.operation.match(/(?:\d+\.\d+|\d+|[a-zA-Z0-9%_$().]+|[+\-*\/%()])/g).filter(e => "" !== e.trim()), p = 1, S = /^[+\-*\/%]$/, _ = 0; _ < E.length; _++)if (S.test(E[_])) { addSet("operationsContainerLabel" + s.name, s.name, p + 1); var M = t.querySelector("#mathOperators" + s.name + p); if (M) for (var l = 0; l <= M.options.length; l++) { var n = M.options[l]; n && n.value == E[_] && (n.selected = !0) } p++ } else if ("&&" != E[_] && "||" != E[_]) { var B = t.querySelector("#listContainerInput" + s.name + p); B && (B.value = (B.value || "") + E[_]) } } return } }) } } function findKpiByCode(e) { var t = customKpis.find(function (t) { return t.code === e }); return t ? "rowData." + t.columkey : e } function getSelectedUserIds() { let e = document.querySelectorAll(".user-checkbox:checked"), t = Array.from(e).map(e => e.value); return t } function retrieveJoinData(e) { let t = document.querySelectorAll(".join-inputs-container"), i = []; for (let a of t) { let s = a.querySelector(".joinSelect"), r = a.querySelector('input[id^="firstJoinKey_"]'), l = a.querySelector('input[id^="secondJoinKey_"]'), n = a.querySelector('input[id^="uniqueKey_"]'), o = parseInt(s.getAttribute("data-id")), c = s.value, u = r.value.trim(), p = l.value.trim(), D = n.value.trim(); if (u && p && c) { let d = { joinType: c, leftKey: u, rightKey: p, uniqueKey: D, reportJoinId: o, reportId: e }; i.push(d) } } return i } function getElementValue(e) { return document.getElementById(e)?.value || null } function getParsedElementValue(e) { try { let t = document.getElementById(e)?.value || "null", i = JSON.parse(t); return i?.value || null } catch (a) { return null } } function getParsedElementJson(e) { try { let t = document.getElementById(e)?.value || "null", i = '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}'; if (t === i) return null; return t } catch (a) { return null } } function setElementValue(e, t) { var i = document.getElementById(e); if (i) switch (i.tagName.toLowerCase()) { case "input": "checkbox" === i.type || "radio" === i.type ? i.checked = t : i.value = t; break; case "select": case "textarea": i.value = t } } function clearCheckboxes(e) { for (var t in e) if (e.hasOwnProperty(t)) { var i = e[t], a = document.getElementById(i); a && (a.checked = !1) } } function setParsedElementValue(e, t) { try { document.getElementById(e).value = t; return } catch (i) { return } } function updateOperationsList(e) { return e.map(e => replaceCondition(e)) } function replaceCondition(e) { let t = e.condition, i = /(\w+\.\w+)\s*>=\s*(\d+)(Days|Weeks|Months|Years)\(\)/, a = t.match(i); if (a) { let s = a[1], r = parseInt(a[2]), l = a[3], n = new Date, o = new Date(n); switch (l) { case "Days": o.setDate(n.getDate() - r); break; case "Weeks": o.setDate(n.getDate() - 7 * r); break; case "Months": o.setMonth(n.getMonth() - r); break; case "Years": o.setFullYear(n.getFullYear() - r) }let c = n.toISOString().split("T")[0] + " 00:00:00.000", u = o.toISOString().split("T")[0] + " 00:00:00.000", p = t.replace(i, `${s} >= '${u}' && ${s} <= '${c}'`); e.condition = p } return e } function processJoinsBySavedData(e, t) { for (let i of e) { let a = i.type.toLowerCase(), s = i.leftKey.trim(), r = i.rightKey.trim(), l = i.uniqueKey.trim(); if (s && r) { let n = s.split(".")[0], o = r.split(".")[0], c = s.split(".")[1], u = r.split(".")[1], p = t[n], D = t[o]; if (!p || !D) return t; let d; "left" === a ? d = performLeftJoin(p, D, c, u) : "right" === a ? d = performRightJoin(p, D, c, u) : "inner" === a && (d = performInnerJoin(p, D, c, u)); let m = makeDataUnique(d, l); t[n] = m } } return t } function performLeftJoin(e, t, i, a) { return lodash.flatMap(e, e => { let s = lodash.filter(t, t => t[a].toLowerCase() === e[i].toLowerCase()); return 0 === s.length ? [lodash.merge({}, e)] : lodash.map(s, t => lodash.merge({}, e, t)) }) } function performRightJoin(e, t, i, a) { return lodash.flatMap(t, t => { let s = lodash.filter(e, e => e[i].toLowerCase() === t[a].toLowerCase()); return 0 === s.length ? [lodash.merge({}, t)] : lodash.map(s, e => lodash.merge({}, e, t)) }) } function performInnerJoin(e, t, i, a) { return lodash.flatMap(e, e => { let s = lodash.filter(t, t => t[a].toLowerCase() === e[i].toLowerCase()); return lodash.map(s, t => lodash.merge({}, e, t)) }) } function makeDataUnique(e, t) { return lodash.uniqBy(e, t) } function processJoins(e) { let t = document.querySelectorAll(".join-inputs-container"), i = e; for (let a of t) { let s = a.querySelector(".joinSelect"), r = a.querySelector('input[id^="firstJoinKey_"]'), l = a.querySelector('input[id^="secondJoinKey_"]'), n = a.querySelector('input[id^="uniqueKey_"]'), o = s.value, c = r.value.trim(), u = l.value.trim(); if (c && u) { let p = c.split(".")[0], D = u.split(".")[0], d = c.split(".")[1], m = u.split(".")[1], y = i[p], k = i[D]; if (!y || !k) return i; let C; "left" === o ? C = performLeftJoin(y, k, d, m) : "right" === o ? C = performRightJoin(y, k, d, m) : "inner" === o && (C = performInnerJoin(y, k, d, m)); let P = makeDataUnique(C, n); i[p] = P } } return i } function getCustomAggFuncs(e, t, i) { let a = {}; for (let s of e) a[s.columnName] = function (e) { let t = e.filter(e => null != e), i; s.condition && (i = Function("value", "return " + s.condition.replace(s.condition.split(" ")[0], "value")), t = t.filter(i)); let a; switch (s.aggFunc) { case "sum": case "derived": a = t.reduce((e, t) => e + t, 0); break; case "count": a = t.reduce((e, t) => e + t, 0); break; default: a = null }return a }; return a } function evalCondition(condition, row, operation) { if (null == row) return !1; if (!condition) return !0; if ("ifelse" === operation.aggFunc) { let regex = /IF\s+(.*?)\s+THEN/; condition = condition.replace(/\b\w+\.(\s*)/g, "row$1."); let matches = condition.match(regex); condition = matches[1] } else condition = condition.replace(/\b\w+\.(\s*)/g, "row$1."); conditionCode = condition.replace(/\.(\s*)(\w)/g, (e, t, i) => "." + t + i.toLowerCase()); let patron = /Today\(\)\s*([\+\-])\s*(\d+)(Days|Weeks|Months|Years)|Today\(\)/; patron.test(conditionCode) && (conditionCode = replaceDateConditions(conditionCode)); try { if ("count" === operation.aggFunc) { var r = eval(conditionCode); return r = r ? 1 : 0 } if ("ifelse" === operation.aggFunc) { if (-1 !== conditionCode.indexOf(".")) { var r = eval(conditionCode); return r = r ? 1 : 0 } { let data = conditionCode.split(/\s*(==|!=|>=|<=|>|<)\s*/); var r = eval("row." + data[0] + " " + data[1] + " " + data[2]); return r = r ? 1 : 0 } } return eval(conditionCode) } catch (e) { return $("#loader").hide(), console.error("Error evaluating condition:", e), 0 } } function regexConditionValue(e, t, i) { try { e = e.replace(/\b\w+\.(\s*)/g, "row$1."); let a = /IF\s+(.*?)\s+THEN\s+(.*?)\s+ELSE\s+THEN\s+((?:\S+\s+\S+|\S+))/, s = e.match(a); if (s) { let r = s[2], l = s[3] ? s[3].split(" ") : [], n = l[0] || "", o = l[1] || ""; o = o.replace(/\.(\s*)(\w)/g, (e, t, i) => "." + t + i.toLowerCase()); let c = o.split("row.")[1], u = t[c]; if (1 == i) return r; if (0 == i) return n + (void 0 !== u ? u : "") } return null } catch (p) { return $("#loader").hide(), console.error("Error evaluating condition:", p), null } } function evalConditionValue(condition, params) { condition = condition.replace(/\b\w+\.(\s*)/g, "row$1."); let data = { ...params.node.aggData, ...params.data }, regex = /IF\s+(.*?)\s+THEN\s+(.*?)\s+ELSE\s+THEN\s+((?:\S+\s+\S+|\S+))/; try { let matches = condition.match(regex); if (matches) { var r; let condition = matches[1], afterThen = matches[2], splitResult = matches[3] ? matches[3].split(" ") : [], prefix = splitResult[0] || "", rowPart = splitResult[1] || ""; rowPart = rowPart.replace(/\.(\s*)(\w)/g, (e, t, i) => "." + t + i.toLowerCase()); let rowValue = data[rowPart], datas = condition.split(/\s*(==|!=|>=|<=|>|<)\s*/), value = data[datas[0]]; if (/[%]/.test(value) && (value = value.replace("%", "")), /[$]/.test(value) && (value = value.replace("$", "")), /[,]/.test(value) && (value = value.replace(",", "")), eval(value + " " + datas[1] + " " + datas[2])) return afterThen; return prefix + (void 0 !== rowValue ? rowValue : "") } return null } catch (error) { return $("#loader").hide(), console.error("Error evaluating condition:", error), null } } function replaceDateConditions(e) { let t = /(\b\w+)\s*(==|!=|>|>=|<|<=)\s*Today\(\)\s*([\+\-]\s*\d+)(Days|Weeks|Months|Years)|(\b\w+)\s*(==|!=|>|>=|<|<=)\s*Today\(\)/g; try { return e.replace(t, (e, t, i, a, s, r, l) => { if (r) { let n = convertToISO({ unit: "Today" }); return `${r} ${l} '${n.toString()}'` } if (t) { let o = a.trim().charAt(0), c = parseInt(a.trim().slice(1).trim(), 10); if (isNaN(c)) throw Error(`Invalid offset value: ${a}`); let u = convertToISO({ operator: o, unit: s, value: c }); return `${t} ${i} '${u.toString()}'` } }) } catch (i) { return $("#loader").hide(), console.error("Error in replaceDateConditions:", i), e } } function convertToISO(e) { let t = extractTimeCondition(); if ((!(t instanceof Date) || isNaN(t)) && (t = new Date), "Today" === e.unit) return t.toISOString(); switch (e.unit) { case "Days": t.setDate(t.getDate() + ("+" === e.operator ? e.value : -e.value)); break; case "Weeks": t.setDate(t.getDate() + ("+" === e.operator ? 7 * e.value : -(7 * e.value))); break; case "Months": t.setMonth(t.getMonth() + ("+" === e.operator ? e.value : -e.value)); break; case "Years": t.setFullYear(t.getFullYear() + ("+" === e.operator ? e.value : -e.value)); break; default: throw Error(`Unknown time unit: ${e.unit}`) }return t.toISOString() } function extractTimeCondition() { let e = document.getElementById("dropDownYearRanges").value, t = new Date, i; switch (e) { case "20": i = new Date(t.getFullYear(), t.getMonth(), t.getDate()); break; case "22": i = new Date(t.getFullYear() - 1, t.getMonth(), t.getDate()); break; case "50": i = new Date(t.getFullYear() + 2, t.getMonth(), t.getDate()); break; case "51": i = new Date(t.getFullYear() + 3, t.getMonth(), t.getDate()); break; case "52": i = new Date(t.getFullYear() + 4, t.getMonth(), t.getDate()); break; case "53": i = new Date(t.getFullYear() + 5, t.getMonth(), t.getDate()); break; default: i = "Invalid selection" }return i } function evalDerivedOperation(e, t) { let i = "let result = "; try { return i += e.replace(/(?:\b\w+|\$\w+|\w+\$\w+|\w+\$)+/g, e => { if (!isNaN(e)) return e; let t = e.includes(".") ? e.split(".")[0] + "." : "", i = e.replace(t, ""); if (i.includes(".")) { let a = i.split("."), s = "row"; return a.forEach(e => { s += `["${e}"]` }), s + " || 0" } return `(row["${i}"] || 0)` }) + "; return isNaN(result) || !isFinite(result) ? 0 : result;", i = i.replace(/\$|%/g, ""), Function("row", i)(t) } catch (a) { return $("#loader").hide(), console.error("Error during derived operation evaluation:", a), -1 } } function sortOperations(e) { try { let t = [], i = new Set, a = e.slice(), s = a.length; for (let r = 0; r < a.length; r++) { let l = a[r]; "derived" !== l.aggFunc && (i.add(l.columnName), t.push(l), a.splice(r, 1), r--) } let n = 0; for (; a.length > 0;) { let o = !1; for (let c = 0; c < a.length; c++) { let u = a[c]; if ("derived" === u.aggFunc && canAddDerivedOperation(u, i)) { i.add(u.columnName), t.push(u), a.splice(c, 1), o = !0; break } } if (n++, !o) return $("#loader").hide(), console.error("Circular dependency detected in derived operations. The problematic operations are:"), console.error(a), []; if (n > s) return $("#loader").hide(), console.error("Maximum iterations reached. Aborting. Remaining operations are:"), console.error(a), [] } return t } catch (p) { return $("#loader").hide(), showSwal("error", "Error sort operations"), null } } function canAddDerivedOperation(e, t) { let i = e.operation.split(/([+\-*/()\s])/g); for (let a of i) if ("" !== (a = a.trim()) && !["+", "-", "*", "/", "(", ")"].includes(a)) { if (!isNaN(a)) continue; if (!t.has(a)) return 0 } return 1 } function populateDropdown(e, t) { var i = document.getElementById("ddlColumns"); if (i.innerHTML = "", t && t.length > 0) { var a = Object.keys(t[0]); for (let s of a) { var r = document.createElement("option"); r.value = s, r.innerHTML = s, i.appendChild(r) } } for (let l of e) { var r = document.createElement("option"); r.value = l.field, r.innerHTML = l.headerName, i.appendChild(r) } } function checkUsersInList(e) { let t = document.querySelectorAll(".user-checkbox"); if (!e || 0 === e.length) { t.forEach(e => { e.checked = !1 }); return } t.forEach(t => { e.includes(t.value) ? t.checked = !0 : t.checked = !1 }) } function getFileType(e) { let t = e.name.split(".").pop().toLowerCase(); if ("json" === t) return "json"; if ("csv" === t) return "csv"; throw Error("Unsupported file type") } function csvJSON(e) { let t = e.split("\n"), i = [], a = t[0].split(","); for (let s = 1; s < t.length; s++) { let r = t[s].split(","); if (r.length !== a.length) continue; let l = {}; for (let n = 0; n < a.length; n++)l[a[n].trim().replace('"', "").replace('"', "")] = r[n].trim().replace('"', "").replace('"', ""); i.push(l) } return i } function updateDateRange(e) { let t; t = null === e ? document.getElementById("dropDownDateRanges").value : e; let i = document.getElementById("dropDownYearRanges"), a = document.getElementById("ctl00_c1_DateTimeCriteriaDetails_startDateRadDatePicker_dateInput"), s = document.getElementById("ctl00_c1_DateTimeCriteriaDetails_endDateRadDatePicker_dateInput"), r = new Date, l = "", n = ""; switch (t) { case "": i.value = ""; break; case "-1": case "2": i.value = "2"; break; case "3": l = n = r.toISOString().split("T")[0]; break; case "4": let o = new Date(r); o.setDate(o.getDate() + 1), l = n = o.toISOString().split("T")[0]; break; case "6": let c = new Date(r); c.setDate(c.getDate() - 1), l = n = c.toISOString().split("T")[0]; break; case "23": let u = new Date(r); u.setDate(u.getDate() - 7), l = u.toISOString().split("T")[0], n = r.toISOString().split("T")[0]; break; case "28": let p = new Date(r); p.setDate(p.getDate() + 7), l = r.toISOString().split("T")[0], n = p.toISOString().split("T")[0]; break; case "7": let D = new Date(r); D.setDate(r.getDate() - r.getDay()), l = D.toISOString().split("T")[0], n = r.toISOString().split("T")[0]; break; case "8": let d = new Date(r), m = new Date(r); d.setDate(r.getDate() - r.getDay()), m.setDate(r.getDate() + (6 - r.getDay())), l = d.toISOString().split("T")[0], n = m.toISOString().split("T")[0]; break; case "9": let y = new Date(r), k = new Date(r); y.setDate(r.getDate() + (7 - r.getDay())), k.setDate(y.getDate() + 6), l = y.toISOString().split("T")[0], n = k.toISOString().split("T")[0]; break; case "10": let C = new Date(r), P = new Date(r); C.setDate(r.getDate() - r.getDay() - 7), P.setDate(C.getDate() + 6), l = C.toISOString().split("T")[0], n = P.toISOString().split("T")[0]; break; case "24": let g = new Date(r); g.setDate(r.getDate() - 30), l = g.toISOString().split("T")[0], n = r.toISOString().split("T")[0]; break; case "25": let I = new Date(r); I.setDate(r.getDate() - 60), l = I.toISOString().split("T")[0], n = r.toISOString().split("T")[0]; break; case "26": let h = new Date(r); h.setDate(r.getDate() - 90), l = h.toISOString().split("T")[0], n = r.toISOString().split("T")[0]; break; case "11": let w = new Date(r.getFullYear(), r.getMonth(), 1); l = w.toISOString().split("T")[0], n = r.toISOString().split("T")[0]; break; case "12": let f = new Date(r.getFullYear(), r.getMonth(), 1), _ = new Date(r.getFullYear(), r.getMonth() + 1, 0); l = f.toISOString().split("T")[0], n = _.toISOString().split("T")[0]; break; case "13": let L = new Date(r.getFullYear(), r.getMonth() + 1, 1), x = new Date(r.getFullYear(), r.getMonth() + 2, 0); l = L.toISOString().split("T")[0], n = x.toISOString().split("T")[0]; break; case "14": let T = new Date(r.getFullYear(), r.getMonth() - 1, 1), v = new Date(r.getFullYear(), r.getMonth(), 0); l = T.toISOString().split("T")[0], n = v.toISOString().split("T")[0]; break; case "15": let E = new Date(r.getFullYear(), 3 * Math.floor(r.getMonth() / 3), 1); l = E.toISOString().split("T")[0], n = r.toISOString().split("T")[0]; break; case "18": let S = new Date(r.getFullYear(), 3 * Math.floor((r.getMonth() - 3) / 3), 1), M = new Date(r.getFullYear(), 3 * Math.floor(r.getMonth() / 3), 0); l = S.toISOString().split("T")[0], n = M.toISOString().split("T")[0]; break; case "19": let B = new Date(r.getFullYear(), 0, 1); l = B.toISOString().split("T")[0], n = r.toISOString().split("T")[0]; break; case "20": let V = new Date(r.getFullYear(), 0, 1), R = new Date(r.getFullYear(), 11, 31), b = e => e.toString().padStart(2, "0"); l = `${b(V.getDate())}/${b(V.getMonth() + 1)}/${V.getFullYear()}`, n = `${b(R.getDate())}/${b(R.getMonth() + 1)}/${R.getFullYear()}` }a.value = l, s.value = n, initial_date = l, last_date = n } function updateYearRange(e) { let t; dateRange = null === e ? document.getElementById("dropDownYearRanges").value : e; let i = document.getElementById("ctl00_c1_DateTimeCriteriaDetails_startDateRadDatePicker_dateInput"), a = document.getElementById("ctl00_c1_DateTimeCriteriaDetails_endDateRadDatePicker_dateInput"), s = new Date().getFullYear(); switch (t) { case "20": i.value = i.value.replace(/^(\d{2}\/\d{2}\/)\d{4}/, `$1${s}`), a.value = a.value.replace(/^(\d{2}\/\d{2}\/)\d{4}/, `$1${s}`); break; case "22": i.value = i.value.replace(/^(\d{2}\/\d{2}\/)\d{4}/, `$1${s - 1}`), a.value = a.value.replace(/^(\d{2}\/\d{2}\/)\d{4}/, `$1${s - 1}`); break; case "50": i.value = i.value.replace(/^(\d{2}\/\d{2}\/)\d{4}/, `$1${s - 2}`), a.value = a.value.replace(/^(\d{2}\/\d{2}\/)\d{4}/, `$1${s - 2}`); break; case "51": i.value = i.value.replace(/^(\d{2}\/\d{2}\/)\d{4}/, `$1${s - 3}`), a.value = a.value.replace(/^(\d{2}\/\d{2}\/)\d{4}/, `$1${s - 3}`); break; case "52": i.value = i.value.replace(/^(\d{2}\/\d{2}\/)\d{4}/, `$1${s - 4}`), a.value = a.value.replace(/^(\d{2}\/\d{2}\/)\d{4}/, `$1${s - 4}`); break; case "53": i.value = i.value.replace(/^(\d{2}\/\d{2}\/)\d{4}/, `$1${s - 5}`), a.value = a.value.replace(/^(\d{2}\/\d{2}\/)\d{4}/, `$1${s - 5}`) } } function updateDropdownOptions() { let e = document.querySelectorAll("#dropDownDateRanges, #dropDownYearRanges"); e.forEach(e => { if (!e) return; let t = []; e.querySelectorAll("option").forEach(e => { t.push({ value: e.value, text: e.textContent }) }); let i = new Set, a = new Map; e.querySelectorAll("option").forEach(e => { let t = e.value; i.has(t) || (i.add(t), a.set(t, e.textContent)) }), e.innerHTML = "", i.forEach(t => { let i = document.createElement("option"); i.value = t, i.textContent = a.get(t), e.appendChild(i) }) }), ++runCount >= 3 && clearInterval(intervalId) } function runAfterDocumentReady() { "complete" === document.readyState ? updateDropdownOptions() : document.addEventListener("DOMContentLoaded", updateDropdownOptions) } function setOverflowAutoForDropDowns() { document.querySelectorAll('div[id$="_DropDown"]').forEach(function (e) { e.style.overflow = "auto" }) } function showFilters() { setOverflowAutoForDropDowns(); var e = document.getElementById("btnAddFilters"), t = document.getElementById("filterPanel"); e.disabled = !0, e.innerHTML = '<i class="fas fa-spinner fa-spin"></i>', setTimeout(function () { t.classList.toggle("active"), e.disabled = !1, e.innerHTML = '<i class="fas fa-filter"></i> KPI Filters' }, 3e3) } function closeFilters() { document.getElementById("filterPanel").classList.toggle("active"), saveReport() } function deleteReport() { unAssignReport(selectedReportId) } function setCheckboxes(e, t) { var i = t.split(","); for (var a in e) if (e.hasOwnProperty(a)) { var s = e[a], r = document.getElementById(s); r && (i.includes(a) ? r.checked = !0 : r.checked = !1) } } function processValueGetterExpression(e, t) { if ("string" == typeof e.valueGetter) try { let i = { ...t, ...t.data }, a = i[e.headerName] || 0; return "string" == typeof a && (a = a.replace(/[%$,]/g, "").trim()), a = Number(a) || 0 } catch (s) { console.error("Error evaluando valueGetter: ", s) } return 0 } async function clearReportTool() { var e; await clearDateTimeCriterial(), await clearCancellationCriteriaFilters(), await clearCustomersCriteriaFilters(), await clearBasicCriterial(), await clearMarketingCriteriaFilters(), await clearPurchaseCriteriaFilters(), await clearSalesCriteriaFilters(), await clearTourCriteriaFilters(), joinCounter = 1, document.querySelectorAll(".join-inputs-container").forEach(function (e) { "joinContainer1" !== e.id ? e.parentNode.removeChild(e) : (document.getElementById("joinSelect1").selectedIndex = 0, document.getElementById("firstJoinKey_1").value = "", document.getElementById("secondJoinKey_1").value = "", document.getElementById("uniqueKey_1").value = "") }); let t = document.getElementById("accordion"); t.innerHTML = "", $("#columnName").val(""); let i = document.getElementById("reportTypeOnDropdown"); i.selectedIndex = 0, $("#reportName").val(""); let a = document.getElementById("preJoinsDropdown"); a.selectedIndex = 0 } $().ready(async function () { var e = userName; try { var t = await fetchUserDataByUserName(e); if (t && t.length > 0) { var i = t[0].UserId; let a = await getReportsUsersMapByUserId(i); a && a.length > 0 && await fetchReportNames(a[0].ReportId) } } catch (s) { } var r = document.getElementById("reportNamesDropdown"); r.innerHTML = ""; var l = document.getElementById("reportTypeOnDropdown"), n = document.getElementById("dropDownDateRanges"), o = document.getElementById("dropDownYearRanges"); getReports(e => { for (var t in e) for (var i = e[t], a = 0; a < i.length; a++) { typeReports.push(i[a]); var s = document.createElement("option"); s.text = i[a].Item1, "" != i[a].Item2 ? s.value = i[a].Item2 : s.value = i[a].Item6, r.add(s) } getReportsOnType(e => { for (var t in e) for (var i = e[t], a = 0; a < i.length; a++) { typeReports.push(i[a]); var s = document.createElement("option"); s.text = i[a].reportName, s.value = i[a].reportId, l.add(s) } }), getDateRange(e => { for (var t in e) { var i = e[t]; if ("dateRanges" == t) for (var a = 0; a < i.length; a++) { typeReports.push(i[a]); var s = document.createElement("option"); s.text = i[a].value, s.value = i[a].key, n.add(s) } else if ("yearRanges" == t) for (var a = 0; a < i.length; a++) { typeReports.push(i[a]); var s = document.createElement("option"); s.text = i[a].value, s.value = i[a].key, o.add(s) } } }) }); var c = document.getElementById("locationsUser"); getLocations(e => { for (var t in e) for (var i = e[t], a = 0; a < i.length; a++) { var s = document.createElement("option"); s.text = i[a].Name, s.value = i[a].LocationID, c.add(s) } }), populateKpi(null), toggleAccordion(); let u = document.querySelector(".SalesCriteriaHeader"); u && u.children.length > 1 && (u.children[1].style.display = "none") }), document.addEventListener("DOMContentLoaded", function () { let e = document.getElementById("dropDownDateRanges"), t = document.getElementById("dropDownYearRanges"); e.addEventListener("change", function () { updateDateRange(null), updateYearRange(null) }), t.addEventListener("change", function () { updateDateRange(null), updateYearRange(null) }) }), runAfterDocumentReady();