﻿
CREATE PROCEDURE [dbo].[InsertCustomDisplay_HideCancellationRequestModule]

AS
BEGIN

	DECLARE @GroupTypeKey nvarchar(64) SET @GroupTypeKey = 'HideCancellationRequestModule'

	DELETE FROM CustomPageControlDisplayRights WHERE (customPageControlDisplayRightGroupTypeKey = @GroupTypeKey)

	DECLARE @Add int SET @Add = 1
	DECLARE @Remove int SET @Remove = 2

	DECLARE @Insert int SET @Insert = 1
	DECLARE @Select int SET @Select = 2
	DECLARE @Update int SET @Update = 3

	DECLARE @Visible int SET @Visible = 1
	DECLARE @Enabled int SET @Enabled = 2
	DECLARE @Text int SET @Text = 3
	DECLARE @Delegate int SET @Delegate = 4
	DECLARE @Authorized int SET @Authorized = 5
	DECLARE @Administrated int SET @Administrated = 6

	DECLARE @PagePathID [varchar] (256)

	SET @PagePathID = 'ASP.default_aspx'

	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'panelCancellationRequests',@Visible,0,NULL,NULL

	SET @PagePathID = 'ASP.reports_default_aspx'

	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'panelCancellationRequestReports',@Visible,0,NULL,NULL
	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'linkCreateCancellationRequestReport',@Visible,0,NULL,NULL
	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'linkUpdateCancellationRequestReport',@Visible,0,NULL,NULL

	SET @PagePathID = 'ASP.reports_cancellationrequestdetailreport_aspx'

	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'form1',@Authorized,0,NULL,NULL

	SET @PagePathID = 'ASP.reports_cancellationrequestefficiencyreport_aspx'

	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'form1',@Authorized,0,NULL,NULL

	SET @PagePathID = 'ASP.reports_createcancellationrequestreport_aspx'

	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'form1',@Authorized,0,NULL,NULL

	SET @PagePathID = 'ASP.reports_updatecancellationrequestreport_aspx'

	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'form1',@Authorized,0,NULL,NULL

	SET @PagePathID = 'ASP.reports_usercontrols_toursearchcriteria_ascx'

	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'dropDownSaleStatusTypes',@Delegate,NULL,NULL,'TrackResults.Web.Security.DropDownDisplay.SaleStatusTypes,TrackResults.Web:RemoveBySaleStatusTypes(TrackResults.BES.Data.Types.SaleStatusType[] {Superseded,CancellationRequest,PenderSuperseded,PenderCancellationRequest})'

	SET @PagePathID = 'ASP.customers_tours_usercontrols_sales_ascx_editlistview'

	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Update,'panelCancellationCommands',@Visible,0,NULL,NULL

	SET @PagePathID = 'ASP.customers_tours_usercontrols_cancellation_ascx'

	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'formView',@Visible,0,NULL,NULL

	SET @PagePathID = 'TrackResults.Web.Data.Cache.TourDateTypesPickListCache_TourDateTypesPickList'

	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'TourDateTypesPickList',@Delegate,NULL,NULL,'TrackResults.Web.Security.CollectionDisplayRules,TrackResults.Web:RemoveKeyValuePairByIntKey(System.Int32 4)'

END