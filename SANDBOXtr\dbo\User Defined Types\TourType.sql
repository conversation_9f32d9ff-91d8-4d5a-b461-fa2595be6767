﻿CREATE TYPE [dbo].[TourType] AS TABLE (
    [tourID]                   INT            NULL,
    [externalTourID]           NVARCHAR (128) NULL,
    [customerID]               INT            NULL,
    [externalLeadID]           NVARCHAR (128) NULL,
    [acquisitionDate]          DATETIME       NULL,
    [acquisitionTime]          DATETIME       NULL,
    [leadSourceID]             INT            NULL,
    [leadTypeID]               INT            NULL,
    [leadStatusTypeID]         INT            NULL,
    [leadDispositionID]        INT            NULL,
    [checkInDate]              DATETIME       NULL,
    [checkInTime]              DATETIME       NULL,
    [checkOutDate]             DATETIME       NULL,
    [checkOutTime]             DATETIME       NULL,
    [leadPickList1ItemID]      INT            NULL,
    [leadText1]                NVARCHAR (512) NULL,
    [leadDecimal1]             DECIMAL (9, 2) NULL,
    [leadBool1]                BIT            NULL,
    [leadPickList2ItemID]      INT            NULL,
    [leadText2]                NVARCHAR (512) NULL,
    [leadDecimal2]             DECIMAL (9, 2) NULL,
    [leadBool2]                BIT            NULL,
    [contactStatusTypeID]      INT            NULL,
    [contactDispositionID]     INT            NULL,
    [callBackDate]             DATETIME       NULL,
    [callBackTime]             DATETIME       NULL,
    [callBackPickList1ItemID]  INT            NULL,
    [callBackText1]            NVARCHAR (512) NULL,
    [callBackDecimal1]         DECIMAL (9, 2) NULL,
    [callBackBool1]            BIT            NULL,
    [tourSourceID]             INT            NULL,
    [tourTypeID]               INT            NULL,
    [tourStatusTypeID]         INT            NULL,
    [tourConcernTypeID]        INT            NULL,
    [tourTypePickList1ItemID]  INT            NULL,
    [locationID]               INT            NULL,
    [regionID]                 INT            NULL,
    [tourDate]                 DATETIME       NULL,
    [tourTime]                 DATETIME       NULL,
    [entryDateTime]            DATETIME       NULL,
    [tourText1]                NVARCHAR (512) NULL,
    [tourDecimal1]             DECIMAL (9, 2) NULL,
    [tourBool1]                BIT            NULL,
    [rescheduledCount]         INT            NULL,
    [rescheduledTourID]        INT            NULL,
    [parentRescheduledTourID]  INT            NULL,
    [marketingTeamID]          INT            NULL,
    [marketingAgentID]         INT            NULL,
    [marketingCloserID]        INT            NULL,
    [confirmerID]              INT            NULL,
    [resetterID]               INT            NULL,
    [venueID]                  INT            NULL,
    [campaignID]               INT            NULL,
    [channelID]                INT            NULL,
    [hotelID]                  INT            NULL,
    [marketingPickList1ItemID] INT            NULL,
    [marketingPickList2ItemID] INT            NULL,
    [marketingText1]           NVARCHAR (512) NULL,
    [marketingDecimal1]        DECIMAL (9, 2) NULL,
    [marketingBool1]           BIT            NULL,
    [marketingDate1]           DATETIME       NULL,
    [marketingTime1]           DATETIME       NULL,
    [depositAmount]            DECIMAL (9, 2) NULL,
    [depositRefundable]        BIT            NULL,
    [salesTeamID]              INT            NULL,
    [podiumID]                 INT            NULL,
    [salesRepID]               INT            NULL,
    [salesCloser1ID]           INT            NULL,
    [salesCloser2ID]           INT            NULL,
    [exitID]                   INT            NULL,
    [verificationOfficerID]    INT            NULL,
    [salesPickList1ItemID]     INT            NULL,
    [salesText1]               NVARCHAR (512) NULL,
    [salesDecimal1]            DECIMAL (9, 2) NULL,
    [salesBool1]               BIT            NULL,
    [salesDate1]               DATETIME       NULL,
    [salesTime1]               DATETIME       NULL,
    [importNumber]             NVARCHAR (64)  NULL,
    [apiConnectionID]          INT            NULL,
    [apiExternalTourID]        NVARCHAR (128) NULL,
    [apiExternalConnectionID]  NVARCHAR (128) NULL,
    [insertTimeStamp]          DATETIME       NULL,
    [updateTimeStamp]          DATETIME       NULL,
    [salesTeamExitID]          INT            NULL,
    [salesRepExit1ID]          INT            NULL,
    [salesRepExit2ID]          INT            NULL,
    [salesRepExit3ID]          INT            NULL);

