﻿

CREATE FUNCTION [dbo].[GetConcantenatedGiftCodesByTourID]
(
	@TourID int,
	@Delimeter nvarchar(8)
)
RETURNS nvarchar(2048)
AS
BEGIN

	DECLARE @ConcantenatedList nvarchar(2048)

	SET @ConcantenatedList = ''

	SELECT @ConcantenatedList = @ConcantenatedList + @Delimeter + Gifts.code
	FROM ToursGiftsMap INNER JOIN
		Gifts ON ToursGiftsMap.giftID = Gifts.giftID
	WHERE ToursGiftsMap.tourID = @TourID
	ORDER BY Gifts.code

	IF DATALENGTH(@ConcantenatedList) > 0
	BEGIN
		SET @ConcantenatedList = RIGHT(@ConcantenatedList, ((DATALENGTH(@ConcantenatedList)/2) - (DATALENGTH(@Delimeter)/2)))
	END

	RETURN @ConcantenatedList

END