﻿CREATE TABLE [dbo].[CustomAnalyticViews] (
    [customAnalyticViewID]                 INT            IDENTITY (1, 1) NOT NULL,
    [customAnalyticViewKey]                NVARCHAR (64)  NULL,
    [title]                                NVARCHAR (128) NOT NULL,
    [viewPath]                             NVARCHAR (128) NULL,
    [defaultTierOneCriteria]               NVARCHAR (MAX) NULL,
    [defaultSortExpression]                NVARCHAR (64)  NOT NULL,
    [defaultSortDirection]                 NVARCHAR (4)   NOT NULL,
    [customAnalyticViewLogicClassFullName] NVARCHAR (128) NULL,
    [customAnalyticViewLogicClassCode]     NVARCHAR (MAX) NULL,
    [dataAccessClassFullName]              NVARCHAR (128) NULL,
    [dataAccessClassCode]                  NVARCHAR (MAX) NULL,
    [calculateSettingsName]                NVARCHAR (64)  NULL,
    [isApplicationView]                    BIT            CONSTRAINT [DF_CustomAnalyticViews_isApplicationView] DEFAULT ((0)) NOT NULL,
    [isFeature]                            BIT            CONSTRAINT [DF_CustomAnalyticViews_isFeature] DEFAULT ((0)) NOT NULL,
    [sortOrder]                            INT            NULL,
    [insertTimeStamp]                      DATETIME       CONSTRAINT [DF_CustomAnalyticViews_insertTimeStamp] DEFAULT (getdate()) NOT NULL,
    [systemReportName]                     NVARCHAR (128) NULL,
    CONSTRAINT [PK_CustomAnalyticViews] PRIMARY KEY CLUSTERED ([customAnalyticViewID] ASC)
);


GO
CREATE UNIQUE NONCLUSTERED INDEX [UK_CustomAnalyticViews_customAnalyticViewKey]
    ON [dbo].[CustomAnalyticViews]([customAnalyticViewKey] ASC) WHERE ([customAnalyticViewKey] IS NOT NULL);


GO
CREATE TRIGGER [dbo].[CustomAnalyticViews.InsertUpdateDelete]
    ON [dbo].[CustomAnalyticViews]
    FOR INSERT, UPDATE, DELETE
AS 
BEGIN
    SET NOCOUNT ON;

    DECLARE @Activity  NVARCHAR (8), @id INT;

    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'updated'
    END

    IF EXISTS (SELECT * FROM inserted) AND NOT EXISTS(SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'inserted'
    END

    IF EXISTS (SELECT * FROM deleted) AND NOT EXISTS(SELECT * FROM inserted)
    BEGIN
        SET @Activity = 'deleted'
    END

    

    IF (@Activity = 'deleted')
    BEGIN
        DECLARE cur CURSOR FOR SELECT customAnalyticViewID FROM deleted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'CustomAnalyticViews', @id, 'customAnalyticViewID',
                (
                    SELECT *
                    FROM deleted i 
                    WHERE i.customAnalyticViewID =  @id
                    FOR JSON AUTO
                ),0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
    ELSE
    BEGIN
        DECLARE cur CURSOR
        FOR SELECT customAnalyticViewID
        FROM inserted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'CustomAnalyticViews', @id, 'customAnalyticViewID',
                (
                    SELECT *
                    FROM inserted i 
                    WHERE i.customAnalyticViewID =  @id
                    FOR JSON AUTO
                ), 0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
END

GO
DISABLE TRIGGER [dbo].[CustomAnalyticViews.InsertUpdateDelete]
    ON [dbo].[CustomAnalyticViews];

