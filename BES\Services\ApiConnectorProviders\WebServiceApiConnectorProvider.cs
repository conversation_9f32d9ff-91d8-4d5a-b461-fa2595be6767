﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Globalization;
using System.Linq;
using System.ServiceModel;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using TrackResults.BES.Data;
using TrackResults.BES.Data.Business;
using TrackResults.BES.Data.Cache;
using TrackResults.BES.Data.Columns;
using TrackResults.BES.Data.Types;
using TrackResults.BES.DataAccess;
using TrackResults.BES.Rules;
using TrackResults.Common.Core.Extensions;
using TrackResults.Common.Core.System;
using TrackResults.Common.DAL;
using TrackResults.Common.Exceptions;

namespace TrackResults.BES.Services.ApiConnectorProviders
{
    public abstract class WebServiceApiConnectorProvider : ApiConnectorProvider
    {
        protected abstract List<IConnectionObject> GetConnectionObjects(ApiConnection apiConnection, ApiConnectorProviderPageToken pageToken, bool esTesting = false, ApiConnectionRequests apiConnectionRequests = null);
        protected abstract List<ApiConnectionRequests> ApiConnectionsRequestIds(ApiConnection apiConnection, ApiConnectorProviderPageToken pageToken);
        protected abstract string ExecuteMiddlewareConditions(int apiConnectionRequestId, string jsonBody);
        protected abstract string ExecuteSchedulerReport(CustomReportsScheduler customReportsScheduler, List<int> reportsIDs);
        protected abstract int ExecuteSendingParams(ApiConnection apiConnection, ApiConnectionRequests requests);
        protected abstract void GetPageComplete(ApiConnection apiConnection, ApiConnectorProviderPageToken pageToken);
        public override void GetPagedRawData(ApiConnection apiConnection, int pagedCount, ApiConnectorProvider.RawDataCommandBlock rawDataCommandBlock)
        {
            ApiConnector connector = ApiConnectorsCache.Instance.Data.GetValue<int, ApiConnector>(apiConnection.ApiConnectionID);
            bool flag = true;
            ApiConnectorProviderPageToken pageToken = new ApiConnectorProviderPageToken
            {
                PageNumber = 1,
                PagedCount = apiConnection.PagedCount
            };
            List<IConnectionObject> instance = null;
            Tuple<string, string, DateTime?> tuple = ApiConnectionsDataAccess.SelectEndpointArgumentsByID(apiConnection.ApiConnectionID);
            apiConnection.EndpointArgument1 = tuple.Item1;
            apiConnection.EndpointArgument2 = tuple.Item2;
            apiConnection.EndpointLastUpdatedTime = tuple.Item3;
            do
            {
                instance = null;
                try
                {
                    instance = this.GetConnectionObjects(apiConnection, pageToken);
                }
                catch (Exception exception)
                {
                    MiddlewareService.LogMiddlewareErrorException(MiddlewareErrorType.ExecuteConnection, apiConnection.ApiConnectionID, connector.ApiConnectorName, "GetConnectionObjects", null, null, exception, null, null);
                    try
                    {
                        instance = this.GetConnectionObjects(apiConnection, pageToken);
                    }
                    catch (Exception exception2)
                    {
                        MiddlewareService.LogMiddlewareErrorException(MiddlewareErrorType.ExecuteConnection, apiConnection.ApiConnectionID, connector.ApiConnectorName, "GetConnectionObjects Retry 1", null, null, exception2, null, null);
                        try
                        {
                            instance = this.GetConnectionObjects(apiConnection, pageToken);
                        }
                        catch (Exception exception3)
                        {
                            throw new MiddlewareErrorException(null, apiConnection.ApiConnectionID, connector.ApiConnectorName, "GetConnectionObjects Retry 2", null, null, null, true, false, exception3);
                        }
                    }
                }
                DataTable rawDataTable = new DataTable();
                List<string> rawDataHeaders = this.GetRawDataHeaders(apiConnection);
                foreach (string str in rawDataHeaders)
                {
                    rawDataTable.Columns.Add(str, typeof(string));
                }
                if (pageToken.PageNumber == 1)
                {
                    apiConnection.LastMiddlewareActionTime = null;
                    apiConnection.EndpointArgument1 = null;
                    apiConnection.EndpointArgument2 = null;
                }
                if (!instance.IsNullOrEmpty<IConnectionObject>())
                {
                    foreach (ConnectionObject obj2 in instance)
                    {
                        Dictionary<string, string> rawData = this.GetRawData(apiConnection, obj2);
                        if (!rawData.IsNullOrEmpty<KeyValuePair<string, string>>())
                        {
                            DataRow row = rawDataTable.NewRow();
                            foreach (KeyValuePair<string, string> pair in rawData)
                            {
                                row[pair.Key] = pair.Value;
                            }
                            rawDataTable.Rows.Add(row);
                        }
                    }
                    flag = rawDataCommandBlock(rawDataTable, pageToken.TotalCount);
                }
                this.GetPageComplete(apiConnection, pageToken);
                pageToken.PageNumber++;
            }
            while (flag && this.HasNextPage(pageToken));
            ApiConnectionsDataAccess.UpdateEndpointArguments(apiConnection.ApiConnectionID, null, null);
        }
        protected abstract bool HasNextPage(ApiConnectorProviderPageToken pageToken);
        protected virtual IConnectionObject GetConnectionObject(ApiConnection apiConnection, string externalTourID) { throw new NotImplementedException(); }

        protected abstract bool UploadRemoteFile(ApiConnection apiConnection, ApiConnectionRequests request);
        public override bool GetUploadRemoteFile(ApiConnection apiConnection, ApiConnectionRequests request)
        {
            return UploadRemoteFile(apiConnection, request);
        }
        protected abstract TourReference ConvertToTourReference(ApiConnection apiConnection, IConnectionObject connectionObject);

        protected abstract TourReferences ConvertToTourReferences(ApiConnection apiConnection, IConnectionObject connectionObject);

        protected virtual List<string> GetRawDataHeaders(ApiConnection apiConnection) { throw new NotImplementedException(); }
        protected virtual Dictionary<string, string> GetRawData(ApiConnection apiConnection, IConnectionObject connectionObject) { throw new NotImplementedException(); }

        public override string MiddlewareConditions(int apiConnectionRequestId, string jsonBody)
        {
            return ExecuteMiddlewareConditions(apiConnectionRequestId, jsonBody);
        }

        public override string SchedulerReports(CustomReportsScheduler customReportsScheduler, List<int> reportsIDs)
        {
            return ExecuteSchedulerReport(customReportsScheduler, reportsIDs);
        }

        public override int SendingParams(ApiConnection apiConnection, ApiConnectionRequests requests)
        {
            return ExecuteSendingParams(apiConnection, requests);
        }


        public override Tuple<DateTime?, int> GetPagedToursBulk(ApiConnection apiConnection, PreActionCommandBlock preActionCommandBlock,
            TourReferenceListCommandBlock tourReferenceCommandBlock, PostActionCommandBlock postActionCommandBlock)
        {
            ApiConnector apiConnector = ApiConnectorsCache.Instance.Data.GetValue(apiConnection.ApiConnectionID);
            int totalCount = 0;
            bool apiTotalCountIsValid = false;
            ApiConnectorProviderPageToken pageToken = new ApiConnectorProviderPageToken { PageNumber = 1, PagedCount = apiConnection.PagedCount };
            List<IConnectionObject> connectionObjects = null;
            DateTime requestDateTime = DateTime.Now;

            Tuple<string, string, DateTime?> endpointArguments = ApiConnectionsDataAccess.SelectEndpointArgumentsByID(apiConnection.ApiConnectionID);

            apiConnection.EndpointArgument1 = endpointArguments.Item1;
            apiConnection.EndpointArgument2 = endpointArguments.Item2;
            apiConnection.EndpointLastUpdatedTime = endpointArguments.Item3;

            preActionCommandBlock(apiConnection);

            Stopwatch stopwatch = new Stopwatch();
            Stopwatch minuteStopwatch = new Stopwatch();

            stopwatch.Start();
            minuteStopwatch.Start();
            string processingPerformance;

            do
            {
                var apiconnectionsrequest = ApiConnectionsRequestIds(apiConnection, pageToken);
                foreach (var apiConnectionRequests in apiconnectionsrequest)
                {
                    if (apiConnectionRequests != null)
                    {
                        connectionObjects = null;

                        try
                        {
                            connectionObjects = GetConnectionObjects(apiConnection, pageToken, apiConnection.EsTesting, apiConnectionRequests);
                        }
                        catch (Exception exception)
                        {
                            MiddlewareService.LogMiddlewareErrorException(MiddlewareErrorType.ExecuteConnection, apiConnection.ApiConnectionID,
                                apiConnector.ApiConnectorName, "GetConnectionObjects", null, null, exception);

                            // Retry 1

                            try
                            {
                                connectionObjects = GetConnectionObjects(apiConnection, pageToken);
                            }
                            catch (Exception retryException)
                            {
                                MiddlewareService.LogMiddlewareErrorException(MiddlewareErrorType.ExecuteConnection, apiConnection.ApiConnectionID,
                                    apiConnector.ApiConnectorName, "GetConnectionObjects Retry 1", null, null, retryException);

                                // Retry 2

                                try
                                {
                                    connectionObjects = GetConnectionObjects(apiConnection, pageToken);
                                }
                                catch (Exception retry2Exception)
                                {
                                    throw new MiddlewareErrorException(null, apiConnection.ApiConnectionID, apiConnector.ApiConnectorName,
                                        "GetConnectionObjects Retry 2", null, null, null, true, false, retry2Exception);
                                }
                            }
                        }

                        if (pageToken.PageNumber == 1)
                        {
                            if (pageToken.TotalCount > 0 && !connectionObjects.IsNullOrEmpty())
                            {
                                apiTotalCountIsValid = true;
                            }
                            else
                            {
                                ApiConnectionsDataAccess.UpdateProcessingRemainingCount(apiConnection.ApiConnectionID, -1);
                            }

                            ApiConnectionsDataAccess.UpdateProcessingStartTimeProcessingTotalCount(apiConnection.ApiConnectionID, DateTime.UtcNow, pageToken.TotalCount);

                            if (pageToken.RequestDateTime != null)
                            {
                                requestDateTime = (DateTime)pageToken.RequestDateTime;
                            }

                            apiConnection.LastMiddlewareActionTime = null;
                            apiConnection.EndpointArgument1 = null;
                            apiConnection.EndpointArgument2 = null;
                        }
                        
                        if (!connectionObjects.IsNullOrEmpty())
                        {
                            //TourReference tourReference;
                            List<TourReferences> tourReferencesList = new List<TourReferences>();

                            //foreach (var connectionObject in connectionObjects)
                            //{
                            //    try
                            //    {
                            //        if (IsBetweenFilterDate(apiConnection, connectionObject.FilterDate))
                            //        {
                            //            // Convertir a TourReference
                            //            TourReferences tourReference = ConvertToTourReferences(apiConnection, connectionObject);
                            //            tourReferencesList.Add(tourReference);
                            //            totalCount++;
                            //        }
                            //    }
                            //    catch (MiddlewareErrorException middlewareErrorException)
                            //    {
                            //        // Añadir información adicional sobre el error
                            //        middlewareErrorException.ExternalCustomerID = connectionObject.ExternalCustomerID;
                            //        middlewareErrorException.ExternalTourID = connectionObject.ToursObjects[0].ExternalTourID;

                            //        MiddlewareService.LogMiddlewareErrorException(middlewareErrorException);
                            //    }
                            //    catch (Exception exception)
                            //    {
                            //        // Loguear cualquier otro tipo de excepción
                            //        MiddlewareService.LogMiddlewareErrorException(MiddlewareErrorType.ConnectorTourTranslation, apiConnection.ApiConnectionID,
                            //            connectionObject.ExternalCustomerID, connectionObject.ToursObjects[0].ExternalTourID, exception);
                            //    }
                            //}

                            Parallel.ForEach(connectionObjects, connectionObject =>
                            {
                                try
                                {
                                    if (IsBetweenFilterDate(apiConnection, connectionObject.FilterDate))
                                    {
                                        //Convertir a TourReference
                                        TourReferences tourReference = ConvertToTourReferences(apiConnection, connectionObject);

                                        //Añadir a la lista de tourReferencesList de manera segura
                                        lock (tourReferencesList)
                                        {
                                            tourReferencesList.Add(tourReference);
                                        }
                                    }
                                }
                                catch (MiddlewareErrorException middlewareErrorException)
                                {
                                    //Añadir información adicional sobre el error
                                    middlewareErrorException.ExternalCustomerID = connectionObject.ExternalCustomerID;
                                    middlewareErrorException.ExternalTourID = connectionObject.ToursObjects[0].ExternalTourID;

                                    MiddlewareService.LogMiddlewareErrorException(middlewareErrorException);
                                }
                                catch (Exception exception)
                                {
                                    //Loguear cualquier otro tipo de excepción
                                    MiddlewareService.LogMiddlewareErrorException(MiddlewareErrorType.ConnectorTourTranslation, apiConnection.ApiConnectionID,
                                        connectionObject.ExternalCustomerID, connectionObject.ToursObjects[0].ExternalTourID, exception);
                                }

                                //Incrementar totalCount de manera segura
                                Interlocked.Increment(ref totalCount);
                            });

                            tourReferenceCommandBlock(tourReferencesList, null, null);
                        }
                    }
                }

                //------------------------

                if (minuteStopwatch.Elapsed.TotalMinutes > 1)
                {
                    minuteStopwatch.Stop();

                    processingPerformance =
                        string.Format("{0} hrs {1} min, {2} records / min", stopwatch.Elapsed.Hours, stopwatch.Elapsed.Minutes,
                        (totalCount / stopwatch.Elapsed.TotalMinutes).ToString("N0"));

                    ApiConnectionsDataAccess.UpdateProcessingPerformance(apiConnection.ApiConnectionID, processingPerformance);
                    minuteStopwatch.Restart();
                }

                if (apiTotalCountIsValid)
                {
                    ApiConnectionsDataAccess.UpdateProcessingRemainingCount(apiConnection.ApiConnectionID,
                        Convert.ToInt32(pageToken.TotalCount - (apiConnection.PagedCount * pageToken.PageNumber)));
                }
                else
                {
                    ApiConnectionsDataAccess.UpdateProcessingTotalCount(apiConnection.ApiConnectionID, totalCount);
                }

                GetPageComplete(apiConnection, pageToken);

                pageToken.PageNumber++;
            }
            while (ApiConnectionsDataAccess.ShouldContinueProcessingByID(apiConnection.ApiConnectionID) && HasNextPage(pageToken));

            ApiConnectionsDataAccess.UpdateEndpointArguments(apiConnection.ApiConnectionID, null, null);

            if (pageToken.RequestDateTime != null)
            {
                ApiConnectionsDataAccess.UpdateEndpointLastUpdatedTime(apiConnection.ApiConnectionID, (DateTime)pageToken.RequestDateTime);
            }

            postActionCommandBlock(apiConnection);

            return new Tuple<DateTime?, int>(requestDateTime, totalCount);
        }

        public override Tuple<DateTime?, int> GetPagedTours(ApiConnection apiConnection, PreActionCommandBlock preActionCommandBlock,
            TourReferenceCommandBlock tourReferenceCommandBlock, PostActionCommandBlock postActionCommandBlock)
        {
            ApiConnector apiConnector = ApiConnectorsCache.Instance.Data.GetValue(apiConnection.ApiConnectionID);
            int totalCount = 0;
            bool apiTotalCountIsValid = false;
            ApiConnectorProviderPageToken pageToken = new ApiConnectorProviderPageToken { PageNumber = 1, PagedCount = apiConnection.PagedCount };
            List<IConnectionObject> connectionObjects = null;
            DateTime requestDateTime = DateTime.Now;

            Tuple<string, string, DateTime?> endpointArguments = ApiConnectionsDataAccess.SelectEndpointArgumentsByID(apiConnection.ApiConnectionID);

            apiConnection.EndpointArgument1 = endpointArguments.Item1;
            apiConnection.EndpointArgument2 = endpointArguments.Item2;
            apiConnection.EndpointLastUpdatedTime = endpointArguments.Item3;

            preActionCommandBlock(apiConnection);

            Stopwatch stopwatch = new Stopwatch();
            Stopwatch minuteStopwatch = new Stopwatch();

            stopwatch.Start();
            minuteStopwatch.Start();
            string processingPerformance;

            do
            {
                var apiconnectionsrequest = ApiConnectionsRequestIds(apiConnection, pageToken);
                foreach (var apiConnectionRequests in apiconnectionsrequest)
                {
                    if (apiConnectionRequests != null)
                    {
                        connectionObjects = null;

                        try
                        {
                            connectionObjects = GetConnectionObjects(apiConnection, pageToken, apiConnection.EsTesting, apiConnectionRequests);
                        }
                        catch (Exception exception)
                        {
                            MiddlewareService.LogMiddlewareErrorException(MiddlewareErrorType.ExecuteConnection, apiConnection.ApiConnectionID,
                                apiConnector.ApiConnectorName, "GetConnectionObjects", null, null, exception);

                            // Retry 1

                            try
                            {
                                connectionObjects = GetConnectionObjects(apiConnection, pageToken);
                            }
                            catch (Exception retryException)
                            {
                                MiddlewareService.LogMiddlewareErrorException(MiddlewareErrorType.ExecuteConnection, apiConnection.ApiConnectionID,
                                    apiConnector.ApiConnectorName, "GetConnectionObjects Retry 1", null, null, retryException);

                                // Retry 2

                                try
                                {
                                    connectionObjects = GetConnectionObjects(apiConnection, pageToken);
                                }
                                catch (Exception retry2Exception)
                                {
                                    throw new MiddlewareErrorException(null, apiConnection.ApiConnectionID, apiConnector.ApiConnectorName,
                                        "GetConnectionObjects Retry 2", null, null, null, true, false, retry2Exception);
                                }
                            }
                        }

                        if (pageToken.PageNumber == 1)
                        {
                            if (pageToken.TotalCount > 0 && !connectionObjects.IsNullOrEmpty())
                            {
                                apiTotalCountIsValid = true;
                            }
                            else
                            {
                                ApiConnectionsDataAccess.UpdateProcessingRemainingCount(apiConnection.ApiConnectionID, -1);
                            }

                            ApiConnectionsDataAccess.UpdateProcessingStartTimeProcessingTotalCount(apiConnection.ApiConnectionID, DateTime.UtcNow, pageToken.TotalCount);

                            if (pageToken.RequestDateTime != null)
                            {
                                requestDateTime = (DateTime)pageToken.RequestDateTime;
                            }

                            apiConnection.LastMiddlewareActionTime = null;
                            apiConnection.EndpointArgument1 = null;
                            apiConnection.EndpointArgument2 = null;
                        }

                        if (!connectionObjects.IsNullOrEmpty())
                        {
                            //TourReference tourReference;
                            //                     //TourReference tourReference;
                            List<TourReference> tourReferencesList = new List<TourReference>();
                            //TourReference tourReference;
                            Parallel.ForEach(connectionObjects, connectionObject =>
                            {
                                try
                                {
                                    if (IsBetweenFilterDate(apiConnection, connectionObject.FilterDate))
                                    {
                                        // Convertir a TourReference
                                        TourReference tourReference = ConvertToTourReference(apiConnection, connectionObject);

                                        // Añadir a la lista de tourReferencesList de manera segura
                                        lock (tourReferencesList)
                                        {
                                            tourReferencesList.Add(tourReference);
                                        }
                                    }
                                }
                                catch (MiddlewareErrorException middlewareErrorException)
                                {
                                    // Añadir información adicional sobre el error
                                    middlewareErrorException.ExternalCustomerID = connectionObject.ExternalCustomerID;
                                    middlewareErrorException.ExternalTourID = connectionObject.ExternalTourID;

                                    MiddlewareService.LogMiddlewareErrorException(middlewareErrorException);
                                }
                                catch (Exception exception)
                                {
                                    // Loguear cualquier otro tipo de excepción
                                    MiddlewareService.LogMiddlewareErrorException(MiddlewareErrorType.ConnectorTourTranslation, apiConnection.ApiConnectionID,
                                        connectionObject.ExternalCustomerID, connectionObject.ExternalTourID, exception);
                                }

                                // Incrementar totalCount de manera segura
                                Interlocked.Increment(ref totalCount);
                            });

                            //foreach (IConnectionObject connectionObject in connectionObjects)
                            //{
                            //    try
                            //    {
                            //        if (IsBetweenFilterDate(apiConnection, connectionObject.FilterDate))
                            //        {
                            //            tourReference = ConvertToTourReference(apiConnection, connectionObject);
                            //            tourReferencesList.Add(tourReference);
                            //        }
                            //    }
                            //    catch (MiddlewareErrorException middlewareErrorException)
                            //    {
                            //        middlewareErrorException.ExternalCustomerID = connectionObject.ExternalCustomerID;
                            //        middlewareErrorException.ExternalTourID = connectionObject.ExternalTourID;

                            //        MiddlewareService.LogMiddlewareErrorException(middlewareErrorException);
                            //    }
                            //    catch (Exception exception)
                            //    {
                            //        MiddlewareService.LogMiddlewareErrorException(MiddlewareErrorType.ConnectorTourTranslation, apiConnection.ApiConnectionID,
                            //            connectionObject.ExternalCustomerID, connectionObject.ExternalTourID, exception);
                            //    }

                            //    totalCount++;
                            //}


                            int totalInsert = 0;
                            foreach (TourReference data in tourReferencesList)
                            {
                                tourReferenceCommandBlock(data, null, null);
                                totalInsert++;

                            }

                        }
                    }
                }

                //------------------------

                if (minuteStopwatch.Elapsed.TotalMinutes > 1)
                {
                    minuteStopwatch.Stop();

                    processingPerformance =
                        string.Format("{0} hrs {1} min, {2} records / min", stopwatch.Elapsed.Hours, stopwatch.Elapsed.Minutes,
                        (totalCount / stopwatch.Elapsed.TotalMinutes).ToString("N0"));

                    ApiConnectionsDataAccess.UpdateProcessingPerformance(apiConnection.ApiConnectionID, processingPerformance);
                    minuteStopwatch.Restart();
                }

                if (apiTotalCountIsValid)
                {
                    ApiConnectionsDataAccess.UpdateProcessingRemainingCount(apiConnection.ApiConnectionID,
                        Convert.ToInt32(pageToken.TotalCount - (apiConnection.PagedCount * pageToken.PageNumber)));
                }
                else
                {
                    ApiConnectionsDataAccess.UpdateProcessingTotalCount(apiConnection.ApiConnectionID, totalCount);
                }

                GetPageComplete(apiConnection, pageToken);

                pageToken.PageNumber++;
            }
            while (ApiConnectionsDataAccess.ShouldContinueProcessingByID(apiConnection.ApiConnectionID) && HasNextPage(pageToken));

            ApiConnectionsDataAccess.UpdateEndpointArguments(apiConnection.ApiConnectionID, null, null);

            if (pageToken.RequestDateTime != null)
            {
                ApiConnectionsDataAccess.UpdateEndpointLastUpdatedTime(apiConnection.ApiConnectionID, (DateTime)pageToken.RequestDateTime);
            }

            postActionCommandBlock(apiConnection);

            return new Tuple<DateTime?, int>(requestDateTime, totalCount);
        }

        public override void GetTourByExternalTourID(ApiConnection apiConnection, string externalTourID, TourReferenceCommandBlock tourReferenceCommandBlock)
        {
            ApiConnector apiConnector = ApiConnectorsCache.Instance.Data.GetValue(apiConnection.ApiConnectionID);
            IConnectionObject connectionObject = null;

            try
            {
                connectionObject = GetConnectionObject(apiConnection, externalTourID);
            }
            catch (Exception exception)
            {
                MiddlewareService.LogMiddlewareErrorException(MiddlewareErrorType.ExecuteConnection, apiConnection.ApiConnectionID,
                    apiConnector.ApiConnectorName, "GetConnectionObject", null, null, exception);

                // Retry 1

                try
                {
                    connectionObject = GetConnectionObject(apiConnection, externalTourID);
                }
                catch (Exception retryException)
                {
                    MiddlewareService.LogMiddlewareErrorException(MiddlewareErrorType.ExecuteConnection, apiConnection.ApiConnectionID,
                        apiConnector.ApiConnectorName, "GetConnectionObject Retry 1", null, null, retryException);

                    // Retry 2

                    try
                    {
                        connectionObject = GetConnectionObject(apiConnection, externalTourID);
                    }
                    catch (Exception retry2Exception)
                    {
                        throw new MiddlewareErrorException(null, apiConnection.ApiConnectionID, apiConnector.ApiConnectorName,
                            "GetConnectionObject Retry 2", null, null, null, true, false, retry2Exception);
                    }
                }
            }

            if (connectionObject != null)
            {
                try
                {
                    if (IsBetweenFilterDate(apiConnection, connectionObject.FilterDate))
                    {
                        TourReference tourReference = ConvertToTourReference(apiConnection, connectionObject);
                        tourReferenceCommandBlock(tourReference, null, null);
                    }
                }
                catch (MiddlewareErrorException middlewareErrorException)
                {
                    middlewareErrorException.ExternalCustomerID = connectionObject.ExternalCustomerID;
                    middlewareErrorException.ExternalTourID = connectionObject.ExternalTourID;

                    MiddlewareService.LogMiddlewareErrorException(middlewareErrorException);
                }
                catch (Exception exception)
                {
                    MiddlewareService.LogMiddlewareErrorException(MiddlewareErrorType.ConnectorTourTranslation, apiConnection.ApiConnectionID,
                        connectionObject.ExternalCustomerID, connectionObject.ExternalTourID, exception);
                }
            }
        }

        public override DataTable GetRawDataByExternalTourID(ApiConnection apiConnection, string externalTourID)
        {
            ApiConnector apiConnector = ApiConnectorsCache.GetApiConnector(apiConnection.ApiConnectionID);

            IConnectionObject connectionObject = null;

            try
            {
                connectionObject = GetConnectionObject(apiConnection, externalTourID);
            }
            catch (Exception exception)
            {
                MiddlewareService.LogMiddlewareErrorException(MiddlewareErrorType.ExecuteConnection, apiConnection.ApiConnectionID,
                    apiConnector.ApiConnectorName, "GetConnectionObject", null, null, exception);

                // Retry 1

                try
                {
                    connectionObject = GetConnectionObject(apiConnection, externalTourID);
                }
                catch (Exception retryException)
                {
                    MiddlewareService.LogMiddlewareErrorException(MiddlewareErrorType.ExecuteConnection, apiConnection.ApiConnectionID,
                        apiConnector.ApiConnectorName, "GetConnectionObject Retry 1", null, null, retryException);

                    // Retry 2

                    try
                    {
                        connectionObject = GetConnectionObject(apiConnection, externalTourID);
                    }
                    catch (Exception retry2Exception)
                    {
                        throw new MiddlewareErrorException(null, apiConnection.ApiConnectionID, apiConnector.ApiConnectorName,
                            "GetConnectionObject Retry 2", null, null, null, true, false, retry2Exception);
                    }
                }
            }

            DataTable rawDataTable = new DataTable();
            List<string> rawDataHeaders = GetRawDataHeaders(apiConnection);

            foreach (string rawDataHeader in rawDataHeaders)
            {
                rawDataTable.Columns.Add(rawDataHeader, typeof(string));
            }

            if (connectionObject != null)
            {
                Dictionary<string, string> rawData = GetRawData(apiConnection, connectionObject);

                if (!rawData.IsNullOrEmpty())
                {
                    DataRow rawDataRow = rawDataTable.NewRow();

                    foreach (KeyValuePair<string, string> rawDataPair in rawData)
                    {
                        rawDataRow[rawDataPair.Key] = rawDataPair.Value;
                    }

                    rawDataTable.Rows.Add(rawDataRow);
                }
            }

            return rawDataTable;
        }

        protected void SetRawData(Dictionary<string, string> rawData, string rawDataHeader, IEnumerable<string> rawDataList)
        {
            if (rawDataList != null)
            {
                bool hasValue = false;
                rawData[rawDataHeader] = null;

                foreach (string rawDataItem in rawDataList)
                {
                    if (!string.IsNullOrWhiteSpace(rawDataItem))
                    {
                        rawData[rawDataHeader] += rawDataItem + Environment.NewLine;

                        hasValue = true;
                    }
                }

                if (hasValue)
                {
                    rawData[rawDataHeader] = rawData[rawDataHeader].RemoveEnd(Environment.NewLine);
                }
            }
        }

        protected void SetRawData(Dictionary<string, string> rawData, string rawDataHeader, IEnumerable<IEnumerable<string>> rawDataList)
        {
            if (rawDataList != null)
            {
                bool hasValue = false;
                rawData[rawDataHeader] = null;

                foreach (IEnumerable<string> rawDataItems in rawDataList)
                {
                    if (rawDataItems != null)
                    {
                        foreach (string rawDataItem in rawDataItems)
                        {
                            if (!string.IsNullOrWhiteSpace(rawDataItem))
                            {
                                rawData[rawDataHeader] += rawDataItem + Environment.NewLine;

                                hasValue = true;
                            }
                        }
                    }
                }

                if (hasValue)
                {
                    rawData[rawDataHeader] = rawData[rawDataHeader].RemoveEnd(Environment.NewLine);
                }
            }
        }



    }
}