﻿CREATE TABLE [dbo].[Teams] (
    [teamID]     INT           IDENTITY (1, 1) NOT NULL,
    [active]     INT           NOT NULL,
    [teamTypeID] INT           CONSTRAINT [DF_Teams_teamTypeID] DEFAULT ((1)) NOT NULL,
    [name]       NVA<PERSON>HA<PERSON> (64) NOT NULL,
    [code]       NVARCHAR (64) NULL,
    [note]       NVARCHAR (64) NULL,
    CONSTRAINT [PK_Teams] PRIMARY KEY CLUSTERED ([teamID] ASC),
    CONSTRAINT [FK_Teams_teamTypeID] FOREIGN KEY ([teamTypeID]) REFERENCES [dbo].[TeamTypes] ([teamTypeID]),
    CONSTRAINT [UK_Teams_name] UNIQUE NONCLUSTERED ([name] ASC)
);


GO
CREATE TRIGGER [dbo].[Teams.InsertUpdateDelete]
    ON [dbo].[Teams]
    FOR INSERT, UPDATE, DELETE
AS 
BEGIN
    SET NOCOUNT ON;

    DECLARE @Activity  NVARCHAR (8), @id INT;

    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'updated'
    END

    IF EXISTS (SELECT * FROM inserted) AND NOT EXISTS(SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'inserted'
    END

    IF EXISTS (SELECT * FROM deleted) AND NOT EXISTS(SELECT * FROM inserted)
    BEGIN
        SET @Activity = 'deleted'
    END

    

    IF (@Activity = 'deleted')
    BEGIN
        DECLARE cur CURSOR FOR SELECT teamID FROM deleted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'Teams', @id, 'teamID',
                (
                    SELECT *
                    FROM deleted i 
                    WHERE i.teamID =  @id
                    FOR JSON AUTO
                ),0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
    ELSE
    BEGIN
        DECLARE cur CURSOR
        FOR SELECT teamID
        FROM inserted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'Teams', @id, 'teamID',
                (
                    SELECT *
                    FROM inserted i 
                    WHERE i.teamID =  @id
                    FOR JSON AUTO
                ), 0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
END

GO
DISABLE TRIGGER [dbo].[Teams.InsertUpdateDelete]
    ON [dbo].[Teams];

