﻿
CREATE PROCEDURE [dbo].[InsertGlobalCustomPageControlDisplayRights]
(
	@customPageControlDisplayRightGroupTypeKey nvarchar(64),
	@customPageControlDisplayRightRuleTypeID int,
	@pagePathID varchar(256),
	@dbModeTypeID int,
	@controlID varchar(64),
	@controlDisplayRightTypeID int,
	@boolean bit,
	@text varchar(1024),
	@controlDelegates ntext
)
AS
BEGIN

	INSERT INTO [CustomPageControlDisplayRights]
	(
		[customPageControlDisplayRightGroupTypeKey],
		[customPageControlDisplayRightRuleTypeID],
		[pagePathID],
		[dbModeTypeID],
		[controlID],
		[controlDisplayRightTypeID],
		[boolean],
		[text],
		[controlDelegates]
	)
	VALUES
	(
		@customPageControlDisplayRightGroupTypeKey,
		@customPageControlDisplayRightRuleTypeID,
		@pagePathID,
		@dbModeTypeID,
		@controlID,
		@controlDisplayRightTypeID,
		@boolean,
		@text,
		@controlDelegates
	)

END