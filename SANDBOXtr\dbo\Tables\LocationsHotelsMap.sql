﻿CREATE TABLE [dbo].[LocationsHotelsMap] (
    [locationsHotelsMapID] INT IDENTITY (1, 1) NOT NULL,
    [locationID]           INT NOT NULL,
    [hotelID]              INT NOT NULL,
    [active]               BIT NOT NULL,
    CONSTRAINT [PK_LocationsHotelsMap] PRIMARY KEY CLUSTERED ([locationsHotelsMapID] ASC),
    CONSTRAINT [FK_LocationsHotelsMap_Hotels] FOREIGN KEY ([hotelID]) REFERENCES [dbo].[Hotels] ([hotelID]),
    CONSTRAINT [FK_LocationsHotelsMap_Locations] FOREIGN KEY ([locationID]) REFERENCES [dbo].[Locations] ([locationID]),
    CONSTRAINT [UK_LocationsHotelsMap] UNIQUE NONCLUSTERED ([locationID] ASC, [hotelID] ASC)
);


GO
CREATE TRIGGER [dbo].[LocationsHotelsMap.InsertUpdateDelete]
    ON [dbo].[LocationsHotelsMap]
    FOR INSERT, UPDATE, DELETE
AS 
BEGIN
    SET NOCOUNT ON;

    DECLARE @Activity  NVARCHAR (8), @id INT;

    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'updated'
    END

    IF EXISTS (SELECT * FROM inserted) AND NOT EXISTS(SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'inserted'
    END

    IF EXISTS (SELECT * FROM deleted) AND NOT EXISTS(SELECT * FROM inserted)
    BEGIN
        SET @Activity = 'deleted'
    END

    

    IF (@Activity = 'deleted')
    BEGIN
        DECLARE cur CURSOR FOR SELECT locationsHotelsMapID FROM deleted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'LocationsHotelsMap', @id, 'locationsHotelsMapID',
                (
                    SELECT *
                    FROM deleted i 
                    WHERE i.locationsHotelsMapID =  @id
                    FOR JSON AUTO
                ),0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
    ELSE
    BEGIN
        DECLARE cur CURSOR
        FOR SELECT locationsHotelsMapID
        FROM inserted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'LocationsHotelsMap', @id, 'locationsHotelsMapID',
                (
                    SELECT *
                    FROM inserted i 
                    WHERE i.locationsHotelsMapID =  @id
                    FOR JSON AUTO
                ), 0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
END

GO
DISABLE TRIGGER [dbo].[LocationsHotelsMap.InsertUpdateDelete]
    ON [dbo].[LocationsHotelsMap];

