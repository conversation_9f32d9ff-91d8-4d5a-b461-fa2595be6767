﻿CREATE TABLE [dbo].[CancellationReceivedTypes] (
    [cancellationReceivedTypeID]   INT           IDENTITY (1, 1) NOT NULL,
    [cancellationReceivedTypeName] NVARCHAR (64) NOT NULL,
    [sortOrder]                    INT           NOT NULL,
    [active]                       BIT           NOT NULL,
    CONSTRAINT [PK_CancellationReceivedTypes] PRIMARY KEY CLUSTERED ([cancellationReceivedTypeID] ASC),
    CONSTRAINT [UK_CancellationReceivedTypes_cancellationReceivedTypeName] UNIQUE NONCLUSTERED ([cancellationReceivedTypeName] ASC)
);


GO
CREATE TRIGGER [dbo].[CancellationReceivedTypes.InsertUpdateDelete]
    ON [dbo].[CancellationReceivedTypes]
    FOR INSERT, UPDATE, DELETE
AS 
BEGIN
    SET NOCOUNT ON;

    DECLARE @Activity  NVARCHAR (8), @id INT;

    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'updated'
    END

    IF EXISTS (SELECT * FROM inserted) AND NOT EXISTS(SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'inserted'
    END

    IF EXISTS (SELECT * FROM deleted) AND NOT EXISTS(SELECT * FROM inserted)
    BEGIN
        SET @Activity = 'deleted'
    END

    

    IF (@Activity = 'deleted')
    BEGIN
        DECLARE cur CURSOR FOR SELECT cancellationReceivedTypeID FROM deleted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'CancellationReceivedTypes', @id, 'cancellationReceivedTypeID',
                (
                    SELECT *
                    FROM deleted i 
                    WHERE i.cancellationReceivedTypeID =  @id
                    FOR JSON AUTO
                ),0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
    ELSE
    BEGIN
        DECLARE cur CURSOR
        FOR SELECT cancellationReceivedTypeID
        FROM inserted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'CancellationReceivedTypes', @id, 'cancellationReceivedTypeID',
                (
                    SELECT *
                    FROM inserted i 
                    WHERE i.cancellationReceivedTypeID =  @id
                    FOR JSON AUTO
                ), 0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
END

GO
DISABLE TRIGGER [dbo].[CancellationReceivedTypes.InsertUpdateDelete]
    ON [dbo].[CancellationReceivedTypes];

