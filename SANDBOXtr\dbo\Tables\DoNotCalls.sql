﻿CREATE TABLE [dbo].[DoNotCalls] (
    [customerID] INT NOT NULL,
    CONSTRAINT [PK_DoNotCalls] PRIMARY KEY CLUSTERED ([customerID] ASC)
);


GO
CREATE TRIGGER [dbo].[DoNotCalls.InsertUpdateDelete]
    ON [dbo].[DoNotCalls]
    FOR INSERT, UPDATE, DELETE
AS 
BEGIN
    SET NOCOUNT ON;

    DECLARE @Activity  NVARCHAR (8), @id INT;

    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'updated'
    END

    IF EXISTS (SELECT * FROM inserted) AND NOT EXISTS(SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'inserted'
    END

    IF EXISTS (SELECT * FROM deleted) AND NOT EXISTS(SELECT * FROM inserted)
    BEGIN
        SET @Activity = 'deleted'
    END

    

    IF (@Activity = 'deleted')
    BEGIN
        DECLARE cur CURSOR FOR SELECT customerID FROM deleted;
        OP<PERSON> cur;
        FETCH NEXT FROM cur INTO @id;
        WH<PERSON><PERSON> @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'DoNotCalls', @id, 'customerID',
                (
                    SELECT *
                    FROM deleted i 
                    WHERE i.customerID =  @id
                    FOR JSON AUTO
                ),0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
    ELSE
    BEGIN
        DECLARE cur CURSOR
        FOR SELECT customerID
        FROM inserted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'DoNotCalls', @id, 'customerID',
                (
                    SELECT *
                    FROM inserted i 
                    WHERE i.customerID =  @id
                    FOR JSON AUTO
                ), 0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
END

GO
DISABLE TRIGGER [dbo].[DoNotCalls.InsertUpdateDelete]
    ON [dbo].[DoNotCalls];

