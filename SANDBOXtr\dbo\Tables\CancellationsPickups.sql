﻿CREATE TABLE [dbo].[CancellationsPickups] (
    [cancellationsPickupID]    INT IDENTITY (1, 1) NOT NULL,
    [cancellationID]           INT NOT NULL,
    [cancellationStatusTypeID] INT NOT NULL,
    [cancellationUserID]       INT NULL,
    CONSTRAINT [PK_CancellationsPickup] PRIMARY KEY CLUSTERED ([cancellationsPickupID] ASC),
    CONSTRAINT [FK_CancellationsPickup_Cancellations] FOREIGN KEY ([cancellationID]) REFERENCES [dbo].[Cancellations] ([cancellationID]),
    CONSTRAINT [FK_CancellationsPickup_CancellationUser] FOREIGN KEY ([cancellationUserID]) REFERENCES [dbo].[Users] ([userID])
);


GO
CREATE NONCLUSTERED INDEX [IX_CancellationsPickups_cancellationID]
    ON [dbo].[CancellationsPickups]([cancellationID] ASC);


GO
CREATE TRIGGER [dbo].[CancellationsPickups.InsertUpdateDelete]
    ON [dbo].[CancellationsPickups]
    FOR INSERT, UPDATE, DELETE
AS 
BEGIN
    SET NOCOUNT ON;

    DECLARE @Activity  NVARCHAR (8), @id INT;

    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'updated'
    END

    IF EXISTS (SELECT * FROM inserted) AND NOT EXISTS(SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'inserted'
    END

    IF EXISTS (SELECT * FROM deleted) AND NOT EXISTS(SELECT * FROM inserted)
    BEGIN
        SET @Activity = 'deleted'
    END

    

    IF (@Activity = 'deleted')
    BEGIN
        DECLARE cur CURSOR FOR SELECT cancellationsPickupID FROM deleted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'CancellationsPickups', @id, 'cancellationsPickupID',
                (
                    SELECT *
                    FROM deleted i 
                    WHERE i.cancellationsPickupID =  @id
                    FOR JSON AUTO
                ),0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
    ELSE
    BEGIN
        DECLARE cur CURSOR
        FOR SELECT cancellationsPickupID
        FROM inserted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'CancellationsPickups', @id, 'cancellationsPickupID',
                (
                    SELECT *
                    FROM inserted i 
                    WHERE i.cancellationsPickupID =  @id
                    FOR JSON AUTO
                ), 0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
END

GO
DISABLE TRIGGER [dbo].[CancellationsPickups.InsertUpdateDelete]
    ON [dbo].[CancellationsPickups];

