﻿
CREATE PROCEDURE [dbo].[InsertCustomDisplay_HideCallCenter]

AS
BEGIN

	DECLARE @controlID nvarchar(64)
	DECLARE @controlDelegates nvarchar(4000)

	DECLARE @GroupTypeKey nvarchar(64) SET @GroupTypeKey = 'HideCallCenter'

	DELETE FROM CustomPageControlDisplayRights WHERE (customPageControlDisplayRightGroupTypeKey = @GroupTypeKey)

	DECLARE @Add int SET @Add = 1
	DECLARE @Remove int SET @Remove = 2

	DECLARE @Insert int SET @Insert = 1
	DECLARE @Select int SET @Select = 2
	DECLARE @Update int SET @Update = 3

	DECLARE @Visible int SET @Visible = 1
	DECLARE @Enabled int SET @Enabled = 2
	DECLARE @Text int SET @Text = 3
	DECLARE @Delegate int SET @Delegate = 4
	DECLARE @Authorized int SET @Authorized = 5
	DECLARE @Administrated int SET @Administrated = 6
	DECLARE @GridColumnVisible int SET @GridColumnVisible = 7

	DECLARE @PagePathID [varchar] (256)

	SET @PagePathID = 'ASP.customers_tours_usercontrols_toursdetails_ascx_formView'

	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Insert,'callCenterPlaceHolder',@Visible,0,NULL,NULL
	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'callCenterPlaceHolder',@Visible,0,NULL,NULL
	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Update,'callCenterPlaceHolder',@Visible,0,NULL,NULL

	SET @PagePathID = 'ASP.reports_default_aspx'
	
	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'panelCallCenterReports',@Visible,0,NULL,NULL

	SET @PagePathID = 'ASP.reports_callcenterdetailreport_aspx'

	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'form1',@Authorized,0,NULL,NULL

	SET @PagePathID = 'ASP.reports_usercontrols_reportconfigurationcommands_ascx'
	
	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'dropDownReportTypes',@Delegate,NULL,NULL,'TrackResults.Web.Security.DropDownDisplay.ReportTypes,TrackResults.Web:RemoveByReportTypes(TrackResults.BES.Data.Types.ReportType[] {CallDetail})'

	SET @PagePathID = 'ASP.reports_updatemarketinginformationreport_aspx'

	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'callBackTimeTableRow',@Visible,0,NULL,NULL

	SET @PagePathID = 'ASP.reports_usercontrols_reportby_ascx'

	SET @controlDelegates = 'TrackResults.Web.Security.MenuDisplay.ReportByTypes,TrackResults.Web:Remove(TrackResults.BES.Data.Types.ReportByType CallBackTimes)'
	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'menuReportByType',@Delegate,NULL,NULL,@controlDelegates

	SET @PagePathID = 'TrackResults.Web.Data.Cache.TourDateTypesPickListCache_TourDateTypesPickList'

	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'TourDateTypesPickList',@Delegate,NULL,NULL,'TrackResults.Web.Security.CollectionDisplayRules,TrackResults.Web:RemoveKeyValuePairByIntKey(System.Int32 5)'
		
	SET @PagePathID = 'TrackResults.BES.Services.ImportExportService'

	SET @controlDelegates = 'TrackResults.BES.Security.CollectionRules:RemoveKeyValuePairByStringKey(System.String callBackDate)'
	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'columnKeyDisplayNames',@Delegate,NULL,NULL,@controlDelegates
	
	SET @controlDelegates = 'TrackResults.BES.Security.CollectionRules:RemoveKeyValuePairByStringKey(System.String callBackTime)'
	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'columnKeyDisplayNames',@Delegate,NULL,NULL,@controlDelegates
	
	SET @controlDelegates = 'TrackResults.BES.Security.CollectionRules:RemoveKeyValuePairByStringKey(System.String callNotes)'
	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'columnKeyDisplayNames',@Delegate,NULL,NULL,@controlDelegates

END