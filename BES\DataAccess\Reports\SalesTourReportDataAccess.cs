using DocumentFormat.OpenXml.Office2010.Excel;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using TrackResults.BES.Data;
using TrackResults.BES.Data.Business;
using TrackResults.BES.Data.Cache;
using TrackResults.BES.Data.Columns;
using TrackResults.BES.Data.Criteria;
using TrackResults.BES.Data.Criteria.TierOne;
using TrackResults.BES.Data.Tables.Reports;
using TrackResults.BES.Data.Types;
using TrackResults.BES.Rules;
using TrackResults.BES.Security.Criteria;
using TrackResults.BES.Services;
using TrackResults.Common.Core.Collections.Generic;
using TrackResults.Common.Core.Data;
using TrackResults.Common.Core.Data.SqlClient;
using TrackResults.Common.Core.Data.SqlClient.MSSql;
using TrackResults.Common.Core.Extensions;
using TrackResults.Common.DAL;
using TrackResults.Common.DAL.Sql;
using TrackResults.Common.DAL.Types;

namespace TrackResults.BES.DataAccess.Reports
{
	public static class SalesTourReportDataAccess
	{
		public static DataSet SelectPagedSalesTourReport(int startPagedIndex, int pagedCount, int? drillThroughReportID, ReportType drillThroughReportType,
			   ReportByType? drillThroughReportByType, ReportBySubType? drillThroughReportBySubType, HashSet<string> drillThroughReportCalculateSettings,
			   SqlCriteria drillThroughSqlCriteria, List<string> drillThroughRowIDs, string drillThroughColumnName, string orderBy, out int totalCount)
		{
			HashSet<int> tourIDs;

			switch (drillThroughReportType)
			{
				case ReportType.CustomAnalyticView:
					{
						ToursReportByBaseDataAccess reportByBaseDataAccess =
							CustomAnalyticViewsService.GetReportByDataAccess((int)drillThroughReportID, drillThroughReportByType);

						reportByBaseDataAccess.SelectReport(drillThroughSqlCriteria, drillThroughReportByType, drillThroughReportBySubType,
							drillThroughReportCalculateSettings, drillThroughRowIDs, false, drillThroughColumnName, out tourIDs);
					}
					break;

				case ReportType.LostVolumeEfficiency:
					{
						LostVolumeDataAccess lostVolumeDataAccess = new LostVolumeDataAccess();

						lostVolumeDataAccess.SelectReport(drillThroughSqlCriteria, drillThroughReportByType, drillThroughReportBySubType,
							drillThroughReportCalculateSettings, drillThroughRowIDs, false, drillThroughColumnName, out tourIDs);
					}
					break;

				case ReportType.PendersEfficiency:
					{
						PenderEfficiencyDataAccess penderEfficiencyDataAccess = new PenderEfficiencyDataAccess();

						penderEfficiencyDataAccess.SelectReport(drillThroughSqlCriteria, drillThroughReportByType, drillThroughReportBySubType,
							drillThroughReportCalculateSettings, drillThroughRowIDs, false, drillThroughColumnName, out tourIDs);
					}
					break;

				case ReportType.SaleStatusEfficiency:
					{
						StatusSalesDataAccess statusSalesDataAccess = new StatusSalesDataAccess();

						statusSalesDataAccess.SelectReport(drillThroughSqlCriteria, drillThroughReportByType, drillThroughReportBySubType,
							drillThroughReportCalculateSettings, drillThroughRowIDs, false, drillThroughColumnName, out tourIDs);
					}
					break;

				default:
					throw new Exception("SelectPagedSalesTourReport does not have a definition for drillThroughReportType: " +
						drillThroughReportType.ToString());
			}

			SqlCriteria sqlCriteria = new SqlCriteria();

			if (tourIDs != null && tourIDs.Count > 0)
			{
				sqlCriteria.Add(TourNames.Qualified.TOUR_ID + tourIDs.ToInList());
			}
			else
			{
				sqlCriteria.Add(TourNames.Qualified.TOUR_ID + " = 0 ");
			}

			return SelectPagedSalesTourReport(startPagedIndex, pagedCount, sqlCriteria, orderBy, false, out totalCount);
		}

		public static DataSet SelectPagedSalesTourReport(int startPagedIndex, int pagedCount, SqlCriteria sqlCriteria, string orderBy,
			out int totalCount)
		{
			return SelectPagedSalesTourReport(startPagedIndex, pagedCount, sqlCriteria, orderBy, true, out totalCount);
		}

		public static DataSet SelectPagedSalesTourReport(int startPagedIndex, int pagedCount, SqlCriteria sqlCriteria, string orderBy,
			bool includeSummary, out int totalCount)
		{
			sqlCriteria.Add("Tours.tourStatusTypeID <> " + (int)TourStatusType.Unscheduled);
			sqlCriteria.Add("rescheduledTourID IS NULL");

			// NOTE: For performance reasons a copy of the object has not been created.  The value is set and reset instead.
			TierOneCriteria purchaseDateTierOneCriteria = sqlCriteria as TierOneCriteria;
			int? currentTierOneDateSubTypeID = null;
			if (purchaseDateTierOneCriteria != null &&
				purchaseDateTierOneCriteria.DateTimeCriteria.TierOneDateTypeID == (int)TierOneDateType.SaleDate)
			{
				currentTierOneDateSubTypeID = purchaseDateTierOneCriteria.DateTimeCriteria.TierOneDateSubTypeID;
				purchaseDateTierOneCriteria.DateTimeCriteria.TierOneDateSubTypeID = (int)TierOneDateSubType.PurchaseTourDate;
			}

			SecureSelectTourCriteria secureCriteria = new SecureSelectTourCriteria(sqlCriteria);

            string purchaseCountStatement = null;
            if (purchaseDateTierOneCriteria != null)
            {
                if (purchaseDateTierOneCriteria.TourTourCriteria.PurchasesCount != null)
                {
                    purchaseCountStatement = " AND ((Select Count(purchaseID) from Purchases p where saleAmount > 0 and p.tourID = Tours.tourID) = " + (int)purchaseDateTierOneCriteria.TourTourCriteria.PurchasesCount + ") ";
                }
            }

			string sqlOrderBy = null;
			string sqlStatementAdditionalColumns = null;

			MarketingTourReportDataAccess.SetPagedToursOrderingStatements(orderBy, false, out sqlOrderBy, out sqlStatementAdditionalColumns);

			string sqlStatement =
			 @" IF object_id('tempdb..#PagedTours') IS NOT NULL
				BEGIN
				   DROP TABLE #PagedTours
				END

				CREATE TABLE #PagedTours
				(
					[tourID] [int] NOT NULL,
					[pageIndex] [int] NOT NULL,
					PRIMARY KEY ([tourID])
				)

				INSERT INTO #PagedTours ([tourID], [pageIndex])
					SELECT DISTINCT Tours.tourID, ROW_NUMBER() OVER(ORDER BY " + sqlOrderBy + @") as pageIndex
					FROM Tours INNER JOIN
						(SELECT DISTINCT Tours.tourID
						FROM Tours " +
                        secureCriteria.BuildJoinWhereStatement() + purchaseCountStatement +
						@" ) AS CriteriaTours ON Tours.tourID = CriteriaTours.tourID " +
						sqlStatementAdditionalColumns +

             @" SELECT Tours.tourID, Customers.customerID, Customers.firstName, Customers.lastName,
					Customers.guestFirstName, Customers.guestLastName, GuestTypes.guestTypeName,
					Customers.primaryPhone, Customers.secondaryPhone, dbo.GetStringWhenIntIsNotNull(DoNotCalls.customerID, 'doNotCall') AS doNotCall,
					States.stateAbbreviation, Countries.countryName,
					TourStatusTypes.tourStatusTypeID, TourStatusTypes.tourStatusTypeName,
					Locations.name AS locationName, Tours.tourDate, Tours.tourTime, Tours.rescheduledCount, Tours.entryDateTime,
					Teams_1.name AS marketingTeam,
					Users_1.fullName AS marketingAgent, 
					Users_2.fullName AS marketingCloser,
					Users_3.fullName AS confirmer,
					Users_4.fullName AS resetter,
					dbo.GetConcantenatedGiftCodesByTourID(Tours.tourID, N'<br />') AS giftCodes, 
					Venues.code AS venueCode, Campaigns.campaignCode, Hotels.hotelCode, Tours.depositAmount,
					Teams_2.name AS salesTeam,
					Users_5.fullName AS podium,
					Users_6.fullName AS salesRep, 
					Users_7.fullName AS salesCloser1,
					Users_8.fullName AS salesCloser2,
					Users_9.fullName AS [exit],
					Users_10.fullName AS verificationOfficer,
                    Users_11.fullName AS salesRepExit1,
		            Users_12.fullName AS salesRepExit2,
		            Users_13.fullName AS salesRepExit3,
					TourConcernTypes.tourConcernTypeName, FlattenedPurchasesView.numberOfPurchases, Purchases.externalPurchaseID,
					Purchases.saleDate, Purchases.productID, Purchases.saleAmount, Purchases.saleTypeID,
					Purchases.saleStatusTypeID, Purchases.productCategoryID, MilestonesWasPendingView.purchaseID AS wasPending,
					dbo.GetConcantenatedNotes(Tours.tourID, N' ------ ','Tour.MarketingNotes') AS marketingNotes,
					dbo.GetConcantenatedNotes(Tours.tourID, N' ------ ','Tour.SalesNotes') AS salesNotes,
                    dbo.GetConcantenatedNotes(Tours.tourID, N' ------ ','Tour.SalesNotesExit') AS salesNotesExit,
                    dbo.GetNotesToursFormLabel ('Tour.FormNotes1.Plural','Form Notes 1') AS notesToursForm1Label,
                    dbo.GetConcantenatedNotes(Tours.tourID, N' ------ ','Tour.FormNotes1') AS notesToursForm1,
                    dbo.GetNotesToursFormLabel ('Tour.FormNotes2.Plural','Form Notes 2') AS notesToursForm2Label,
                    dbo.GetConcantenatedNotes(Tours.tourID, N' ------ ','Tour.FormNotes2') As notesToursForm2,
                    dbo.GetNotesToursFormLabel ('Tour.FormNotes3.Plural','Form Notes 3') AS notesToursForm3Label,
                    dbo.GetConcantenatedNotes(Tours.tourID, N' ------ ','Tour.FormNotes3') As notesToursForm3,
                    dbo.GetNotesToursFormLabel ('Tour.FormNotes4.Plural','Form Notes 4') AS notesToursForm4Label,
                    dbo.GetConcantenatedNotes(Tours.tourID, N' ------ ','Tour.FormNotes4') As notesToursForm4,
                    dbo.GetNotesToursFormLabel ('Tour.FormNotes5.Plural','Form Notes 5') AS notesToursForm5Label,
                    dbo.GetConcantenatedNotes(Tours.tourID, N' ------ ','Tour.FormNotes5') As notesToursForm5,
                    dbo.GetNotesToursFormLabel ('Tour.FormNotes6.Plural','Form Notes 6') AS notesToursForm6Label,
                    dbo.GetConcantenatedNotes(Tours.tourID, N' ------ ','Tour.FormNotes6') As notesToursForm6,
                    dbo.GetNotesToursFormLabel ('Tour.FormNotes7.Plural','Form Notes 7') AS notesToursForm7Label,
                    dbo.GetConcantenatedNotes(Tours.tourID, N' ------ ','Tour.FormNotes7') As notesToursForm7,
                    dbo.GetNotesToursFormLabel ('Tour.FormNotes8.Plural','Form Notes 8') AS notesToursForm8Label,
                    dbo.GetConcantenatedNotes(Tours.tourID, N' ------ ','Tour.FormNotes8') As notesToursForm8,
                    dbo.GetNotesToursFormLabel ('Tour.FormNotes9.Plural','Form Notes 9') AS notesToursForm9Label,
                    dbo.GetConcantenatedNotes(Tours.tourID, N' ------ ','Tour.FormNotes9') As notesToursForm9,
                    dbo.GetNotesToursFormLabel ('Tour.FormNotes10.Plural','Form Notes 10') AS notesToursForm10Label,
                    dbo.GetConcantenatedNotes(Tours.tourID, N' ------ ','Tour.FormNotes10') As notesToursForm10,
                    dbo.GetNotesToursFormLabel ('Tour.FormNotes11.Plural','Form Notes 11') AS notesToursForm11Label,
                    dbo.GetConcantenatedNotes(Tours.tourID, N' ------ ','Tour.FormNotes11') As notesToursForm11,
                    dbo.GetNotesToursFormLabel ('Tour.FormNotes12.Plural','Form Notes 12') AS notesToursForm12Label,
                    dbo.GetConcantenatedNotes(Tours.tourID, N' ------ ','Tour.FormNotes12') As notesToursForm12,
                    dbo.GetNotesToursFormLabel ('Tour.FormNotes13.Plural','Form Notes 13') AS notesToursForm13Label,
                    dbo.GetConcantenatedNotes(Tours.tourID, N' ------ ','Tour.FormNotes13') As notesToursForm13,
                    dbo.GetNotesToursFormLabel ('Tour.FormNotes14.Plural','Form Notes 14') AS notesToursForm14Label,
                    dbo.GetConcantenatedNotes(Tours.tourID, N' ------ ','Tour.FormNotes14') As notesToursForm14,
                    dbo.GetNotesToursFormLabel ('Tour.FormNotes15.Plural','Form Notes 15') AS notesToursForm15Label,
                    dbo.GetConcantenatedNotes(Tours.tourID, N' ------ ','Tour.FormNotes15') As notesToursForm15
				FROM Tours INNER JOIN
					#PagedTours ON Tours.tourID = #PagedTours.tourID INNER JOIN
					Customers ON Tours.customerID = Customers.customerID LEFT OUTER JOIN
					GuestTypes ON Customers.guestTypeID = GuestTypes.guestTypeID LEFT OUTER JOIN
					DoNotCalls ON Customers.customerID = DoNotCalls.customerID LEFT OUTER JOIN
					Countries ON Customers.countryID = Countries.countryID LEFT OUTER JOIN
					States ON Customers.stateID = States.stateID LEFT OUTER JOIN
					TourStatusTypes ON Tours.tourStatusTypeID = TourStatusTypes.tourStatusTypeID LEFT OUTER JOIN
					Locations ON Tours.locationID = Locations.locationID LEFT OUTER JOIN
					Teams AS Teams_1 ON Tours.marketingTeamID = Teams_1.teamID LEFT OUTER JOIN
					Users AS Users_1 ON Tours.marketingAgentID = Users_1.userID LEFT OUTER JOIN
					Users AS Users_2 ON Tours.marketingCloserID = Users_2.userID LEFT OUTER JOIN
					Users AS Users_3 ON Tours.confirmerID = Users_3.userID LEFT OUTER JOIN
					Users AS Users_4 ON Tours.resetterID = Users_4.userID LEFT OUTER JOIN
					Venues ON Tours.venueID = Venues.venueID LEFT OUTER JOIN
					Campaigns ON Tours.campaignID = Campaigns.campaignID LEFT OUTER JOIN
					Hotels ON Tours.hotelID = Hotels.hotelID LEFT OUTER JOIN
					Teams AS Teams_2 ON Tours.salesTeamID = Teams_2.teamID LEFT OUTER JOIN
					Users AS Users_5 ON Tours.podiumID = Users_5.userID LEFT OUTER JOIN
					Users AS Users_6 ON Tours.salesRepID = Users_6.userID LEFT OUTER JOIN
					Users AS Users_7 ON Tours.salesCloser1ID = Users_7.userID LEFT OUTER JOIN
					Users AS Users_8 ON Tours.salesCloser2ID = Users_8.userID LEFT OUTER JOIN
					Users AS Users_9 ON Tours.exitID = Users_9.userID LEFT OUTER JOIN
					Users AS Users_10 ON Tours.verificationOfficerID = Users_10.userID LEFT OUTER JOIN
                    Users AS Users_11 ON Tours.salesRepExit1ID = Users_11.userID LEFT OUTER JOIN
		            Users AS Users_12 ON Tours.salesRepExit2ID = Users_12.userID LEFT OUTER JOIN
		            Users AS Users_13 ON Tours.salesRepExit3ID = Users_13.userID LEFT OUTER JOIN
					TourConcernTypes ON Tours.tourConcernTypeID = TourConcernTypes.tourConcernTypeID INNER JOIN
					FlattenedPurchasesView ON Tours.tourID = FlattenedPurchasesView.tourID LEFT OUTER JOIN
					Purchases ON FlattenedPurchasesView.PurchaseID = Purchases.PurchaseID LEFT OUTER JOIN
					MilestonesWasPendingView ON Purchases.purchaseID = MilestonesWasPendingView.purchaseID

				WHERE (#PagedTours.pageIndex BETWEEN (@startPagedIndex + 1) AND @startPagedIndex + @pagedCount)
				ORDER by #PagedTours.pageIndex

				SET @totalCount = (SELECT COUNT(*) FROM #PagedTours)

				DROP TABLE #PagedTours";

			SqlCommand sqlCommand = new SqlCommand(sqlStatement);

			sqlCommand.Parameters.AddWithValue("@" + DBConstants.START_PAGED_INDEX, startPagedIndex);
			sqlCommand.Parameters.AddWithValue("@" + DBConstants.PAGED_COUNT, pagedCount);

			string totalCountKey = "totalCount";
			SqlParameter identityParameter = new SqlParameter(totalCountKey, SqlDbType.Int);
			identityParameter.Direction = ParameterDirection.Output;
			sqlCommand.Parameters.Add(identityParameter);

			DataTable reportTable = new DataTable();
			MSSqlConnectionScope.ExecuteTransactionNewSnapshot(ConnectionStrings.Default, delegate()
			{
				MSSqlDataAccess.Fill(ConnectionStrings.Default, sqlCommand, reportTable);
			});

			reportTable.Columns.Add("calculatedSaleStatusType", typeof(System.Int32));
			reportTable.Columns.Add("purchases", typeof(List<Purchase>));

			List<Purchase> purchases;

			foreach (DataRow reportRow in reportTable.Rows)
			{
				if ((int)reportRow["numberOfPurchases"] == 0)
				{
					reportRow["calculatedSaleStatusType"] = CalculatedSaleStatusType.Unknown;
					reportRow["purchases"] = new List<Purchase>();
				}
				else if ((int)reportRow["numberOfPurchases"] == 1)
				{
					reportRow["calculatedSaleStatusType"] = SaleRules.CalculateSaleType(
						(!(reportRow["saleAmount"] is DBNull)) ? (decimal)reportRow["saleAmount"] : 0,
						(!(reportRow["saleStatusTypeID"] is DBNull)) ? (SaleStatusType)reportRow["saleStatusTypeID"] : SaleStatusType.Unknown);

					reportRow["purchases"] = new List<Purchase>().Build(
						new Purchase
						{
							ExternalPurchaseID = (string)reportRow.GetNullableValue("externalPurchaseID"),
							SaleDate = (DateTime?)reportRow.GetNullableValue("saleDate"),
							ProductID = (int?)reportRow.GetNullableValue("productID"),
                            ProductCategoryID = (int?)reportRow.GetNullableValue("productCategoryID"),
							SaleAmount = (decimal?)reportRow.GetNullableValue("saleAmount"),
							SaleTypeID = (int?)reportRow.GetNullableValue("saleTypeID"),
							SaleStatusTypeID = (int?)reportRow.GetNullableValue("saleStatusTypeID"),
						});
				}
				else
				{
					purchases = PurchasesDataAccess.SelectDataByTourID((int)reportRow["tourID"]);

					reportRow["calculatedSaleStatusType"] = SaleRules.CalculateSaleType(purchases.ToArray());
					reportRow["purchases"] = purchases;
				}
			}

			DataSet dataSet = new DataSet();
			dataSet.Tables.Add(reportTable);

			if (includeSummary)
			{
				StatusSalesDataAccess statusSalesDataAccess = new StatusSalesDataAccess();

				DataSet summaryDataSet = statusSalesDataAccess.SelectReport(sqlCriteria, null, null, null, null, false);
				DataTable summaryDataTable = summaryDataSet.Tables[0];
				summaryDataTable.TableName = "summaryOld";
				summaryDataSet.Tables.Remove("summaryOld");
				summaryDataTable.TableName = Constants.SUMMARY_TABLE;
				dataSet.Tables.Add(summaryDataTable);
			}

			totalCount = (int)sqlCommand.Parameters[totalCountKey].Value;

			if (currentTierOneDateSubTypeID != null)
			{
				purchaseDateTierOneCriteria.DateTimeCriteria.TierOneDateSubTypeID = (int)currentTierOneDateSubTypeID;
			}

			return dataSet;
		}

        public static DataSet SelectAllReport(ReportByType? unionReportByType, int startPagedIndex, int pagedCount, SqlCriteria sqlCriteria, string orderBy, string query, bool tableTemporal,
            out int totalCount)
        {
            return SelectAllReport(unionReportByType, startPagedIndex, pagedCount, sqlCriteria, orderBy, query, tableTemporal, true, out totalCount);
        }
        public static DataSet SelectAllReport(ReportByType? unionReportByType, int startPagedIndex, int pagedCount, SqlCriteria sqlCriteria, string orderBy, string query, bool tableTemporal,
            bool includeSummary, out int totalCount)
        {
            sqlCriteria.Add("Tours.tourStatusTypeID <> " + (int)TourStatusType.Unscheduled);
            //sqlCriteria.Add("rescheduledTourID IS NULL");

            // NOTE: For performance reasons a copy of the object has not been created.  The value is set and reset instead.
            TierOneCriteria tierOneCriteria = sqlCriteria as TierOneCriteria;

            int? currentTierOneDateSubTypeID = null;
            
			if (tierOneCriteria != null &&
                tierOneCriteria.DateTimeCriteria.TierOneDateTypeID == (int)TierOneDateType.SaleDate)
            {
                currentTierOneDateSubTypeID = tierOneCriteria.DateTimeCriteria.TierOneDateSubTypeID;
                tierOneCriteria.DateTimeCriteria.TierOneDateSubTypeID = (int)TierOneDateSubType.PurchaseTourDate;
            }

            SqlCriteria sqlFilteredCriteria = new SqlCriteria();
            SqlCriteria sqlUnionFilteredCriteria = new SqlCriteria();

            bool includeCustomers;
            bool includePurchases;
            string sqlStatementAdditionalColumnsSource;
            string sqlStatementAdditionalJoinsSource;
            string sqlStatementAdditionalColumns;
            ReportBySubType? unionReportBySubType;

            ToursReportByBaseDataAccess.GetAdditionalSql(sqlCriteria, tierOneCriteria, sqlFilteredCriteria, unionReportByType, ReportBySubType.Tour_TourDate, false, null,
                out includeCustomers, out includePurchases, out sqlStatementAdditionalColumnsSource, out sqlStatementAdditionalJoinsSource,
                out unionReportByType, out unionReportBySubType);


            SecureSelectTourCriteria secureCriteria = new SecureSelectTourCriteria(sqlCriteria);

			string purchaseCountStatement = null;
			if (tierOneCriteria != null)
			{
				if (tierOneCriteria.TourTourCriteria.PurchasesCount != null)
				{
					purchaseCountStatement = " AND ((Select Count(purchaseID) from Purchases p where saleAmount > 0 and p.tourID = Tours.tourID) = " + (int)tierOneCriteria.TourTourCriteria.PurchasesCount + ") ";
				}
			}

			string sqlOrderBy = null;
            //string sqlStatementAdditionalColumns = null;

            MarketingTourReportDataAccess.SetPagedToursOrderingStatements(orderBy, false, out sqlOrderBy, out sqlStatementAdditionalColumns);

            StringBuilder sqlStatementBuilder = new StringBuilder();

            if (tableTemporal)
            {
                sqlStatementBuilder.AppendLine(
                         @" IF object_id('tempdb..#FilteredTours') IS NOT NULL
			BEGIN
			   DROP TABLE #FilteredTours
			END

			CREATE TABLE #FilteredTours
			(
				[tourID] [int] NOT NULL,
				PRIMARY KEY ([tourID])
			)

			INSERT INTO #FilteredTours ([tourID])
			SELECT DISTINCT Tours.tourID
			FROM Tours "
                            );
            }
            
            string sqlStatement = "";

            if (tableTemporal)
            {
                sqlStatementBuilder.AppendLine(secureCriteria.BuildJoinWhereStatement());

                sqlStatement = @"

				ORDER BY Tours.tourID	

				DROP TABLE #FilteredTours";
            }

            sqlStatementBuilder.AppendLine(query);
            sqlStatementBuilder.AppendLine(sqlFilteredCriteria.BuildWhereStatement());

            if (tableTemporal)
            {
                sqlStatementBuilder.AppendLine(sqlStatement);
            }
            SqlCommand sqlCommand = new SqlCommand(sqlStatementBuilder.ToString());

            sqlCommand.Parameters.AddWithValue("@" + DBConstants.START_PAGED_INDEX, startPagedIndex);
            sqlCommand.Parameters.AddWithValue("@" + DBConstants.PAGED_COUNT, pagedCount);


            string totalCountKey = "totalCount";
            SqlParameter identityParameter = new SqlParameter(totalCountKey, SqlDbType.Int);
            identityParameter.Direction = ParameterDirection.Output;
            sqlCommand.Parameters.Add(identityParameter);

            DataTable reportTable = new DataTable();
            MSSqlConnectionScope.ExecuteTransactionNewSnapshot(ConnectionStrings.Default, delegate ()
            {
                MSSqlDataAccess.Fill(ConnectionStrings.Default, sqlCommand, reportTable);
            });

            DataSet dataSet = new DataSet();
            dataSet.Tables.Add(reportTable);

            totalCount = 0;

            return dataSet;
        }
    }
}
