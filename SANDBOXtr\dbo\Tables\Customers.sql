﻿CREATE TABLE [dbo].[Customers] (
    [customerID]               INT            IDENTITY (1, 1) NOT NULL,
    [externalCustomerID]       NVARCHAR (128) NULL,
    [customerTypeID]           INT            NULL,
    [customerStatusTypeID]     INT            NULL,
    [customerDispositionID]    INT            NULL,
    [firstName]                NVARCHAR (64)  NULL,
    [lastName]                 NVARCHAR (64)  NULL,
    [age]                      INT            NULL,
    [sex]                      BIT            NULL,
    [customersPickList1ItemID] INT            NULL,
    [guestFirstName]           NVARCHAR (64)  NULL,
    [guestLastName]            NVARCHAR (64)  NULL,
    [guestTypeID]              INT            NULL,
    [guestAge]                 INT            NULL,
    [guestSex]                 BIT            NULL,
    [customersPickList2ItemID] INT            NULL,
    [incomeTypeID]             INT            NULL,
    [customersPickList3ItemID] INT            NULL,
    [customersText1]           NVARCHAR (512) NULL,
    [customersDecimal1]        DECIMAL (9, 2) NULL,
    [customersBool1]           BIT            NULL,
    [customersDate1]           DATETIME       NULL,
    [customersTime1]           DATETIME       NULL,
    [doNotCall]                BIT            NULL,
    [primaryPhone]             NVARCHAR (32)  NULL,
    [secondaryPhone]           NVARCHAR (32)  NULL,
    [streetAddress]            NVARCHAR (128) NULL,
    [streetAddress2]           NVARCHAR (128) NULL,
    [city]                     NVARCHAR (64)  NULL,
    [stateID]                  INT            NULL,
    [zipcode]                  NVARCHAR (16)  NULL,
    [countryID]                INT            NULL,
    [businessName]             NVARCHAR (64)  NULL,
    [email]                    NVARCHAR (128) NULL,
    [customersPickList4ItemID] INT            NULL,
    [customersText2]           NVARCHAR (512) NULL,
    [customersText3]           NVARCHAR (512) NULL,
    [customersDecimal2]        DECIMAL (9, 2) NULL,
    [customersBool2]           BIT            NULL,
    [customersDate2]           DATETIME       NULL,
    [customersTime2]           DATETIME       NULL,
    [apiConnectionID]          INT            NULL,
    [apiExternalCustomerID]    NVARCHAR (128) NULL,
    [apiExternalConnectionID]  NVARCHAR (128) NULL,
    [insertTimeStamp]          DATETIME       CONSTRAINT [DF_Customers_insertTimeStamp] DEFAULT (getdate()) NOT NULL,
    [updateTimeStamp]          DATETIME       CONSTRAINT [DF_Customers_updateTimeStamp] DEFAULT (getdate()) NOT NULL,
    [city2]                    NVARCHAR (64)  NULL,
    [stateID2]                 INT            NULL,
    [zipcode2]                 NVARCHAR (16)  NULL,
    [countryID2]               INT            NULL,
    [userAddDoNotCall]         NVARCHAR (50)  NULL,
    [insertDoNotCall]          DATETIME       NULL,
    [streetAddressGuest]       NVARCHAR (128) NULL,
    [streetAddress2Guest]      NVARCHAR (128) NULL,
    [emailGuest]               NVARCHAR (128) NULL,
    CONSTRAINT [PK_Customers] PRIMARY KEY CLUSTERED ([customerID] ASC),
    CONSTRAINT [FK_Customers_apiConnectionID] FOREIGN KEY ([apiConnectionID]) REFERENCES [dbo].[ApiConnections] ([apiConnectionID]),
    CONSTRAINT [FK_Customers_countryID] FOREIGN KEY ([countryID]) REFERENCES [dbo].[Countries] ([countryID]),
    CONSTRAINT [FK_Customers_customerDispositionID] FOREIGN KEY ([customerDispositionID]) REFERENCES [dbo].[CustomerDispositions] ([customerDispositionID]),
    CONSTRAINT [FK_Customers_customersPickList1ItemID] FOREIGN KEY ([customersPickList1ItemID]) REFERENCES [dbo].[CustomersPickList1Items] ([customersPickList1ItemID]),
    CONSTRAINT [FK_Customers_customersPickList2ItemID] FOREIGN KEY ([customersPickList2ItemID]) REFERENCES [dbo].[CustomersPickList2Items] ([customersPickList2ItemID]),
    CONSTRAINT [FK_Customers_customersPickList3ItemID] FOREIGN KEY ([customersPickList3ItemID]) REFERENCES [dbo].[CustomersPickList3Items] ([customersPickList3ItemID]),
    CONSTRAINT [FK_Customers_customersPickList4ItemID] FOREIGN KEY ([customersPickList4ItemID]) REFERENCES [dbo].[CustomersPickList4Items] ([customersPickList4ItemID]),
    CONSTRAINT [FK_Customers_customerStatusTypeID] FOREIGN KEY ([customerStatusTypeID]) REFERENCES [dbo].[CustomerStatusTypes] ([customerStatusTypeID]),
    CONSTRAINT [FK_Customers_customerTypeID] FOREIGN KEY ([customerTypeID]) REFERENCES [dbo].[CustomerTypes] ([customerTypeID]),
    CONSTRAINT [FK_Customers_GuestTypes] FOREIGN KEY ([guestTypeID]) REFERENCES [dbo].[GuestTypes] ([guestTypeID]),
    CONSTRAINT [FK_Customers_IncomeTypes] FOREIGN KEY ([incomeTypeID]) REFERENCES [dbo].[IncomeTypes] ([incomeTypeID]),
    CONSTRAINT [FK_Customers_States] FOREIGN KEY ([stateID]) REFERENCES [dbo].[States] ([stateID])
);


GO
CREATE NONCLUSTERED INDEX [IX_Customers_apiConnectionID]
    ON [dbo].[Customers]([apiConnectionID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_Customers_apiExternalTourID]
    ON [dbo].[Customers]([apiExternalCustomerID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_Customers_LastName]
    ON [dbo].[Customers]([lastName] ASC);


GO
CREATE TRIGGER [dbo].[Customers.InsertUpdateDelete]
    ON [dbo].[Customers]
    FOR INSERT, UPDATE, DELETE
AS 
BEGIN
    SET NOCOUNT ON;

    DECLARE @Activity  NVARCHAR (8), @id INT;

    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'updated'
    END

    IF EXISTS (SELECT * FROM inserted) AND NOT EXISTS(SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'inserted'
    END

    IF EXISTS (SELECT * FROM deleted) AND NOT EXISTS(SELECT * FROM inserted)
    BEGIN
        SET @Activity = 'deleted'
    END

    

    IF (@Activity = 'deleted')
    BEGIN
        DECLARE cur CURSOR FOR SELECT customerID FROM deleted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'Customers', @id, 'customerID',
                (
                    SELECT *
                    FROM deleted i 
                    WHERE i.customerID =  @id
                    FOR JSON AUTO
                ),0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
    ELSE
    BEGIN
        DECLARE cur CURSOR
        FOR SELECT customerID
        FROM inserted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'Customers', @id, 'customerID',
                (
                    SELECT *
                    FROM inserted i 
                    WHERE i.customerID =  @id
                    FOR JSON AUTO
                ), 0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
END
GO
DISABLE TRIGGER [dbo].[Customers.InsertUpdateDelete]
    ON [dbo].[Customers];

