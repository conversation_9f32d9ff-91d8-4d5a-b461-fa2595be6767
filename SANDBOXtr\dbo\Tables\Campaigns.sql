﻿CREATE TABLE [dbo].[Campaigns] (
    [campaignID]   INT           IDENTITY (1, 1) NOT NULL,
    [campaignName] NVARCHAR (64) NOT NULL,
    [campaignCode] NVARCHAR (64) NULL,
    [active]       BIT           NOT NULL,
    CONSTRAINT [PK_Campaigns] PRIMARY KEY CLUSTERED ([campaignID] ASC),
    CONSTRAINT [UK_Campaigns_campaignName] UNIQUE NONCLUSTERED ([campaignName] ASC)
);


GO
CREATE TRIGGER [dbo].[Campaigns.InsertUpdateDelete]
    ON [dbo].[Campaigns]
    FOR INSERT, UPDATE, DELETE
AS 
BEGIN
    SET NOCOUNT ON;

    DECLARE @Activity  NVARCHAR (8), @id INT;

    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'updated'
    END

    IF EXISTS (SELECT * FROM inserted) AND NOT EXISTS(SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'inserted'
    END

    IF EXISTS (SELECT * FROM deleted) AND NOT EXISTS(SELECT * FROM inserted)
    BEGIN
        SET @Activity = 'deleted'
    END

    

    IF (@Activity = 'deleted')
    BEGIN
        DECLARE cur CURSOR FOR SELECT campaignID FROM deleted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'Campaigns', @id, 'campaignID',
                (
                    SELECT *
                    FROM deleted i 
                    WHERE i.campaignID =  @id
                    FOR JSON AUTO
                ),0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
    ELSE
    BEGIN
        DECLARE cur CURSOR
        FOR SELECT campaignID
        FROM inserted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'Campaigns', @id, 'campaignID',
                (
                    SELECT *
                    FROM inserted i 
                    WHERE i.campaignID =  @id
                    FOR JSON AUTO
                ), 0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
END

GO
DISABLE TRIGGER [dbo].[Campaigns.InsertUpdateDelete]
    ON [dbo].[Campaigns];

