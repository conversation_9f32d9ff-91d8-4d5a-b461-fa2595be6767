﻿CREATE TABLE [dbo].[EulaAcceptances] (
    [eulaAcceptanceID] INT              IDENTITY (1, 1) NOT NULL,
    [eulaVersion]      DECIMAL (9, 2)   NOT NULL,
    [userID]           INT              NOT NULL,
    [name]             NVARCHAR (256)   NOT NULL,
    [userAccountID]    UNIQUEIDENTIFIER NOT NULL,
    [userName]         NVARCHAR (256)   NOT NULL,
    [accepted]         BIT              NOT NULL,
    [signature]        NVARCHAR (256)   NOT NULL,
    [insertTimeStamp]  DATETIME         CONSTRAINT [DF_EulaAcceptances_insertTimeStamp] DEFAULT (getdate()) NOT NULL,
    CONSTRAINT [PK_EulaAcceptances] PRIMARY KEY CLUSTERED ([eulaAcceptanceID] ASC),
    CONSTRAINT [FK_EulaAcceptances_userID] FOREIGN KEY ([userID]) REFERENCES [dbo].[Users] ([userID])
);


GO
CREATE NONCLUSTERED INDEX [IX_EulaAcceptances_userID]
    ON [dbo].[EulaAcceptances]([userID] ASC);


GO
CREATE TRIGGER [dbo].[EulaAcceptances.InsertUpdateDelete]
    ON [dbo].[EulaAcceptances]
    FOR INSERT, UPDATE, DELETE
AS 
BEGIN
    SET NOCOUNT ON;

    DECLARE @Activity  NVARCHAR (8), @id INT;

    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'updated'
    END

    IF EXISTS (SELECT * FROM inserted) AND NOT EXISTS(SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'inserted'
    END

    IF EXISTS (SELECT * FROM deleted) AND NOT EXISTS(SELECT * FROM inserted)
    BEGIN
        SET @Activity = 'deleted'
    END

    

    IF (@Activity = 'deleted')
    BEGIN
        DECLARE cur CURSOR FOR SELECT eulaAcceptanceID FROM deleted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'EulaAcceptances', @id, 'eulaAcceptanceID',
                (
                    SELECT *
                    FROM deleted i 
                    WHERE i.eulaAcceptanceID =  @id
                    FOR JSON AUTO
                ),0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
    ELSE
    BEGIN
        DECLARE cur CURSOR
        FOR SELECT eulaAcceptanceID
        FROM inserted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'EulaAcceptances', @id, 'eulaAcceptanceID',
                (
                    SELECT *
                    FROM inserted i 
                    WHERE i.eulaAcceptanceID =  @id
                    FOR JSON AUTO
                ), 0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
END

GO
DISABLE TRIGGER [dbo].[EulaAcceptances.InsertUpdateDelete]
    ON [dbo].[EulaAcceptances];

