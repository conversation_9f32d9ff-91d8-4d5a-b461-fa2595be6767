﻿
CREATE PROCEDURE [dbo].[InsertCustomDisplay_HidePenderCancellationRequest]

AS
BEGIN

	DECLARE @GroupTypeKey nvarchar(64) SET @GroupTypeKey = 'HidePenderCancellationRequest'
	DECLARE @controlDelegates nvarchar(4000)

	DELETE FROM CustomPageControlDisplayRights WHERE (customPageControlDisplayRightGroupTypeKey = @GroupTypeKey)

	DECLARE @Add int SET @Add = 1
	DECLARE @Remove int SET @Remove = 2

	DECLARE @Insert int SET @Insert = 1
	DECLARE @Select int SET @Select = 2
	DECLARE @Update int SET @Update = 3

	DECLARE @Visible int SET @Visible = 1
	DECLARE @Enabled int SET @Enabled = 2
	DECLARE @Text int SET @Text = 3
	DECLARE @Delegate int SET @Delegate = 4
	DECLARE @Authorized int SET @Authorized = 5
	DECLARE @Administrated int SET @Administrated = 6
	DECLARE @GridColumnVisible int SET @GridColumnVisible = 7

	DECLARE @PagePathID [varchar] (256)


	SET @PagePathID = 'ASP.reports_usercontrols_toursearchcriteria_ascx'

	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'trPenderCancellationRequest',@Visible,0,NULL,NULL
		
	SET @PagePathID = 'ASP.reports_penderefficiencyreport_aspx_gridviewsummary'

	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'supersededColumn',@GridColumnVisible,0,'id_superseded',NULL
	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'supersededVolumeColumn',@GridColumnVisible,0,'id_supersededVolume',NULL
	
	SET @PagePathID = 'ASP.reports_penderefficiencyreport_aspx_gridview'

	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'supersededColumn',@GridColumnVisible,0,'id_superseded',NULL
	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'supersededVolumeColumn',@GridColumnVisible,0,'id_supersededVolume',NULL

	SET @PagePathID = 'ASP.customers_tours_usercontrols_sales_ascx_editlistview'

	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Update,'panelPenderCancellationCommands',@Visible,0,NULL,NULL

	SET @PagePathID = 'TrackResults.BES.Data.Cache.IDNames.IIDNamesCache'
	
	SET @controlDelegates = 'TrackResults.BES.Security.CollectionRules:RemoveKeyValuePairByIntKey(System.Int32 12)'
	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'c_sstin',@Delegate,NULL,NULL,@controlDelegates
	
	SET @PagePathID = 'ASP.customers_tours_usercontrols_sales_ascx_editlistview'

	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Update,'panelPenderCancellationCommands',@Visible,0,NULL,NULL

END