﻿CREATE TABLE [dbo].[ApiConnectionsDataMappings] (
    [apiConnectionsDataMappingID] INT            IDENTITY (1, 1) NOT NULL,
    [apiConnectionID]             INT            NULL,
    [dataMappingKey]              NVARCHAR (64)  NULL,
    [connectionValue]             NVARCHAR (256) COLLATE SQL_Latin1_General_CP1_CS_AS NULL,
    [systemValue]                 NVARCHAR (256) NULL,
    CONSTRAINT [PK_ApiConnectionsDataMappings] PRIMARY KEY CLUSTERED ([apiConnectionsDataMappingID] ASC),
    CONSTRAINT [FK_ApiConnectionsDataMappings_apiConnectionID] FOREIGN KEY ([apiConnectionID]) REFERENCES [dbo].[ApiConnections] ([apiConnectionID]),
    CONSTRAINT [UK_ApiConnectionsDataMappings_dataMappingKey] UNIQUE NONCLUSTERED ([apiConnectionID] ASC, [dataMappingKey] ASC, [connectionValue] ASC)
);

