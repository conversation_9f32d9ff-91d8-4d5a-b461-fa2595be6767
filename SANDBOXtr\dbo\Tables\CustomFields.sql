﻿CREATE TABLE [dbo].[CustomFields] (
    [fieldID]    INT           IDENTITY (1, 1) NOT NULL,
    [fieldName]  NVARCHAR (50) NOT NULL,
    [dataType]   NVARCHAR (50) NOT NULL,
    [featureKey] NVARCHAR (50) NOT NULL,
    [dataFormat] NVARCHAR (50) NULL,
    PRIMARY KEY CLUSTERED ([fieldID] ASC)
);


GO

CREATE TRIGGER [dbo].[CustomFields.InsertUpdateDelete]
    ON [dbo].[CustomFields]
    FOR INSERT, UPDATE, DELETE
AS 
BEGIN
    SET NOCOUNT ON;

    DECLARE @Activity  NVARCHAR (8), @id INT;

    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'updated'
    END

    IF EXISTS (SELECT * FROM inserted) AND NOT EXISTS(SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'inserted'
    END

    IF EXISTS (SELECT * FROM deleted) AND NOT EXISTS(SELECT * FROM inserted)
    BEGIN
        SET @Activity = 'deleted'
    END

    

    IF (@Activity = 'deleted')
    BEGIN
        DECLARE cur CURSOR FOR SELECT fieldID FROM deleted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'CustomFields', @id, 'fieldID',
                (
                    SELECT *
                    FROM deleted i 
                    WHERE i.fieldID =  @id
                    FOR JSON AUTO
                ),0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
    ELSE
    BEGIN
        DECLARE cur CURSOR
        FOR SELECT fieldID
        FROM inserted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'CustomFields', @id, 'fieldID',
                (
                    SELECT *
                    FROM inserted i 
                    WHERE i.fieldID =  @id
                    FOR JSON AUTO
                ), 0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
END

GO
DISABLE TRIGGER [dbo].[CustomFields.InsertUpdateDelete]
    ON [dbo].[CustomFields];

