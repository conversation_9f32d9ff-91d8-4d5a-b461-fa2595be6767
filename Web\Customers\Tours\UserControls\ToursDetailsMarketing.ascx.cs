﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

using TrackResults.BES.Data.Business;
using TrackResults.BES.Data.Cache.IDNames;
using TrackResults.BES.Data.Cache.Static;
using TrackResults.BES.Data.Types;
using TrackResults.BES.DataAccess;
using TrackResults.BES.DataAccess.ManyPickLists;
using TrackResults.BES.DataAccess.Notes;
using TrackResults.BES.Rules;
using TrackResults.BES.Services;
using TrackResults.Common.Core.Collections;
using TrackResults.Common.Core.Extensions;
using TrackResults.Common.Core.System.Generic;
using TrackResults.Common.Core.Web.UI.WebControls;
using TrackResults.Common.DAL;
using TrackResults.Common.Exceptions;
using TrackResults.Common.Web.Globalization;
using TrackResults.Web.Services;
using TrackResults.Web.Customers.Tours.UserControls;
using TrackResults.Web.UserControls;
using TrackResults.Web.WebControls;
using TrackResults.Web.UI;
using System.Web.UI.HtmlControls;


namespace TrackResults.Web.Customers.Tours.UserControls
{
    public partial class ToursDetailsMarketing : PageBaseUserControl
    {
        protected global::TrackResults.Web.UserControls.ManyPickLists giftIDsManyPickLists;

        public bool ShowAddNoteOnly
		{
			get;
			set;
		}

        private static DynamicLoadComboBox dropDownHotels;


        public void ChangeMode(DBModes.Mode mode)
		{
			formView.ChangeMode(DBModes.GetFormViewMode(mode));
		}

		public void BindData(Tour tour, DBModes.Mode mode)
		{
			formView.ChangeMode(DBModes.GetFormViewMode(mode));

			formView.DataSource = new List<Tour>(1) { tour };
			formView.DataBind();
		}

        protected override void OnLoad(EventArgs e)
        {
            base.OnLoad(e);
            if(!Page.IsPostBack)
            {
                if (FeaturesCache.HasFeature("Validation.RequireLocationForVenue"))
                {
                    DynamicLoadComboBox dropDownVenues = (DynamicLoadComboBox)formView.FindControl("dropDownVenues");
                    if (dropDownVenues != null)
                    { 
                        dropDownVenues.Enabled = false;
                    }
                }

                if (FeaturesCache.HasFeature("Validation.RequireLocationForHotel"))
                {
                    dropDownHotels = (DynamicLoadComboBox)formView.FindControl("dropDownHotels");
                    if (dropDownHotels != null)
                    {
                        dropDownHotels.Enabled = false;
                    }
                }

                if (FeaturesCache.HasFeature("Validation.RequireLocationForGift"))
                {
                    ManyPickLists giftIDsManyPickLists = (ManyPickLists)formView.FindControl("giftIDsManyPickLists");
                    if (giftIDsManyPickLists != null)
                    {
                        giftIDsManyPickLists.Enabled = false;
                    }
                }
                giftIDsManyPickLists = (ManyPickLists)formView.FindControl("giftIDsManyPickLists");

            }
        }

		public void InBindData(Tour tour)
		{
			tour.MarketingTeamID = formView.GetRadComboBoxSelectedValueAsInt("dropDownMarketingTeams", "Tour.MarketingTeamID");
			tour.MarketingAgentID = formView.GetRadComboBoxSelectedValueAsInt("dropDownMarketingAgents", "Tour.MarketingAgentID");
			tour.MarketingCloserID = formView.GetRadComboBoxSelectedValueAsInt("dropDownMarketingClosers", "Tour.MarketingCloserID");
			tour.ConfirmerID = formView.GetRadComboBoxSelectedValueAsInt("dropDownConfirmers", "Tour.ConfirmerID");
			tour.ResetterID = formView.GetRadComboBoxSelectedValueAsInt("dropDownResetters", "Tour.ResetterID");
			tour.VenueID = formView.GetRadComboBoxSelectedValueAsInt("dropDownVenues", "Tour.VenueID");
			tour.CampaignID = formView.GetRadComboBoxSelectedValueAsInt("dropDownCampaigns", "Tour.CampaignID");
			tour.ChannelID = formView.GetRadComboBoxSelectedValueAsInt("dropDownChannels", "Tour.ChannelID");
			tour.HotelID = formView.GetRadComboBoxSelectedValueAsInt("dropDownHotels", "Tour.HotelID");

			ManyPickLists giftIDsManyPickLists = (ManyPickLists)formView.FindControl("giftIDsManyPickLists");
			tour.GiftIDs = giftIDsManyPickLists.GetIDs();
			tour.GiftIDsUpdated = giftIDsManyPickLists.GetUpdated(tour.GiftIDs);

			tour.MarketingPickList1ItemID = formView.GetRadComboBoxSelectedValueAsInt("dropDownMarketingPickList1Items", "Tour.MarketingPickList1ItemID");
			tour.MarketingPickList2ItemID = formView.GetRadComboBoxSelectedValueAsInt("dropDownMarketingPickList2Items", "Tour.MarketingPickList2ItemID");
			tour.MarketingText1 = formView.GetTextBoxText("textBoxMarketingText1", "Tour.MarketingText1");
			tour.MarketingDecimal1 = formView.GetTextBoxTextAsDecimal("textBoxMarketingDecimal1", "Tour.MarketingDecimal1");
			tour.MarketingBool1 = formView.GetDropDownListSelectedValueAsBool("dropDownMarketingBool1", "Tour.MarketingBool1");
			tour.MarketingDate1 = formView.GetRadDatePickerSelectedDate("marketingDate1RadDatePicker", "Tour.MarketingDate1");
			tour.MarketingTime1 = formView.GetRadTimePickerSelectedDate("marketingTime1TimePicker", "Tour.MarketingTime1");
			tour.DepositAmount = formView.GetTextBoxTextAsDecimal("textBoxDepositAmount", "Tour.DepositAmount");
			tour.DepositRefundable = formView.GetDropDownListSelectedValueAsBool("dropDownDepositRefundable", "Tour.DepositRefundable");

			NotesDetails marketingNotesDetails = (NotesDetails)formView.FindControl("marketingNotesDetails");
			tour.MarketingNotes = marketingNotesDetails.GetNotes();
			tour.MarketingNotesUpdated = !tour.MarketingNotes.IsNullOrEmpty();
		}

		protected void formView_DataBound(object sender, EventArgs e)
		{
			Tour tour = (Tour)formView.DataItem;

			switch (formView.CurrentMode)
			{
				case FormViewMode.Edit:
					{
						formView.AddRadComboBoxDefaultItem("dropDownMarketingTeams", tour.MarketingTeamID, TeamIDNamesCache.Instance, "Tour.MarketingTeamID");
						formView.AddRadComboBoxDefaultItem("dropDownMarketingAgents", tour.MarketingAgentID, UserIDTeamNameUserNameCache.Instance, "Tour.MarketingAgentID");
						formView.AddRadComboBoxDefaultItem("dropDownMarketingClosers", tour.MarketingCloserID, UserIDTeamNameUserNameCache.Instance, "Tour.MarketingCloserID");
						formView.AddRadComboBoxDefaultItem("dropDownConfirmers", tour.ConfirmerID, UserIDTeamNameUserNameCache.Instance, "Tour.ConfirmerID");
						formView.AddRadComboBoxDefaultItem("dropDownResetters", tour.ResetterID, UserIDTeamNameUserNameCache.Instance, "Tour.ResetterID");
						formView.AddRadComboBoxDefaultItem("dropDownVenues", tour.VenueID, VenueIDNamesCache.Instance, "Tour.VenueID");
						formView.AddRadComboBoxDefaultItem("dropDownCampaigns", tour.CampaignID, CampaignIDNamesCache.Instance, "Tour.CampaignID");
						formView.AddRadComboBoxDefaultItem("dropDownChannels", tour.ChannelID, ChannelIDNamesCache.Instance, "Tour.ChannelID");
						formView.AddRadComboBoxDefaultItem("dropDownHotels", tour.HotelID, HotelIDNamesCache.Instance, "Tour.HotelID");

						ManyPickLists giftIDsManyPickLists = (ManyPickLists)formView.FindControl("giftIDsManyPickLists");
						giftIDsManyPickLists.BindData(tour.TourID, ToursGiftsManyDataAccess.Instance, GiftIDNamesCache.Instance);

						formView.AddRadComboBoxDefaultItem("dropDownMarketingPickList1Items", tour.MarketingPickList1ItemID, MarketingPickList1ItemIDNamesCache.Instance, "Tour.MarketingPickList1ItemID");
						formView.AddRadComboBoxDefaultItem("dropDownMarketingPickList2Items", tour.MarketingPickList2ItemID, MarketingPickList2ItemIDNamesCache.Instance, "Tour.MarketingPickList2ItemID");

						NotesDetails marketingNotesDetails = (NotesDetails)formView.FindControl("marketingNotesDetails");
						marketingNotesDetails.BindData(tour.TourID, tour.MarketingNotesCount, NoteType.ToursMarketing, "Tour.MarketingNotes");
						marketingNotesDetails.ShowAddNoteOnly = ShowAddNoteOnly;
					}
					break;

				case FormViewMode.Insert:
					{
						NotesDetails marketingNotesDetails = (NotesDetails)formView.FindControl("marketingNotesDetails");
						marketingNotesDetails.ShowAddNoteOnly = ShowAddNoteOnly;
					}
					
					break;

				case FormViewMode.ReadOnly:
					{
						ManyPickLists giftIDsManyPickLists = (ManyPickLists)formView.FindControl("giftIDsManyPickLists");
						giftIDsManyPickLists.BindData(tour.TourID, ToursGiftsManyDataAccess.Instance, GiftIDNamesCache.Instance);

						NotesDetails marketingNotesDetails = (NotesDetails)formView.FindControl("marketingNotesDetails");
						marketingNotesDetails.BindData(tour.TourID, tour.MarketingNotesCount, NoteType.ToursMarketing, "Tour.MarketingNotes");
						marketingNotesDetails.ShowAddNoteOnly = ShowAddNoteOnly;
					}

					break;

			}

			PageControlDisplayService.SetDisplay(this.GetType(), formView.ID, DBModes.GetMode(formView.CurrentMode), formView);
		}

		protected void RequireConfirmerForConfirmed_ServerValidate(object source, ServerValidateEventArgs eventArgs)
		{
			eventArgs.IsValid = true;

			DynamicLoadComboBox dropDownConfirmers = (DynamicLoadComboBox)formView.FindControl("dropDownConfirmers");
			if (string.IsNullOrEmpty(dropDownConfirmers.SelectedValue))
			{
				int? tourStatusTypeID = ((CustomersDetails)Page).GetCurrentTourStatusTypeID();
				if (tourStatusTypeID != null && (TourStatusType)tourStatusTypeID == TourStatusType.Confirmed)
				{
					eventArgs.IsValid = false;
				}
			}
		}

        protected void ValidateGiftIDs(object source, ServerValidateEventArgs args)
        {
            ManyPickLists giftIDsControl = (ManyPickLists)formView.FindControl("giftIDsManyPickLists");

            if (giftIDsControl != null)
            {
                // Get selected IDs
                var selectedIDs = giftIDsControl.GetIDs();
                // Log the IDs to verify they are correct
                System.Diagnostics.Debug.WriteLine("Selected IDs: " + string.Join(",", selectedIDs));

                // Find the hidden field
                var hiddenField = (HtmlInputHidden)FindControl("hiddenSelectedIDs");

                if (hiddenField != null)
                {
                    // Set the value of the hidden field
                    hiddenField.Value = string.Join(",", selectedIDs);
                }

                // Validate based on the number of selected IDs
                args.IsValid = selectedIDs.Count > 0;
            }
            else
            {
                args.IsValid = false;
            }
        }

        protected override void OnPreRender(EventArgs e)
        {
            base.OnPreRender(e);

            var hiddenField = (HtmlInputHidden)FindControl("hiddenSelectedIDs");

            if (hiddenField != null)
            {
                if (giftIDsManyPickLists == null)
                {
                    giftIDsManyPickLists = (ManyPickLists)formView.FindControl("giftIDsManyPickLists");
                }

                if (giftIDsManyPickLists != null)
                {
                    // Get selected IDs
                    var selectedIDs = giftIDsManyPickLists.GetIDs();
                    // Log the IDs to verify they are correct
                    System.Diagnostics.Debug.WriteLine("Selected IDs: " + string.Join(",", selectedIDs));
                    // Set the value of the hidden field
                    hiddenField.Value = string.Join(",", selectedIDs);
                }
            }
        }


        public static Control FindControlRecursive(Control root, string id)
        {
            if (root.ID == id)
            {
                return root;
            }

            foreach (Control control in root.Controls)
            {
                Control foundControl = FindControlRecursive(control, id);
                if (foundControl != null)
                {
                    return foundControl;
                }
            }

            return null;
        }

        protected void Page_Load(object sender, EventArgs e)
        {
            // Always execute this code to ensure the hidden field is updated
            var hiddenField = (HtmlInputHidden)FindControl("hiddenSelectedIDs");

            if (hiddenField != null)
            {
                if (giftIDsManyPickLists == null)
                {
                    giftIDsManyPickLists = (ManyPickLists)formView.FindControl("giftIDsManyPickLists");
                }

                if (giftIDsManyPickLists != null)
                {
                    // Get selected IDs
                    var selectedIDs = giftIDsManyPickLists.GetIDs();
                    // Log the IDs to verify they are correct
                    System.Diagnostics.Debug.WriteLine("Selected IDs: " + string.Join(",", selectedIDs));
                    // Set the value of the hidden field
                    hiddenField.Value = string.Join(",", selectedIDs);
                }
            }
        }

        
    }
}