﻿
CREATE PROCEDURE [dbo].[InsertCustomDisplay_SetSalesAgentAsSalesDashboard]

AS
BEGIN

	DECLARE @GroupTypeKey nvarchar(64) SET @GroupTypeKey = 'SetSalesAgentAsSalesDashboard'
	
	DELETE FROM CustomPageControlDisplayRights WHERE (customPageControlDisplayRightGroupTypeKey = @GroupTypeKey)

	DECLARE @Insert int SET @Insert = 1
	DECLARE @Select int SET @Select = 2
	DECLARE @Update int SET @Update = 3

	DECLARE @Add int SET @Add = 1
	DECLARE @Remove int SET @Remove = 2

	DECLARE @Visible int SET @Visible = 1
	DECLARE @Enabled int SET @Enabled = 2
	DECLARE @Text int SET @Text = 3
	DECLARE @Delegate int SET @Delegate = 4
	DECLARE @Authorized int SET @Authorized = 5
	DECLARE @Administrated int SET @Administrated = 6

	DECLARE @PagePathID [varchar] (256)


	SET @PagePathID = 'ASP.customers_customersdetails_aspx'

	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Insert,'Sales Agent','form1',@Authorized,0,NULL,NULL


	SET @PagePathID = 'ASP.reports_default_aspx'

	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Sales Agent','form1',@Authorized,0,NULL,NULL
	
	SET @PagePathID = 'ASP.reports_callcenterdetailreport_aspx'

	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Sales Agent','form1',@Authorized,0,NULL,NULL

	SET @PagePathID = 'ASP.reports_cancellationrequestdetailreport_aspx'

	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Sales Agent','form1',@Authorized,0,NULL,NULL

	SET @PagePathID = 'ASP.reports_cancellationrequestefficiencyreport_aspx'

	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Sales Agent','form1',@Authorized,0,NULL,NULL

	SET @PagePathID = 'ASP.reports_comparativemarketingreport_aspx'

	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Sales Agent','form1',@Authorized,0,NULL,NULL

	SET @PagePathID = 'ASP.reports_comparativesalesreport_aspx'

	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Sales Agent','form1',@Authorized,0,NULL,NULL

	SET @PagePathID = 'ASP.reports_createcancellationrequestreport_aspx'

	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Sales Agent','form1',@Authorized,0,NULL,NULL

	SET @PagePathID = 'ASP.reports_createpurchasesreport_aspx'

	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Sales Agent','form1',@Authorized,0,NULL,NULL

	SET @PagePathID = 'ASP.reports_efficiencymarketingreport_aspx'

	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Sales Agent','form1',@Authorized,0,NULL,NULL

	SET @PagePathID = 'ASP.reports_efficiencysalesreport_aspx'

	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Sales Agent','form1',@Authorized,0,NULL,NULL

	SET @PagePathID = 'ASP.reports_lostvolumereport_aspx'

	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Sales Agent','form1',@Authorized,0,NULL,NULL

	SET @PagePathID = 'ASP.reports_manifestreport_aspx'

	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Sales Agent','form1',@Authorized,0,NULL,NULL

	SET @PagePathID = 'ASP.reports_penderefficiencyreport_aspx'

	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Sales Agent','form1',@Authorized,0,NULL,NULL

	SET @PagePathID = 'ASP.reports_revenuereport_aspx'

	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Sales Agent','form1',@Authorized,0,NULL,NULL

	SET @PagePathID = 'ASP.reports_salesreport_aspx'

	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Sales Agent','form1',@Authorized,0,NULL,NULL

	SET @PagePathID = 'ASP.reports_salestourreport_aspx'

	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Sales Agent','form1',@Authorized,0,NULL,NULL

	SET @PagePathID = 'ASP.reports_tourreport_aspx'

	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Sales Agent','form1',@Authorized,0,NULL,NULL
	
	SET @PagePathID = 'ASP.reports_tourstatuseffeciencyreport_aspx'

	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Sales Agent','form1',@Authorized,0,NULL,NULL
	
	SET @PagePathID = 'ASP.reports_updatecancellationrequestreport_aspx'

	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Sales Agent','form1',@Authorized,0,NULL,NULL

	SET @PagePathID = 'ASP.reports_updatemarketinginformationreportt_aspx'

	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Sales Agent','form1',@Authorized,0,NULL,NULL

	SET @PagePathID = 'ASP.reports_updatesalesinformationreport_aspx'

	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Sales Agent','form1',@Authorized,0,NULL,NULL

	SET @PagePathID = 'ASP.reports_updatetoursreport_aspx'

	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Sales Agent','form1',@Authorized,0,NULL,NULL
	
	SET @PagePathID = 'ASP.reports_updatetoursstatusreport_aspx'

	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Sales Agent','form1',@Authorized,0,NULL,NULL
	
	
	SET @PagePathID = 'ASP.customers_tours_usercontrols_sales_ascx_itemlistview'
	
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Sales Agent','trContractNumber',@Visible,0,NULL,NULL
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Sales Agent','trProduct',@Visible,0,NULL,NULL
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Sales Agent','labelSalesAmount',@Text,NULL,NULL,'TrackResults.BES.Security.ToursSaleRules:GetTextSaleYesNo'
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Sales Agent','trFees1Amount',@Visible,0,NULL,NULL
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Sales Agent','trFees2Amount',@Visible,0,NULL,NULL
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Sales Agent','trDownPaymentAmount',@Visible,0,NULL,NULL
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Sales Agent','trToursSalesPickList1Items',@Visible,0,NULL,NULL
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Sales Agent','trToursSalesPickList2Items',@Visible,0,NULL,NULL
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Sales Agent','trDate1',@Visible,0,NULL,NULL
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Sales Agent','trSalesType',@Visible,0,NULL,NULL
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Sales Agent','trSaleStatusType',@Visible,0,NULL,NULL
	EXECUTE InsertCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'Sales Agent','trSalesType',@Visible,0,NULL,NULL


END