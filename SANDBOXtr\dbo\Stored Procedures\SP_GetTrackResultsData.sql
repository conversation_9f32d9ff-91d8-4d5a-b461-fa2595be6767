﻿-- =============================================
-- Author:      <Author, , Name>
-- Create Date: <Create Date, , >
-- Description: <Description, , >
-- =============================================
CREATE PROCEDURE [dbo].[SP_GetTrackResultsData]
(
    @tourId INT
)
AS
BEGIN
    SELECT 
	t.[tourID] as [tourID],
	t.[externalTourID] as [externalTourID],
	t.[externalLeadID] as [externalLeadID],
	t.[acquisitionDate] as [acquisitionDate],
	t.[acquisitionTime] as [acquisitionTime],

	ls.[leadSourceID] as [LeadSource.leadSourceID],
	ls.[leadSourceName] as [LeadSource.leadSourceName],
	ls.[leadSourceCode] as [LeadSource.leadSourceCode],

	ltp.[leadTypeID] as [LeadType.leadTypeID],
	ltp.[leadTypeName] as [LeadType.leadTypeName],
	ltp.[leadTypeCode] as [LeadType.leadTypeCode],

	lst.[leadStatusTypeID] as [LeadStatusType.leadStatusTypeID],
	lst.[leadStatusTypeName] as [LeadStatusType.leadStatusTypeName],
	lst.[leadStatusTypeCode] as [LeadStatusType.leadStatusTypeCode],

	ld.[leadDispositionID] as [LeadDisposition.leadDispositionID],
	ld.[leadDispositionName] as [LeadDisposition.leadDispositionName],
	ld.[leadDispositionCode] as [LeadDisposition.leadDispositionCode],

	t.[checkInDate] as [checkInDate],
	t.[checkInTime] as [checkInTime],
	t.[checkOutDate] as [checkOutDate],
	t.[checkOutTime] as [checkOutTime],

	lpl1i.[leadPickList1ItemID] as [LeadPickList1Item.leadPickList1ItemID],
	lpl1i.[leadPickList1ItemName] as [LeadPickList1Item.leadPickList1ItemName],
	lpl1i.[leadPickList1ItemCode] as [LeadPickList1Item.leadPickList1ItemCode],

	t.[leadText1] as [leadText1],
	t.[leadDecimal1] as [leadDecimal1],
	t.[leadBool1] as [leadBool1],

	lpl2i.[leadPickList2ItemID] as [LeadPickList2Item.leadPickList2ItemID],
	lpl2i.[leadPickList2ItemName] as [LeadPickList2Item.leadPickList2ItemName],
	lpl2i.[leadPickList2ItemCode] as [LeadPickList2Item.leadPickList2ItemCode],

	t.[leadText2] as [leadText2],
	t.[leadDecimal2] as [leadDecimal2],
	t.[leadBool2] as [leadBool2],

	'NotesToursLeads' = (
	SELECT
	ntl.[noteID] as [noteID],
	ntl.[referenceID] as [referenceID],
	ntl.[noteText] as [noteText],

	u.[userID] as [User.userID],
	u.[firstName] as [User.firstName],
	u.[lastName] as [User.lastName],

	ntl.[insertTimeStamp] as [insertTimeStamp],
	ntl.[updateTimeStamp] as [updateTimeStamp]

	FROM NotesToursLeads ntl 
	Left OUTER JOIN Users u ON u.userID = ntl.userID  
	WHERE ntl.[referenceID] = t.tourID
	FOR JSON PATH),

	ctst.[contactStatusTypeID] as [ContactStatusType.contactStatusTypeID],
	ctst.[contactStatusTypeName] as [ContactStatusType.contactStatusTypeName],
	ctst.[contactStatusTypeCode] as [ContactStatusType.contactStatusTypeCode],

	ctd.[contactDispositionID] as [ContactDisposition.contactDispositionID],
	ctd.[contactDispositionName] as [ContactDisposition.contactDispositionName],
	ctd.[contactDispositionCode] as [ContactDisposition.contactDispositionCode],

	t.[callBackDate] as [callBackDate],
	t.[callBackTime] as [callBackTime],

	cbpl1i.[callBackPickList1ItemID] as [CallBackPickList1Item.callBackPickList1ItemID],
	cbpl1i.[callBackPickList1ItemName] as [CallBackPickList1Item.callBackPickList1ItemName],
	cbpl1i.[callBackPickList1ItemCode] as [CallBackPickList1Item.callBackPickList1ItemCode],

	t.[callBackText1] as [callBackText1],
	t.[callBackDecimal1] as [callBackDecimal1],
	t.[callBackBool1] as [callBackBool1],

	'NotesToursCalls' = (
	SELECT
	ntc.[noteID] as [noteID],
	ntc.[referenceID] as [referenceID],
	ntc.[noteText] as [noteText],

	u.[userID] as [User.userID],
	u.[firstName] as [User.firstName],
	u.[lastName] as [User.lastName],

	ntc.[insertTimeStamp] as [insertTimeStamp],
	ntc.[updateTimeStamp] as [updateTimeStamp]

	FROM NotesToursCalls ntc
	Left OUTER JOIN Users u ON u.userID = ntc.userID  
	WHERE ntc.[referenceID] = t.tourID
	FOR JSON PATH),

	ts.[tourSourceID] as [TourSource.tourSourceID],
	ts.[tourSourceName] as [TourSource.tourSourceName],
	ts.[tourSourceCode] as [TourSource.tourSourceCode],

	ttp.[tourTypeID] as [TourType.tourTypeID],
	ttp.[tourTypeName] as [TourType.tourTypeName],
	ttp.[tourTypeCode] as [TourType.tourTypeCode],

	tstp.[tourStatusTypeID] as [TourStatusType.tourStatusTypeID],
	tstp.[tourStatusTypeName] as [TourStatusType.tourStatusTypeName],
	tstp.[tourStatusTypeCode] as [TourStatusType.tourStatusTypeCode],

	tctp.[tourConcernTypeID] as [TourConcernType.tourConcernTypeID],
	tctp.[tourConcernTypeName] as [TourConcernType.tourConcernTypeName],
	tctp.[tourConcernTypeCode] as [TourConcernType.tourConcernTypeCode],

	ttpl1i.[tourTypePickList1ItemID] as [TourTypePickList1Item.tourTypePickList1ItemID],
	ttpl1i.[tourTypePickList1ItemName] as [TourTypePickList1Item.tourTypePickList1ItemName],
	ttpl1i.[tourTypePickList1ItemCode] as [TourTypePickList1Item.tourTypePickList1ItemCode],

	l.[locationID] as [Location.locationID],
	l.[name] as [Location.locationName],
	l.[code] as [Location.locationCode],

	r.[regionID] as [Region.regionID],
	r.[regionName] as [Region.regionName],
	r.[regionCode] as [Region.regionCode],

	t.[tourDate] as [tourDate],
	t.[tourTime] as [tourTime],
	t.[entryDateTime] as [entryDateTime],
	t.[tourText1] as [tourText1],
	t.[tourDecimal1] as [tourDecimal1],
	t.[tourBool1] as [tourBool1],

	'ToursManyPickList1Items' = (
	SELECT
	tmpl1i.[ToursManyPickList1ItemID] as [toursManyPickList1ItemID],
	tmpl1i.[ToursManyPickList1ItemName] as [toursManyPickList1ItemName],
	tmpl1i.[ToursManyPickList1ItemCode] as [toursManyPickList1ItemCode]

	FROM ToursManyPickList1ItemsMap tmpl1im
	JOIN ToursManyPickList1Items tmpl1i ON tmpl1i.toursManyPickList1ItemID = tmpl1im.toursManyPickList1ItemID
	WHERE tmpl1im.tourID = t.tourID
	FOR JSON PATH),

	t.[rescheduledCount] as [rescheduledCount],
	t.[rescheduledTourID] as [rescheduledTourID],
	t.[parentRescheduledTourID] as [parentRescheduledTourID],

	mktm.[teamID] as [MarketingTeam.teamID],
	mktm.[teamTypeID] as [MarketingTeam.teamTypeID],
	mktm.[name] as [MarketingTeam.teamName],
	mktm.[code] as [MarketingTeam.teamCode],

	mu1.[userID] as [MarketingAgent.userID],
	mu1.[firstName] as [MarketingAgent.firstName],
	mu1.[lastName] as [MarketingAgent.lastName],

	mu2.[userID] as [MarketingCloser.userID],
	mu2.[firstName] as [MarketingCloser.firstName],
	mu2.[lastName] as [MarketingCloser.lastName],

	mu3.[userID] as [Confirmer.userID],
	mu3.[firstName] as [Confirmer.firstName],
	mu3.[lastName] as [Confirmer.lastName],

	mu4.[userID] as [Resetter.userID],
	mu4.[firstName] as [Resetter.firstName],
	mu4.[lastName] as [Resetter.lastName],

	v.[venueID] as [Venue.venueID],
	v.[name] as [Venue.venueName],
	v.[code] as [Venue.venueCode],

	cmp.[campaignID] as [Campaign.campaignID],
	cmp.[campaignName] as [Campaign.campaignName],
	cmp.[campaignCode] as [Campaign.campaignCode],

	ch.[channelID] as [Channel.channelID],
	ch.[channelName] as [Channel.channelName],
	ch.[channelCode] as [Channel.channelCode],

	h.[hotelID] as [Hotel.hotelID],
	h.[hotelName] as [Hotel.hotelName],
	h.[hotelCode] as [Hotel.hotelCode],

	'Gifts' = (
	SELECT
	g.[giftID] as [giftID],
	g.[name] as [giftName],
	g.[code] as [giftCode],
	g.[value] as [giftValue]

	FROM ToursGiftsMap tgm
	JOIN Gifts g ON g.giftID = tgm.giftID
	WHERE tgm.tourID = t.tourID
	FOR JSON PATH),

	mpl1i.[marketingPickList1ItemID] as [MarketingPickList1Item.marketingPickList1ItemID],
	mpl1i.[marketingPickList1ItemName] as [MarketingPickList1Item.marketingPickList1ItemName],
	mpl1i.[marketingPickList1ItemCode] as [MarketingPickList1Item.marketingPickList1ItemCode],

	mpl2i.[marketingPickList2ItemID] as [MarketingPickList2Item.marketingPickList2ItemID],
	mpl2i.[marketingPickList2ItemName] as [MarketingPickList2Item.marketingPickList2ItemName],
	mpl2i.[marketingPickList2ItemCode] as [MarketingPickList2Item.marketingPickList2ItemCode],

	t.[marketingText1] as [marketingText1],
	t.[marketingDecimal1] as [marketingDecimal1],
	t.[marketingBool1] as [marketingBool1],
	t.[marketingDate1] as [marketingDate1],
	t.[marketingTime1] as [marketingTime1],
	t.[depositAmount] as [depositAmount],
	t.[depositRefundable] as [depositRefundable],

	'NotesToursMarketing' = (
	SELECT
	ntm.[noteID] as [noteID],
	ntm.[referenceID] as [referenceID],
	ntm.[noteText] as [noteText],

	u.[userID] as [User.userID],
	u.[firstName] as [User.firstName],
	u.[lastName] as [User.lastName],

	ntm.[insertTimeStamp] as [insertTimeStamp],
	ntm.[updateTimeStamp] as [updateTimeStamp]

	FROM NotesToursMarketing ntm 
	Left OUTER JOIN Users u ON u.userID = ntm.userID  
	WHERE ntm.[referenceID] = t.tourID
	FOR JSON PATH),

	sltm.[teamID] as [SalesTeam.teamID],
	sltm.[teamTypeID] as [SalesTeam.teamTypeID],
	sltm.[name] as [SalesTeam.teamName],
	sltm.[code] as [SalesTeam.teamCode],

	su1.[userID] as [Podium.userID],
	su1.[firstName] as [Podium.firstName],
	su1.[lastName] as [Podium.lastName],

	su2.[userID] as [SalesRep.userID],
	su2.[firstName] as [SalesRep.firstName],
	su2.[lastName] as [SalesRep.lastName],

	su3.[userID] as [SalesCloser1.userID],
	su3.[firstName] as [SalesCloser1.firstName],
	su3.[lastName] as [SalesCloser1.lastName],

	su4.[userID] as [SalesCloser2.userID],
	su4.[firstName] as [SalesCloser2.firstName],
	su4.[lastName] as [SalesCloser2.lastName],

	su5.[userID] as [ExitRep.userID],
	su5.[firstName] as [ExitRep.firstName],
	su5.[lastName] as [ExitRep.lastName],

	su6.[userID] as [VerificationOfficer.userID],
	su6.[firstName] as [VerificationOfficer.firstName],
	su6.[lastName] as [VerificationOfficer.lastName],

	spl1i.[salesPickList1ItemID] as [SalesPickList1Item.salesPickList1ItemID],
	spl1i.[salesPickList1ItemName] as [SalesPickList1Item.salesPickList1ItemName],
	spl1i.[salesPickList1ItemCode] as [SalesPickList1Item.salesPickList1ItemCode],

	t.[salesText1] as [salesText1],
	t.[salesDecimal1] as [salesDecimal1],
	t.[salesBool1] as [salesBool1],
	t.[salesDate1] as [salesDate1],
	t.[salesTime1] as [salesTime1],

	'NotesToursSales' = (
	SELECT
	nts.[noteID] as [noteID],
	nts.[referenceID] as [referenceID],
	nts.[noteText] as [noteText],

	u.[userID] as [User.userID],
	u.[firstName] as [User.firstName],
	u.[lastName] as [User.lastName],

	nts.[insertTimeStamp] as [insertTimeStamp],
	nts.[updateTimeStamp] as [updateTimeStamp]

	FROM NotesToursSales nts
	Left OUTER JOIN Users u ON u.userID = nts.userID  
	WHERE nts.[referenceID] = t.tourID
	FOR JSON PATH),

	sltmex.[teamID] as [ExitSalesTeam.teamID],
	sltmex.[teamTypeID] as [ExitSalesTeam.teamTypeID],
	sltmex.[name] as [ExitSalesTeam.teamName],
	sltmex.[code] as [ExitSalesTeam.teamCode],

	seu1.[userID] as [SalesRepExit1.userID],
	seu1.[firstName] as [SalesRepExit1.firstName],
	seu1.[lastName] as [SalesRepExit1.lastName],

	seu2.[userID] as [SalesRepExit2.userID],
	seu2.[firstName] as [SalesRepExit2.firstName],
	seu2.[lastName] as [SalesRepExit2.lastName],

	seu3.[userID] as [SalesRepExit3.userID],
	seu3.[firstName] as [SalesRepExit3.firstName],
	seu3.[lastName] as [SalesRepExit3.lastName],

	'NotesToursExit' = (
	SELECT
	nte.[noteID] as [noteID],
	nte.[referenceID] as [referenceID],
	nte.[noteText] as [noteText],

	u.[userID] as [User.userID],
	u.[firstName] as [User.firstName],
	u.[lastName] as [User.lastName],

	nte.[insertTimeStamp] as [insertTimeStamp],
	nte.[updateTimeStamp] as [updateTimeStamp]

	FROM NotesToursExit nte 
	Left OUTER JOIN Users u ON u.userID = nte.userID  
	WHERE nte.[referenceID] = t.tourID
	FOR JSON PATH),

	'NotesToursForm1' = (
	SELECT
	ntf1.[noteID] as [noteID],
	ntf1.[referenceID] as [referenceID],
	ntf1.[noteText] as [noteText],

	u.[userID] as [User.userID],
	u.[firstName] as [User.firstName],
	u.[lastName] as [User.lastName],

	ntf1.[insertTimeStamp] as [insertTimeStamp],
	ntf1.[updateTimeStamp] as [updateTimeStamp]

	FROM NotesToursForm1 ntf1
	Left OUTER JOIN Users u ON u.userID = ntf1.userID  
	WHERE ntf1.[referenceID] = t.tourID
	FOR JSON PATH),

	'NotesToursForm2' = (
	SELECT
	ntf2.[noteID] as [noteID],
	ntf2.[referenceID] as [referenceID],
	ntf2.[noteText] as [noteText],

	u.[userID] as [User.userID],
	u.[firstName] as [User.firstName],
	u.[lastName] as [User.lastName],

	ntf2.[insertTimeStamp] as [insertTimeStamp],
	ntf2.[updateTimeStamp] as [updateTimeStamp]

	FROM NotesToursForm2 ntf2 
	Left OUTER JOIN Users u ON u.userID = ntf2.userID  
	WHERE ntf2.[referenceID] = t.tourID
	FOR JSON PATH),

	'NotesToursForm3' = (
	SELECT
	ntf3.[noteID] as [noteID],
	ntf3.[referenceID] as [referenceID],
	ntf3.[noteText] as [noteText],

	u.[userID] as [User.userID],
	u.[firstName] as [User.firstName],
	u.[lastName] as [User.lastName],

	ntf3.[insertTimeStamp] as [insertTimeStamp],
	ntf3.[updateTimeStamp] as [updateTimeStamp]

	FROM NotesToursForm3 ntf3
	Left OUTER JOIN Users u ON u.userID = ntf3.userID  
	WHERE ntf3.[referenceID] = t.tourID
	FOR JSON PATH),

	'NotesToursForm4' = (
	SELECT
	ntf4.[noteID] as [noteID],
	ntf4.[referenceID] as [referenceID],
	ntf4.[noteText] as [noteText],

	u.[userID] as [User.userID],
	u.[firstName] as [User.firstName],
	u.[lastName] as [User.lastName],

	ntf4.[insertTimeStamp] as [insertTimeStamp],
	ntf4.[updateTimeStamp] as [updateTimeStamp]

	FROM NotesToursForm4 ntf4
	Left OUTER JOIN Users u ON u.userID = ntf4.userID  
	WHERE ntf4.[referenceID] = t.tourID
	FOR JSON PATH),

	'NotesToursForm5' = (
	SELECT
	ntf5.[noteID] as [noteID],
	ntf5.[referenceID] as [referenceID],
	ntf5.[noteText] as [noteText],

	u.[userID] as [User.userID],
	u.[firstName] as [User.firstName],
	u.[lastName] as [User.lastName],

	ntf5.[insertTimeStamp] as [insertTimeStamp],
	ntf5.[updateTimeStamp] as [updateTimeStamp]

	FROM NotesToursForm5 ntf5
	Left OUTER JOIN Users u ON u.userID = ntf5.userID  
	WHERE ntf5.[referenceID] = t.tourID
	FOR JSON PATH),

	'NotesToursForm6' = (
	SELECT
	ntf6.[noteID] as [noteID],
	ntf6.[referenceID] as [referenceID],
	ntf6.[noteText] as [noteText],

	u.[userID] as [User.userID],
	u.[firstName] as [User.firstName],
	u.[lastName] as [User.lastName],

	ntf6.[insertTimeStamp] as [insertTimeStamp],
	ntf6.[updateTimeStamp] as [updateTimeStamp]

	FROM NotesToursForm6 ntf6
	Left OUTER JOIN Users u ON u.userID = ntf6.userID  
	WHERE ntf6.[referenceID] = t.tourID
	FOR JSON PATH),

	'NotesToursForm7' = (
	SELECT
	ntf7.[noteID] as [noteID],
	ntf7.[referenceID] as [referenceID],
	ntf7.[noteText] as [noteText],

	u.[userID] as [User.userID],
	u.[firstName] as [User.firstName],
	u.[lastName] as [User.lastName],

	ntf7.[insertTimeStamp] as [insertTimeStamp],
	ntf7.[updateTimeStamp] as [updateTimeStamp]

	FROM NotesToursForm7 ntf7
	Left OUTER JOIN Users u ON u.userID = ntf7.userID  
	WHERE ntf7.[referenceID] = t.tourID
	FOR JSON PATH),

	'NotesToursForm8' = (
	SELECT
	ntf8.[noteID] as [noteID],
	ntf8.[referenceID] as [referenceID],
	ntf8.[noteText] as [noteText],

	u.[userID] as [User.userID],
	u.[firstName] as [User.firstName],
	u.[lastName] as [User.lastName],

	ntf8.[insertTimeStamp] as [insertTimeStamp],
	ntf8.[updateTimeStamp] as [updateTimeStamp]

	FROM NotesToursForm8 ntf8
	Left OUTER JOIN Users u ON u.userID = ntf8.userID  
	WHERE ntf8.[referenceID] = t.tourID
	FOR JSON PATH),

	'NotesToursForm9' = (
	SELECT
	ntf9.[noteID] as [noteID],
	ntf9.[referenceID] as [referenceID],
	ntf9.[noteText] as [noteText],

	u.[userID] as [User.userID],
	u.[firstName] as [User.firstName],
	u.[lastName] as [User.lastName],

	ntf9.[insertTimeStamp] as [insertTimeStamp],
	ntf9.[updateTimeStamp] as [updateTimeStamp]

	FROM NotesToursForm9 ntf9
	Left OUTER JOIN Users u ON u.userID = ntf9.userID  
	WHERE ntf9.[referenceID] = t.tourID
	FOR JSON PATH),

	'NotesToursForm10' = (
	SELECT
	ntf10.[noteID] as [noteID],
	ntf10.[referenceID] as [referenceID],
	ntf10.[noteText] as [noteText],

	u.[userID] as [User.userID],
	u.[firstName] as [User.firstName],
	u.[lastName] as [User.lastName],

	ntf10.[insertTimeStamp] as [insertTimeStamp],
	ntf10.[updateTimeStamp] as [updateTimeStamp]

	FROM NotesToursForm10 ntf10 
	Left OUTER JOIN Users u ON u.userID = ntf10.userID  
	WHERE ntf10.[referenceID] = t.tourID
	FOR JSON PATH),

	'NotesToursForm11' = (
	SELECT
	ntf11.[noteID] as [noteID],
	ntf11.[referenceID] as [referenceID],
	ntf11.[noteText] as [noteText],

	u.[userID] as [User.userID],
	u.[firstName] as [User.firstName],
	u.[lastName] as [User.lastName],

	ntf11.[insertTimeStamp] as [insertTimeStamp],
	ntf11.[updateTimeStamp] as [updateTimeStamp]

	FROM NotesToursForm11 ntf11
	Left OUTER JOIN Users u ON u.userID = ntf11.userID  
	WHERE ntf11.[referenceID] = t.tourID
	FOR JSON PATH),

	'NotesToursForm12' = (
	SELECT
	ntf12.[noteID] as [noteID],
	ntf12.[referenceID] as [referenceID],
	ntf12.[noteText] as [noteText],

	u.[userID] as [User.userID],
	u.[firstName] as [User.firstName],
	u.[lastName] as [User.lastName],

	ntf12.[insertTimeStamp] as [insertTimeStamp],
	ntf12.[updateTimeStamp] as [updateTimeStamp]

	FROM NotesToursForm12 ntf12
	Left OUTER JOIN Users u ON u.userID = ntf12.userID  
	WHERE ntf12.[referenceID] = t.tourID
	FOR JSON PATH),

	'NotesToursForm13' = (
	SELECT
	ntf13.[noteID] as [noteID],
	ntf13.[referenceID] as [referenceID],
	ntf13.[noteText] as [noteText],

	u.[userID] as [User.userID],
	u.[firstName] as [User.firstName],
	u.[lastName] as [User.lastName],

	ntf13.[insertTimeStamp] as [insertTimeStamp],
	ntf13.[updateTimeStamp] as [updateTimeStamp]

	FROM NotesToursForm13 ntf13 
	Left OUTER JOIN Users u ON u.userID = ntf13.userID  
	WHERE ntf13.[referenceID] = t.tourID
	FOR JSON PATH),

	'NotesToursForm14' = (
	SELECT
	ntf14.[noteID] as [noteID],
	ntf14.[referenceID] as [referenceID],
	ntf14.[noteText] as [noteText],

	u.[userID] as [User.userID],
	u.[firstName] as [User.firstName],
	u.[lastName] as [User.lastName],

	ntf14.[insertTimeStamp] as [insertTimeStamp],
	ntf14.[updateTimeStamp] as [updateTimeStamp]

	FROM NotesToursForm14 ntf14
	Left OUTER JOIN Users u ON u.userID = ntf14.userID  
	WHERE ntf14.[referenceID] = t.tourID
	FOR JSON PATH),

	'NotesToursForm15' = (
	SELECT
	ntf15.[noteID] as [noteID],
	ntf15.[referenceID] as [referenceID],
	ntf15.[noteText] as [noteText],

	u.[userID] as [User.userID],
	u.[firstName] as [User.firstName],
	u.[lastName] as [User.lastName],
	ntf15.[insertTimeStamp] as [insertTimeStamp],
	ntf15.[updateTimeStamp] as [updateTimeStamp]

	FROM NotesToursForm15 ntf15 
	Left OUTER JOIN Users u ON u.userID = ntf15.userID  
	WHERE ntf15.[referenceID] = t.tourID
	FOR JSON PATH),

	t.[importNumber] as [importNumber],

	'Customer' = (
	SELECT
	c.[customerID] as [customerID],
	c.[externalCustomerID] as [externalCustomerID],

	ctp.[customerTypeID] as [CustomerType.customerTypeID],
	ctp.[customerTypeName] as [CustomerType.customerTypeName],
	ctp.[customerTypeCode] as [CustomerType.customerTypeCode],

	cstp.[customerStatusTypeID] as [CustomerStatusType.customerStatusTypeID],
	cstp.[customerStatusTypeName] as [CustomerStatusType.customerStatusTypeName],
	cstp.[customerStatusTypeCode] as [CustomerStatusType.customerStatusTypeCode],

	cdp.[customerDispositionID] as [CustomerDisposition.customerDispositionID],
	cdp.[customerDispositionName] as [CustomerDisposition.customerDispositionName],
	cdp.[customerDispositionCode] as [CustomerDisposition.customerDispositionCode],

	c.[firstName] as [firstName],
	c.[lastName] as [lastName],
	c.[age] as [age],
	c.[sex] as [sex],

	cpl1i.[customersPickList1ItemID] as [CustomersPickList1Item.customersPickList1ItemID],
	cpl1i.[customersPickList1ItemName] as [CustomersPickList1Item.customersPickList1ItemName],
	cpl1i.[customersPickList1ItemCode] as [CustomersPickList1Item.customersPickList1ItemCode],

	c.[guestFirstName] as [guestFirstName],
	c.[guestLastName] AS [guestLastName],

	gtp.[guestTypeID] as [GuestType.guestTypeID],
	gtp.[guestTypeName] as [GuestType.guestTypeName],

	c.[guestAge] as [guestAge],
	c.[guestSex] as [guestSex],

	cpl2i.[customersPickList2ItemID] as [CustomersPickList2Item.customersPickList2ItemID],
	cpl2i.[customersPickList2ItemName] as [CustomersPickList2Item.customersPickList2ItemName],
	cpl2i.[customersPickList2ItemCode] as [CustomersPickList2Item.customersPickList2ItemCode],

	itp.[incomeTypeID] as [IncomeType.incomeTypeID],
	itp.[incomeTypeName] as [IncomeType.incomeTypeName],

	cpl3i.[customersPickList3ItemID] as [CustomersPickList3Item.customersPickList3ItemID],
	cpl3i.[customersPickList3ItemName] as [CustomersPickList3Item.customersPickList3ItemName],
	cpl3i.[customersPickList3ItemCode] as [CustomersPickList3Item.customersPickList3ItemCode],

	c.[customersText1] as [customersText1],
	c.[customersDecimal1] as [customersDecimal1],
	c.[customersBool1] as [customersBool1],
	c.[customersDate1] as [customersDate1],
	c.[customersTime1] as [customersTime1],
	c.[doNotCall] as [doNotCall],
	c.[primaryPhone] as [primaryPhone],
	c.[secondaryPhone] as [secondaryPhone],
	c.[streetAddress] as [streetAddress],
	c.[streetAddress2] as [streetAddress2],
	c.[city] as [city],

	st.[stateID] as [State.stateID],
	st.[stateAbbreviation] as [State.stateAbbreviation],
	st.[stateName] as [State.stateName],

	c.[zipcode] as [zipcode],

	cntry.[countryID] as [Country.countryID],
	cntry.[countryName] as [Country.countryName],

	c.[businessName] as [businessName],
	c.[email] as [email],

	cpl4i.[customersPickList4ItemID] as [CustomersPickList4Item.customersPickList4ItemID],
	cpl4i.[customersPickList4ItemName] as [CustomersPickList4Item.customersPickList4ItemName],
	cpl4i.[customersPickList4ItemCode] as [CustomersPickList4Item.customersPickList4ItemCode],

	c.[customersText2] as [customersText2],
	c.[customersText3] as [customersText3],
	c.[customersDecimal2] as [customersDecimal2],
	c.[customersBool2] as [customersBool2],
	c.[customersDate2] as [customersDate2],
	c.[customersTime2] as [customersTime2],

	'NotesCustomers' = (
	SELECT
	nc.[noteID] as [noteID],
	nc.[referenceID] as [referenceID],
	nc.[noteText] as [noteText],

	u.[userID] as [User.userID],
	u.[firstName] as [User.firstName],
	u.[lastName] as [User.lastName],
	nc.[insertTimeStamp] as [insertTimeStamp],
	nc.[updateTimeStamp] as [updateTimeStamp]

	FROM NotesCustomers nc 
	Left OUTER JOIN Users u ON u.userID = nc.userID  
	WHERE nc.[referenceID] = c.customerID
	FOR JSON PATH),

	c.[apiConnectionID] as [apiConnectionID],
	c.[apiExternalCustomerID] as [apiExternalCustomerID],
	c.[apiExternalConnectionID] as [apiExternalConnectionID],
	c.[insertTimeStamp] as [insertTimeStamp],
	c.[updateTimeStamp] as [updateTimeStamp]

	FROM Customers c
	LEFT OUTER JOIN CustomerTypes ctp ON c.customerTypeID = ctp.customerTypeID
	LEFT OUTER JOIN CustomerStatusTypes cstp ON c.customerStatusTypeID = cstp.customerStatusTypeID
	LEFT OUTER JOIN CustomerDispositions cdp ON c.customerDispositionID = cdp.customerDispositionID
	LEFT OUTER JOIN CustomersPickList1Items cpl1i ON c.customersPickList1ItemID = cpl1i.customersPickList1ItemID
	LEFT OUTER JOIN GuestTypes gtp ON c.guestTypeID = gtp.guestTypeID
	LEFT OUTER JOIN CustomersPickList2Items cpl2i ON c.customersPickList2ItemID = cpl2i.customersPickList2ItemID
	LEFT OUTER JOIN IncomeTypes itp ON c.incomeTypeID = itp.incomeTypeID
	LEFT OUTER JOIN CustomersPickList3Items cpl3i ON c.customersPickList3ItemID = cpl3i.customersPickList3ItemID
	LEFT OUTER JOIN States st ON c.stateID = st.stateID
	LEFT OUTER JOIN Countries cntry ON c.countryID = cntry.countryID
	LEFT OUTER JOIN CustomersPickList4Items cpl4i ON c.customersPickList4ItemID = cpl4i.customersPickList4ItemID
	WHERE t.customerID = c.customerID
	FOR JSON PATH),

	'Purchases' = (
	SELECT
	p.[purchaseID] as [purchaseID],
	p.[externalPurchaseID] as [externalPurchaseID],
	p.[tourID] as [tourID],
	p.[parentSupersededFromPurchaseID] as [parentSupersededFromPurchaseID],
	p.[supersededFromPurchaseID] as [supersededFromPurchaseID],
	p.[supersedingFromPender] as [supersedingFromPender],
	p.[saleDate] as [saleDate],
	p.[saleTime] as [saleTime],

	pc.[productCategoryID] as [ProductCategory.productCategoryID],
	pc.[productCategoryName] as [ProductCategory.productCategoryName],
	pc.[productCategoryCode] as [ProductCategory.productCategoryCode],

	pr.[productID] as [Product.productID],
	pr.[productName] as [Product.productName],
	pr.[productCode] as [Product.productCode],

	spr.[subProductID] as [SubProduct.subProductID],
	spr.[subProductName] as [SubProduct.subProductName],
	spr.[subProductCode] as [SubProduct.subProductCode],

	p.[saleAmount] as [saleAmount],
	p.[downPaymentAmount] as [downPaymentAmount],
	p.[fees1Amount] as [fees1Amount],
	p.[fees2Amount] as [fees2Amount],
	p.[fees3Amount] as [fees3Amount],
	p.[fees4Amount] as [fees4Amount],
	p.[fees5Amount] as [fees5Amount],

	stp.[saleTypeID] as [SaleType.saleTypeID],
	stp.[saleTypeName] as [SaleType.saleTypeName],
	stp.[saleTypeCode] as [SaleType.saleTypeCode],

	sstp.[saleStatusTypeID] as [SaleStatusType.saleStatusTypeID],
	sstp.[saleStatusTypeName] as [SaleStatusType.saleStatusTypeName],
	sstp.[saleStatusTypeCode] as [SaleStatusType.saleStatusTypeCode],

	sd.[saleDispositionID] as [SaleDisposition.saleDispositionID],
	sd.[saleDispositionName] as [SaleDisposition.saleDispositionName],
	sd.[saleDispositionCode] as [SaleDisposition.saleDispositionCode],

	ppl1i.[purchasesPickList1ItemID] as [PurchasesPickList1Item.purchasesPickList1ItemID],
	ppl1i.[purchasesPickList1ItemName] as [PurchasesPickList1Item.purchasesPickList1ItemName],
	ppl1i.[purchasesPickList1ItemCode] as [PurchasesPickList1Item.purchasesPickList1ItemCode],

	ppl2i.[purchasesPickList2ItemID] as [PurchasesPickList2Item.purchasesPickList2ItemID],
	ppl2i.[purchasesPickList2ItemName] as [PurchasesPickList2Item.purchasesPickList2ItemName],
	ppl2i.[purchasesPickList2ItemCode] as [PurchasesPickList2Item.purchasesPickList2ItemCode],

	ppl3i.[purchasesPickList3ItemID] as [PurchasesPickList3Item.purchasesPickList3ItemID],
	ppl3i.[purchasesPickList3ItemName] as [PurchasesPickList3Item.purchasesPickList3ItemName],
	ppl3i.[purchasesPickList3ItemCode] as [PurchasesPickList3Item.purchasesPickList3ItemCode],

	p.[purchasesText1] as [purchasesText1],
	p.[purchasesText2] as [purchasesText2],
	p.[purchasesBool1] as [purchasesBool1],
	p.[purchasesBool2] as [purchasesBool2],
	p.[purchasesDate1] as [purchasesDate1],
	p.[purchasesTime1] as [purchasesTime1],
	p.[purchasesDate2] as [purchasesDate2],
	p.[purchasesTime2] as [purchasesTime2],

	'Cancellation' = (
	SELECT
	cx.[cancellationID] as [cancellationID],
	cx.[cancellationPurchaseID] as [cancellationPurchaseID],
	cx.[cancellationDate] as [cancellationDate],

	cxrsn.[cancellationReasonTypeID] as [CancellationResonType.cancellationReasonTypeID],
	cxrsn.[cancellationReasonTypeName] as [CancellationResonType.cancellationReasonTypeName],

	cxrcv.[cancellationReceivedTypeID] as [CancellationReceivedType.cancellationReceivedTypeID],
	cxrcv.[cancellationReceivedTypeName] as [CancellationReceivedType.cancellationReceivedTypeName],

	cxdp.[cancellationDispositionTypeID] as [CancellationDispositionType.cancellationDispositionTypeID],
	cxdp.[cancellationDispositionTypeName] as [CancellationDispositionType.cancellationDispositionTypeName],

	cx.[withinRescission] as [withinRescission],
	cx.[pender] as [pender],

	'CancellationPickup' = (
	SELECT
	cxpu.[cancellationsPickupID] as [cancellationsPickupID],
	cxpu.[cancellationID] as [cancellationID],

	cxstp.[cancellationStatusTypeID] as [CancellationStatysType.cancellationStatusTypeID],
	cxstp.[cancellationStatusTypeName] as [CancellationStatysType.cancellationStatusTypeName],

	cxpu.[cancellationUserID] as [CancellationUser.userID],

	u.[firstName] as [CancellationUser.firstName],
	u.[lastName] as [CancellationUser.lastName]

	FROM CancellationsPickups cxpu
	LEFT OUTER JOIN CancellationStatusTypes cxstp ON cxpu.cancellationStatusTypeID = cxstp.cancellationStatusTypeID
	LEFT OUTER JOIN Users u ON cxpu.cancellationUserID = u.userID
	WHERE cxpu.cancellationID = cx.cancellationID
	FOR JSON PATH),

	'SupersededPurchase' = (
	SELECT
	cxsupp.[supersededPurchaseID] as [supersededPurchaseID]

	FROM CancellationsSupersededPurchases cxsupp
	WHERE cxsupp.cancellationID = cx.cancellationID
	FOR JSON PATH)

	FROM Cancellations cx
	LEFT OUTER JOIN CancellationReasonTypes cxrsn ON cx.cancellationReasonTypeID = cxrsn.cancellationReasonTypeID
	LEFT OUTER JOIN CancellationReceivedTypes cxrcv ON cx.cancellationReceivedTypeID = cxrcv.cancellationReceivedTypeID
	LEFT OUTER JOIN CancellationDispositionTypes cxdp ON cx.cancellationDispositionTypeID = cxdp.cancellationDispositionTypeID
	WHERE cx.cancellationPurchaseID = p.purchaseID
	FOR JSON PATH),

	p.[apiConnectionID] as [apiConnectionID],
	p.[apiExternalPurchaseID] as [apiExternalPurchaseID],
	p.[apiExternalConnectionID] as [apiExternalConnectionID],
	p.[insertTimeStamp] as [insertTimeStamp],
	p.[updateTimeStamp] as [updateTimeStamp]

	FROM Purchases p
	LEFT OUTER JOIN ProductCategories pc ON p.productCategoryID = pc.productCategoryID
	LEFT OUTER JOIN Products pr ON p.productID = pr.productID
	LEFT OUTER JOIN SubProducts spr ON p.subProductID = spr.subProductID
	LEFT OUTER JOIN SaleTypes stp ON p.saleTypeID = stp.saleTypeID
	LEFT OUTER JOIN SaleStatusTypes sstp ON p.saleStatusTypeID = sstp.saleStatusTypeID
	LEFT OUTER JOIN SaleDispositions sd ON p.saleDispositionID = sd.saleDispositionID
	LEFT OUTER JOIN PurchasesPickList1Items ppl1i ON p.purchasesPickList1ItemID = ppl1i.purchasesPickList1ItemID
	LEFT OUTER JOIN PurchasesPickList2Items ppl2i ON p.purchasesPickList2ItemID = ppl2i.purchasesPickList2ItemID
	LEFT OUTER JOIN PurchasesPickList3Items ppl3i ON p.purchasesPickList3ItemID = ppl3i.purchasesPickList3ItemID
	WHERE p.tourID = t.tourID
	FOR JSON PATH),

	t.[apiConnectionID] as [apiConnectionID],
	t.[apiExternalTourID] as [apiExternalTourID],
	t.[apiExternalConnectionID] as [apiExternalConnectionID],
	t.[insertTimeStamp] as [insertTimeStamp],
	t.[updateTimeStamp] as [updateTimeStamp]

	FROM Tours t
	LEFT OUTER JOIN LeadSources ls ON t.leadSourceID = ls.leadSourceID
	LEFT OUTER JOIN LeadTypes ltp ON t.leadTypeID = ltp.leadTypeID
	LEFT OUTER JOIN LeadStatusTypes lst ON t.leadStatusTypeID = lst.leadStatusTypeID
	LEFT OUTER JOIN LeadDispositions ld ON t.leadDispositionID = ld.leadDispositionID
	LEFT OUTER JOIN LeadPickList1Items lpl1i ON t.leadPickList1ItemID = lpl1i.leadPickList1ItemID
	LEFT OUTER JOIN LeadPickList2Items lpl2i ON t.leadPickList2ItemID = lpl2i.leadPickList2ItemID
	LEFT OUTER JOIN ContactStatusTypes ctst ON t.contactStatusTypeID = ctst.contactStatusTypeID
	LEFT OUTER JOIN ContactDispositions ctd ON t.contactDispositionID = ctd.contactDispositionID
	LEFT OUTER JOIN CallBackPickList1Items cbpl1i ON t.callBackPickList1ItemID = cbpl1i.callBackPickList1ItemID
	LEFT OUTER JOIN TourSources ts ON t.tourSourceID = ts.tourSourceID
	LEFT OUTER JOIN TourTypes ttp ON t.tourTypeID = ttp.tourTypeID
	LEFT OUTER JOIN TourStatusTypes tstp ON t.tourStatusTypeID = tstp.tourStatusTypeID
	LEFT OUTER JOIN TourConcernTypes tctp ON t.tourConcernTypeID = tctp.tourConcernTypeID
	LEFT OUTER JOIN TourTypePickList1Items ttpl1i ON t.tourTypePickList1ItemID = ttpl1i.tourTypePickList1ItemID
	LEFT OUTER JOIN Locations l ON t.locationID = l.locationID
	LEFT OUTER JOIN Regions r ON t.regionID = r.regionID
	LEFT OUTER JOIN Teams mktm ON t.marketingTeamID = mktm.teamID
	LEFT OUTER JOIN Users mu1 ON t.marketingAgentID = mu1.userID
	LEFT OUTER JOIN Users mu2 ON t.marketingCloserID = mu2.userID
	LEFT OUTER JOIN Users mu3 ON t.confirmerID = mu3.userID
	LEFT OUTER JOIN Users mu4 ON t.resetterID = mu4.userID
	LEFT OUTER JOIN Venues v ON t.venueID = v.venueID
	LEFT OUTER JOIN Campaigns cmp ON t.campaignID = cmp.campaignID
	LEFT OUTER JOIN Channels ch ON t.channelID = ch.channelID
	LEFT OUTER JOIN Hotels h ON t.hotelID = h.hotelID
	LEFT OUTER JOIN MarketingPickList1Items mpl1i ON t.marketingPickList1ItemID = mpl1i.marketingPickList1ItemID
	LEFT OUTER JOIN MarketingPickList2Items mpl2i ON t.marketingPickList2ItemID = mpl2i.marketingPickList2ItemID
	LEFT OUTER JOIN Teams sltm ON t.salesTeamID = sltm.teamID
	LEFT OUTER JOIN Users su1 ON t.podiumID = su1.userID
	LEFT OUTER JOIN Users su2 ON t.salesRepID = su2.userID
	LEFT OUTER JOIN Users su3 ON t.salesCloser1ID = su3.userID
	LEFT OUTER JOIN Users su4 ON t.salesCloser2ID = su4.userID
	LEFT OUTER JOIN Users su5 ON t.exitID = su5.userID
	LEFT OUTER JOIN Users su6 ON t.verificationOfficerID = su6.userID
	LEFT OUTER JOIN SalesPickList1Items spl1i ON t.salesPickList1ItemID = spl1i.salesPickList1ItemID
	LEFT OUTER JOIN Teams sltmex ON t.salesTeamExitID = sltmex.teamID
	LEFT OUTER JOIN Users seu1 ON t.salesRepExit1ID = seu1.userID
	LEFT OUTER JOIN Users seu2 ON t.salesRepExit2ID = seu2.userID
	LEFT OUTER JOIN Users seu3 ON t.salesRepExit3ID = seu3.userID
	WHERE t.tourid = @tourId
	FOR JSON PATH, INCLUDE_NULL_VALUES 
END
