﻿CREATE TABLE [dbo].[TourStatusTypes] (
    [tourStatusTypeID]   INT           NOT NULL,
    [tourStatusTypeName] NVARCHAR (64) NOT NULL,
    [tourStatusTypeCode] NVARCHAR (64) NULL,
    [sortOrder]          INT           NOT NULL,
    CONSTRAINT [PK_TourStatusTypes] PRIMARY KEY CLUSTERED ([tourStatusTypeID] ASC),
    CONSTRAINT [UK_TourStatusTypes_tourStatusTypeName] UNIQUE NONCLUSTERED ([tourStatusTypeName] ASC)
);


GO
CREATE TRIGGER [dbo].[TourStatusTypes.InsertUpdateDelete]
    ON [dbo].[TourStatusTypes]
    FOR INSERT, UPDATE, DELETE
AS 
BEGIN
    SET NOCOUNT ON;

    DECLARE @Activity  NVARCHAR (8), @id INT;

    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'updated'
    END

    IF EXISTS (SELECT * FROM inserted) AND NOT EXISTS(SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'inserted'
    END

    IF EXISTS (SELECT * FROM deleted) AND NOT EXISTS(SELECT * FROM inserted)
    BEGIN
        SET @Activity = 'deleted'
    END

    

    IF (@Activity = 'deleted')
    BEGIN
        DECLARE cur CURSOR FOR SELECT tourStatusTypeID FROM deleted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'TourStatusTypes', @id, 'tourStatusTypeID',
                (
                    SELECT *
                    FROM deleted i 
                    WHERE i.tourStatusTypeID =  @id
                    FOR JSON AUTO
                ),0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
    ELSE
    BEGIN
        DECLARE cur CURSOR
        FOR SELECT tourStatusTypeID
        FROM inserted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'TourStatusTypes', @id, 'tourStatusTypeID',
                (
                    SELECT *
                    FROM inserted i 
                    WHERE i.tourStatusTypeID =  @id
                    FOR JSON AUTO
                ), 0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
END

GO
DISABLE TRIGGER [dbo].[TourStatusTypes.InsertUpdateDelete]
    ON [dbo].[TourStatusTypes];

