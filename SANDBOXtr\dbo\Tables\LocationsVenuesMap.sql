﻿CREATE TABLE [dbo].[LocationsVenuesMap] (
    [locationsVenuesMapID] INT IDENTITY (1, 1) NOT NULL,
    [locationID]           INT NOT NULL,
    [venueID]              INT NOT NULL,
    [active]               BIT NOT NULL,
    CONSTRAINT [PK_LocationsVenuesMap] PRIMARY KEY CLUSTERED ([locationsVenuesMapID] ASC),
    CONSTRAINT [FK_LocationsVenuesMap_Locations] FOREIGN KEY ([locationID]) REFERENCES [dbo].[Locations] ([locationID]),
    CONSTRAINT [FK_LocationsVenuesMap_Venues] FOREIGN KEY ([venueID]) REFERENCES [dbo].[Venues] ([venueID]),
    CONSTRAINT [UK_LocationsVenuesMap] UNIQUE NONCLUSTERED ([locationID] ASC, [venueID] ASC)
);


GO
CREATE TRIGGER [dbo].[LocationsVenuesMap.InsertUpdateDelete]
    ON [dbo].[LocationsVenuesMap]
    FOR INSERT, UPDATE, DELETE
AS 
BEGIN
    SET NOCOUNT ON;

    DECLARE @Activity  NVARCHAR (8), @id INT;

    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'updated'
    END

    IF EXISTS (SELECT * FROM inserted) AND NOT EXISTS(SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'inserted'
    END

    IF EXISTS (SELECT * FROM deleted) AND NOT EXISTS(SELECT * FROM inserted)
    BEGIN
        SET @Activity = 'deleted'
    END

    

    IF (@Activity = 'deleted')
    BEGIN
        DECLARE cur CURSOR FOR SELECT locationsVenuesMapID FROM deleted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'LocationsVenuesMap', @id, 'locationsVenuesMapID',
                (
                    SELECT *
                    FROM deleted i 
                    WHERE i.locationsVenuesMapID =  @id
                    FOR JSON AUTO
                ),0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
    ELSE
    BEGIN
        DECLARE cur CURSOR
        FOR SELECT locationsVenuesMapID
        FROM inserted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'LocationsVenuesMap', @id, 'locationsVenuesMapID',
                (
                    SELECT *
                    FROM inserted i 
                    WHERE i.locationsVenuesMapID =  @id
                    FOR JSON AUTO
                ), 0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
END

GO
DISABLE TRIGGER [dbo].[LocationsVenuesMap.InsertUpdateDelete]
    ON [dbo].[LocationsVenuesMap];

