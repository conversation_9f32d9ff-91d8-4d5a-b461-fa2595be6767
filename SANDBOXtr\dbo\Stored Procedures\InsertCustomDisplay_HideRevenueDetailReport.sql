﻿
CREATE PROCEDURE [dbo].[InsertCustomDisplay_HideRevenueDetailReport]

AS
BEGIN

	DECLARE @GroupTypeKey nvarchar(64) SET @GroupTypeKey = 'HideRevenueDetailReport'

	DELETE FROM CustomPageControlDisplayRights WHERE (customPageControlDisplayRightGroupTypeKey = @GroupTypeKey)

	DECLARE @Add int SET @Add = 1
	DECLARE @Remove int SET @Remove = 2

	DECLARE @Insert int SET @Insert = 1
	DECLARE @Select int SET @Select = 2
	DECLARE @Update int SET @Update = 3

	DECLARE @Visible int SET @Visible = 1
	DECLARE @Enabled int SET @Enabled = 2
	DECLARE @Text int SET @Text = 3
	DECLARE @Delegate int SET @Delegate = 4
	DECLARE @Authorized int SET @Authorized = 5
	DECLARE @Administrated int SET @Administrated = 6

	DECLARE @PagePathID [varchar] (256)

	SET @PagePathID = 'ASP.reports_default_aspx'

	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'linkRevenueReport',@Visible,0,NULL,NULL

	SET @PagePathID = 'ASP.reports_revenuereport_aspx'

	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'form1',@Authorized,0,NULL,NULL

	SET @PagePathID = 'ASP.reports_usercontrols_reportconfigurationcommands_ascx'
	
	EXECUTE InsertGlobalCustomPageControlDisplayRights @GroupTypeKey,@Add,@PagePathID,@Select,'dropDownReportTypes',@Delegate,NULL,NULL,'TrackResults.Web.Security.DropDownDisplay.ReportTypes,TrackResults.Web:RemoveByReportTypes(TrackResults.BES.Data.Types.ReportType[] {RevenueDetail})'


END