﻿CREATE TABLE [dbo].[MarketingPickList2Items] (
    [marketingPickList2ItemID]   INT           IDENTITY (1, 1) NOT NULL,
    [marketingPickList2ItemName] NVARCHAR (64) NOT NULL,
    [marketingPickList2ItemCode] NVARCHAR (64) NOT NULL,
    [sortOrder]                  INT           NOT NULL,
    [active]                     BIT           NOT NULL,
    CONSTRAINT [PK_MarketingPickList2Items] PRIMARY KEY CLUSTERED ([marketingPickList2ItemID] ASC),
    CONSTRAINT [UK_MarketingPickList2Items_marketingPickList2ItemName] UNIQUE NONCLUSTERED ([marketingPickList2ItemName] ASC)
);


GO
CREATE TRIGGER [dbo].[MarketingPickList2Items.InsertUpdateDelete]
    ON [dbo].[MarketingPickList2Items]
    FOR INSERT, UPDATE, DELETE
AS 
BEGIN
    SET NOCOUNT ON;

    DECLARE @Activity  NVARCHAR (8), @id INT;

    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'updated'
    END

    IF EXISTS (SELECT * FROM inserted) AND NOT EXISTS(SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'inserted'
    END

    IF EXISTS (SELECT * FROM deleted) AND NOT EXISTS(SELECT * FROM inserted)
    BEGIN
        SET @Activity = 'deleted'
    END

    

    IF (@Activity = 'deleted')
    BEGIN
        DECLARE cur CURSOR FOR SELECT marketingPickList2ItemID FROM deleted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'MarketingPickList2Items', @id, 'marketingPickList2ItemID',
                (
                    SELECT *
                    FROM deleted i 
                    WHERE i.marketingPickList2ItemID =  @id
                    FOR JSON AUTO
                ),0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
    ELSE
    BEGIN
        DECLARE cur CURSOR
        FOR SELECT marketingPickList2ItemID
        FROM inserted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'MarketingPickList2Items', @id, 'marketingPickList2ItemID',
                (
                    SELECT *
                    FROM inserted i 
                    WHERE i.marketingPickList2ItemID =  @id
                    FOR JSON AUTO
                ), 0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
END

GO
DISABLE TRIGGER [dbo].[MarketingPickList2Items.InsertUpdateDelete]
    ON [dbo].[MarketingPickList2Items];

