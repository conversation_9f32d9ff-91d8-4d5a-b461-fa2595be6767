﻿CREATE TABLE [dbo].[MiddlewareErrors] (
    [middlewareErrorID]     INT            IDENTITY (1, 1) NOT NULL,
    [middlewareErrorTypeID] INT            NOT NULL,
    [apiConnectionID]       INT            NOT NULL,
    [importNumber]          NVARCHAR (64)  NULL,
    [fileNamePath]          NVARCHAR (128) NULL,
    [errorMessage]          NVARCHAR (MAX) NOT NULL,
    [isException]           BIT            NOT NULL,
    [externalCustomerID]    NVARCHAR (64)  NULL,
    [externalTourID]        NVARCHAR (64)  NULL,
    [info1]                 NVARCHAR (128) NULL,
    [info2]                 NVARCHAR (128) NULL,
    [info3]                 NVARCHAR (128) NULL,
    [info4]                 NVARCHAR (128) NULL,
    [insertTimeStamp]       DATETIME       CONSTRAINT [DF_MiddlewareErrors_insertTimeStamp] DEFAULT (getdate()) NOT NULL,
    CONSTRAINT [PK_MiddlewareErrors] PRIMARY KEY CLUSTERED ([middlewareErrorID] ASC),
    CONSTRAINT [FK_MiddlewareErrors_apiConnectionID] FOREIGN KEY ([apiConnectionID]) REFERENCES [dbo].[ApiConnections] ([apiConnectionID]),
    CONSTRAINT [FK_MiddlewareErrors_middlewareErrorTypeID] FOREIGN KEY ([middlewareErrorTypeID]) REFERENCES [dbo].[MiddlewareErrorTypes] ([middlewareErrorTypeID])
);


GO
CREATE TRIGGER [dbo].[MiddlewareErrors.InsertUpdateDelete]
    ON [dbo].[MiddlewareErrors]
    FOR INSERT, UPDATE, DELETE
AS 
BEGIN
    SET NOCOUNT ON;

    DECLARE @Activity  NVARCHAR (8), @id INT;

    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'updated'
    END

    IF EXISTS (SELECT * FROM inserted) AND NOT EXISTS(SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'inserted'
    END

    IF EXISTS (SELECT * FROM deleted) AND NOT EXISTS(SELECT * FROM inserted)
    BEGIN
        SET @Activity = 'deleted'
    END

    

    IF (@Activity = 'deleted')
    BEGIN
        DECLARE cur CURSOR FOR SELECT middlewareErrorID FROM deleted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'MiddlewareErrors', @id, 'middlewareErrorID',
                (
                    SELECT *
                    FROM deleted i 
                    WHERE i.middlewareErrorID =  @id
                    FOR JSON AUTO
                ),0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
    ELSE
    BEGIN
        DECLARE cur CURSOR
        FOR SELECT middlewareErrorID
        FROM inserted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'MiddlewareErrors', @id, 'middlewareErrorID',
                (
                    SELECT *
                    FROM inserted i 
                    WHERE i.middlewareErrorID =  @id
                    FOR JSON AUTO
                ), 0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
END
GO
DISABLE TRIGGER [dbo].[MiddlewareErrors.InsertUpdateDelete]
    ON [dbo].[MiddlewareErrors];

