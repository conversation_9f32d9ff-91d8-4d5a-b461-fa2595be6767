﻿CREATE PROCEDURE [dbo].[BulkUpdateTours]
    @DataTableParam dbo.TourType READONLY -- Use the TVP defined above
AS
BEGIN
    SET NOCOUNT ON;

    -- Use MERGE to update existing records and insert new ones if needed
    MERGE INTO Tours AS target
    USING @DataTableParam AS source
    ON target.tourID = source.tourID
    
    -- When a matching tourID is found, update the existing row
    WHEN MATCHED THEN
        UPDATE SET
            target.externalTourID = source.externalTourID,
            target.customerID = source.customerID,
            target.externalLeadID = source.externalLeadID,
            target.acquisitionDate = source.acquisitionDate,
            target.acquisitionTime = source.acquisitionTime,
            target.leadSourceID = source.leadSourceID,
            target.leadTypeID = source.leadTypeID,
            target.leadStatusTypeID = source.leadStatusTypeID,
            target.leadDispositionID = source.leadDispositionID,
            target.checkInDate = source.checkInDate,
            target.checkInTime = source.checkInTime,
            target.checkOutDate = source.checkOutDate,
            target.checkOutTime = source.checkOutTime,
            target.leadPickList1ItemID = source.leadPickList1ItemID,
            target.leadText1 = source.leadText1,
            target.leadDecimal1 = source.leadDecimal1,
            target.leadBool1 = source.leadBool1,
            target.leadPickList2ItemID = source.leadPickList2ItemID,
            target.leadText2 = source.leadText2,
            target.leadDecimal2 = source.leadDecimal2,
            target.leadBool2 = source.leadBool2,
            target.contactStatusTypeID = source.contactStatusTypeID,
            target.contactDispositionID = source.contactDispositionID,
            target.callBackDate = source.callBackDate,
            target.callBackTime = source.callBackTime,
            target.callBackPickList1ItemID = source.callBackPickList1ItemID,
            target.callBackText1 = source.callBackText1,
            target.callBackDecimal1 = source.callBackDecimal1,
            target.callBackBool1 = source.callBackBool1,
            target.tourSourceID = source.tourSourceID,
            target.tourTypeID = source.tourTypeID,
            target.tourStatusTypeID = source.tourStatusTypeID,
            target.tourConcernTypeID = source.tourConcernTypeID,
            target.tourTypePickList1ItemID = source.tourTypePickList1ItemID,
            target.locationID = source.locationID,
            target.regionID = source.regionID,
            target.tourDate = source.tourDate,
            target.tourTime = source.tourTime,
            target.entryDateTime = source.entryDateTime,
            target.tourText1 = source.tourText1,
            target.tourDecimal1 = source.tourDecimal1,
            target.tourBool1 = source.tourBool1,
            target.rescheduledCount = source.rescheduledCount,
            target.rescheduledTourID = source.rescheduledTourID,
            target.parentRescheduledTourID = source.parentRescheduledTourID,
            target.marketingTeamID = source.marketingTeamID,
            target.marketingAgentID = source.marketingAgentID,
            target.marketingCloserID = source.marketingCloserID,
            target.confirmerID = source.confirmerID,
            target.resetterID = source.resetterID,
            target.venueID = source.venueID,
            target.campaignID = source.campaignID,
            target.channelID = source.channelID,
            target.hotelID = source.hotelID,
            target.marketingPickList1ItemID = source.marketingPickList1ItemID,
            target.marketingPickList2ItemID = source.marketingPickList2ItemID,
            target.marketingText1 = source.marketingText1,
            target.marketingDecimal1 = source.marketingDecimal1,
            target.marketingBool1 = source.marketingBool1,
            target.marketingDate1 = source.marketingDate1,
            target.marketingTime1 = source.marketingTime1,
            target.depositAmount = source.depositAmount,
            target.depositRefundable = source.depositRefundable,
            target.salesTeamID = source.salesTeamID,
            target.podiumID = source.podiumID,
            target.salesRepID = source.salesRepID,
            target.salesCloser1ID = source.salesCloser1ID,
            target.salesCloser2ID = source.salesCloser2ID,
            target.exitID = source.exitID,
            target.verificationOfficerID = source.verificationOfficerID,
            target.salesPickList1ItemID = source.salesPickList1ItemID,
            target.salesText1 = source.salesText1,
            target.salesDecimal1 = source.salesDecimal1,
            target.salesBool1 = source.salesBool1,
            target.salesDate1 = source.salesDate1,
            target.salesTime1 = source.salesTime1,
            target.importNumber = source.importNumber,
            target.apiConnectionID = source.apiConnectionID,
            target.apiExternalTourID = source.apiExternalTourID,
            target.apiExternalConnectionID = source.apiExternalConnectionID,
			target.insertTimeStamp = target.insertTimeStamp,
            target.updateTimeStamp = GETDATE(), -- Automatically update the timestamp
            target.salesTeamExitID = source.salesTeamExitID,
            target.salesRepExit1ID = source.salesRepExit1ID,
            target.salesRepExit2ID = source.salesRepExit2ID,
            target.salesRepExit3ID = source.salesRepExit3ID;


END;