﻿CREATE TABLE [dbo].[IPSecurities] (
    [ipSecurityID]        INT            IDENTITY (1, 1) NOT NULL,
    [ipAddressBase]       NVARCHAR (12)  NOT NULL,
    [ipAddressStartRange] TINYINT        NOT NULL,
    [ipAddressEndRange]   TINYINT        NOT NULL,
    [roleName]            NVARCHAR (256) NULL,
    [userID]              INT            NULL,
    CONSTRAINT [PK_ipSecurityID] PRIMARY KEY CLUSTERED ([ipSecurityID] ASC),
    CONSTRAINT [FK_IPSecurities_userID] FOREIGN KEY ([userID]) REFERENCES [dbo].[Users] ([userID])
);


GO
CREATE NONCLUSTERED INDEX [IX_IPSecurities_ipAddress]
    ON [dbo].[IPSecurities]([ipAddressBase] ASC);


GO
CREATE TRIGGER [dbo].[IPSecurities.InsertUpdateDelete]
    ON [dbo].[IPSecurities]
    FOR INSERT, UPDATE, DELETE
AS 
BEGIN
    SET NOCOUNT ON;

    DECLARE @Activity  NVARCHAR (8), @id INT;

    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'updated'
    END

    IF EXISTS (SELECT * FROM inserted) AND NOT EXISTS(SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'inserted'
    END

    IF EXISTS (SELECT * FROM deleted) AND NOT EXISTS(SELECT * FROM inserted)
    BEGIN
        SET @Activity = 'deleted'
    END

    

    IF (@Activity = 'deleted')
    BEGIN
        DECLARE cur CURSOR FOR SELECT ipSecurityID FROM deleted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'IPSecurities', @id, 'ipSecurityID',
                (
                    SELECT *
                    FROM deleted i 
                    WHERE i.ipSecurityID =  @id
                    FOR JSON AUTO
                ),0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
    ELSE
    BEGIN
        DECLARE cur CURSOR
        FOR SELECT ipSecurityID
        FROM inserted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'IPSecurities', @id, 'ipSecurityID',
                (
                    SELECT *
                    FROM inserted i 
                    WHERE i.ipSecurityID =  @id
                    FOR JSON AUTO
                ), 0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
END

GO
DISABLE TRIGGER [dbo].[IPSecurities.InsertUpdateDelete]
    ON [dbo].[IPSecurities];

