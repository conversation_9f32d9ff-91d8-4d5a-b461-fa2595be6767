﻿CREATE TABLE [dbo].[ToursGiftsMap] (
    [toursGiftsMapID] INT IDENTITY (1, 1) NOT NULL,
    [tourID]          INT NOT NULL,
    [giftID]          INT NOT NULL,
    CONSTRAINT [PK_ToursGiftsMap] PRIMARY KEY CLUSTERED ([toursGiftsMapID] ASC),
    CONSTRAINT [FK_ToursGiftsMap_Gifts] FOREIGN KEY ([giftID]) REFERENCES [dbo].[Gifts] ([giftID]),
    CONSTRAINT [FK_ToursGiftsMap_Tour] FOREIGN KEY ([tourID]) REFERENCES [dbo].[Tours] ([tourID])
);


GO
CREATE NONCLUSTERED INDEX [IX_ToursGiftsMap_giftID]
    ON [dbo].[ToursGiftsMap]([giftID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_ToursGiftsMap_tourID]
    ON [dbo].[ToursGiftsMap]([tourID] ASC);


GO
CREATE TRIGGER [dbo].[ToursGiftsMap.InsertUpdateDelete]
    ON [dbo].[ToursGiftsMap]
    FOR INSERT, UPDATE, DELETE
AS 
BEGIN
    SET NOCOUNT ON;

    DECLARE @Activity  NVARCHAR (8), @id INT;

    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'updated'
    END

    IF EXISTS (SELECT * FROM inserted) AND NOT EXISTS(SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'inserted'
    END

    IF EXISTS (SELECT * FROM deleted) AND NOT EXISTS(SELECT * FROM inserted)
    BEGIN
        SET @Activity = 'deleted'
    END

    

    IF (@Activity = 'deleted')
    BEGIN
        DECLARE cur CURSOR FOR SELECT toursGiftsMapID FROM deleted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'ToursGiftsMap', @id, 'toursGiftsMapID',
                (
                    SELECT *
                    FROM deleted i 
                    WHERE i.toursGiftsMapID =  @id
                    FOR JSON AUTO
                ),0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
    ELSE
    BEGIN
        DECLARE cur CURSOR
        FOR SELECT toursGiftsMapID
        FROM inserted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'ToursGiftsMap', @id, 'toursGiftsMapID',
                (
                    SELECT *
                    FROM inserted i 
                    WHERE i.toursGiftsMapID =  @id
                    FOR JSON AUTO
                ), 0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
END
GO
DISABLE TRIGGER [dbo].[ToursGiftsMap.InsertUpdateDelete]
    ON [dbo].[ToursGiftsMap];

