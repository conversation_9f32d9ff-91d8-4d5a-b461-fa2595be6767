﻿CREATE TABLE [dbo].[Locations] (
    [locationID]  INT            IDENTITY (1, 1) NOT NULL,
    [active]      BIT            CONSTRAINT [DF_Locations_active] DEFAULT ((0)) NOT NULL,
    [name]        NVARCHAR (64)  NOT NULL,
    [code]        <PERSON>VA<PERSON><PERSON><PERSON> (64)  NULL,
    [description] <PERSON>VARCHAR (MAX) NULL,
    [address]     <PERSON>VA<PERSON>HAR (64)  NULL,
    [address2]    NVARCHAR (64)  NULL,
    [city]        NVARCHAR (64)  NULL,
    [stateID]     INT            NULL,
    [zipcode]     NVARCHAR (16)  NULL,
    [phone]       NVARCHAR (32)  NULL,
    [fax]         NVARCHAR (32)  NULL,
    CONSTRAINT [PK_Locations] PRIMARY KEY CLUSTERED ([locationID] ASC),
    CONSTRAINT [UK_Locations_name] UNIQUE NONCLUSTERED ([name] ASC)
);


GO
CREATE TRIGGER [dbo].[Locations.InsertUpdateDelete]
    ON [dbo].[Locations]
    FOR INSERT, UPDATE, DELETE
AS 
BEGIN
    SET NOCOUNT ON;

    DECLARE @Activity  NVARCHAR (8), @id INT;

    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'updated'
    END

    IF EXISTS (SELECT * FROM inserted) AND NOT EXISTS(SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'inserted'
    END

    IF EXISTS (SELECT * FROM deleted) AND NOT EXISTS(SELECT * FROM inserted)
    BEGIN
        SET @Activity = 'deleted'
    END

    

    IF (@Activity = 'deleted')
    BEGIN
        DECLARE cur CURSOR FOR SELECT locationID FROM deleted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'Locations', @id, 'locationID',
                (
                    SELECT *
                    FROM deleted i 
                    WHERE i.locationID =  @id
                    FOR JSON AUTO
                ),0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
    ELSE
    BEGIN
        DECLARE cur CURSOR
        FOR SELECT locationID
        FROM inserted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'Locations', @id, 'locationID',
                (
                    SELECT *
                    FROM inserted i 
                    WHERE i.locationID =  @id
                    FOR JSON AUTO
                ), 0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
END

GO
DISABLE TRIGGER [dbo].[Locations.InsertUpdateDelete]
    ON [dbo].[Locations];

