﻿

CREATE FUNCTION [dbo].[GetConcantenatedLocationsByUserID]
(
	@UserID int,
	@Delimeter nvarchar(8)
)
RETURNS nvarchar(2048)
AS
BEGIN

	DECLARE @ConcantenatedList nvarchar(2048)

	SET @ConcantenatedList = ''

	SELECT @ConcantenatedList = @ConcantenatedList + @Delimeter + ISNULL(Locations.name,'-Unassigned')
	FROM         AllUsersLocationsMapView LEFT JOIN
						  Locations ON AllUsersLocationsMapView.locationID = Locations.locationID
	WHERE AllUsersLocationsMapView.userID = @UserID
	ORDER BY Locations.name

	IF DATALENGTH(@ConcantenatedList) > 0
	BEGIN
		SET @ConcantenatedList = RIGHT(@ConcantenatedList, ((DATALENGTH(@ConcantenatedList)/2) - (DATALENGTH(@Delimeter)/2)))
	END

	RETURN @ConcantenatedList

END