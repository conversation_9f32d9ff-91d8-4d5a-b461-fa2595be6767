﻿-- =============================================
-- Author:      <Author, , Name>
-- Create Date: <Create Date, , >
-- Description: <Description, , >
-- =============================================
CREATE PROCEDURE [dbo].[SPS_GetRangeObject]
(
   @entity nvarchar(max), @json nvarchar(max)
)
AS
BEGIN
    DECLARE @column nvarchar(max), @data nvarchar(max);
	DECLARE @table TABLE (Id int Identity(1,1), Columns nvarchar(max), Data nvarchar(max));
	DECLARE @columns_data TABLE(Id int Identity(1,1), Columns nvarchar(max),  Data nvarchar(max));
	DECLARE @cont int = 1, @stop int;
	DECLARE @query nvarchar(max) = 'SELECT * FROM ' + @entity + ' WHERE ';
	DECLARE @conditions nvarchar(max) = '', @lastColum nvarchar(250) = '', @currentColum nvarchar(50);

	INSERT INTO @table
	SELECT * FROM OPENJSON(@json)WITH
	(
		columns NVARCHAR(MAX) '$.columns', 
		data NVARCHAR(MAX) '$.data'
	);

	SELECT @stop = MAX(Id) FROM @table

	WHILE @cont <= @stop
	BEGIN
		SELECT @column = Columns, @data = Data FROM @table WHERE Id = @cont
		INSERT INTO @columns_data
		SELECT @column, VALUE FROM string_split(@data, ',')
		SET @cont = @cont + 1;
	END

	SET @cont = 1;
	SELECT @stop = MAX(Id) FROM @columns_data;

	WHILE @cont <= @stop
	BEGIN
		SELECT @currentColum = Columns, @data = Data FROM @columns_data WHERE Id = @cont

		IF @lastColum = ''
		BEGIN
			SET @conditions = '(' +  + @currentColum + '=' + @data;
			SET @lastColum = @currentColum;
		END
		ELSE IF @lastColum != @currentColum
		BEGIN
			SET @conditions = @conditions + ') OR (' + @currentColum + '=' + @data;
			SET @lastColum = @currentColum
		END
		ELSE IF @lastColum = @currentColum
		BEGIN
			SET @conditions = @conditions + ' OR ' + @currentColum + '=' + @data;
		END

		SET @cont = @cont + 1;
	END

	SET @query = @query + @conditions + ')';
	
	EXEC(@query)
END
