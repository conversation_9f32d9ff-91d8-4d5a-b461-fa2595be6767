﻿CREATE TABLE [dbo].[PurchasesPickList3Items] (
    [purchasesPickList3ItemID]   INT           IDENTITY (1, 1) NOT NULL,
    [purchasesPickList3ItemName] NVARCHAR (64) NOT NULL,
    [purchasesPickList3ItemCode] NVARCHAR (64) NOT NULL,
    [sortOrder]                  INT           NOT NULL,
    [active]                     BIT           NOT NULL,
    CONSTRAINT [PK_PurchasesPickList3Items] PRIMARY KEY CLUSTERED ([purchasesPickList3ItemID] ASC),
    CONSTRAINT [UK_PurchasesPickList3Items_purchasesPickList3ItemName] UNIQUE NONCLUSTERED ([purchasesPickList3ItemName] ASC)
);


GO
CREATE TRIGGER [dbo].[PurchasesPickList3Items.InsertUpdateDelete]
    ON [dbo].[PurchasesPickList3Items]
    FOR INSERT, UPDATE, DELETE
AS 
BEGIN
    SET NOCOUNT ON;

    DECLARE @Activity  NVARCHAR (8), @id INT;

    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'updated'
    END

    IF EXISTS (SELECT * FROM inserted) AND NOT EXISTS(SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'inserted'
    END

    IF EXISTS (SELECT * FROM deleted) AND NOT EXISTS(SELECT * FROM inserted)
    BEGIN
        SET @Activity = 'deleted'
    END

    

    IF (@Activity = 'deleted')
    BEGIN
        DECLARE cur CURSOR FOR SELECT purchasesPickList3ItemID FROM deleted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'PurchasesPickList3Items', @id, 'purchasesPickList3ItemID',
                (
                    SELECT *
                    FROM deleted i 
                    WHERE i.purchasesPickList3ItemID =  @id
                    FOR JSON AUTO
                ),0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
    ELSE
    BEGIN
        DECLARE cur CURSOR
        FOR SELECT purchasesPickList3ItemID
        FROM inserted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'PurchasesPickList3Items', @id, 'purchasesPickList3ItemID',
                (
                    SELECT *
                    FROM inserted i 
                    WHERE i.purchasesPickList3ItemID =  @id
                    FOR JSON AUTO
                ), 0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
END

GO
DISABLE TRIGGER [dbo].[PurchasesPickList3Items.InsertUpdateDelete]
    ON [dbo].[PurchasesPickList3Items];

