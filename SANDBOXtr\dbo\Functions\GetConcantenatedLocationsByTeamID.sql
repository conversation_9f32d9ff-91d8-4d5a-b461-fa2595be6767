﻿

CREATE FUNCTION [dbo].[GetConcantenatedLocationsByTeamID]
(
	@TeamID int,
	@Delimeter nvarchar(8)
)
RETURNS nvarchar(2048)
AS
BEGIN

	DECLARE @ConcantenatedList nvarchar(2048)

	SET @ConcantenatedList = ''

	SELECT @ConcantenatedList = @ConcantenatedList + @Delimeter + Locations.name
	FROM         TeamsLocationsMap INNER JOIN
						  Locations ON TeamsLocationsMap.locationID = Locations.locationID
	WHERE TeamsLocationsMap.teamID = @TeamID
	ORDER BY Locations.name

	IF DATALENGTH(@ConcantenatedList) > 0
	BEGIN
		SET @ConcantenatedList = RIGHT(@ConcantenatedList, ((DATALENGTH(@ConcantenatedList)/2) - (DATALENGTH(@Delimeter)/2)))
	END

	RETURN @ConcantenatedList

END