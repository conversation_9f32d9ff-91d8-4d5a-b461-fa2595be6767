﻿CREATE VIEW [dbo].[NotesExtended]
AS
SELECT        N.noteID, CASE WHEN CF.featureKey LIKE 'Tour.%' THEN
                             (SELECT        externalTourID
                               FROM            Tours
                               WHERE        tourID = N .parentID) WHEN CF.featureKey LIKE 'Customer.%' THEN
                             (SELECT        externalCustomerID
                               FROM            Customers
                               WHERE        customerID = N .parentID) END AS externalParentID
FROM            dbo.Notes AS N INNER JOIN
                         dbo.CustomFields AS CF ON N.fieldID = CF.fieldID
