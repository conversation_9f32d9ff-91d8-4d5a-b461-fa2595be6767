﻿CREATE TABLE [dbo].[Regions] (
    [regionID]   INT           IDENTITY (1, 1) NOT NULL,
    [regionName] NVARCHAR (64) NOT NULL,
    [regionCode] NVARCHAR (64) NOT NULL,
    [sortOrder]  INT           NOT NULL,
    [active]     BIT           NOT NULL,
    CONSTRAINT [PK_Regions] PRIMARY KEY CLUSTERED ([regionID] ASC),
    CONSTRAINT [UK_Regions_regionName] UNIQUE NONCLUSTERED ([regionName] ASC)
);


GO
CREATE TRIGGER [dbo].[Regions.InsertUpdateDelete]
    ON [dbo].[Regions]
    FOR INSERT, UPDATE, DELETE
AS 
BEGIN
    SET NOCOUNT ON;

    DECLARE @Activity  NVARCHAR (8), @id INT;

    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'updated'
    END

    IF EXISTS (SELECT * FROM inserted) AND NOT EXISTS(SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'inserted'
    END

    IF EXISTS (SELECT * FROM deleted) AND NOT EXISTS(SELECT * FROM inserted)
    BEGIN
        SET @Activity = 'deleted'
    END

    

    IF (@Activity = 'deleted')
    BEGIN
        DECLARE cur CURSOR FOR SELECT regionID FROM deleted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'Regions', @id, 'regionID',
                (
                    SELECT *
                    FROM deleted i 
                    WHERE i.regionID =  @id
                    FOR JSON AUTO
                ),0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
    ELSE
    BEGIN
        DECLARE cur CURSOR
        FOR SELECT regionID
        FROM inserted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'Regions', @id, 'regionID',
                (
                    SELECT *
                    FROM inserted i 
                    WHERE i.regionID =  @id
                    FOR JSON AUTO
                ), 0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
END

GO
DISABLE TRIGGER [dbo].[Regions.InsertUpdateDelete]
    ON [dbo].[Regions];

