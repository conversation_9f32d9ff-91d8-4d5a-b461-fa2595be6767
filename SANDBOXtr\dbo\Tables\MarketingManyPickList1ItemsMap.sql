﻿CREATE TABLE [dbo].[MarketingManyPickList1ItemsMap] (
    [marketingManyPickList1ItemMapID] INT IDENTITY (1, 1) NOT NULL,
    [tourID]                          INT NOT NULL,
    [marketingManyPickList1ItemID]    INT NOT NULL,
    CONSTRAINT [PK_MarketingManyPickList1ItemsMap] PRIMARY KEY CLUSTERED ([marketingManyPickList1ItemMapID] ASC),
    CONSTRAINT [FK_MarketingManyPickList1ItemsMap_marketingManyPickList1ItemID] FOREIGN KEY ([marketingManyPickList1ItemID]) REFERENCES [dbo].[MarketingManyPickList1Items] ([marketingManyPickList1ItemID]),
    CONSTRAINT [FK_MarketingManyPickList1ItemsMap_tourID] FOREIGN KEY ([tourID]) REFERENCES [dbo].[Tours] ([tourID])
);


GO
CREATE TRIGGER [dbo].[MarketingManyPickList1ItemsMap.InsertUpdateDelete]
    ON [dbo].[MarketingManyPickList1ItemsMap]
    FOR INSERT, UPDATE, DELETE
AS 
BEGIN
    SET NOCOUNT ON;

    DECLARE @Activity  NVARCHAR (8), @id INT;

    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'updated'
    END

    IF EXISTS (SELECT * FROM inserted) AND NOT EXISTS(SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'inserted'
    END

    IF EXISTS (SELECT * FROM deleted) AND NOT EXISTS(SELECT * FROM inserted)
    BEGIN
        SET @Activity = 'deleted'
    END

    

    IF (@Activity = 'deleted')
    BEGIN
        DECLARE cur CURSOR FOR SELECT marketingManyPickList1ItemMapID FROM deleted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'MarketingManyPickList1ItemsMap', @id, 'marketingManyPickList1ItemMapID',
                (
                    SELECT *
                    FROM deleted i 
                    WHERE i.marketingManyPickList1ItemMapID =  @id
                    FOR JSON AUTO
                ),0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
    ELSE
    BEGIN
        DECLARE cur CURSOR
        FOR SELECT marketingManyPickList1ItemMapID
        FROM inserted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'MarketingManyPickList1ItemsMap', @id, 'marketingManyPickList1ItemMapID',
                (
                    SELECT *
                    FROM inserted i 
                    WHERE i.marketingManyPickList1ItemMapID =  @id
                    FOR JSON AUTO
                ), 0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
END

GO
DISABLE TRIGGER [dbo].[MarketingManyPickList1ItemsMap.InsertUpdateDelete]
    ON [dbo].[MarketingManyPickList1ItemsMap];

