﻿CREATE TABLE [dbo].[LocationsGiftsMap] (
    [locationID] INT NOT NULL,
    [giftID]     INT NOT NULL,
    [active]     BIT NOT NULL,
    CONSTRAINT [PK_LocationsGiftsMap] PRIMARY KEY CLUSTERED ([locationID] ASC, [giftID] ASC)
);


GO
CREATE TRIGGER [dbo].[LocationsGiftsMap.InsertUpdateDelete]
    ON [dbo].[LocationsGiftsMap]
    FOR INSERT, UPDATE, DELETE
AS 
BEGIN
    SET NOCOUNT ON;

    DECLARE @Activity  NVARCHAR (8), @id INT;

    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'updated'
    END

    IF EXISTS (SELECT * FROM inserted) AND NOT EXISTS(SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'inserted'
    END

    IF EXISTS (SELECT * FROM deleted) AND NOT EXISTS(SELECT * FROM inserted)
    BEGIN
        SET @Activity = 'deleted'
    END

    

    IF (@Activity = 'deleted')
    BEGIN
        DECLARE cur CURSOR FOR SELECT locationID FROM deleted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'LocationsGiftsMap', @id, 'locationID',
                (
                    SELECT *
                    FROM deleted i 
                    WHERE i.locationID =  @id
                    FOR JSON AUTO
                ),0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
    ELSE
    BEGIN
        DECLARE cur CURSOR
        FOR SELECT locationID
        FROM inserted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'LocationsGiftsMap', @id, 'locationID',
                (
                    SELECT *
                    FROM inserted i 
                    WHERE i.locationID =  @id
                    FOR JSON AUTO
                ), 0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
END

GO
DISABLE TRIGGER [dbo].[LocationsGiftsMap.InsertUpdateDelete]
    ON [dbo].[LocationsGiftsMap];

