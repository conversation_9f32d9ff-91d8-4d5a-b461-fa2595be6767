﻿CREATE TABLE [dbo].[Users] (
    [userID]         INT              IDENTITY (1, 1) NOT NULL,
    [active]         BIT              CONSTRAINT [DF_Users_active] DEFAULT ((0)) NOT NULL,
    [roleID]         INT              NULL,
    [firstName]      NVARCHAR (64)    NOT NULL,
    [lastName]       NVARCHAR (64)    NOT NULL,
    [fullName]       NVARCHAR (130)   NULL,
    [primaryPhone]   NVARCHAR (32)    NULL,
    [secondaryPhone] NVARCHAR (32)    NULL,
    [email]          NVARCHAR (64)    NULL,
    [address]        NVARCHAR (64)    NULL,
    [address2]       NVARCHAR (64)    NULL,
    [city]           NVARCHAR (64)    NULL,
    [stateID]        INT              NULL,
    [zipcode]        NVARCHAR (16)    NULL,
    [userAccountID]  UNIQUEIDENTIFIER NULL,
    CONSTRAINT [PK_Users] PRIMARY KEY CLUSTERED ([userID] ASC),
    CONSTRAINT [FK_Users_Roles] FOREIGN KEY ([roleID]) REFERENCES [dbo].[Roles] ([roleID]),
    CONSTRAINT [UK_Users_fullName] UNIQUE NONCLUSTERED ([fullName] ASC)
);


GO
CREATE TRIGGER [dbo].[Users.InsertUpdateDelete]
    ON [dbo].[Users]
    FOR INSERT, UPDATE, DELETE
AS 
BEGIN
    SET NOCOUNT ON;

    DECLARE @Activity  NVARCHAR (8), @id INT;

    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'updated'
    END

    IF EXISTS (SELECT * FROM inserted) AND NOT EXISTS(SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'inserted'
    END

    IF EXISTS (SELECT * FROM deleted) AND NOT EXISTS(SELECT * FROM inserted)
    BEGIN
        SET @Activity = 'deleted'
    END

    

    IF (@Activity = 'deleted')
    BEGIN
        DECLARE cur CURSOR FOR SELECT userID FROM deleted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'Users', @id, 'userID',
                (
                    SELECT *
                    FROM deleted i 
                    WHERE i.userID =  @id
                    FOR JSON AUTO
                ),0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
    ELSE
    BEGIN
        DECLARE cur CURSOR
        FOR SELECT userID
        FROM inserted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'Users', @id, 'userID',
                (
                    SELECT *
                    FROM inserted i 
                    WHERE i.userID =  @id
                    FOR JSON AUTO
                ), 0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
END

GO
DISABLE TRIGGER [dbo].[Users.InsertUpdateDelete]
    ON [dbo].[Users];


GO
-- ==============================================
-- Create dml trigger template Azure SQL Database 
-- ==============================================
-- Drop the dml trigger if it already exists


CREATE TRIGGER TR_User_Insert
   ON  dbo.Users
   AFTER INSERT
AS 
BEGIN
	DECLARE @nameFull nvarchar(500), @id int;
	SELECT @nameFull = CONCAT(firstName, ' ', lastName), @id = userID FROM inserted
	UPDATE Users SET 
	fullName = @nameFull
	WHERE userID = @id;
END

GO
DISABLE TRIGGER [dbo].[TR_User_Insert]
    ON [dbo].[Users];


GO
-- ==============================================
-- Create dml trigger template Azure SQL Database 
-- ==============================================
-- Drop the dml trigger if it already exists


CREATE TRIGGER [dbo].[TR_User_Update]
   ON  dbo.Users
   AFTER UPDATE
AS 
BEGIN
	DECLARE @fullName nvarchar(500), @id int, @lastFullName nvarchar(500);
	SELECT @lastFullName = CONCAT(firstName, ' ', lastName) FROM deleted;
	SELECT @fullName = CONCAT(firstName, ' ', lastName), @id = userID FROM inserted
	IF(@lastFullName != @fullName)
		UPDATE Users SET 
		fullName = @fullName
		WHERE userID = @id;
END

GO
DISABLE TRIGGER [dbo].[TR_User_Update]
    ON [dbo].[Users];

