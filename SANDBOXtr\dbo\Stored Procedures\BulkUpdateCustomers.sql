﻿CREATE PROCEDURE [dbo].[BulkUpdateCustomers]
    @DataTableParam dbo.CustomerType READONLY -- Table-Valued Parameter
AS
BEGIN
    SET NOCOUNT ON;

    -- Use MERGE to update existing rows and insert new rows
    MERGE INTO Customers AS target
    USING @DataTableParam AS source
    ON target.customerID = source.customerID

    -- When there is a match, update the customer data
    WHEN MATCHED THEN
        UPDATE SET
            target.externalCustomerID = source.externalCustomerID,
            target.customerTypeID = source.customerTypeID,
            target.customerStatusTypeID = source.customerStatusTypeID,
            target.customerDispositionID = source.customerDispositionID,
            target.firstName = source.firstName,
            target.lastName = source.lastName,
            target.age = source.age,
            target.sex = source.sex,
            target.customersPickList1ItemID = source.customersPickList1ItemID,
            target.guestFirstName = source.guestFirstName,
            target.guestLastName = source.guestLastName,
            target.guestTypeID = source.guestTypeID,
            target.guestAge = source.guestAge,
            target.guestSex = source.guestSex,
            target.customersPickList2ItemID = source.customersPickList2ItemID,
            target.incomeTypeID = source.incomeTypeID,
            target.customersPickList3ItemID = source.customersPickList3ItemID,
            target.customersText1 = source.customersText1,
            target.customersDecimal1 = source.customersDecimal1,
            target.customersBool1 = source.customersBool1,
            target.customersDate1 = source.customersDate1,
            target.customersTime1 = source.customersTime1,
            target.doNotCall = source.doNotCall,
            target.primaryPhone = source.primaryPhone,
            target.secondaryPhone = source.secondaryPhone,
            target.streetAddress = source.streetAddress,
            target.streetAddress2 = source.streetAddress2,
            target.city = source.city,
            target.stateID = source.stateID,
            target.zipcode = source.zipcode,
            target.countryID = source.countryID,
            target.businessName = source.businessName,
            target.email = source.email,
            target.customersPickList4ItemID = source.customersPickList4ItemID,
            target.customersText2 = source.customersText2,
            target.customersText3 = source.customersText3,
            target.customersDecimal2 = source.customersDecimal2,
            target.customersBool2 = source.customersBool2,
            target.customersDate2 = source.customersDate2,
            target.customersTime2 = source.customersTime2,
            target.apiConnectionID = source.apiConnectionID,
            target.apiExternalCustomerID = source.apiExternalCustomerID,
            target.apiExternalConnectionID = source.apiExternalConnectionID,
			target.insertTimeStamp = target.insertTimeStamp,
            target.updateTimeStamp = GETDATE(), 
            target.city2 = source.city2,
            target.stateID2 = source.stateID2,
            target.zipcode2 = source.zipcode2,
            target.countryID2 = source.countryID2,
            target.userAddDoNotCall = source.userAddDoNotCall,
            target.insertDoNotCall = source.insertDoNotCall,
            target.streetAddressGuest = source.streetAddressGuest,
            target.streetAddress2Guest = source.streetAddress2Guest,
            target.emailGuest = source.emailGuest;
END;