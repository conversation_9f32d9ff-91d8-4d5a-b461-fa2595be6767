﻿CREATE TABLE [dbo].[TeamsLocationsMap] (
    [teamID]     INT NOT NULL,
    [locationID] INT NOT NULL,
    CONSTRAINT [PK_TeamsLocationsMap] PRIMARY KEY CLUSTERED ([teamID] ASC, [locationID] ASC)
);


GO
CREATE TRIGGER [dbo].[TeamsLocationsMap.InsertUpdateDelete]
    ON [dbo].[TeamsLocationsMap]
    FOR INSERT, UPDATE, DELETE
AS 
BEGIN
    SET NOCOUNT ON;

    DECLARE @Activity  NVARCHAR (8), @id INT;

    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'updated'
    END

    IF EXISTS (SELECT * FROM inserted) AND NOT EXISTS(SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'inserted'
    END

    IF EXISTS (SELECT * FROM deleted) AND NOT EXISTS(SELECT * FROM inserted)
    BEGIN
        SET @Activity = 'deleted'
    END

    

    IF (@Activity = 'deleted')
    BEGIN
        DECLARE cur CURSOR FOR SELECT teamID FROM deleted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHIL<PERSON> @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'TeamsLocationsMap', @id, 'teamID',
                (
                    SELECT *
                    FROM deleted i 
                    WHERE i.teamID =  @id
                    FOR JSON AUTO
                ),0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
    ELSE
    BEGIN
        DECLARE cur CURSOR
        FOR SELECT teamID
        FROM inserted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'TeamsLocationsMap', @id, 'teamID',
                (
                    SELECT *
                    FROM inserted i 
                    WHERE i.teamID =  @id
                    FOR JSON AUTO
                ), 0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
END

GO
DISABLE TRIGGER [dbo].[TeamsLocationsMap.InsertUpdateDelete]
    ON [dbo].[TeamsLocationsMap];

