﻿CREATE TABLE [dbo].[MilestonesTourStatusTypes] (
    [milestonesTourStatusTypeID] INT      IDENTITY (1, 1) NOT NULL,
    [tourID]                     INT      NOT NULL,
    [tourStatusTypeID]           INT      NOT NULL,
    [insertTimeStamp]            DATETIME CONSTRAINT [DF_MilestonesTourStatusTypes_insertTimeStamp] DEFAULT (getdate()) NOT NULL,
    CONSTRAINT [PK_MilestonesTourStatusTypes] PRIMARY KEY CLUSTERED ([milestonesTourStatusTypeID] ASC),
    CONSTRAINT [FK_MilestonesTourStatusTypes_tourID] FOREIGN KEY ([tourID]) REFERENCES [dbo].[Tours] ([tourID]),
    CONSTRAINT [FK_MilestonesTourStatusTypes_tourStatusTypeID] FOREIGN KEY ([tourStatusTypeID]) REFERENCES [dbo].[TourStatusTypes] ([tourStatusTypeID])
);


GO
CREATE TRIGGER [dbo].[MilestonesTourStatusTypes.InsertUpdateDelete]
    ON [dbo].[MilestonesTourStatusTypes]
    FOR INSERT, UPDATE, DELETE
AS 
BEGIN
    SET NOCOUNT ON;

    DECLARE @Activity  NVARCHAR (8), @id INT;

    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'updated'
    END

    IF EXISTS (SELECT * FROM inserted) AND NOT EXISTS(SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'inserted'
    END

    IF EXISTS (SELECT * FROM deleted) AND NOT EXISTS(SELECT * FROM inserted)
    BEGIN
        SET @Activity = 'deleted'
    END

    

    IF (@Activity = 'deleted')
    BEGIN
        DECLARE cur CURSOR FOR SELECT milestonesTourStatusTypeID FROM deleted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'MilestonesTourStatusTypes', @id, 'milestonesTourStatusTypeID',
                (
                    SELECT *
                    FROM deleted i 
                    WHERE i.milestonesTourStatusTypeID =  @id
                    FOR JSON AUTO
                ),0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
    ELSE
    BEGIN
        DECLARE cur CURSOR
        FOR SELECT milestonesTourStatusTypeID
        FROM inserted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'MilestonesTourStatusTypes', @id, 'milestonesTourStatusTypeID',
                (
                    SELECT *
                    FROM inserted i 
                    WHERE i.milestonesTourStatusTypeID =  @id
                    FOR JSON AUTO
                ), 0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
END

GO
DISABLE TRIGGER [dbo].[MilestonesTourStatusTypes.InsertUpdateDelete]
    ON [dbo].[MilestonesTourStatusTypes];

