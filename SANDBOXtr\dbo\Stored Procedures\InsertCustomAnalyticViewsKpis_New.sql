﻿CREATE PROCEDURE [dbo].[InsertCustomAnalyticViewsKpis_New]
(
	@customAnalyticViewID int,
	@name varchar(64),
	@code varchar(64),
	@property<PERSON>ey varchar(64),
	@column<PERSON>ey varchar(64),
	@formatType nvarchar(4),
	@isCalculated bit,
	@sortOrder int OUTPUT,
	@drillThroughUrl nvarchar(64) = NULL,
	@isVisible bit = 1, 
	@expression text = NULL
)
AS
BEGIN

DECLARE @nameValueNumberDot2 nvarchar(16) SET @nameValueNumberDot2 = '{0} ~ {1:N2}'
DECLARE @valueNumberDot2 nvarchar(16) SET @valueNumberDot2 = '{0:N2}'
DECLARE @nameValuePercent nvarchar(16) SET @nameValuePercent = '{0} ~ {1:N0}%'
DECLARE @valuePercent nvarchar(16) SET @valuePercent = '{0:N0}%'
DECLARE @nameValueCurrency nvarchar(16) SET @nameValueCurrency = '{0} ~ {1:C0}'
DECLARE @valueCurrency nvarchar(16) SET @valueCurrency = '{0:C0}'
DECLARE @nameValueNumber nvarchar(16) SET @nameValueNumber = '{0} ~ {1:N0}'
DECLARE @valueNumber nvarchar(16) SET @valueNumber = '{0:N0}'

DECLARE @nameValueDataFormatString nvarchar(16)
DECLARE @valueDataFormatString nvarchar(16)

SET @nameValueDataFormatString =
	CASE @formatType
		WHEN '#.2' THEN @nameValueNumberDot2
		WHEN '%' THEN @nameValuePercent
		WHEN '$' THEN @nameValueCurrency
		ELSE @nameValueNumber
	END

SET @valueDataFormatString =
	CASE @formatType
		WHEN '#.2' THEN @valueNumberDot2
		WHEN '%' THEN @valuePercent
		WHEN '$' THEN @valueCurrency
		ELSE @valueNumber
	END

	INSERT INTO CustomAnalyticViewsKpis(customAnalyticViewID,propertyKey,name,code,columnKey,nameValueDataFormatString,valueDataFormatString,drillThroughUrl,
		isCalculated,isVisible,sortOrder,expression)
	VALUES(@customAnalyticViewID,@propertyKey,@name,@code,@columnKey,@nameValueDataFormatString,@valueDataFormatString,@drillThroughUrl,@isCalculated,
		@isVisible,@sortOrder,@expression)
	
	SET @sortOrder = @sortOrder + 1

END