﻿CREATE TABLE [dbo].[SaleStatusTypes] (
    [saleStatusTypeID]   INT           NOT NULL,
    [saleStatusTypeName] NVARCHAR (64) NOT NULL,
    [saleStatusTypeCode] NVARCHAR (64) NULL,
    [sortOrder]          INT           NOT NULL,
    CONSTRAINT [PK_SaleStatusTypes] PRIMARY KEY CLUSTERED ([saleStatusTypeID] ASC),
    CONSTRAINT [UK_SaleStatusTypes_saleStatusTypeName] UNIQUE NONCLUSTERED ([saleStatusTypeName] ASC)
);


GO
CREATE TRIGGER [dbo].[SaleStatusTypes.InsertUpdateDelete]
    ON [dbo].[SaleStatusTypes]
    FOR INSERT, UPDATE, DELETE
AS 
BEGIN
    SET NOCOUNT ON;

    DECLARE @Activity  NVARCHAR (8), @id INT;

    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'updated'
    END

    IF EXISTS (SELECT * FROM inserted) AND NOT EXISTS(SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'inserted'
    END

    IF EXISTS (SELECT * FROM deleted) AND NOT EXISTS(SELECT * FROM inserted)
    BEGIN
        SET @Activity = 'deleted'
    END

    

    IF (@Activity = 'deleted')
    BEGIN
        DECLARE cur CURSOR FOR SELECT saleStatusTypeID FROM deleted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'SaleStatusTypes', @id, 'saleStatusTypeID',
                (
                    SELECT *
                    FROM deleted i 
                    WHERE i.saleStatusTypeID =  @id
                    FOR JSON AUTO
                ),0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
    ELSE
    BEGIN
        DECLARE cur CURSOR
        FOR SELECT saleStatusTypeID
        FROM inserted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'SaleStatusTypes', @id, 'saleStatusTypeID',
                (
                    SELECT *
                    FROM inserted i 
                    WHERE i.saleStatusTypeID =  @id
                    FOR JSON AUTO
                ), 0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
END

GO
DISABLE TRIGGER [dbo].[SaleStatusTypes.InsertUpdateDelete]
    ON [dbo].[SaleStatusTypes];

