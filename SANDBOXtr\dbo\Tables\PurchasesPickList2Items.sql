﻿CREATE TABLE [dbo].[PurchasesPickList2Items] (
    [purchasesPickList2ItemID]   INT           IDENTITY (1, 1) NOT NULL,
    [purchasesPickList2ItemCode] NVARCHAR (64) NULL,
    [purchasesPickList2ItemName] NVARCHAR (64) NOT NULL,
    [sortOrder]                  INT           NOT NULL,
    [active]                     BIT           NOT NULL,
    CONSTRAINT [PK_PurchasesPickList2Items] PRIMARY KEY CLUSTERED ([purchasesPickList2ItemID] ASC),
    CONSTRAINT [UK_PurchasesPickList2Items_purchasesPickList2ItemName] UNIQUE NONCLUSTERED ([purchasesPickList2ItemName] ASC)
);


GO
CREATE TRIGGER [dbo].[PurchasesPickList2Items.InsertUpdateDelete]
    ON [dbo].[PurchasesPickList2Items]
    FOR INSERT, UPDATE, DELETE
AS 
BEGIN
    SET NOCOUNT ON;

    DECLARE @Activity  NVARCHAR (8), @id INT;

    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'updated'
    END

    IF EXISTS (SELECT * FROM inserted) AND NOT EXISTS(SELECT * FROM deleted)
    BEGIN
        SET @Activity = 'inserted'
    END

    IF EXISTS (SELECT * FROM deleted) AND NOT EXISTS(SELECT * FROM inserted)
    BEGIN
        SET @Activity = 'deleted'
    END

    

    IF (@Activity = 'deleted')
    BEGIN
        DECLARE cur CURSOR FOR SELECT purchasesPickList2ItemID FROM deleted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'PurchasesPickList2Items', @id, 'purchasesPickList2ItemID',
                (
                    SELECT *
                    FROM deleted i 
                    WHERE i.purchasesPickList2ItemID =  @id
                    FOR JSON AUTO
                ),0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
    ELSE
    BEGIN
        DECLARE cur CURSOR
        FOR SELECT purchasesPickList2ItemID
        FROM inserted;
        OPEN cur;
        FETCH NEXT FROM cur INTO @id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            INSERT INTO [dbo].[WorkFlows] (OperationTime, OperationType, TableName, TableId, PrimaryKey, TableData, Completed)
            SELECT GETUTCDATE(), @Activity,'PurchasesPickList2Items', @id, 'purchasesPickList2ItemID',
                (
                    SELECT *
                    FROM inserted i 
                    WHERE i.purchasesPickList2ItemID =  @id
                    FOR JSON AUTO
                ), 0
            FETCH NEXT FROM cur INTO @id;
        END;
        CLOSE cur;
        DEALLOCATE cur;
    END
END

GO
DISABLE TRIGGER [dbo].[PurchasesPickList2Items.InsertUpdateDelete]
    ON [dbo].[PurchasesPickList2Items];

