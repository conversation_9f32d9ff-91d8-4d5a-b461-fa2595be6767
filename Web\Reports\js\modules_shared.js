﻿/* =========================== INITIAL ACTIONS =========================== */

var customKpis = [];
var typeReports = [];
var get_reports_list = [];
var file_local_active = false;
var reportFilterID = 0;
var listKpis = [];
var ReportFilters = null;
let runCount = 0;
const intervalId = setInterval(updateDropdownOptions, 3000);
var initialFilters = [];
var reportSelectedName = '';
let newColumnDefs;
let previusly_reportSelected;
let reportIdSelected = 0;
var allRowsGrid;
let initial_date;
let last_date;

/* =========================== READY ACTIONS =========================== */

$().ready(async function () {

    var userFullName = userName;
    try {
        // Call fetchUserDataByUserName and store the result in userData
        var userData = await fetchUserDataByUserName(userFullName);

        if (userData && userData.length > 0) {
            // Extract UserId from the response
            var userId = userData[0].UserId;

            // Call getReportsUsersMapByUserId and store the result in rtnData
            const rtnData = await getReportsUsersMapByUserId(userId);
            if (rtnData && rtnData.length > 0) {
                // Extract the ReportId from the response (assuming it's in the first item)
                var reportId = rtnData[0].ReportId;

                // Call fetchReportNames with the extracted ReportId
                const reportNames = await fetchReportNames(reportId);

            } else {
            }
        } else {
        }
    } catch (error) {
    }

    var reportNamesDropdown = document.getElementById("reportNamesDropdown");
    reportNamesDropdown.innerHTML = "";
    var reportTypeOnDropdown = document.getElementById("reportTypeOnDropdown");
    var dropDownDateRanges = document.getElementById("dropDownDateRanges");
    var dropDownYearRanges = document.getElementById("dropDownYearRanges");
    /*var ddlKPIsGroupBy = document.getElementById("ddlKPIsGroupBy");*/

    getReports((data) => {
        for (var key in data) {
            var dataArray = data[key];
            for (var i = 0; i < dataArray.length; i++) {

                typeReports.push(dataArray[i]);
                var option_select = document.createElement("option");
                option_select.text = dataArray[i].Item1;
                if (dataArray[i].Item2 != "")
                    option_select.value = dataArray[i].Item2;
                else
                    option_select.value = dataArray[i].Item6;
                reportNamesDropdown.add(option_select);
            }
        }

        getReportsOnType((data) => {
            for (var key in data) {
                var dataArray = data[key];
                for (var i = 0; i < dataArray.length; i++) {
                    typeReports.push(dataArray[i])
                    var option_select = document.createElement("option");

                    option_select.text = dataArray[i].reportName;
                    option_select.value = dataArray[i].reportId;
                    reportTypeOnDropdown.add(option_select);

                }
            }
        });

        getDateRange((data) => {
            for (var key in data) {
                var dataArray = data[key];
                if (key == 'dateRanges') {
                    for (var i = 0; i < dataArray.length; i++) {
                        typeReports.push(dataArray[i])
                        var option_select = document.createElement("option");
                        option_select.text = dataArray[i].value;
                        option_select.value = dataArray[i].key;
                        dropDownDateRanges.add(option_select);
                    }
                } else if (key == 'yearRanges') {
                    for (var i = 0; i < dataArray.length; i++) {
                        typeReports.push(dataArray[i])
                        var option_select = document.createElement("option");
                        option_select.text = dataArray[i].value;
                        option_select.value = dataArray[i].key;
                        dropDownYearRanges.add(option_select);
                    }
                }
            }
        });


    });

    var locationsUser = document.getElementById("locationsUser");

    getLocations((data) => {
        for (var key in data) {
            var dataArray = data[key];
            for (var i = 0; i < dataArray.length; i++) {
                var option_select = document.createElement("option");
                option_select.text = dataArray[i].Name;
                option_select.value = dataArray[i].LocationID;
                locationsUser.add(option_select);
            }
        }
    });

    populateKpi(null);

    toggleAccordion();

    // Obtén el primer elemento con la clase SalesCriteriaHeader
    const salesCriteriaHeader = document.querySelector('.SalesCriteriaHeader');

    // Verifica si el elemento tiene al menos dos hijos
    if (salesCriteriaHeader && salesCriteriaHeader.children.length > 1) {
        // Selecciona el segundo hijo y ocúltalo
        salesCriteriaHeader.children[1].style.display = 'none'; // Opción para ocultar completamente
        // salesCriteriaHeader.children[1].style.visibility = 'hidden'; // Opción para ocultar pero mantener el espacio
    }

});

document.addEventListener('DOMContentLoaded', function () {
    const dropDownDateRanges = document.getElementById('dropDownDateRanges');
    const dropDownYearRanges = document.getElementById('dropDownYearRanges');

    dropDownDateRanges.addEventListener('change', function () {
        updateDateRange(null);
        updateYearRange(null);
    });

    dropDownYearRanges.addEventListener('change', function () {
        updateDateRange(null);
        updateYearRange(null);
    });
});

runAfterDocumentReady();

/* =========================== KPI FUNCTIONS =========================== */

function populateKpi(kpi) {
    var ddlKPIsGroupBy = document.getElementById("ddlKPIsGroupBy");
    ddlKPIsGroupBy.innerHTML = "";
    getKPIsDataByReportId((data) => {
        for (var key in data) {
            var dataArray = data[key];
            for (var i = 0; i < dataArray.length; i++) {
                if (dataArray[i].headerText != "") {
                    var option_select = document.createElement("option");
                    option_select.text = dataArray[i].headerText;
                    option_select.value = (dataArray[i].pivot !== undefined) ? dataArray[i].pivot : dataArray[i].headerText;

                    ddlKPIsGroupBy.add(option_select);
                }
            }
        }

        if (kpi != null) {
            for (var i = 0; i < ddlKPIsGroupBy.options.length; i++) {
                if (ddlKPIsGroupBy.options[i].value == kpi) {
                    ddlKPIsGroupBy.options[i].selected = true;
                    break;
                }
            }
        }
    });
}

/* =========================== REPORT ACTIONS =========================== */

async function onReportTypeOnSelected(dropdown) {

    let name;
    var ddlKPIsGroupBy = document.getElementById("ddlKPIsGroupBy");
    if (dropdown.options[dropdown.selectedIndex].text != "--") {

        if (dropdown.options[dropdown.selectedIndex].text != "Customer Name" && dropdown.options[dropdown.selectedIndex].text != "Users Name" && dropdown.options[dropdown.selectedIndex].text != "User FullName")
            name = dropdown.options[dropdown.selectedIndex].text + ".name";
        else if (dropdown.options[dropdown.selectedIndex].text == "Customer Name")
            name = dropdown.options[dropdown.selectedIndex].text + ".customerName";
        else if (dropdown.options[dropdown.selectedIndex].text == "Users Name")
            name = dropdown.options[dropdown.selectedIndex].text + ".name";
        else if (dropdown.options[dropdown.selectedIndex].text == "User FullName")
            name = dropdown.options[dropdown.selectedIndex].text + ".userFullName";



        // Verificar si la opción ya existe en el menú desplegable
        var existingOption = Array.from(ddlKPIsGroupBy.options).find(option => option.text === name);

        if (existingOption) {
            // La opción ya existe, solo activa el selected true
            existingOption.selected = true;
        } else {
            // La opción no existe, agrégala
            var option_select = document.createElement("option");
            option_select.text = name;
            option_select.value = name;

            for (var i = 0; i < ddlKPIsGroupBy.options.length; i++) {
                ddlKPIsGroupBy.options[i].selected = false;
            }

            ddlKPIsGroupBy.add(option_select);
            option_select.selected = true;
        }

        ddlKPIsGroupBy.disabled = true;
        ddlKPIsGroupBy.style.setProperty("background-color", "#ffffff", "important");
    }
    else {
        ddlKPIsGroupBy.disabled = false;
        ddlKPIsGroupBy.selectedIndex = 0;
    }
}

async function onReportSelected(dropdown) {

    var flag = false;

    if (dropdown.options[dropdown.selectedIndex].text != '--') {
        await clearReportTool();
        const btnDeleteReport = document.getElementById('btnDeleteReport');
        btnDeleteReport.style.display = 'inline';
        flag = true;
    }
    else if ($("#reportName").val() == previusly_reportSelected && dropdown.options[dropdown.selectedIndex].text == '--') {
        await clearReportTool();
        const btnDeleteReport = document.getElementById('btnDeleteReport');
        btnDeleteReport.style.display = 'none';
    }

    if (flag) {

        await clearDateTimeCriterial();
        await clearCancellationCriteriaFilters();
        await clearCustomersCriteriaFilters();
        await clearBasicCriterial();
        await clearMarketingCriteriaFilters();
        await clearPurchaseCriteriaFilters();
        await clearSalesCriteriaFilters();
        await clearTourCriteriaFilters();
        await clearLeadCriteriaFilters();

        reportFilterID = 0;
        selectedReportId = dropdown.value; // Get the selected ReportId
        reportSelectedName = dropdown.options[dropdown.selectedIndex].text;
        previusly_reportSelected = dropdown.options[dropdown.selectedIndex].text;
        const regex = /^local_.*$/;

        if (regex.test(selectedReportId)) {
            file_local_active = true;
            document.querySelector('.fileInputData').style.display = 'block';
            document.querySelectorAll('.options').forEach(element => element.disabled = true);
        }
        else {
            file_local_active = false;
            document.querySelector('.fileInputData').style.display = 'none';
            document.querySelectorAll('.options').forEach(element => element.disabled = false);
            if (selectedReportId > 0) {
                try {
                    $('#loader').show();
                    selectedReportId = parseInt(selectedReportId);

                    reportSelectedFromDropdown = true;

                    reportDetail = await fetchReportById(selectedReportId);
                    fetchReportUsersById(selectedReportId);
                    [joinDefinitions, reportKPIRelationship] = await Promise.all([
                        fetchJoinDefinitions(selectedReportId),
                        fetchReportKPIRelationship(selectedReportId),
                    ]);

                    reportFilters = reportDetail.filters;

                    if (reportFilters !== null && reportFilters.length > 0) {
                        const filterObj = JSON.parse(reportFilters);
                        if (filterObj.dateCriteriaFilters != undefined)
                            setDateTimeCriteriaFilters(filterObj.dateCriteriaFilters);
                        if (filterObj.customersCriteriaFilters != undefined)
                            setCustomersCriteriaFilters(filterObj.customersCriteriaFilters);
                        if (filterObj.cancellationFilters != undefined)
                            setCancellationCriteriaFilters(filterObj.cancellationFilters);
                        if (filterObj.purchaseFilters != undefined)
                            setPurchaseCriteriaFilters(filterObj.purchaseFilters);
                        if (filterObj.salesFilters != undefined)
                            setSalesCriteriaFilters(filterObj.salesFilters);
                        if (filterObj.marketingFilters != undefined)
                            setMarketingCriteriaFilters(filterObj.marketingFilters);
                        if (filterObj.tourFilters != undefined)
                            setTourCriteriaFilters(filterObj.tourFilters);
                        if (filterObj.basicFilters != undefined)
                            setBasicCriteriaFilters(filterObj.basicFilters);
                        if (filterObj.leadFilters != undefined)
                            setLeadCriteriaFilters(filterObj.leadFilters);

                        // Set the values of the dropdowns based on the filterObj
                        if (filterObj.dateTypeLogic !== undefined) {

                            document.getElementById('dropDownDateTypeLogic').value = filterObj.dateTypeLogic
                        }
                        function getCheckboxId(mapping, value) {
                            return mapping[value];
                        }

                        function setDropdownValues(mapping, values) {
                            values.forEach(function (value) {
                                var checkBoxId = getCheckboxId(mapping, value);
                                var checkBox = document.getElementById(checkBoxId);
                                if (checkBox && !checkBox.checked) {
                                    checkBox.checked = true;
                                    var clickEvent = new Event('click', { bubbles: true, cancelable: true });
                                    checkBox.dispatchEvent(clickEvent);
                                    var parentPanel = checkBox.parentElement;
                                    if (parentPanel) {
                                        var parentClickEvent = new Event('click', { bubbles: true, cancelable: true });
                                        parentPanel.dispatchEvent(parentClickEvent);
                                    }
                                }
                            });
                        }

                        function processFilter(filterObj, key, mapping, inputId) {
                            if (filterObj[key] !== 'All' && filterObj[key] != null) {
                                var valuesString = filterObj[key];
                                if (valuesString !== undefined && valuesString !== null) {
                                    var valuesToSet = valuesString.split(',').map(function (item) {
                                        return item.trim();
                                    });
                                    setDropdownValues(mapping, valuesToSet);
                                    var inputElement = document.getElementById(inputId);
                                    if (inputElement) {
                                        inputElement.value = valuesString;
                                        inputElement.text = valuesString;
                                    }
                                }
                            }

                        }

                        var daysOfWeekMapping = {
                            "Sunday": 'ctl00_c1_DateTimeCriteriaDetails_dropDownDaysOfWeek_i0_checkBox',
                            "Monday": 'ctl00_c1_DateTimeCriteriaDetails_dropDownDaysOfWeek_i1_checkBox',
                            "Tuesday": 'ctl00_c1_DateTimeCriteriaDetails_dropDownDaysOfWeek_i2_checkBox',
                            "Wednesday": 'ctl00_c1_DateTimeCriteriaDetails_dropDownDaysOfWeek_i3_checkBox',
                            "Thursday": 'ctl00_c1_DateTimeCriteriaDetails_dropDownDaysOfWeek_i4_checkBox',
                            "Friday": 'ctl00_c1_DateTimeCriteriaDetails_dropDownDaysOfWeek_i5_checkBox',
                            "Saturday": 'ctl00_c1_DateTimeCriteriaDetails_dropDownDaysOfWeek_i6_checkBox'
                        };

                        var weeksMapping = {};
                        for (var i = 1; i <= 52; i++) {
                            weeksMapping[i.toString()] = 'ctl00_c1_DateTimeCriteriaDetails_dropDownWeeks_i' + (i - 1) + '_checkBox';
                        }

                        var monthsMapping = {
                            "January": 'ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i0_checkBox',
                            "February": 'ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i1_checkBox',
                            "March": 'ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i2_checkBox',
                            "April": 'ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i3_checkBox',
                            "May": 'ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i4_checkBox',
                            "June": 'ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i5_checkBox',
                            "July": 'ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i6_checkBox',
                            "August": 'ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i7_checkBox',
                            "September": 'ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i8_checkBox',
                            "October": 'ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i9_checkBox',
                            "November": 'ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i10_checkBox',
                            "December": 'ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i11_checkBox'
                        };

                        var yearsMapping = {};
                        for (var j = 0; j <= 24; j++) {
                            yearsMapping[(2023 - j).toString()] = 'ctl00_c1_DateTimeCriteriaDetails_dropDownYears_i' + j + '_checkBox';
                        }

                        var timesMapping = {
                            "8:00 AM": 'ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_i0_checkBox',
                            "8:40 AM": 'ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_i1_checkBox',
                            "10:00 AM": 'ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_i2_checkBox',
                            "11:30 AM": 'ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_i3_checkBox',
                            "12:00 PM": 'ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_i4_checkBox',
                            "1:00 PM": 'ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_i5_checkBox',
                            "2:00 PM": 'ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_i6_checkBox',
                            "3:45 PM": 'ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_i7_checkBox',
                            "4:00 PM": 'ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_i8_checkBox',
                            "6:00 PM": 'ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_i9_checkBox',
                            "8:00 PM": 'ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_i10_checkBox'
                        };

                        updateDropdownOptions();


                        if (filterObj.dateCriteriaFilters) {
                            if (filterObj.dateCriteriaFilters.daysOfWeek !== undefined) {
                                processFilter(filterObj.dateCriteriaFilters, 'daysOfWeek', daysOfWeekMapping, 'ctl00_c1_DateTimeCriteriaDetails_dropDownDaysOfWeek_Input');
                            }
                            if (filterObj.dateCriteriaFilters.weeks !== undefined) {
                                processFilter(filterObj.dateCriteriaFilters, 'weeks', weeksMapping, 'ctl00_c1_DateTimeCriteriaDetails_dropDownWeeks_Input');
                            }
                            if (filterObj.dateCriteriaFilters.months !== undefined) {
                                processFilter(filterObj.dateCriteriaFilters, 'months', monthsMapping, 'ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_Input');
                            }
                            if (filterObj.dateCriteriaFilters.years !== undefined) {
                                processFilter(filterObj.dateCriteriaFilters, 'years', yearsMapping, 'ctl00_c1_DateTimeCriteriaDetails_dropDownYears_Input');
                            }
                            if (filterObj.dateCriteriaFilters.times !== undefined) {
                                processFilter(filterObj.dateCriteriaFilters, 'times', timesMapping, 'ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_Input');
                            }
                            if (filterObj.dateCriteriaFilters.endDate && filterObj.dateCriteriaFilters.endDate !== 'All') {
                                document.getElementById('ctl00_c1_DateTimeCriteriaDetails_endDateRadDatePicker_dateInput').value = last_date;
                            }
                            if (filterObj.dateCriteriaFilters.startDate && filterObj.dateCriteriaFilters.startDate !== 'All') {
                                document.getElementById('ctl00_c1_DateTimeCriteriaDetails_startDateRadDatePicker_dateInput').value = initial_date;
                            }
                            if (filterObj.dateCriteriaFilters.dateRange && filterObj.dateCriteriaFilters.dateRange !== 'All') {
                                document.getElementById('dropDownDateRanges').value = filterObj.dateCriteriaFilters.dateRange.toString();
                            }
                            if (filterObj.dateCriteriaFilters.yearRange && filterObj.dateCriteriaFilters.yearRange !== 'All') {
                                document.getElementById('dropDownYearRanges').value = filterObj.dateCriteriaFilters.yearRange.toString();
                            }
                        }



                        ReportFilters = filterObj;
                    }
                    $("[id$=reportName]").val(reportDetail.name);
                    $("[id$=initialReportName]").val(reportDetail.name);
                    reportIdSelected = reportDetail.reportId;

                    var preJoinsDropdown = document.getElementById("preJoinsDropdown");

                    for (var i = 0; i < preJoinsDropdown.options.length; i++) {
                        var option = preJoinsDropdown.options[i];

                        if (option.value.toLowerCase() == reportDetail.preJoin.toLowerCase()) {
                            option.selected = true;
                            break;
                        }
                    }

                    populateKpi(reportDetail.groupBy);

                    var reportTypeOnDropdown = document.getElementById("reportTypeOnDropdown");
                    reportTypeOnDropdown.selectedIndex = 0;
                    if (reportDetail.reportOn > 0) {
                        for (var i = 0; i <= reportTypeOnDropdown.options.length; i++) {
                            var option = reportTypeOnDropdown.options[i];

                            if (option.value == reportDetail.reportOn) {
                                option.selected = true;
                                var name;
                                if (reportDetail.reportOn != 1000 && reportDetail.reportOn != 2000)
                                    name = option.text + ".name";
                                else if (reportDetail.reportOn == 1000)
                                    name = option.text + ".customerName";
                                else if (reportDetail.reportOn == 2000)
                                    name = option.text + ".userFullName";

                                var ddlKPIsGroupBy = document.getElementById("ddlKPIsGroupBy");

                                // Verificar si la opción ya existe en el menú desplegable
                                var existingOption = Array.from(ddlKPIsGroupBy.options).find(option2 => option2.text === name);

                                if (existingOption) {
                                    // La opción ya existe, solo activa el selected true
                                    existingOption.selected = true;
                                } else {
                                    // La opción no existe, agrégala
                                    var option_select = document.createElement("option");
                                    option_select.text = name;
                                    option_select.value = name;

                                    for (var i = 0; i < ddlKPIsGroupBy.options.length; i++) {
                                        ddlKPIsGroupBy.options[i].selected = false;
                                    }

                                    ddlKPIsGroupBy.add(option_select);
                                    option_select.selected = true;
                                }

                                break;
                            }
                        }
                    }

                    //// Fetch KPI data based on the extracted KpiIds
                    kpiData = await fetchKPIs(selectedReportId);

                    var accordion = document.getElementById('accordion');
                    accordion.innerHTML = '';

                    addAccordion(kpiData);

                    if (joinDefinitions.length > 1) {
                        for (var i = 0; i < joinDefinitions.length - 1; i++)
                            addJoin();
                    }

                    displayRowsJoin(joinDefinitions);

                    displayRowsKpi(kpiData);

                    if (kpiData.length > 0) {
                        /*$('#loader').hide();*/
                        loadGrid();
                    }
                    else {
                        var selectedOption = $('#reportNamesDropdown option:selected').text();
                        $('#loader').hide();
                        showSwal('success', selectedOption + ' loaded successfully.');
                    }

                } catch (error) {
                    ////console.log(error);
                    $('#loader').hide();
                }
            }
        }
    }
}

function loadGrid() {

    if (gridApi != undefined) {
        gridApi.setGridOption('rowData', []);
        gridApi.setGridOption('columnDefs', []);
        gridApi.setGridOption('pinnedTopRowData', []);
        gridApi.setGridOption('pinnedBottomRowData', []);
    }

    if (!file_local_active) {
        let operationsList = [];
        let headerContainers = document.querySelectorAll('.header-container');
        let groupColumn = '';

        for (let container of headerContainers) {
            // Get the column name and aggFunc from the header
            try {
                let headerText = container.querySelector('h4').textContent.trim();
                let [columnName, aggFunc] = headerText.split(' - ').map(item => item.trim());
                let inputContainer = container.querySelector('.inputs-container');
                let buttonContainer = container.querySelector('.buttons-container');
                let operationValue = '';
                let conditionValue = '';

                // Process operation value and preserve spaces
                if (aggFunc != 'ifelse') {
                    var oValue = inputContainer.querySelector('.operation').value.trim().split(' ');
                    for (var i = 0; i < oValue.length; i++) {
                        operationValue += findKpiByCode(oValue[i]) + (i < oValue.length - 1 ? ' ' : '');  // Add space between items
                    }
                }

                // Process condition value and preserve spaces
                var cValue = inputContainer.querySelector('.condition').value.trim().split(' ');
                for (var i = 0; i < cValue.length; i++) {
                    conditionValue += findKpiByCode(cValue[i]) + (i < cValue.length - 1 ? ' ' : '');  // Add space between items
                }

                var buttonToggle = buttonContainer.querySelector('.visible_row')
                var liElement = buttonToggle.querySelector('i');
                let visibleKpi = false;
                if (liElement.className == 'fa fa-eye')
                    visibleKpi = true;

                operationsList.push({
                    columnName: columnName,
                    operation: operationValue,
                    condition: conditionValue,
                    aggFunc: aggFunc,
                    original: inputContainer.querySelector('.condition').value,
                    format: container.nextElementSibling.querySelector('.mathFormat').value,
                    kpiData: kpiData,
                    Visible: visibleKpi
                });
            } catch (e) {
                ////console.log(e);
            }
        }

        groupColumn = document.getElementById('ddlKPIsGroupBy').value.trim();



        loadGridData(operationsList, groupColumn);
    }
    else {
        readLocalFile();
    }
}

async function assignUsers(selectedReportId) {

    const selectedUserIds = getSelectedUserIds();

    // Check if selectedReportId is greater than 0
    if (selectedReportId === 0) {
        selectedReportId = $("#reportNamesDropdown").val();
    }

    if (selectedReportId > 0) {
        const result = await saveReportUsers(selectedReportId, selectedUserIds);
        const message = result.success
            ? 'Users have been successfully assigned to the selected report.'
            : 'Unable to assign users to the selected report.';
        const status = result.success ? 'success' : 'error';
        showSwal(status, message);
    } else {

        //////console.log('Please select a valid report before assigning users.');
        showSwal('error', 'Please select a valid report before assigning users.');
    }
}

async function saveReport() {
    try {
        $('#loader').show();
        const reportName = document.getElementById('reportName').value;

        if (!reportName) {
            showSwal('error', 'Please Select Report from dropdown!');
            return;
        }

        const filters_ = collectFiltersData2();
        const reportData = {
            reportName: reportName,
            reportType: document.getElementById('aggFunc').value,
            groupBy: document.getElementById('ddlKPIsGroupBy').value,
            dropDownDateRanges: document.getElementById('dropDownDateRanges').value,
            dropDownYearRanges: document.getElementById('dropDownYearRanges').value,
            preJoin: document.getElementById('preJoinsDropdown').value,
            reportId: document.getElementById('reportNamesDropdown').value === '--'
                ? 0
                : parseInt(document.getElementById('reportNamesDropdown').value),
            reportOn: document.getElementById('reportTypeOnDropdown').value === '--'
                ? 0
                : parseInt(document.getElementById('reportTypeOnDropdown').value),
            filters: JSON.stringify(filters_)
        };

        var isNewReport = false;
        //Logic for new report
        let initialValue = document.getElementById('initialReportName').value;
        let currentValue = document.getElementById('reportName').value;
        if (currentValue !== initialValue && document.getElementById('reportNamesDropdown').value === '--') {
            reportData.reportId = 0;
            isNewReport = true;
        }

        const reportId = await saveReportToApi(reportData);
        assignUsers(reportId);
        get_reports_list = [];


        if (reportId) {

            /*           const filters = collectFiltersData2();*/
            if (filters_) {
                const filterResponse = await saveFiltersToApi(reportId, filters_);
                if (!filterResponse.success) {
                    // alert('Error saving the filters: ' + filterResponse.message);
                    return;
                }
            }
            // Step 2: Save the joins and get the joinId
            const joinData = retrieveJoinData(reportId);

            if (joinData.length > 0) {
                const joinResponse = await saveJoinsToApi(joinData);

                if (!joinResponse.success) {
                    // alert('Error saving the joins: ' + joinResponse.message);
                    return;
                }
            }

            const kpiData = collectKPIData();

            if (kpiData.length > 0) {

                // Extracting kpiIds from kpiData
                const kpiIds = kpiData.map(kpi => kpi.kpiId);

                // Save KPIs to API
                const generatedKpiIdsResponse = await saveKPIsToApi(reportId, kpiData);

                ////console.log(generatedKpiIdsResponse);
                if (!generatedKpiIdsResponse) {
                    // alert('Error saving KPIs');
                    return;
                }

                // Extract kpiIds from the response
                const newKpiIds = generatedKpiIdsResponse.kpiIds;

                // Combine kpiIds from kpiData and generatedKpiIds only if it's a new report
                const allKpiIds = isNewReport ? [...kpiIds, ...newKpiIds] : newKpiIds;

                // Update the ReportKPIRelationship table with KpiIds
                if (!isNewReport)
                    reportIdSelected = 0;

                const updateKPIRelationshipResponse = await updateReportKPIRelationship(reportId, { success: true, kpiIds: allKpiIds }, reportIdSelected);


                // Check if update was successful
                if (!updateKPIRelationshipResponse.success) {
                    // alert('Error updating KPI Relationship: ' + updateKPIRelationshipResponse.message);
                    return;
                }

            }

            showSwal('success', 'Report saved successfully!');

            //alert('Report saved successfully!');
        } else {
            // alert('Error saving the report: Unable to retrieve report ID.');
        }

        var reportNamesDropdown = document.getElementById("reportNamesDropdown");
        reportNamesDropdown.innerHTML = "";
        getReports((data) => {
            for (var key in data) {
                var dataArray = data[key];
                get_reports_list = dataArray;
                for (var i = 0; i < dataArray.length; i++) {
                    typeReports.push(dataArray[i])
                    option_select = document.createElement("option");
                    option_select.text = dataArray[i].Item1;
                    option_select.value = dataArray[i].Item2;
                    reportNamesDropdown.add(option_select);
                }
            }
            $('#reportNamesDropdown').val(reportId).change();
        });
    } catch (error) {
        $('#loader').hide();
    } finally {
        $('#loader').hide();
    }

}

function loadGridData(operationsList, groupColumn) {
    try {
        var selectedValue = document.getElementById("reportNamesDropdown").value;
        var reportonValue;

        var locationsUser = [];
        var selectedOptions = document.getElementById("locationsUser").selectedOptions;
        for (var i = 0; i < selectedOptions.length; i++) {
            if (selectedOptions[i].value != "--")
                locationsUser.push(selectedOptions[i].value);
        }

        reportonValue = document.getElementById("reportTypeOnDropdown").value;

        var dateOnVal = document.getElementById("dropDownDateRanges").value;
        var dateOffVal = document.getElementById("dropDownYearRanges").value;
        var preJoinsDropdown = document.getElementById("preJoinsDropdown").value;
        const joinData = retrieveJoinData(reportonValue);

        var dateTimeCriteriaObject = JSON.stringify(collectDateCriteriaFilters());
        var customerCriteriaObject = JSON.stringify(collectCustomersCriteriaFilters());
        var cancellationCriteriaObject = JSON.stringify(collectCancellationCriteriaFilters());
        var purchasesCriteriaObject = JSON.stringify(collectPurchaseCriteriaFilters());
        var salesCriteriaObject = JSON.stringify(collectSalesCriteriaFilters());
        var marketingCriteriaObject = JSON.stringify(collectMarketingCriteriaFilters());
        var tourCriteriaObject = JSON.stringify(collectTourCriteriaFilters());
        var basicCriteriaObject = JSON.stringify(collectBasicCriteriaFilters());
        var leadCriteriaObject = JSON.stringify(collectLeadCriteriaFilters());

        var filters = JSON.stringify({
            dateTimeCriteriaObject: dateTimeCriteriaObject,
            customerCriteriaObject: customerCriteriaObject,
            cancellationCriteriaObject: cancellationCriteriaObject,
            purchasesCriteriaObject: purchasesCriteriaObject,
            salesCriteriaObject: salesCriteriaObject,
            marketingCriteriaObject: marketingCriteriaObject,
            tourCriteriaObject: tourCriteriaObject,
            basicCriteriaObject: basicCriteriaObject,
            leadCriteriaObject: leadCriteriaObject
        })

        var kpisValue = false;
        if (operationsList.length > 0)
            kpisValue = true;
        var payload = JSON.stringify({
            avk: preJoinsDropdown,
            reportonId: reportonValue,
            dropDownDateRanges: dateOnVal,
            kpis: kpisValue,
            preJoin: preJoinsDropdown,
            joins: JSON.stringify({ joinData }),
            locationId: JSON.stringify({ locationsUser }),
            filters: filters

        });

        $.ajax({
            type: "POST",
            url: "ApiReportsWebService.aspx/getDataForGrid",
            data: payload,
            contentType: "application/json; charset=utf-8",
            dataType: "json",
            success: function (response) {
                return handleGridDataResponse(response, operationsList, groupColumn);
            },
            failure: function (response) {
                $('#loader').hide();
            }
        });
    } catch (e) {
        $('#loader').hide();
        showSwal('error', 'Data not loaded correctly.');
    }
}

function handleGridDataResponse(response, operationsList, groupColumn) {
    const result = JSON.parse(response.d);

    let processedData = processJoinsBasedOnSelection(result);
    if (!processedData) return;

    const combinedRowData = combineRowData(processedData);
    let newColumnDefs = generateColumnDefs(operationsList, processedData);

    if (groupColumn && groupColumn !== "--") {
        addGroupColumn(groupColumn, result, newColumnDefs);
    }

    const sortedOperations = sortOperations(operationsList);
    processOperations(sortedOperations, combinedRowData, processedData);
    newColumnDefs = finalizeColumnDefs(operationsList, newColumnDefs, result, sortedOperations);

    populateDropdown(newColumnDefs, combinedRowData);
    updateGrid(newColumnDefs, combinedRowData);

    newColumnDefs = newColumnDefs.filter(colDef =>
        !sortedOperations.some(
            kpi => kpi.Visible === false && kpi.columnName === colDef.headerName
        )
    );

    getAllRowsGrid(gridApi);

    setTimeout(() => {
        const visibleRows = getVisibleRows(gridApi);
        calculateMinMaxAndApplyStyles(newColumnDefs, visibleRows);
        gridApi.refreshCells({ force: true });
        gridApi.setGridOption('columnDefs', newColumnDefs);
    }, 50);

    $('#loader').hide();
    showSwal('success', 'Data loaded successfully.');
}

function processJoinsBasedOnSelection(result) {
    try {
        return reportSelectedFromDropdown
            ? processJoinsBySavedData(joinDefinitions, result)
            : processJoins(result);
    }
    catch (e) {
        $('#loader').hide();
        showSwal('error', 'Error detected while processing joins.');
        return null;
    }
}

function combineRowData(processedData) {
    try {
        const dataKeys = ['rowData'];
        return Array.from(new Set(
            dataKeys
                .filter(key => Array.isArray(processedData?.[key]))
                .flatMap(key => processedData[key])
        ));
    }
    catch (e) {
        $('#loader').hide();
        showSwal('error', 'Error combine row data');
        return null;
    }
}

function generateColumnDefs(operationsList, processedData) {
    try {
        const sourceList = operationsList.length > 0 ? operationsList : processedData['kpiCodesList'];
        return sourceList.map(item => ({
            headerName: operationsList.length > 0 ? item.columnName : item.headerText,
            field: operationsList.length > 0 ? item.columnName : item.pivot,
            cellClassRules: {}
        }));
    }
    catch (e) {
        $('#loader').hide();
        showSwal('error', 'Error generate column defs');
        return null;
    }
}

function addGroupColumn(groupColumn, result, newColumnDefs) {
    try {
        const columGroup = groupColumn.split('.')[1].replace(/^./, char => char.toLowerCase());
        if (result?.rowData?.length > 0 && columGroup in result.rowData[0]) {
            const groupColumnDef = newColumnDefs.find(colDef => colDef.field === columGroup) || {
                headerName: columGroup,
                field: columGroup,
                rowGroup: true
            };
            if (!newColumnDefs.includes(groupColumnDef)) {
                newColumnDefs.push(groupColumnDef);
            }
        }
    }
    catch (e) {
        $('#loader').hide();
        showSwal('error', 'Error add group column');
        return null;
    }
}

function processOperations(sortedOperations, combinedRowData, rawData) {
    try {
        const results = {};

        for (let operation of sortedOperations) {
            const { columnName, operation: operationName, condition, aggFunc } = operation;
            results[columnName] = 0;

            const columnNameTransformed = operationName.split('.')[1]?.replace(/^./, char => char.toLowerCase());
            const uniqueTourIDs = new Set();

            for (let row of rawData['rowData'] ?? []) {
                /*let evalResult = ['count', 'sum'].includes(aggFunc) ? evalCondition(condition, row, operation) : null;*/
                let evalResult = evalCondition(operation.condition, row, operation);
                switch (aggFunc) {
                    case 'count':
                        if (!uniqueTourIDs.has(row.tourID)) {
                            results[columnName] += evalResult;
                            row[columnName] = evalResult;
                            uniqueTourIDs.add(row.tourID);
                        }
                        break;
                    case 'ifelse':
                        const conResult = regexConditionValue(condition, row, evalResult);
                        results[columnName] += conResult;
                        row[columnName] = conResult;
                        break;
                    case 'String':
                        row[columnName] = row[columnNameTransformed];
                        break;
                    case 'derived':
                        if (operation.operation && evalResult) {
                            const resultDerived = evalDerivedOperation(operation.operation, row);
                            if (resultDerived != -1) {
                                results[columnName] += resultDerived;
                                row[columnName] = resultDerived;
                            }
                        }
                        break;
                    default:
                        if (operation.operation && evalResult) {
                            const operationResult = evalOperation(operation.operation, row);
                            if (operationResult != -1) {
                                results[columnName] += operationResult;
                                row[columnName] = operationResult;
                            }
                        }
                        else {
                            results[columnName] += 0;
                            row[columnName] = 0;
                        }
                        break;
                }
            }
        }

        return results;
    }
    catch (e) {
        $('#loader').hide();
        showSwal('error', 'Error process kpi operations');
        return null;
    }
}

function finalizeColumnDefs(operationsList, newColumnDefs, result, sortedOperations) {
    try {
        if (operationsList.length === 0) {
            return configureKPIDefs(newColumnDefs, result);
        } else {
            return configureOperationDefs(sortedOperations, newColumnDefs);
        }
    }
    catch (e) {
        $('#loader').hide();
        showSwal('error', 'Error finalize columns defs in grid');
        return null;
    }
}

function updateGrid(newColumnDefs, combinedRowData) {
    try {
        gridApi.setGridOption('columnDefs', newColumnDefs);
        gridApi.setGridOption('rowData', combinedRowData);
        updateTotals(newColumnDefs, combinedRowData);
    }
    catch (e) {
        $('#loader').hide();
        showSwal('error', 'Error update grid');
        return null;
    }
}

function updateTotals(newColumnDefs, combinedRowData) {
    const totalRowData = [{}];
    newColumnDefs.forEach(colDef => {
        if (colDef.aggFunc && colDef.aggFunc !== 'first') {
            const total = combinedRowData.reduce((sum, row) => sum + (row[colDef.field] || 0), 0);
            totalRowData[0][colDef.field] = isNaN(total) ? '--' : total;//.toFixed(2);
        } else {
            totalRowData[0][colDef.field] = '--';
        }
    });
    gridApi.setGridOption('pinnedTopRowData', totalRowData);
    gridApi.setGridOption('pinnedBottomRowData', totalRowData);
}

function configureKPIDefs(newColumnDefs, result) {
    return newColumnDefs.map(kpi => {
        const data = result.rowData.find(item => kpi.field in item);
        if (!data) return null;

        const tipoDeDato = typeof data[kpi.field];
        const operation = tipoDeDato === 'number' ? 'sum' : 'countNonEmpty';

        return {
            ...kpi,
            aggFunc: params => operation === 'countNonEmpty'
                ? params.values.filter(value => value != null && value !== '').length
                : params.values.reduce((sum, value) => sum + (value || 0), 0),
            valueFormatter: params => formatValue(params, operation)
        };
    }).filter(Boolean);
}

function configureOperationDefs(sortedOperations, newColumnDefs) {
    sortedOperations.forEach(operation => {
        const columnDef = newColumnDefs.find(colDef => colDef.field === operation.columnName);
        if (columnDef) {
            configureColumnDef(columnDef, operation);
        }
    });
    return newColumnDefs;
}

function configureColumnDef(columnDef, operation) {
    columnDef.headerName = operation.columnName;
    columnDef.cellClassRules = {};
    columnDef.aggFunc = operation.aggFunc === 'String' || operation.aggFunc === 'ifelse' ? 'first' : 'sum';
    columnDef.cellClass = operation.aggFunc !== 'none' && operation.aggFunc !== 'ifelse' ? "number-cell" : undefined;
    columnDef.cellRenderer = operation.aggFunc !== 'none' && operation.aggFunc !== 'ifelse' ? "agAnimateShowChangeCellRenderer" : undefined;
    columnDef.valueGetter = operation.aggFunc === 'derived' ? createValueGetter(operation.operation) : undefined;
    columnDef.valueFormatter = createValueFormatter(operation);
    columnDef.sortable = true;
    if (operation.aggFunc === 'derived') {
        columnDef.comparator = (valueA, valueB, nodeA, nodeB) => {
            const evaluateExpression = (node) => {

                let nodeSelected = allRowsGrid.find(row => row.key === node.key);

                try {
                    var result = nodeSelected.data[operation.columnName];
                    if (/[%$,]/.test(result)) {
                        result = result.replace(/[%$,]/g, '');
                    }
                    return isNaN(result) || !isFinite(result) ? 0 : result;
                } catch (error) {
                    console.error('Error evaluating the formula:', formula, error);
                    return 0;
                }
            };

            const resultA = evaluateExpression(nodeA);
            const resultB = evaluateExpression(nodeB);

            return resultA - resultB;
        };
    }
    else {
        columnDef.comparator = (valueA, valueB) => {
            return valueA - valueB;
        };

    }
}

function createValueGetter(operationString) {
    return operationString.replace(/[\w$%]+/g, match => {
        return isNaN(match) ? `getValue("${match}")` : match;
    });
}

function createValueFormatter(operation) {
    const cache = {};

    return function (params) {

        const key = `${params.node.id}-${params.colDef.field}`;

        if (cache[key]) return cache[key];

        let formattedValue = params.value;

        if (/[%$,]/.test(formattedValue)) {
            formattedValue = formattedValue.replace(/[%$,]/g, '');
            formattedValue = isNaN(Number(formattedValue)) ? 0 : Number(formattedValue);
        }

        if (formattedValue === params.colDef.headerName) {
            return (cache[key] = '--');
        }

        if (formattedValue === null || formattedValue === undefined) {
            return (cache[key] = typeof formattedValue === 'string' ? '' : 0);
        }

        if (operation.aggFunc === 'ifelse') {
            if (!operation.condition.includes('.')) {
                formattedValue = evalConditionValue(operation.condition, params);
            }
            return (cache[key] = operation.format === 'none' ? formattedValue : '');
        }

        if (operation.aggFunc === 'String' && (typeof formattedValue !== 'number' || isNaN(formattedValue))) {
            return (cache[key] = formattedValue || '');
        }

        if (operation.aggFunc === 'derived') {
            formattedValue = processDerivedOperation(params, formattedValue, operation);
        }

        if ((isNaN(formattedValue) || !isFinite(formattedValue)) && ['ifelse', 'none'].includes(operation.aggFunc)) {
            if (params.node.aggData) params.node.aggData[operation.columnName] = '--';
            return (cache[key] = '--');
        }

        if (!Number.isInteger(formattedValue)) {
            formattedValue = parseFloat(formattedValue).toFixed(2);
        }

        const result = formatByType(formattedValue, operation.format);
        if (params.node.aggData) {
            params.node.aggData[operation.columnName] = result;
        } else {
            params.node.data[operation.columnName] = result;
        }

        return (cache[key] = result);
    };
}

function getVisibleRows(gridApi) {
    const visibleRows = [];
    const rowCount = gridApi.getDisplayedRowCount();

    for (let i = 0; i < rowCount; i++) {
        const rowNode = gridApi.getDisplayedRowAtIndex(i);
        if (rowNode) {
            if (rowNode.aggData) {
                visibleRows.push(rowNode.aggData);
            } else if (rowNode.data) {
                visibleRows.push(rowNode.data);
            }
        }
    }

    return visibleRows;
}

function getAllRowsGrid(gridApi) {
    allRowsGrid = [];
    const rowCount = gridApi.getDisplayedRowCount();

    for (let i = 0; i < rowCount; i++) {
        const rowNode = gridApi.getDisplayedRowAtIndex(i);
        if (rowNode) {
            allRowsGrid.push({
                key: rowNode.key,
                data: rowNode.aggData || rowNode.data || null
            });
        }
    }
}

function calculateMinMaxAndApplyStyles(newColumnDefs, visibleRows) {
    try {
        newColumnDefs.forEach(colDef => {
            if (colDef.field || colDef.valueGetter) {
                // Obtener los valores de la columna
                const columnValues = visibleRows
                    .map(row => {
                        // Usar `valueGetter` si está definido, de lo contrario usar `field`
                        if (typeof colDef.valueGetter === 'string') {
                            return processValueGetterExpression(colDef, row) || 0;
                        }
                        let value = row[colDef.field] || 0;
                        value = cleanCellValue(value);
                        return value;
                    })
                    .filter(value => !isNaN(value)); // Filtrar valores no numéricos

                if (columnValues.length > 0) {
                    // Calcular mínimos y máximos
                    colDef.minValue = Math.min(...columnValues);
                    colDef.maxValue = Math.max(...columnValues);

                    // Configurar reglas de estilo de celda
                    colDef.cellClassRules = {
                        'green-bold': (params) => {
                            const rowValue = visibleRows[params.rowIndex]?.[colDef.headerName];
                            return (
                                isValidValue(rowValue) &&
                                !params.node.footer &&
                                params.node.allLeafChildren &&
                                calculateMax(rowValue, colDef.maxValue)
                            );
                        },
                        'red-bold': (params) => {
                            const rowValue = visibleRows[params.rowIndex]?.[colDef.headerName];
                            return (
                                isValidValue(rowValue) &&
                                !params.node.footer &&
                                params.node.allLeafChildren &&
                                calculateMin(rowValue, colDef.minValue)
                            );
                        },
                    };
                }
            }
        });
    }
    catch (e) {
        $('#loader').hide();
        showSwal('error', 'Error calculated max and min data');
        return null;
    }
}

function isValidValue(value) {
    if (typeof value === 'string') {
        value = value.replace(/[%$,]/g, '').trim();
    }
    value = Number(value);
    if (!isNaN(value))
        return true;
    else
        return false;
}

function calculateMax(value, maxValue) {
    let cellValue = value;
    cellValue = cleanCellValue(cellValue);
    return cellValue === maxValue;
}

function calculateMin(value, minValue) {
    let cellValue = value;
    cellValue = cleanCellValue(cellValue);
    return cellValue === minValue;
}

function cleanCellValue(value) {
    if (typeof value === 'string') {
        value = value.replace(/[%$,]/g, '').trim();
    }
    return Number(value);
}

function processDerivedOperation(params, formattedValue, operation) {
    let operationString = operation.operation;
    const combinedData = { ...params.node.aggData, ...params.data };

    Object.keys(combinedData).forEach(columnName => {
        let columnValue = combinedData[columnName];

        if (typeof columnValue === 'string') {
            const cleanedValue = columnValue.replace(/[%$,]/g, '').trim();
            combinedData[columnName] = isNaN(Number(cleanedValue)) ? 0 : Number(cleanedValue);
            columnValue = combinedData[columnName];
        }

        let columnRegex;

        if (/[%$,]/.test(columnName))
            columnRegex = new RegExp(`(^|[^a-zA-Z0-9_])${escapeRegExp(columnName)}(?=$|[^a-zA-Z0-9_])`, 'g');
        else
            columnRegex = new RegExp(`\\b${escapeRegExp(columnName)}\\b`, 'g');

        if (columnRegex.test(operationString)) {
            operationString = operationString.replace(columnRegex, columnValue);
        }
    });

    if (/[%$,]/.test(operationString)) {
        operationString = operationString.replace(/[%$,]/g, '');
    }

    if (operation.operation !== operationString) {
        try {
            formattedValue = eval(operationString);
            return isNaN(formattedValue) || !isFinite(formattedValue) ? 0 : formattedValue;
        } catch {
            return 0;
        }
    } else {
        return 0;
    }
}

function nodeDerivedOperation(combinedData, formattedValue, operation) {
    let operationString = operation;

    Object.keys(combinedData).forEach(columnName => {
        const columnValue = combinedData[columnName];
        let columnRegex;

        if (/[%$,]/.test(columnName))
            columnRegex = new RegExp(`(^|[^a-zA-Z0-9_])${escapeRegExp(columnName)}(?=$|[^a-zA-Z0-9_])`, 'g');
        else
            columnRegex = new RegExp(`\\b${escapeRegExp(columnName)}\\b`, 'g');

        if (columnRegex.test(operationString)) {
            operationString = operationString.replace(columnRegex, columnValue);
        }
    });

    if (/[%$,]/.test(operationString)) {
        operationString = operationString.replace(/[%$,]/g, '');
    }

    if (operation.operation !== operationString) {
        try {
            formattedValue = eval(operationString);
            return isNaN(formattedValue) || !isFinite(formattedValue) ? 0 : formattedValue;
        } catch {
            return 0;
        }
    } else {
        return 0;
    }
}

function escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&').replace(/[%4#]/g, '');;
}

function formatByType(value, format) {
    if (format === 'currency') {
        return "$" + Math.round(value).toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    } else if (format === 'percentage') {
        let percentageValue = (Math.abs(value) < 1) ? value * 100 : value;
        return Math.round(percentageValue) + "%";
    }
    return value;
}

function formatByTypeColumn(value, format) {
    if (format === 'currency') {
        return Math.round(value).toString().replace(/\B(?=(\d{3})+(?!\d))/g, "");
    } else if (format === 'percentage') {
        let percentageValue = (Math.abs(value) < 1) ? value * 100 : value;
        return Math.round(percentageValue);
    }
    return value;
}

function loadDataGrid(jsonData) {
    // Get the keys from the first object to define columns
    const columnDefs = Object.keys(jsonData[0]).map(key => ({ headerName: key, field: key }));

    var ddlKPIsGroupBy = document.getElementById("ddlKPIsGroupBy");
    ddlKPIsGroupBy.innerHTML = "";
    for (var i = 0; i < columnDefs.length; i++) {
        var option_select = document.createElement("option");
        option_select.text = columnDefs[i].headerName;
        option_select.value = columnDefs[i].headerName;
        ddlKPIsGroupBy.add(option_select);
    }

    populateDropdown(columnDefs, jsonData);
    //*********Change*********/
    gridApi.setGridOption('columnDefs', columnDefs);

    gridApi.setGridOption('rowData', jsonData);

}

function readLocalFile() {
    const fileInput = document.getElementById('fileInputData');
    const file = fileInput.files[0];
    const reader = new FileReader();

    reader.onload = function (event) {
        const fileType = getFileType(file);
        let jsonData;

        if (fileType === 'json') {
            jsonData = JSON.parse(event.target.result);
        } else if (fileType === 'csv') {
            jsonData = csvJSON(event.target.result);
        }

        loadDataGrid(jsonData);
    };

    reader.readAsText(file);
    $('#loader').hide();
}

/* =========================== AJAX FUNCTIONS =========================== */

function getKPIsDataByReportId(callback) {
    var preJoinsDropdown;
    preJoinsDropdown = document.getElementById("preJoinsDropdown").value;

    var payload;
    payload = JSON.stringify({ avk: preJoinsDropdown });

    kpi_get_report = [];

    $.ajax({
        type: "POST",
        url: "ApiReportsWebService.aspx/getKPIsDataByReportId",
        contentType: "application/json; charset=utf-8",
        data: payload,
        dataType: "json",
        success: function (response) {
            var data = JSON.parse(response.d);
            kpi_get_report.push(...data.kpiCodesList);
            /*////console.log(kpi_get_report)*/
            callback(data);
        },
        error: function (err) {
            ////console.log("Error in AJAX request", err);
        }
    });
}

function getReports(callback) {
    $.ajax({
        type: "POST",
        url: "ApiReportsWebService.aspx/getReports",
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        success: function (response) {
            var data = JSON.parse(response.d);
            // Call the callback function with the fetched data
            callback(data);
        },
        error: function (err) {
            ////console.error("Error in AJAX request", err);
        }
    });
}

function getLocations(callback) {
    $.ajax({
        type: "POST",
        url: "ApiReportsWebService.aspx/getLocations",
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        success: function (response) {
            var data = JSON.parse(response.d);
            // Call the callback function with the fetched data
            callback(data);
        },
        error: function (err) {
            ////console.error("Error in AJAX request", err);
        }
    });
}

function getReportsOnType(callback) {
    $.ajax({
        type: "POST",
        url: "ApiReportsWebService.aspx/getReportsOnType",
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        success: function (response) {
            var data = JSON.parse(response.d);
            // Call the callback function with the fetched data
            callback(data);
        },
        error: function (err) {
            ////console.error("Error in AJAX request", err);
        }
    });
}

function getDateRange(callback) {
    $.ajax({
        type: "POST",
        url: "ApiReportsWebService.aspx/getDateRange",
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        success: function (response) {
            var data = JSON.parse(response.d);
            // Call the callback function with the fetched data
            callback(data);
        },
        error: function (err) {
            ////console.error("Error in AJAX request", err);
        }
    });
}



/* =========================== NODEJS FUNCTIONS =========================== */

async function UnAssignSelectedUserId(UserId) {
    try {
        const apiUrl = request_path + 'unAssignReportUser';
        var ReportId = selectedReportId;
        const requestBody = JSON.stringify({ ReportId, UserId });

        const response = await fetch(apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            },
            body: requestBody,
        });

        if (!response.ok) {
            showSwal('error', 'Failed to unassign user from report');
        } else {
            showSwal('success', 'User unassigned from report successfully');
        }

        const result = await response.json();
        return result;
    } catch (error) {
        showSwal('error', 'Error unassigning user from report');
        throw error;
    }
}

async function UnAssignReportKpiId(KpiId) {
    try {
        const apiUrl = request_path + 'unAssignKpiReport';
        var ReportId = selectedReportId;
        const requestBody = JSON.stringify({ ReportId, KpiId });

        const response = await fetch(apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            },
            body: requestBody,
        });

        if (!response.ok) {
            showSwal('error', 'Failed to unassign kpi from report');
        } else {
            showSwal('success', 'KPI unassigned from report successfully');
        }

        const result = await response.json();
        return result;
    } catch (error) {
        showSwal('error', 'Error unassigning KPI from report');
        throw error;
    }
}

async function UnAssignJoinReport(JoinId) {
    try {
        const apiUrl = request_path + 'unAssignJoinReport';
        const requestBody = JSON.stringify({ JoinId });

        const response = await fetch(apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            },
            body: requestBody,
        });

        if (!response.ok) {
            showSwal('error', 'Failed to unassign join from report');
        } else {
            showSwal('success', 'Join unassigned from report successfully');
        }

        const result = await response.json();
        return result;
    } catch (error) {
        showSwal('error', 'Error unassigning join from report');
        throw error;
    }
}

async function unAssignReport(ReportId) {
    try {
        const apiUrl = request_path + 'unAssignReport';
        const requestBody = JSON.stringify({ ReportId });

        const response = await fetch(apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            },
            body: requestBody,
        });

        if (!response.ok) {
            showSwal('error', 'Failed to unassign report');
        } else {
            showSwal('success', 'Report unassigned successfully');

            gridApi.setGridOption('rowData', []);
            gridApi.setGridOption('columnDefs', []);
            gridApi.setGridOption('pinnedTopRowData', []);
            gridApi.setGridOption('pinnedBottomRowData', []);

            var containers = document.querySelectorAll('.join-inputs-container');
            joinCounter = 1;
            // Iterar sobre los contenedores seleccionados
            containers.forEach(function (container) {
                // Verificar si el contenedor tiene el id "joinContainer1"
                if (container.id !== 'joinContainer1') {
                    // Eliminar el contenedor si no tiene el id "joinContainer1"
                    container.parentNode.removeChild(container);
                }
                else {
                    document.getElementById('joinSelect1').selectedIndex = 0;
                    document.getElementById('firstJoinKey_1').value = "";
                    document.getElementById('secondJoinKey_1').value = "";
                    document.getElementById('uniqueKey_1').value = "";
                }
            });

            const accordionDiv = document.getElementById('accordion');
            accordionDiv.innerHTML = '';
            $("#columnName").val("");
            const reportTypeDropdown = document.getElementById('reportTypeOnDropdown');
            reportTypeDropdown.selectedIndex = 0;
            $("#reportName").val("");
            const preJoinsDropdown = document.getElementById('preJoinsDropdown');
            preJoinsDropdown.selectedIndex = 0;

            await clearDateTimeCriterial();
            await clearCancellationCriteriaFilters();
            await clearCustomersCriteriaFilters();
            await clearBasicCriterial();
            await clearMarketingCriteriaFilters();
            await clearPurchaseCriteriaFilters();
            await clearSalesCriteriaFilters();
            await clearTourCriteriaFilters();
            await clearLeadCriteriaFilters();

            const btnDeleteReport = document.getElementById('btnDeleteReport');
            btnDeleteReport.style.display = 'none';

            var reportNamesDropdown = document.getElementById("reportNamesDropdown");
            reportNamesDropdown.innerHTML = "";

            getReports((data) => {
                for (var key in data) {
                    var dataArray = data[key];
                    for (var i = 0; i < dataArray.length; i++) {

                        typeReports.push(dataArray[i]);
                        var option_select = document.createElement("option");
                        option_select.text = dataArray[i].Item1;
                        if (dataArray[i].Item2 != "")
                            option_select.value = dataArray[i].Item2;
                        else
                            option_select.value = dataArray[i].Item6;
                        reportNamesDropdown.add(option_select);
                    }
                }
            });
        }

        const result = await response.json();
        return result;
    } catch (error) {
        showSwal('error', 'Error unassigning report');
        throw error;
    }
}

async function fetchReportById(reportId) {
    try {
        const apiUrl = request_path + `getReportById/${reportId}`;

        const response = await fetch(apiUrl, {
            method: 'GET',
            headers: {
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            }
        });

        if (!response.ok) {
            throw new Error(`Failed to fetch report with ID ${reportId}: ${response.statusText}`);
        }

        const reportData = await response.json();
        return reportData;
    } catch (error) {
        $('#loader').hide();
        console.error(`Error fetching report by ID ${reportId}:`, error);
        throw error;
    }
}

async function fetchReportFiltersById(reportId) {
    try {
        const apiUrl = request_path + `getFilters/${reportId}`;

        const response = await fetch(apiUrl, {
            method: 'GET',
            headers: {
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            }
        });

        if (!response.ok) {
            throw new Error(`Failed to fetch report filters with ID ${reportId}`);
        }

        const filterData = await response.json();
        return filterData;
    } catch (error) {
        $('#loader').hide();
        console.error(error);
    }
}

async function fetchReportUsersById(reportId) {
    try {
        const apiUrl = request_path + `getSavedUserByReportId/${reportId}`;

        const response = await fetch(apiUrl, {
            method: 'GET',
            headers: {
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            }
        });

        if (!response.ok) {
            throw new Error(`Failed to fetch report with ID ${reportId}`);
        }

        const usersData = await response.json();
        checkUsersInList(usersData);

    } catch (error) {
        $('#loader').hide();
        console.error(error);
    }
}

async function fetchJoinDefinitions(reportId) {
    try {
        const apiUrl = request_path + `getJoinDefinitions/${reportId}`;

        const response = await fetch(apiUrl, {
            method: 'GET',
            headers: {
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            }
        });

        if (!response.ok) {
            throw new Error('Failed to fetch join definitions');
        }

        const joinDefinitions = await response.json();
        return joinDefinitions;
    } catch (error) {
        throw error;
    }
}

async function fetchReportKPIRelationship(reportId) {
    try {
        const apiUrl = request_path + `getReportKPIRelationship/${reportId}`;

        const response = await fetch(apiUrl, {
            method: 'GET',
            headers: {
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            }
        });

        if (!response.ok) {
            throw new Error('Failed to fetch ReportKPIRelationship');
        }

        const reportKPIRelationship = await response.json();
        return reportKPIRelationship;
    } catch (error) {
        throw error;
    }
}

async function fetchKPIs(reportId) {
    try {
        const apiUrl = request_path + `getKPIs?reportId=${reportId}`;

        const response = await fetch(apiUrl, {
            method: 'GET',
            headers: {
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            }
        });

        if (!response.ok) {
            throw new Error('Error fetching KPIs');
        }

        const kpiData = await response.json();
        return kpiData;
    } catch (error) {
        throw error;
    }
}

async function saveReportUsers(ReportId, UserIds) {
    try {
        const apiUrl = request_path + 'saveReportUsers';
        const requestBody = JSON.stringify({ ReportId, UserIds });

        const response = await fetch(apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            },
            body: requestBody,
        });

        if (!response.ok) {
            throw new Error('Failed to save data into ReportUser');
        }

        const result = await response.json();
        return result;
    } catch (error) {
        throw error;
    }
}

async function saveReportToApi(reportData) {
    try {
        const apiUrl = request_path + 'saveReport';

        const response = await fetch(apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            },
            body: JSON.stringify(reportData),
        });

        if (!response.ok) {
            throw new Error('Error saving report');
        }

        const data = await response.json();
        return data.reportId;
    } catch (error) {
        throw error;
    }
}

async function saveFiltersToApi(reportId, filters) {
    try {
        const apiUrl = request_path + 'saveFilters';
        const response = await fetch(apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            },
            body: JSON.stringify({ reportId: reportId, filters: JSON.stringify(filters) }),
        });

        if (!response.ok) {
            throw new Error('Error saving filters to the API');
        }

        const data = await response.json();
        return data;
    } catch (error) {
        throw error;
    }
}

async function saveJoinsToApi(joinData) {
    try {
        const apiUrl = request_path + 'saveJoins';
        const response = await fetch(apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            },
            body: JSON.stringify(joinData),
        });

        if (!response.ok) {
            throw new Error('Error saving joins to the API');
        }

        const data = await response.json();
        return data;
    } catch (error) {
        throw error;
    }
}

async function saveReportsToApi(joinData) {
    try {
        const apiUrl = request_path + 'saveJoins';
        const response = await fetch(apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            },
            body: JSON.stringify(joinData),
        });

        if (!response.ok) {
            throw new Error('Error saving joins to the API');
        }

        const data = await response.json();
        return data;
    } catch (error) {
        throw error;
    }
}

async function updateReportWithJoinId(reportId, joinIds) {
    try {
        const apiUrl = request_path + 'updateReportWithJoinId';
        const requestBody = {
            reportId: reportId,
            joinIds: joinIds,
        };

        const response = await fetch(apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            },
            body: JSON.stringify(requestBody),
        });

        if (!response.ok) {
            throw new Error('Error updating report with join IDs');
        }

        const data = await response.json();
        return data;
    } catch (error) {
        throw error;
    }
}

async function saveKPIsToApi(reportId, kpiData) {
    try {
        const apiUrl = request_path + 'saveKPIs';
        const response = await fetch(apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            },
            body: JSON.stringify({ reportId, kpiData }),
        });

        if (!response.ok) {
            throw new Error('Error saving KPIs');
        }

        const data = await response.json();
        return data;
    } catch (error) {
        throw error;
    }
}

async function updateReportKPIRelationship(reportId, kpiIds, reportIdOld) {
    try {
        const apiUrl = request_path + 'updateReportKPIRelationship';
        const requestBody = {
            reportId: reportId,
            kpiIds: kpiIds,
            reportIdOld: reportIdOld,
        };

        const response = await fetch(apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            },
            body: JSON.stringify(requestBody),
        });

        if (!response.ok) {
            throw new Error('Error updating ReportKPIRelationship');
        }

        const data = await response.json();
        return data;
    } catch (error) {
        throw error;
    }
}

async function fetchUserDataByUserName(userName) {
    try {
        const apiUrl = request_path + `getUserData/${userName}`;

        const response = await fetch(apiUrl, {
            method: 'GET',
            headers: {
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            }
        });

        if (!response.ok) {
            throw new Error(`Failed to fetch user data for ${userName}`);
        }

        const userData = await response.json();
        return userData;
    } catch (error) {
        $('#loader').hide();
        console.error(error);
    }
}


/* =========================== COLLECTED FUNCTIONS =========================== */

function collectKPIData() {
    const kpiData = [];
    const accordionItems = document.querySelectorAll('.header-container');
    for (const item of accordionItems) {
        const columnName = item.getAttribute('data-column-name');
        if (columnName != null) {
            const kpiId = parseInt(item.getAttribute('data-id'));

            const operation = item.querySelector('.operation') ? item.querySelector('.operation').value : null;
            const condition = item.querySelector('.condition') ? item.querySelector('.condition').value : null;

            // Ensure sibling element exists before querying it
            const typeFormatElement = item.nextElementSibling ? item.nextElementSibling.querySelector('.mathFormat') : null;
            const typeFormat = typeFormatElement ? typeFormatElement.value : null;

            // Extract text content and parse aggregation function value
            const h4Element = item.querySelector('h4');
            const h4Text = h4Element ? h4Element.textContent : '';
            const aggFuncMatch = h4Text.match(/ - (.+)/);
            const aggFuncValue = aggFuncMatch ? aggFuncMatch[1] : ''; // Extract aggFuncValue

            // Construct the KPI data object
            const kpiItem = {
                columnName,
                operation,
                condition,
                aggFunc: aggFuncValue,
                kpiId,
                typeFormat,
            };

            // Add the KPI data object to the array
            kpiData.push(kpiItem);
        }
    }

    return kpiData;
}

function collectFiltersData2() {

    var dateCriteriaFilters = collectDateCriteriaFilters();
    var locationFilters = collectLocationFilters();
    var customersCriteriaFilters = collectCustomersCriteriaFilters2();
    var cancellationFilters = collectCancellationCriteriaFilters2();
    var purchaseFilters = collectPurchaseCriteriaFilters2();
    var salesFilters = collectSalesCriteriaFilters2();
    var marketingFilters = collectMarketingCriteriaFilters2();
    var tourFilters = collectTourCriteriaFilters2();
    var basicFilters = collectBasicCriteriaFilters2();
    var leadFilters = collectLeadCriteriaFilters2();



    const filters = {
        dateCriteriaFilters: dateCriteriaFilters,
        loctionFilters: locationFilters,
        customersCriteriaFilters: customersCriteriaFilters,
        cancellationFilters: cancellationFilters,
        purchaseFilters: purchaseFilters,
        salesFilters: salesFilters,
        marketingFilters: marketingFilters,
        tourFilters: tourFilters,
        basicFilters: basicFilters,
        leadFilters: leadFilters
    };

    return filters;
}

function collectFiltersData() {

    var dateCriteriaFilters = collectDateCriteriaFilters();
    var locationFilters = collectLocationFilters();
    var customersCriteriaFilters = collectCustomersCriteriaFilters();
    var cancellationFilters = collectCancellationCriteriaFilters();
    var purchaseFilters = collectPurchaseCriteriaFilters();
    var salesFilters = collectSalesCriteriaFilters();
    var marketingFilters = collectMarketingCriteriaFilters();
    var tourFilters = collectTourCriteriaFilters();
    var basicFilters = collectBasicCriteriaFilters();
    var leadFilters = collectLeadCriteriaFilters();



    const filters = {
        dateCriteriaFilters: dateCriteriaFilters,
        loctionFilters: locationFilters,
        customersCriteriaFilters: customersCriteriaFilters,
        cancellationFilters: cancellationFilters,
        purchaseFilters: purchaseFilters,
        salesFilters: salesFilters,
        marketingFilters: marketingFilters,
        tourFilters: tourFilters,
        basicFilters: basicFilters,
        leadFilters: leadFilters
    };

    return filters;
}

function collectLocationFilters() {

    var locationsUser = [];
    var selectedOptions = document.getElementById("locationsUser").selectedOptions;
    for (var i = 0; i < selectedOptions.length; i++) {
        locationsUser.push(selectedOptions[i].value);
    }

    const filters = {
        locationsUser: locationsUser
    };

    return filters;

}

function collectDateCriteriaFilters() {
    function getValue(id, parser = val => val) {
        const el = document.getElementById(id);
        return el ? parser(el.value) : null;
    }

    const filters = {
        dateTypeLogic: getValue('dropDownDateTypeLogic'),
        daysOfWeek: getValue('ctl00_c1_DateTimeCriteriaDetails_dropDownDaysOfWeek_Input'),
        weeks: getValue('ctl00_c1_DateTimeCriteriaDetails_dropDownWeeks_Input'),
        months: getValue('ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_Input'),
        years: getValue('ctl00_c1_DateTimeCriteriaDetails_dropDownYears_Input'),
        times: getValue('ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_Input'),
        endDate: getValue('ctl00_c1_DateTimeCriteriaDetails_endDateRadDatePicker_dateInput'),
        startDate: getValue('ctl00_c1_DateTimeCriteriaDetails_startDateRadDatePicker_dateInput'),
        dateRange: getValue('dropDownDateRanges', parseInt),
        yearRange: getValue('dropDownYearRanges', parseInt),
        tierOneDateTypes: getValue('dropDownTierOneDateTypes'),
        multipleDateTypes: getValue('ctl00_c1_DateTimeCriteriaDetails_dropDownTierOneDateTypesMultiSelect_Input'),
    };

    return filters;
}


function collectBasicCriteriaFilters() {
    var customerId = getElementValue('customerIDTextBox');
    var extId = getElementValue('externalCustomerIDTextBox');
    var extLeadId = getElementValue('externalLeadIDTextBox');
    var tourId = getElementValue('tourIDTextBox');
    var extTourId = getElementValue('externalTourIDTextBox');
    var purchase = getElementValue('dropDownPurchasesCount');
    var purchaseId = getElementValue('purchaseIDTextBox');
    var extPurchaseId = getElementValue('externalPurchaseIDTextBox');
    var notes = getElementValue('textBoxNotes');
    var user = getParsedElementValue('ctl00_c1_BasicCriteria1_dropDownUser_ClientState');
    var importNmbr = getElementValue('textBoxImportNumber');

    const filters = {
        customerId: customerId,
        extId: extId,
        extLeadId: extLeadId,
        tourId: tourId,
        extTourId: extTourId,
        purchase: purchase,
        purchaseId: purchaseId,
        extPurchaseId: extPurchaseId,
        notes: notes,
        user: user,
        importNmbr: importNmbr,
    };

    return filters;
}

function collectCustomersCriteriaFilters() {
    var customerDisposition = getParsedElementValue('ctl00_c1_CustomersCriteriaDetails_customerDispositionIDManyPickLists_manyPickListsDropDown_ClientState');
    var name = getElementValue('textBoxName');
    var sex = getElementValue('dropDownSex');
    var customersPickList1Item = getParsedElementValue('ctl00_c1_CustomersCriteriaDetails_customersPickList1ItemIDManyPickLists_manyPickListsDropDown_ClientState');
    var guestType = getParsedElementValue('ctl00_c1_CustomersCriteriaDetails_guestTypeIDManyPickLists_manyPickListsDropDown_ClientState');
    var guestSex = getElementValue('dropDownGuestSex');
    var customersPickList2Item = getParsedElementValue('ctl00_c1_CustomersCriteriaDetails_customersPickList2ItemIDManyPickLists_manyPickListsDropDown_ClientState');
    var incomeType = getParsedElementValue('ctl00_c1_CustomersCriteriaDetails_incomeTypeIDManyPickLists_manyPickListsDropDown_ClientState');
    var customersPickList3Item = getParsedElementValue('ctl00_c1_CustomersCriteriaDetails_customersPickList3ItemIDManyPickLists_manyPickListsDropDown_ClientState');
    var customersDecimal1 = getElementValue('dropDownCustomersDecimal1');
    var customersBool1 = getElementValue('dropDownCustomersBool1');
    var phone = getElementValue('textBoxPhone');
    var streetAddress = getElementValue('TextBoxStreetAddress');
    var streetAddress2 = getElementValue('TextBoxStreetAddress2');
    var city = getElementValue('textBoxCity');
    var state = getParsedElementValue('ctl00_c1_CustomersCriteriaDetails_stateIDManyPickLists_manyPickListsDropDown_ClientState');
    var zipcode = getElementValue('textBoxZipcode');
    var country = getParsedElementValue('ctl00_c1_CustomersCriteriaDetails_countryIDManyPickLists_manyPickListsDropDown_ClientState');
    var businessName = getElementValue('TextBoxBusiness');
    var email = getElementValue('TextBoxEmail');
    var customersPickList4Item = getParsedElementValue('ctl00_c1_CustomersCriteriaDetails_customersPickList4ItemIDManyPickLists_manyPickListsDropDown_ClientState');
    var customersBool2 = getElementValue('dropDownCustomersBool2');
    var customersText = getElementValue('TextBoxCustomersText1');
    var customersText2 = getElementValue('TextBoxCustomersText2');
    var customersText3 = getElementValue('TextBoxCustomersText3');

    const filters = {
        customerDisposition: customerDisposition,
        name: name,
        sex: sex,
        customersPickList1Item: customersPickList1Item,
        guestType: guestType,
        guestSex: guestSex,
        customersPickList2Item: customersPickList2Item,
        incomeType: incomeType,
        customersPickList3Item: customersPickList3Item,
        customersDecimal1: customersDecimal1,
        customersBool1: customersBool1,
        phone: phone,
        streetAddress: streetAddress,
        streetAddress2: streetAddress2,
        city: city,
        state: state,
        zipcode: zipcode,
        country: country,
        businessName: businessName,
        email: email,
        customersPickList4Item: customersPickList4Item,
        customersBool2: customersBool2,
        customersText: customersText,
        customersText2: customersText2,
        customersText3: customersText3,
    };

    return filters;
}

function collectCancellationCriteriaFilters() {
    var cxlRequestReason = getParsedElementValue('ctl00_c1_CancellationCriteriaDetails_dropDownCancellationReasonTypes_ClientState');
    var cxlRequestReceived = getParsedElementValue('ctl00_c1_CancellationCriteriaDetails_dropDownCancellationReceivedTypes_ClientState');
    var withinRescission = getElementValue('dropDownWithinRescission');
    var cxlRequestDisposition = getParsedElementValue('ctl00_c1_CancellationCriteriaDetails_dropDownCancellationDispositionTypes_ClientState');
    var cxlRequestStatus = getParsedElementValue('ctl00_c1_CancellationCriteriaDetails_dropDownCancellationStatusTypes_ClientState');
    var cxlRequestUser = getParsedElementValue('ctl00_c1_CancellationCriteriaDetails_dropDownCancellationUser_manyPickListsDropDown_ClientState');

    const filters = {
        cxlRequestReason: cxlRequestReason,
        cxlRequestReceived: cxlRequestReceived,
        withinRescission: withinRescission,
        cxlRequestDisposition: cxlRequestDisposition,
        cxlRequestStatus: cxlRequestStatus,
        cxlRequestUser: cxlRequestUser,
    };

    return filters;
}

function collectPurchaseCriteriaFilters() {
    var productCategory = getParsedElementValue('ctl00_c1_PurchasessCriteriaDetails_productCategoryIDManyPickLists_manyPickListsDropDown_ClientState');
    var product = getParsedElementValue('ctl00_c1_PurchasessCriteriaDetails_productIDManyPickLists_manyPickListsDropDown_ClientState');
    var subProduct = getParsedElementValue('ctl00_c1_PurchasessCriteriaDetails_subProductIDManyPickLists_manyPickListsDropDown_ClientState');
    var saleAmount = getElementValue('dropDownSaleAmount');
    var downPaymentAmount = getElementValue('dropDownDownPaymentAmount');
    var fees1 = getElementValue('dropDownDownFees1Amount');
    var fees2 = getElementValue('dropDownDownFees2Amount');
    var fees3 = getElementValue('dropDownDownFees3Amount');
    var fees4 = getElementValue('dropDownDownFees4Amount');
    var proposalValue = getElementValue('dropDownDownFees5Amount');
    var saleTypes = getParsedElementValue('ctl00_c1_PurchasessCriteriaDetails_dropDownSaleTypes_ClientState');
    var saleStatus = getParsedElementValue('ctl00_c1_PurchasessCriteriaDetails_dropDownSaleStatusTypes_ClientState');
    var test = document.getElementById('ctl00_c1_PurchasessCriteriaDetails_dropDownSaleStatusTypes_Input').value;
    var milestones = getElementValue('milestoneStatesDropDown');
    var saleDisposition = getParsedElementValue('ctl00_c1_PurchasessCriteriaDetails_saleDispositionIDManyPickLists_manyPickListsDropDown_ClientState');
    var purchasesPickList1Item = getParsedElementValue('ctl00_c1_PurchasessCriteriaDetails_purchasesPickList1ItemIDManyPickLists_manyPickListsDropDown_ClientState');
    var purchasesPickList2Item = getParsedElementValue('ctl00_c1_PurchasessCriteriaDetails_purchasesPickList2ItemIDManyPickLists_manyPickListsDropDown_ClientState');
    var purchasesPickList3Item = getParsedElementValue('ctl00_c1_PurchasessCriteriaDetails_purchasesPickList3ItemIDManyPickLists_manyPickListsDropDown_ClientState');
    var purchasesText1 = getElementValue('textBoxPurchasesText1');
    var purchasesText2 = getElementValue('textBoxPurchasesText2');
    var purchasesBool1 = getElementValue('dropDownPurchasesBool1');
    var purchasesBool2 = getElementValue('dropDownPurchasesBool2');

    const filters = {
        productCategory: productCategory,
        product: product,
        subProduct: subProduct,
        saleAmount: saleAmount,
        downPaymentAmount: downPaymentAmount,
        fees1: fees1,
        fees2: fees2,
        fees3: fees3,
        fees4: fees4,
        proposalValue: proposalValue,
        saleTypes: saleTypes,
        saleStatus: saleStatus,
        milestones: milestones,
        saleDisposition: saleDisposition,
        purchasesPickList1Item: purchasesPickList1Item,
        purchasesPickList2Item: purchasesPickList2Item,
        purchasesPickList3Item: purchasesPickList3Item,
        purchasesText1: purchasesText1,
        purchasesText2: purchasesText2,
        purchasesBool1: purchasesBool1,
        purchasesBool2: purchasesBool2,
    };

    return filters;
}

function collectSalesCriteriaFilters() {
    var salesTeam = getParsedElementValue('ctl00_c1_SalesCriteriaDetails_salesTeamIDManyPickLists_manyPickListsDropDown_ClientState');
    var podium = getParsedElementValue('ctl00_c1_SalesCriteriaDetails_podiumIDManyPickLists_manyPickListsDropDown_ClientState');
    var salesAgent = getParsedElementValue('ctl00_c1_SalesCriteriaDetails_salesRepIDManyPickLists_manyPickListsDropDown_ClientState');
    var salesCloser1 = getParsedElementValue('ctl00_c1_SalesCriteriaDetails_salesCloser1IDManyPickLists_manyPickListsDropDown_ClientState');
    var salesCloser2 = getParsedElementValue('ctl00_c1_SalesCriteriaDetails_salesCloser2IDManyPickLists_manyPickListsDropDown_ClientState');
    var extRep = getParsedElementValue('ctl00_c1_SalesCriteriaDetails_exitIDManyPickLists_manyPickListsDropDown_ClientState');
    var verificationOfficer = getParsedElementValue('ctl00_c1_SalesCriteriaDetails_verificationOfficerIDManyPickLists_manyPickListsDropDown_ClientState');
    var salesPickList1Item = getParsedElementValue('ctl00_c1_SalesCriteriaDetails_salesPickList1ItemIDManyPickLists_manyPickListsDropDown_ClientState');
    var salesText = getElementValue('textBoxSalesText1');
    var salesDecimal = getElementValue('dropDownSalesDecimal1');
    var salesBool = getElementValue('dropDownSalesBool1');

    var salesExitTeam = getParsedElementValue('ctl00_c1_SalesCriteriaDetails_exitTeamIDManyPickLists_manyPickListsDropDown_ClientState');
    var extRep1 = getParsedElementValue('ctl00_c1_SalesCriteriaDetails_exitRep1IDManyPickLists_manyPickListsDropDown_ClientState');
    var extRep2 = getParsedElementValue('ctl00_c1_SalesCriteriaDetails_exitRep2IDManyPickLists_manyPickListsDropDown_ClientState');
    var extRep3 = getParsedElementValue('ctl00_c1_SalesCriteriaDetails_exitRep3IDManyPickLists_manyPickListsDropDown_ClientState');

    const filters = {
        salesTeam: salesTeam,
        podium: podium,
        salesAgent: salesAgent,
        salesCloser1: salesCloser1,
        salesCloser2: salesCloser2,
        extRep: extRep,
        verificationOfficer: verificationOfficer,
        salesPickList1Item: salesPickList1Item,
        salesText: salesText,
        salesDecimal: salesDecimal,
        salesBool: salesBool,
        salesExitTeam: salesExitTeam,
        extRep1: extRep1,
        extRep2: extRep2,
        extRep3: extRep3
    };

    return filters;
}

function collectMarketingCriteriaFilters() {
    var marketingTeam = getParsedElementValue('ctl00_c1_MarketingCriteriaDetails_marketingTeamIDManyPickLists_manyPickListsDropDown_ClientState');
    var marketingAgent = getParsedElementValue('ctl00_c1_MarketingCriteriaDetails_marketingAgentIDManyPickLists_manyPickListsDropDown_ClientState');
    var marketingCloser = getParsedElementValue('ctl00_c1_MarketingCriteriaDetails_marketingCloserIDManyPickLists_manyPickListsDropDown_ClientState');
    var confirmer = getParsedElementValue('ctl00_c1_MarketingCriteriaDetails_confirmerIDManyPickLists_manyPickListsDropDown_ClientState');
    var resetter = getParsedElementValue('ctl00_c1_MarketingCriteriaDetails_resetterIDManyPickLists_manyPickListsDropDown_ClientState');
    var venue = getParsedElementValue('ctl00_c1_MarketingCriteriaDetails_venueIDManyPickLists_manyPickListsDropDown_ClientState');
    var campaign = getParsedElementValue('ctl00_c1_MarketingCriteriaDetails_campaignIDManyPickLists_manyPickListsDropDown_ClientState');
    var channel = getParsedElementValue('ctl00_c1_MarketingCriteriaDetails_channelIDManyPickLists_manyPickListsDropDown_ClientState');
    var hotel = getParsedElementValue('ctl00_c1_MarketingCriteriaDetails_hotelIDManyPickLists_manyPickListsDropDown_ClientState');
    var giftIDs = getParsedElementValue('ctl00_c1_MarketingCriteriaDetails_giftIDsManyPickLists_manyPickListsDropDown_ClientState');
    var marketingPickList1Item1 = getParsedElementValue('ctl00_c1_MarketingCriteriaDetails_marketingPickList1ItemIDManyPickLists_manyPickListsDropDown_ClientState');
    var marketingPickList1Item2 = getParsedElementValue('ctl00_c1_MarketingCriteriaDetails_marketingPickList2ItemIDManyPickLists_manyPickListsDropDown_ClientState');

    var marketingText = getElementValue('textBoxMarketingText1');
    var marketingBool = getElementValue('dropDownMarketingBool1');
    var depositAmount = getElementValue('dropDownDepositAmount');
    var depositRefundable = getElementValue('dropDownDepositRefundable');

    const filters = {
        marketingTeam: marketingTeam,
        marketingAgent: marketingAgent,
        marketingCloser: marketingCloser,
        confirmer: confirmer,
        resetter: resetter,
        venue: venue,
        campaign: campaign,
        channel: channel,
        hotel: hotel,
        giftIDs: giftIDs,
        marketingPickList1Item1: marketingPickList1Item1,
        marketingPickList1Item2: marketingPickList1Item2,
        marketingText: marketingText,
        marketingBool: marketingBool,
        depositAmount: depositAmount,
        depositRefundable: depositRefundable
    };

    return filters;
}

function collectTourCriteriaFilters() {
    var tourSource = getParsedElementValue('ctl00_c1_TourCriteriaDetails_tourSourceIDManyPickLists_manyPickListsDropDown_ClientState');
    var office = getParsedElementValue('ctl00_c1_TourCriteriaDetails_tourTypeIDManyPickLists_manyPickListsDropDown_ClientState');
    var tourStatus = document.getElementById('ctl00_c1_TourCriteriaDetails_dropDownTourStatusTypes_Input').value;
    var tourDisposition = getParsedElementValue('ctl00_c1_TourCriteriaDetails_tourConcernTypeIDManyPickLists_manyPickListsDropDown_ClientState');
    var importMethod = getParsedElementValue('ctl00_c1_TourCriteriaDetails_tourTypePickList1ItemIDManyPickLists_manyPickListsDropDown_ClientState');
    var businessPartner = getParsedElementValue('ctl00_c1_TourCriteriaDetails_locationIDManyPickLists_manyPickListsDropDown_ClientState');
    var businessGroup = getParsedElementValue('ctl00_c1_TourCriteriaDetails_regionIDManyPickLists_manyPickListsDropDown_ClientState');
    var isShow = null;
    var dropDownTourBool1 = document.getElementById('dropDownTourBool1');
    if (dropDownTourBool1 != null)
        isShow = document.getElementById('dropDownTourBool1').value;
    var saleBonusGifts = getParsedElementValue('ctl00_c1_TourCriteriaDetails_toursManyPickList1ItemIDsManyPickLists_manyPickListsDropDown_ClientState');

    const filters = {
        tourSource: tourSource,
        office: office,
        tourStatus: tourStatus,
        tourDisposition: tourDisposition,
        importMethod: importMethod,
        businessPartner: businessPartner,
        businessGroup: businessGroup,
        isShow: isShow,
        saleBonusGifts: saleBonusGifts
    };

    return filters;
}

function collectLeadCriteriaFilters() {
    var leadSourceFile = getParsedElementValue('ctl00_c1_LeadCriteria_leadSourceIDManyPickLists_manyPickListsDropDown_ClientState');
    var leadDisposition = getParsedElementValue('ctl00_c1_LeadCriteria_leadDispositionIDManyPickLists_manyPickListsDropDown_ClientState');
    var calendarName = getParsedElementValue('ctl00_c1_LeadCriteria_leadPickList1ItemIDManyPickLists_manyPickListsDropDown_ClientState');
    var leadSource = getParsedElementValue('ctl00_c1_LeadCriteria_leadPickList2ItemIDManyPickLists_manyPickListsDropDown_ClientState');

    const filters = {
        leadSourceFile: leadSourceFile,
        leadDisposition: leadDisposition,
        calendarName: calendarName,
        leadSource: leadSource
    };

    return filters;
}

function collectCustomersCriteriaFilters2() {
    var customerDisposition = getParsedElementJson('ctl00_c1_CustomersCriteriaDetails_customerDispositionIDManyPickLists_manyPickListsDropDown_ClientState');
    var name = getElementValue('textBoxName');
    var sex = getElementValue('dropDownSex');
    var customersPickList1Item = getParsedElementJson('ctl00_c1_CustomersCriteriaDetails_customersPickList1ItemIDManyPickLists_manyPickListsDropDown_ClientState');
    var guestType = getParsedElementJson('ctl00_c1_CustomersCriteriaDetails_guestTypeIDManyPickLists_manyPickListsDropDown_ClientState');
    var guestSex = getElementValue('dropDownGuestSex');
    var customersPickList2Item = getParsedElementJson('ctl00_c1_CustomersCriteriaDetails_customersPickList2ItemIDManyPickLists_manyPickListsDropDown_ClientState');
    var incomeType = getParsedElementJson('ctl00_c1_CustomersCriteriaDetails_incomeTypeIDManyPickLists_manyPickListsDropDown_ClientState');
    var customersPickList3Item = getParsedElementJson('ctl00_c1_CustomersCriteriaDetails_customersPickList3ItemIDManyPickLists_manyPickListsDropDown_ClientState');
    var customersDecimal1 = getElementValue('dropDownCustomersDecimal1');
    var customersBool1 = getElementValue('dropDownCustomersBool1');
    var phone = getElementValue('textBoxPhone');
    var streetAddress = getElementValue('TextBoxStreetAddress');
    var streetAddress2 = getElementValue('TextBoxStreetAddress2');
    var city = getElementValue('textBoxCity');
    var state = getParsedElementJson('ctl00_c1_CustomersCriteriaDetails_stateIDManyPickLists_manyPickListsDropDown_ClientState');
    var zipcode = getElementValue('textBoxZipcode');
    var country = getParsedElementJson('ctl00_c1_CustomersCriteriaDetails_countryIDManyPickLists_manyPickListsDropDown_ClientState');
    var businessName = getElementValue('TextBoxBusiness');
    var email = getElementValue('TextBoxEmail');
    var customersPickList4Item = getParsedElementJson('ctl00_c1_CustomersCriteriaDetails_customersPickList4ItemIDManyPickLists_manyPickListsDropDown_ClientState');
    var customersBool2 = getElementValue('dropDownCustomersBool2');
    var customersText = getElementValue('TextBoxCustomersText1');
    var customersText2 = getElementValue('TextBoxCustomersText2');
    var customersText3 = getElementValue('TextBoxCustomersText3');

    const filters = {
        customerDisposition: customerDisposition,
        name: name,
        sex: sex,
        customersPickList1Item: customersPickList1Item,
        guestType: guestType,
        guestSex: guestSex,
        customersPickList2Item: customersPickList2Item,
        incomeType: incomeType,
        customersPickList3Item: customersPickList3Item,
        customersDecimal1: customersDecimal1,
        customersBool1: customersBool1,
        phone: phone,
        streetAddress: streetAddress,
        streetAddress2: streetAddress2,
        city: city,
        state: state,
        zipcode: zipcode,
        country: country,
        businessName: businessName,
        email: email,
        customersPickList4Item: customersPickList4Item,
        customersBool2: customersBool2,
        customersText: customersText,
        customersText2: customersText2,
        customersText3: customersText3
    };

    return filters;
}

function collectBasicCriteriaFilters2() {
    var customerId = getElementValue('customerIDTextBox');
    var extId = getElementValue('externalCustomerIDTextBox');
    var extLeadId = getElementValue('externalLeadIDTextBox');
    var tourId = getElementValue('tourIDTextBox');
    var extTourId = getElementValue('externalTourIDTextBox');
    var purchase = getElementValue('dropDownPurchasesCount');
    var purchaseId = getElementValue('purchaseIDTextBox');
    var extPurchaseId = getElementValue('externalPurchaseIDTextBox');
    var notes = getElementValue('textBoxNotes');
    var user = getParsedElementJson('ctl00_c1_BasicCriteria1_dropDownUser_ClientState');
    var importNmbr = getElementValue('textBoxImportNumber');

    const filters = {
        customerId: customerId,
        extId: extId,
        extLeadId: extLeadId,
        tourId: tourId,
        extTourId: extTourId,
        purchase: purchase,
        purchaseId: purchaseId,
        extPurchaseId: extPurchaseId,
        notes: notes,
        user: user,
        importNmbr: importNmbr,
    };

    return filters;
}

function collectCancellationCriteriaFilters2() {
    var cxlRequestReason = getParsedElementJson('ctl00_c1_CancellationCriteriaDetails_dropDownCancellationReasonTypes_ClientState');
    var cxlRequestReceived = getParsedElementJson('ctl00_c1_CancellationCriteriaDetails_dropDownCancellationReceivedTypes_ClientState');
    var withinRescission = getElementValue('dropDownWithinRescission');
    var cxlRequestDisposition = getParsedElementJson('ctl00_c1_CancellationCriteriaDetails_dropDownCancellationDispositionTypes_ClientState');
    var cxlRequestStatus = getParsedElementJson('ctl00_c1_CancellationCriteriaDetails_dropDownCancellationStatusTypes_ClientState');
    var cxlRequestUser = getParsedElementJson('ctl00_c1_CancellationCriteriaDetails_dropDownCancellationUser_manyPickListsDropDown_ClientState');

    const filters = {
        cxlRequestReason: cxlRequestReason,
        cxlRequestReceived: cxlRequestReceived,
        withinRescission: withinRescission,
        cxlRequestDisposition: cxlRequestDisposition,
        cxlRequestStatus: cxlRequestStatus,
        cxlRequestUser: cxlRequestUser,
    };

    return filters;
}

function collectPurchaseCriteriaFilters2() {
    var productCategory = getParsedElementJson('ctl00_c1_PurchasessCriteriaDetails_productCategoryIDManyPickLists_manyPickListsDropDown_ClientState');
    var product = getParsedElementJson('ctl00_c1_PurchasessCriteriaDetails_productIDManyPickLists_manyPickListsDropDown_ClientState');
    var subProduct = getParsedElementJson('ctl00_c1_PurchasessCriteriaDetails_subProductIDManyPickLists_manyPickListsDropDown_ClientState');
    var saleAmount = getElementValue('dropDownSaleAmount');
    var downPaymentAmount = getElementValue('dropDownDownPaymentAmount');
    var fees1 = getElementValue('dropDownDownFees1Amount');
    var fees2 = getElementValue('dropDownDownFees2Amount');
    var fees3 = getElementValue('dropDownDownFees3Amount');
    var fees4 = getElementValue('dropDownDownFees4Amount');
    var proposalValue = getElementValue('dropDownDownFees5Amount');
    var saleTypes = getParsedElementJson('ctl00_c1_PurchasessCriteriaDetails_dropDownSaleTypes_ClientState');
    var saleStatus = getParsedElementJson('ctl00_c1_PurchasessCriteriaDetails_dropDownSaleStatusTypes_ClientState');
    var milestones = getElementValue('milestoneStatesDropDown');
    var saleDisposition = getParsedElementJson('ctl00_c1_PurchasessCriteriaDetails_saleDispositionIDManyPickLists_manyPickListsDropDown_ClientState');
    var purchasesPickList1Item = getParsedElementJson('ctl00_c1_PurchasessCriteriaDetails_purchasesPickList1ItemIDManyPickLists_manyPickListsDropDown_ClientState');
    var purchasesPickList2Item = getParsedElementJson('ctl00_c1_PurchasessCriteriaDetails_purchasesPickList2ItemIDManyPickLists_manyPickListsDropDown_ClientState');
    var purchasesPickList3Item = getParsedElementJson('ctl00_c1_PurchasessCriteriaDetails_purchasesPickList3ItemIDManyPickLists_manyPickListsDropDown_ClientState');
    var purchasesText1 = getElementValue('textBoxPurchasesText1');
    var purchasesText2 = getElementValue('textBoxPurchasesText2');
    var purchasesBool1 = getElementValue('dropDownPurchasesBool1');
    var purchasesBool2 = getElementValue('dropDownPurchasesBool2');

    const filters = {
        productCategory: productCategory,
        product: product,
        subProduct: subProduct,
        saleAmount: saleAmount,
        downPaymentAmount: downPaymentAmount,
        fees1: fees1,
        fees2: fees2,
        fees3: fees3,
        fees4: fees4,
        proposalValue: proposalValue,
        saleTypes: saleTypes,
        saleStatus: saleStatus,
        milestones: milestones,
        saleDisposition: saleDisposition,
        purchasesPickList1Item: purchasesPickList1Item,
        purchasesPickList2Item: purchasesPickList2Item,
        purchasesPickList3Item: purchasesPickList3Item,
        purchasesText1: purchasesText1,
        purchasesText2: purchasesText2,
        purchasesBool1: purchasesBool1,
        purchasesBool2: purchasesBool2
    };

    return filters;
}

function collectSalesCriteriaFilters2() {
    var salesTeam = getParsedElementJson('ctl00_c1_SalesCriteriaDetails_salesTeamIDManyPickLists_manyPickListsDropDown_ClientState');
    var podium = getParsedElementJson('ctl00_c1_SalesCriteriaDetails_podiumIDManyPickLists_manyPickListsDropDown_ClientState');
    var salesAgent = getParsedElementJson('ctl00_c1_SalesCriteriaDetails_salesRepIDManyPickLists_manyPickListsDropDown_ClientState');
    var salesCloser1 = getParsedElementJson('ctl00_c1_SalesCriteriaDetails_salesCloser1IDManyPickLists_manyPickListsDropDown_ClientState');
    var salesCloser2 = getParsedElementJson('ctl00_c1_SalesCriteriaDetails_salesCloser2IDManyPickLists_manyPickListsDropDown_ClientState');
    var extRep = getParsedElementJson('ctl00_c1_SalesCriteriaDetails_exitIDManyPickLists_manyPickListsDropDown_ClientState');
    var verificationOfficer = getParsedElementJson('ctl00_c1_SalesCriteriaDetails_verificationOfficerIDManyPickLists_manyPickListsDropDown_ClientState');
    var salesPickList1Item = getParsedElementJson('ctl00_c1_SalesCriteriaDetails_salesPickList1ItemIDManyPickLists_manyPickListsDropDown_ClientState');
    var salesText = getElementValue('textBoxSalesText1');
    var salesDecimal = getElementValue('dropDownSalesDecimal1');
    var salesBool = getElementValue('dropDownSalesBool1');

    var salesExitTeam = getParsedElementJson('ctl00_c1_SalesCriteriaDetails_exitTeamIDManyPickLists_manyPickListsDropDown_ClientState');
    var extRep1 = getParsedElementJson('ctl00_c1_SalesCriteriaDetails_exitRep1IDManyPickLists_manyPickListsDropDown_ClientState');
    var extRep2 = getParsedElementJson('ctl00_c1_SalesCriteriaDetails_exitRep2IDManyPickLists_manyPickListsDropDown_ClientState');
    var extRep3 = getParsedElementJson('ctl00_c1_SalesCriteriaDetails_exitRep3IDManyPickLists_manyPickListsDropDown_ClientState');

    const filters = {
        salesTeam: salesTeam,
        podium: podium,
        salesAgent: salesAgent,
        salesCloser1: salesCloser1,
        salesCloser2: salesCloser2,
        extRep: extRep,
        verificationOfficer: verificationOfficer,
        salesPickList1Item: salesPickList1Item,
        salesText: salesText,
        salesDecimal: salesDecimal,
        salesBool: salesBool,
        salesExitTeam: salesExitTeam,
        extRep1: extRep1,
        extRep2: extRep2,
        extRep3: extRep3
    };

    return filters;
}

function collectSalesCriteriaFilters2() {
    var salesTeam = getParsedElementJson('ctl00_c1_SalesCriteriaDetails_salesTeamIDManyPickLists_manyPickListsDropDown_ClientState');
    var podium = getParsedElementJson('ctl00_c1_SalesCriteriaDetails_podiumIDManyPickLists_manyPickListsDropDown_ClientState');
    var salesAgent = getParsedElementJson('ctl00_c1_SalesCriteriaDetails_salesRepIDManyPickLists_manyPickListsDropDown_ClientState');
    var salesCloser1 = getParsedElementJson('ctl00_c1_SalesCriteriaDetails_salesCloser1IDManyPickLists_manyPickListsDropDown_ClientState');
    var salesCloser2 = getParsedElementJson('ctl00_c1_SalesCriteriaDetails_salesCloser2IDManyPickLists_manyPickListsDropDown_ClientState');
    var extRep = getParsedElementJson('ctl00_c1_SalesCriteriaDetails_exitIDManyPickLists_manyPickListsDropDown_ClientState');
    var verificationOfficer = getParsedElementJson('ctl00_c1_SalesCriteriaDetails_verificationOfficerIDManyPickLists_manyPickListsDropDown_ClientState');
    var salesPickList1Item = getParsedElementJson('ctl00_c1_SalesCriteriaDetails_salesPickList1ItemIDManyPickLists_manyPickListsDropDown_ClientState');
    var salesText = getElementValue('textBoxSalesText1');
    var salesDecimal = getElementValue('dropDownSalesDecimal1');
    var salesBool = getElementValue('dropDownSalesBool1');

    var salesExitTeam = getParsedElementJson('ctl00_c1_SalesCriteriaDetails_exitTeamIDManyPickLists_manyPickListsDropDown_ClientState');
    var extRep1 = getParsedElementJson('ctl00_c1_SalesCriteriaDetails_exitRep1IDManyPickLists_manyPickListsDropDown_ClientState');
    var extRep2 = getParsedElementJson('ctl00_c1_SalesCriteriaDetails_exitRep2IDManyPickLists_manyPickListsDropDown_ClientState');
    var extRep3 = getParsedElementJson('ctl00_c1_SalesCriteriaDetails_exitRep3IDManyPickLists_manyPickListsDropDown_ClientState');

    const filters = {
        salesTeam: salesTeam,
        podium: podium,
        salesAgent: salesAgent,
        salesCloser1: salesCloser1,
        salesCloser2: salesCloser2,
        extRep: extRep,
        verificationOfficer: verificationOfficer,
        salesPickList1Item: salesPickList1Item,
        salesText: salesText,
        salesDecimal: salesDecimal,
        salesBool: salesBool,
        salesExitTeam: salesExitTeam,
        extRep1: extRep1,
        extRep2: extRep2,
        extRep3: extRep3
    };

    return filters;
}

function collectMarketingCriteriaFilters2() {
    var marketingTeam = getParsedElementJson('ctl00_c1_MarketingCriteriaDetails_marketingTeamIDManyPickLists_manyPickListsDropDown_ClientState');
    var marketingAgent = getParsedElementJson('ctl00_c1_MarketingCriteriaDetails_marketingAgentIDManyPickLists_manyPickListsDropDown_ClientState');
    var marketingCloser = getParsedElementJson('ctl00_c1_MarketingCriteriaDetails_marketingCloserIDManyPickLists_manyPickListsDropDown_ClientState');
    var confirmer = getParsedElementJson('ctl00_c1_MarketingCriteriaDetails_confirmerIDManyPickLists_manyPickListsDropDown_ClientState');
    var resetter = getParsedElementJson('ctl00_c1_MarketingCriteriaDetails_resetterIDManyPickLists_manyPickListsDropDown_ClientState');
    var venue = getParsedElementJson('ctl00_c1_MarketingCriteriaDetails_venueIDManyPickLists_manyPickListsDropDown_ClientState');
    var campaign = getParsedElementJson('ctl00_c1_MarketingCriteriaDetails_campaignIDManyPickLists_manyPickListsDropDown_ClientState');
    var channel = getParsedElementJson('ctl00_c1_MarketingCriteriaDetails_channelIDManyPickLists_manyPickListsDropDown_ClientState');
    var hotel = getParsedElementJson('ctl00_c1_MarketingCriteriaDetails_hotelIDManyPickLists_manyPickListsDropDown_ClientState');
    var giftIDs = getParsedElementJson('ctl00_c1_MarketingCriteriaDetails_giftIDsManyPickLists_manyPickListsDropDown_ClientState');
    var marketingPickList1Item1 = getParsedElementJson('ctl00_c1_MarketingCriteriaDetails_marketingPickList1ItemIDManyPickLists_manyPickListsDropDown_ClientState');
    var marketingPickList1Item2 = getParsedElementJson('ctl00_c1_MarketingCriteriaDetails_marketingPickList2ItemIDManyPickLists_manyPickListsDropDown_ClientState');

    var marketingText = getElementValue('textBoxMarketingText1');
    var marketingBool = getElementValue('dropDownMarketingBool1');
    var depositAmount = getElementValue('dropDownDepositAmount');
    var depositRefundable = getElementValue('dropDownDepositRefundable');

    const filters = {
        marketingTeam: marketingTeam,
        marketingAgent: marketingAgent,
        marketingCloser: marketingCloser,
        confirmer: confirmer,
        resetter: resetter,
        venue: venue,
        campaign: campaign,
        channel: channel,
        hotel: hotel,
        giftIDs: giftIDs,
        marketingPickList1Item1: marketingPickList1Item1,
        marketingPickList1Item2: marketingPickList1Item2,
        marketingText: marketingText,
        marketingBool: marketingBool,
        depositAmount: depositAmount,
        depositRefundable: depositRefundable
    };

    return filters;
}

function collectTourCriteriaFilters2() {
    var tourSource = getParsedElementJson('ctl00_c1_TourCriteriaDetails_tourSourceIDManyPickLists_manyPickListsDropDown_ClientState');
    var office = getParsedElementJson('ctl00_c1_TourCriteriaDetails_tourTypeIDManyPickLists_manyPickListsDropDown_ClientState');
    var tourStatus = document.getElementById('ctl00_c1_TourCriteriaDetails_dropDownTourStatusTypes_Input').value;
    var tourDisposition = getParsedElementJson('ctl00_c1_TourCriteriaDetails_tourConcernTypeIDManyPickLists_manyPickListsDropDown_ClientState');
    var importMethod = getParsedElementJson('ctl00_c1_TourCriteriaDetails_tourTypePickList1ItemIDManyPickLists_manyPickListsDropDown_ClientState');
    var businessPartner = getParsedElementJson('ctl00_c1_TourCriteriaDetails_locationIDManyPickLists_manyPickListsDropDown_ClientState');
    var businessGroup = getParsedElementJson('ctl00_c1_TourCriteriaDetails_regionIDManyPickLists_manyPickListsDropDown_ClientState');
    var isShow = null;
    var dropDownTourBool1 = document.getElementById('dropDownTourBool1');
    if (dropDownTourBool1 != null)
        isShow = document.getElementById('dropDownTourBool1').value;
    var saleBonusGifts = getParsedElementJson('ctl00_c1_TourCriteriaDetails_toursManyPickList1ItemIDsManyPickLists_manyPickListsDropDown_ClientState');

    const filters = {
        tourSource: tourSource,
        office: office,
        tourStatus: tourStatus,
        tourDisposition: tourDisposition,
        importMethod: importMethod,
        businessPartner: businessPartner,
        businessGroup: businessGroup,
        isShow: isShow,
        saleBonusGifts: saleBonusGifts
    };

    return filters;
}

function collectLeadCriteriaFilters2() {
    var leadSourceFile = getParsedElementJson('ctl00_c1_LeadCriteria_leadSourceIDManyPickLists_manyPickListsDropDown_ClientState');
    var leadDisposition = getParsedElementJson('ctl00_c1_LeadCriteria_leadDispositionIDManyPickLists_manyPickListsDropDown_ClientState');
    var calendarName = getParsedElementJson('ctl00_c1_LeadCriteria_leadPickList1ItemIDManyPickLists_manyPickListsDropDown_ClientState');
    var leadSource = getParsedElementJson('ctl00_c1_LeadCriteria_leadPickList2ItemIDManyPickLists_manyPickListsDropDown_ClientState');

    const filters = {
        leadSourceFile: leadSourceFile,
        leadDisposition: leadDisposition,
        calendarName: calendarName,
        leadSource: leadSource
    };

    return filters;
}

/* =========================== CLEAR KPI FILTERS FUNCTIONS =========================== */

async function clearDateTimeCriterial() {

    var dropDownDateRanges = document.getElementById('dropDownDateRanges');
    dropDownDateRanges.selectedIndex = 0;
    var dropDownYearRanges = document.getElementById('dropDownYearRanges');
    dropDownYearRanges.selectedIndex = 0;
    setParsedElementValue('ctl00_c1_DateTimeCriteriaDetails_startDateRadDatePicker_dateInput', '');
    setParsedElementValue('ctl00_c1_DateTimeCriteriaDetails_startDateRadDatePicker_dateInput_ClientState', '{"enabled":true,"emptyMessage":"","validationText":"","valueAsString":"","minDateStr":"1900-01-01-00-00-00","maxDateStr":"2099-12-31-00-00-00","lastSetTextBoxValue":""}');
    setParsedElementValue('ctl00_c1_DateTimeCriteriaDetails_endDateRadDatePicker_dateInput', '');
    setParsedElementValue('ctl00_c1_DateTimeCriteriaDetails_endDateRadDatePicker_dateInput_ClientState', '{"enabled":true,"emptyMessage":"","validationText":"","valueAsString":"","minDateStr":"1900-01-01-00-00-00","maxDateStr":"2099-12-31-00-00-00","lastSetTextBoxValue":""}');
    setElementValue('dropDownDateTypeLogic', '');
    setElementValue('dropDownTierOneDateTypes', '');

    var daysOfWeekMapping = {
        "Sunday": 'ctl00_c1_DateTimeCriteriaDetails_dropDownDaysOfWeek_i0_checkBox',
        "Monday": 'ctl00_c1_DateTimeCriteriaDetails_dropDownDaysOfWeek_i1_checkBox',
        "Tuesday": 'ctl00_c1_DateTimeCriteriaDetails_dropDownDaysOfWeek_i2_checkBox',
        "Wednesday": 'ctl00_c1_DateTimeCriteriaDetails_dropDownDaysOfWeek_i3_checkBox',
        "Thursday": 'ctl00_c1_DateTimeCriteriaDetails_dropDownDaysOfWeek_i4_checkBox',
        "Friday": 'ctl00_c1_DateTimeCriteriaDetails_dropDownDaysOfWeek_i5_checkBox',
        "Saturday": 'ctl00_c1_DateTimeCriteriaDetails_dropDownDaysOfWeek_i6_checkBox'
    };

    var weeksMapping = {};
    for (var i = 1; i <= 52; i++) {
        weeksMapping[i.toString()] = 'ctl00_c1_DateTimeCriteriaDetails_dropDownWeeks_i' + (i - 1) + '_checkBox';
    }

    var monthsMapping = {
        "January": 'ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i0_checkBox',
        "February": 'ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i1_checkBox',
        "March": 'ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i2_checkBox',
        "April": 'ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i3_checkBox',
        "May": 'ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i4_checkBox',
        "June": 'ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i5_checkBox',
        "July": 'ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i6_checkBox',
        "August": 'ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i7_checkBox',
        "September": 'ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i8_checkBox',
        "October": 'ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i9_checkBox',
        "November": 'ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i10_checkBox',
        "December": 'ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i11_checkBox'
    };

    var yearsMapping = {};
    for (var j = 0; j <= 24; j++) {
        yearsMapping[(2023 - j).toString()] = 'ctl00_c1_DateTimeCriteriaDetails_dropDownYears_i' + j + '_checkBox';
    }

    var timesMapping = {
        "8:00 AM": 'ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_i0_checkBox',
        "8:40 AM": 'ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_i1_checkBox',
        "10:00 AM": 'ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_i2_checkBox',
        "11:30 AM": 'ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_i3_checkBox',
        "12:00 PM": 'ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_i4_checkBox',
        "1:00 PM": 'ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_i5_checkBox',
        "2:00 PM": 'ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_i6_checkBox',
        "3:45 PM": 'ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_i7_checkBox',
        "4:00 PM": 'ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_i8_checkBox',
        "6:00 PM": 'ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_i9_checkBox',
        "8:00 PM": 'ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_i10_checkBox'
    };

    clearCheckboxes(daysOfWeekMapping);

    // Limpiar los checkboxes de las semanas
    clearCheckboxes(weeksMapping);

    // Limpiar los checkboxes de los meses
    clearCheckboxes(monthsMapping);

    // Limpiar los checkboxes de los años
    clearCheckboxes(yearsMapping);

    // Limpiar los checkboxes de los horarios de tour
    clearCheckboxes(timesMapping);

    setParsedElementValue('ctl00_c1_DateTimeCriteriaDetails_dropDownDaysOfWeek_ClientState', '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}');
    $("input[id$='ctl00_c1_DateTimeCriteriaDetails_dropDownDaysOfWeek_Input']").val("All");

    setParsedElementValue('ctl00_c1_DateTimeCriteriaDetails_dropDownWeeks_ClientState', '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}');
    $("input[id$='ctl00_c1_DateTimeCriteriaDetails_dropDownWeeks_Input']").val("All");

    setParsedElementValue('ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_ClientState', '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}');
    $("input[id$='ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_Input']").val("All");

    setParsedElementValue('ctl00_c1_DateTimeCriteriaDetails_dropDownYears_ClientState', '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}');
    $("input[id$='ctl00_c1_DateTimeCriteriaDetails_dropDownYears_Input']").val("All");

    setParsedElementValue('ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_ClientState', '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}');
    $("input[id$='ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_Input']").val("All");
}

async function clearBasicCriterial() {

    setElementValue('customerIDTextBox', '');
    setElementValue('externalCustomerIDTextBox', '');
    setElementValue('externalLeadIDTextBox', '');
    setElementValue('tourIDTextBox', '');
    setElementValue('externalTourIDTextBox', '');
    setElementValue('dropDownPurchasesCount', '');
    setElementValue('purchaseIDTextBox', '');
    setElementValue('externalPurchaseIDTextBox', '');
    setElementValue('textBoxNotes', '');
    setParsedElementValue('ctl00_c1_BasicCriteriaDetails_dropDownUser_ClientState', '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}');
    $("input[id$='ctl00_c1_BasicCriteriaDetails_dropDownUser_Input']").val("All (select or type to search)");
    setElementValue('textBoxImportNumber', '');

}

async function clearCustomersCriteriaFilters() {
    // ManyPickLists: Customer Disposition
    setParsedElementValue('ctl00_c1_CustomersCriteriaDetails_customerDispositionIDManyPickLists_manyPickListsDropDown_ClientState', '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}');
    $("input[id$='ctl00_c1_CustomersCriteriaDetails_customerDispositionIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)");

    // ManyPickLists: Customers PickList 1
    setParsedElementValue('ctl00_c1_CustomersCriteriaDetails_customersPickList1ItemIDManyPickLists_manyPickListsDropDown_ClientState', '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}');
    $("input[id$='ctl00_c1_CustomersCriteriaDetails_customersPickList1ItemIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)");

    // DropDowns: Sex
    setElementValue('dropDownSex', ''); // Selects "All"

    // DropDowns: Guest Sex
    setElementValue('dropDownGuestSex', ''); // Selects "All"

    // DropDowns: Locations
    setElementValue('locationUser', '--');

    // ManyPickLists: Customers PickList 2
    setParsedElementValue('ctl00_c1_CustomersCriteriaDetails_customersPickList2ItemIDManyPickLists_manyPickListsDropDown_ClientState', '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}');
    $("input[id$='ctl00_c1_CustomersCriteriaDetails_customersPickList2ItemIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)");

    // ManyPickLists: Guest Type
    setParsedElementValue('ctl00_c1_CustomersCriteriaDetails_guestTypeIDManyPickLists_manyPickListsDropDown_ClientState', '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}');
    $("input[id$='ctl00_c1_CustomersCriteriaDetails_guestTypeIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)");

    // DropDowns: Decimal 1
    setElementValue('dropDownCustomersDecimal1', ''); // Selects "All"

    // DropDowns: Bool 1
    setElementValue('dropDownCustomersBool1', ''); // Selects "All"

    // TextBoxes: Phone
    setElementValue('textBoxPhone', ''); // Clears the text box

    // TextBoxes: Street Address
    setElementValue('TextBoxStreetAddress', ''); // Clears the text box

    // TextBoxes: Street Address 2
    setElementValue('TextBoxStreetAddress2', ''); // Clears the text box

    // TextBoxes: City
    setElementValue('textBoxCity', ''); // Clears the text box

    // ManyPickLists: State
    setParsedElementValue('ctl00_c1_CustomersCriteriaDetails_stateIDManyPickLists_manyPickListsDropDown_ClientState', '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}');
    $("input[id$='ctl00_c1_CustomersCriteriaDetails_stateIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)");

    // TextBoxes: Zipcode
    setElementValue('textBoxZipcode', ''); // Clears the text box

    // ManyPickLists: Country
    setParsedElementValue('ctl00_c1_CustomersCriteriaDetails_countryIDManyPickLists_manyPickListsDropDown_ClientState', '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}');
    $("input[id$='ctl00_c1_CustomersCriteriaDetails_countryIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)");

    // TextBoxes: Business Name
    setElementValue('TextBoxBusiness', ''); // Clears the text box

    // TextBoxes: Email
    setElementValue('TextBoxEmail', ''); // Clears the text box

    // ManyPickLists: Customers PickList 4
    setParsedElementValue('ctl00_c1_CustomersCriteriaDetails_customersPickList4ItemIDManyPickLists_manyPickListsDropDown_ClientState', '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}');
    $("input[id$='ctl00_c1_CustomersCriteriaDetails_customersPickList4ItemIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)");

    // DropDowns: Bool 2
    setElementValue('dropDownCustomersBool2', ''); // Selects "All"

    // TextBoxes: Custom Text
    setElementValue('TextBoxCustomersText1', ''); // Clears the text box
    setElementValue('TextBoxCustomersText2', ''); // Clears the text box
    setElementValue('TextBoxCustomersText3', ''); // Clears the text box
}

async function clearCancellationCriteriaFilters() {
    // ManyPickLists: Cancellation Reason Types
    setParsedElementValue('ctl00_c1_CancellationCriteriaDetails_dropDownCancellationReasonTypes_ClientState', '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}');
    $("input[id$='ctl00_c1_CancellationCriteriaDetails_dropDownCancellationReasonTypes_Input']").val("All (select or type to search)");

    // ManyPickLists: Cancellation Received Types
    setParsedElementValue('ctl00_c1_CancellationCriteriaDetails_dropDownCancellationReceivedTypes_ClientState', '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}');
    $("input[id$='ctl00_c1_CancellationCriteriaDetails_dropDownCancellationReceivedTypes_Input']").val("All (select or type to search)");

    // DropDowns: Within Rescission
    setElementValue('dropDownWithinRescission', ''); // Selects "All"

    // ManyPickLists: Cancellation Disposition Types
    setParsedElementValue('ctl00_c1_CancellationCriteriaDetails_dropDownCancellationDispositionTypes_ClientState', '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}');
    $("input[id$='ctl00_c1_CancellationCriteriaDetails_dropDownCancellationDispositionTypes_Input']").val("All (select or type to search)");

    // ManyPickLists: Cancellation Status Types
    setParsedElementValue('ctl00_c1_CancellationCriteriaDetails_dropDownCancellationStatusTypes_ClientState', '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}');
    $("input[id$='ctl00_c1_CancellationCriteriaDetails_dropDownCancellationStatusTypes_Input']").val("All (select or type to search)");

    // ManyPickLists: Cancellation User
    setParsedElementValue('ctl00_c1_CancellationCriteriaDetails_dropDownCancellationUser_manyPickListsDropDown_ClientState', '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}');
    $("input[id$='ctl00_c1_CancellationCriteriaDetails_dropDownCancellationUser_manyPickListsDropDown_Input']").val("All (select or type to search)");
}

async function clearPurchaseCriteriaFilters() {
    // ManyPickLists: Product Category
    setParsedElementValue('ctl00_c1_PurchasessCriteriaDetails_productCategoryIDManyPickLists_manyPickListsDropDown_ClientState', '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}');
    $("input[id$='ctl00_c1_PurchasessCriteriaDetails_productCategoryIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)");

    // ManyPickLists: Product
    setParsedElementValue('ctl00_c1_PurchasessCriteriaDetails_productIDManyPickLists_manyPickListsDropDown_ClientState', '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}');
    $("input[id$='ctl00_c1_PurchasessCriteriaDetails_productIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)");

    // ManyPickLists: SubProduct
    setParsedElementValue('ctl00_c1_PurchasessCriteriaDetails_subProductIDManyPickLists_manyPickListsDropDown_ClientState', '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}');
    $("input[id$='ctl00_c1_PurchasessCriteriaDetails_subProductIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)");

    // DropDowns: Sale Amount
    setElementValue('dropDownSaleAmount', ''); // Selects "All"

    // DropDowns: Down Payment Amount
    setElementValue('dropDownDownPaymentAmount', ''); // Selects "All"

    // DropDowns: Fees 1 to 4
    setElementValue('dropDownDownFees1Amount', ''); // Selects "All"
    setElementValue('dropDownDownFees2Amount', ''); // Selects "All"
    setElementValue('dropDownDownFees3Amount', ''); // Selects "All"
    setElementValue('dropDownDownFees4Amount', ''); // Selects "All"

    // DropDowns: Proposal Value
    setElementValue('dropDownDownFees5Amount', ''); // Selects "All"

    // ManyPickLists: Sale Types
    setParsedElementValue('ctl00_c1_PurchasessCriteriaDetails_dropDownSaleTypes_ClientState', '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}');
    $("input[id$='ctl00_c1_PurchasessCriteriaDetails_dropDownSaleTypes_Input']").val("All (select or type to search)");

    // ManyPickLists: Sale Status Types
    setParsedElementValue('ctl00_c1_PurchasessCriteriaDetails_dropDownSaleStatusTypes_ClientState', '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}');
    $("input[id$='ctl00_c1_PurchasessCriteriaDetails_dropDownSaleStatusTypes_Input']").val("All (select or type to search)");

    // DropDowns: Milestones
    setElementValue('milestoneStatesDropDown', ''); // Selects "All"

    // ManyPickLists: Sale Disposition
    setParsedElementValue('ctl00_c1_PurchasessCriteriaDetails_saleDispositionIDManyPickLists_manyPickListsDropDown_ClientState', '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}');
    $("input[id$='ctl00_c1_PurchasessCriteriaDetails_saleDispositionIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)");

    // ManyPickLists: Purchases PickList 1 to 3
    setParsedElementValue('ctl00_c1_PurchasessCriteriaDetails_purchasesPickList1ItemIDManyPickLists_manyPickListsDropDown_ClientState', '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}');
    $("input[id$='ctl00_c1_PurchasessCriteriaDetails_purchasesPickList1ItemIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)");

    setParsedElementValue('ctl00_c1_PurchasessCriteriaDetails_purchasesPickList2ItemIDManyPickLists_manyPickListsDropDown_ClientState', '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}');
    $("input[id$='ctl00_c1_PurchasessCriteriaDetails_purchasesPickList2ItemIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)");

    setParsedElementValue('ctl00_c1_PurchasessCriteriaDetails_purchasesPickList3ItemIDManyPickLists_manyPickListsDropDown_ClientState', '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}');
    $("input[id$='ctl00_c1_PurchasessCriteriaDetails_purchasesPickList3ItemIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)");

    // TextBoxes: Purchases Text 1 to 2
    setElementValue('textBoxPurchasesText1', ''); // Clears the text box
    setElementValue('textBoxPurchasesText2', ''); // Clears the text box

    // DropDowns: Purchases Bool 1 to 2
    setElementValue('dropDownPurchasesBool1', ''); // Selects "All"
    setElementValue('dropDownPurchasesBool2', ''); // Selects "All"
}

async function clearSalesCriteriaFilters() {
    // ManyPickLists: Sales Team
    setParsedElementValue('ctl00_c1_SalesCriteriaDetails_salesTeamIDManyPickLists_manyPickListsDropDown_ClientState', '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}');
    $("input[id$='ctl00_c1_SalesCriteriaDetails_salesTeamIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)");

    // ManyPickLists: Podium
    setParsedElementValue('ctl00_c1_SalesCriteriaDetails_podiumIDManyPickLists_manyPickListsDropDown_ClientState', '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}');
    $("input[id$='ctl00_c1_SalesCriteriaDetails_podiumIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)");

    // ManyPickLists: Sales Agent
    setParsedElementValue('ctl00_c1_SalesCriteriaDetails_salesRepIDManyPickLists_manyPickListsDropDown_ClientState', '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}');
    $("input[id$='ctl00_c1_SalesCriteriaDetails_salesRepIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)");

    // ManyPickLists: Sales Closer 1
    setParsedElementValue('ctl00_c1_SalesCriteriaDetails_salesCloser1IDManyPickLists_manyPickListsDropDown_ClientState', '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}');
    $("input[id$='ctl00_c1_SalesCriteriaDetails_salesCloser1IDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)");

    // ManyPickLists: Sales Closer 2
    setParsedElementValue('ctl00_c1_SalesCriteriaDetails_salesCloser2IDManyPickLists_manyPickListsDropDown_ClientState', '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}');
    $("input[id$='ctl00_c1_SalesCriteriaDetails_salesCloser2IDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)");

    // ManyPickLists: Exit Rep
    setParsedElementValue('ctl00_c1_SalesCriteriaDetails_exitIDManyPickLists_manyPickListsDropDown_ClientState', '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}');
    $("input[id$='ctl00_c1_SalesCriteriaDetails_exitIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)");

    // ManyPickLists: Verification Officer
    setParsedElementValue('ctl00_c1_SalesCriteriaDetails_verificationOfficerIDManyPickLists_manyPickListsDropDown_ClientState', '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}');
    $("input[id$='ctl00_c1_SalesCriteriaDetails_verificationOfficerIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)");

    // DropDown: Sale Types
    setElementValue('ctl00_c1_SalesCriteriaDetails_dropDownSaleTypes_ClientState', "");
    $("input[id$='ctl00_c1_SalesCriteriaDetails_dropDownSaleTypes_Input']").val("All");

    // DropDown: Sale Status
    setElementValue('ctl00_c1_SalesCriteriaDetails_dropDownSaleStatusTypes_ClientState', "");
    $("input[id$='ctl00_c1_SalesCriteriaDetails_dropDownSaleStatusTypes_Input']").val("All");

    // DropDown: Milestones
    setElementValue('milestoneStatesDropDown', "");

    // TextBoxes: Clear text inputs
    setElementValue('textBoxSalesText1', "");
    setElementValue('textBoxSalesText2', "");

    // DropDowns: Reset any decimal/bool fields
    setElementValue('dropDownSalesDecimal1', "");
    setElementValue('dropDownSalesBool1', "");
}

async function clearMarketingCriteriaFilters() {
    // ManyPickLists: Marketing Team
    setParsedElementValue('ctl00_c1_MarketingCriteriaDetails_marketingTeamIDManyPickLists_manyPickListsDropDown_ClientState', '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}');
    $("input[id$='ctl00_c1_MarketingCriteriaDetails_marketingTeamIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)");

    // ManyPickLists: Marketing Agent
    setParsedElementValue('ctl00_c1_MarketingCriteriaDetails_marketingAgentIDManyPickLists_manyPickListsDropDown_ClientState', '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}');
    $("input[id$='ctl00_c1_MarketingCriteriaDetails_marketingAgentIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)");

    // ManyPickLists: Marketing Closer
    setParsedElementValue('ctl00_c1_MarketingCriteriaDetails_marketingCloserIDManyPickLists_manyPickListsDropDown_ClientState', '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}');
    $("input[id$='ctl00_c1_MarketingCriteriaDetails_marketingCloserIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)");

    // ManyPickLists: Confirmer
    setParsedElementValue('ctl00_c1_MarketingCriteriaDetails_confirmerIDManyPickLists_manyPickListsDropDown_ClientState', '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}');
    $("input[id$='ctl00_c1_MarketingCriteriaDetails_confirmerIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)");

    // ManyPickLists: Resetter
    setParsedElementValue('ctl00_c1_MarketingCriteriaDetails_resetterIDManyPickLists_manyPickListsDropDown_ClientState', '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}');
    $("input[id$='ctl00_c1_MarketingCriteriaDetails_resetterIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)");

    // ManyPickLists: Venue
    setParsedElementValue('ctl00_c1_MarketingCriteriaDetails_venueIDManyPickLists_manyPickListsDropDown_ClientState', '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}');
    $("input[id$='ctl00_c1_MarketingCriteriaDetails_venueIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)");

    // ManyPickLists: Campaign
    setParsedElementValue('ctl00_c1_MarketingCriteriaDetails_campaignIDManyPickLists_manyPickListsDropDown_ClientState', '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}');
    $("input[id$='ctl00_c1_MarketingCriteriaDetails_campaignIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)");

    // ManyPickLists: Channel
    setParsedElementValue('ctl00_c1_MarketingCriteriaDetails_channelIDManyPickLists_manyPickListsDropDown_ClientState', '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}');
    $("input[id$='ctl00_c1_MarketingCriteriaDetails_channelIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)");

    // ManyPickLists: Hotel
    setParsedElementValue('ctl00_c1_MarketingCriteriaDetails_hotelIDManyPickLists_manyPickListsDropDown_ClientState', '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}');
    $("input[id$='ctl00_c1_MarketingCriteriaDetails_hotelIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)");

    // ManyPickLists: Gift IDs
    setParsedElementValue('ctl00_c1_MarketingCriteriaDetails_giftIDsManyPickLists_manyPickListsDropDown_ClientState', '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}');
    $("input[id$='ctl00_c1_MarketingCriteriaDetails_giftIDsManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)");

    // ManyPickLists: Marketing PickList 1 Item
    setParsedElementValue('ctl00_c1_MarketingCriteriaDetails_marketingPickList1ItemIDManyPickLists_manyPickListsDropDown_ClientState', '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}');
    $("input[id$='ctl00_c1_MarketingCriteriaDetails_marketingPickList1ItemIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)");

    // ManyPickLists: Marketing PickList 2 Item
    setParsedElementValue('ctl00_c1_MarketingCriteriaDetails_marketingPickList2ItemIDManyPickLists_manyPickListsDropDown_ClientState', '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}');
    $("input[id$='ctl00_c1_MarketingCriteriaDetails_marketingPickList2ItemIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)");

    // Campos adicionales que no son ManyPickListsDropDown
    setElementValue('textBoxMarketingText1', "");  // Limpia el campo de texto
    setElementValue('dropDownMarketingBool1', "");  // Restablece el campo booleano
    setElementValue('dropDownDepositAmount', "");  // Restablece el campo de monto de depósito
    setElementValue('dropDownDepositRefundable', "");  // Restablece el campo de depósito reembolsable
}

async function clearTourCriteriaFilters() {
    setParsedElementValue('ctl00_c1_TourCriteriaDetails_tourSourceIDManyPickLists_manyPickListsDropDown_ClientState', '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}');
    $("input[id$='ctl00_c1_TourCriteriaDetails_tourSourceIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)");

    setParsedElementValue('ctl00_c1_TourCriteriaDetails_tourTypeIDManyPickLists_manyPickListsDropDown_ClientState', '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}');
    $("input[id$='ctl00_c1_TourCriteriaDetails_tourTypeIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)");

    setParsedElementValue('ctl00_c1_TourCriteriaDetails_dropDownTourStatusTypes_ClientState', '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}');
    $("input[id$='ctl00_c1_TourCriteriaDetails_dropDownTourStatusTypes_Input']").val("All");

    setParsedElementValue('ctl00_c1_TourCriteriaDetails_tourConcernTypeIDManyPickLists_manyPickListsDropDown_ClientState', '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}');
    $("input[id$='ctl00_c1_TourCriteriaDetails_tourConcernTypeIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)");

    setParsedElementValue('ctl00_c1_TourCriteriaDetails_tourTypePickList1ItemIDManyPickLists_manyPickListsDropDown_ClientState', '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}');
    $("input[id$='ctl00_c1_TourCriteriaDetails_tourTypePickList1ItemIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)");

    setParsedElementValue('ctl00_c1_TourCriteriaDetails_locationIDManyPickLists_manyPickListsDropDown_ClientState', '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}');
    $("input[id$='ctl00_c1_TourCriteriaDetails_locationIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)");

    setParsedElementValue('ctl00_c1_TourCriteriaDetails_regionIDManyPickLists_manyPickListsDropDown_ClientState', '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}');
    $("input[id$='ctl00_c1_TourCriteriaDetails_regionIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)");

    var dropDownTourBool1 = document.getElementById('dropDownTourBool1');
    if (dropDownTourBool1 != null)
        dropDownTourBool1.selectedIndex = 0;

    setParsedElementValue('ctl00_c1_TourCriteriaDetails_toursManyPickList1ItemIDsManyPickLists_manyPickListsDropDown_ClientState', '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}');
    $("input[id$='ctl00_c1_TourCriteriaDetails_toursManyPickList1ItemIDsManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)");
}

async function clearLeadCriteriaFilters() {
    setParsedElementValue('ctl00_c1_LeadCriteria_leadSourceIDManyPickLists_manyPickListsDropDown_ClientState', '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}');
    $("input[id$='ctl00_c1_LeadCriteria_leadSourceIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)");

    setParsedElementValue('ctl00_c1_LeadCriteria_leadDispositionIDManyPickLists_manyPickListsDropDown_ClientState', '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}');
    $("input[id$='ctl00_c1_LeadCriteria_leadDispositionIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)");

    setParsedElementValue('ctl00_c1_LeadCriteria_leadPickList1ItemIDManyPickLists_manyPickListsDropDown_ClientState', '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}');
    $("input[id$='ctl00_c1_LeadCriteria_leadPickList1ItemIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)");

    setParsedElementValue('ctl00_c1_LeadCriteria_leadPickList2ItemIDManyPickLists_manyPickListsDropDown_ClientState', '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}');
    $("input[id$='ctl00_c1_LeadCriteria_leadPickList2ItemIDManyPickLists_manyPickListsDropDown_Input']").val("All (select or type to search)");
}

/* =========================== SET KPI FILTERS FUNCTIONS =========================== */

function setDateTimeCriteriaFilters(datetimeFilter) {
    if (datetimeFilter.dateRange != null && datetimeFilter.dateRange != 'null')
        document.getElementById('dropDownDateRanges').selectedIndex = datetimeFilter.dateRange;

    // Asegurarse de que se maneja el campo correcto para yearRange
    if (datetimeFilter.yearRange != null && datetimeFilter.yearRange != 'null')
        document.getElementById('dropDownYearRanges').selectedIndex = datetimeFilter.yearRange;  // Cambiar el campo

    if (datetimeFilter.dateRange != 2 && (datetimeFilter.dateRange != null && datetimeFilter.dateRange != 'null'))
        updateDateRange(String(datetimeFilter.dateRange));
    else if (datetimeFilter.dateRange == 2) {
        initial_date = datetimeFilter.startDate;
        last_date = datetimeFilter.endDate;
    }

    //if (datetimeFilter.startDate != null && datetimeFilter.startDate != 'null')
    //    setParsedElementValue('ctl00_c1_DateTimeCriteriaDetails_startDateRadDatePicker_dateInput', datetimeFilter.startDate);

    //if (datetimeFilter.endDate != null && datetimeFilter.endDate != 'null')
    //    setParsedElementValue('ctl00_c1_DateTimeCriteriaDetails_endDateRadDatePicker_dateInput', datetimeFilter.endDate);

    // Ajustar el manejo de dateTypeLogic si es necesario
    if (datetimeFilter.dateTypeLogic != null && datetimeFilter.dateTypeLogic != '')
        setElementValue('dropDownDateTypeLogic', datetimeFilter.dateTypeLogic);

    // Asegúrate de que tierOneDateTypes tiene un valor adecuado
    if (datetimeFilter.tierOneDateTypes != null && datetimeFilter.tierOneDateTypes != '')
        setElementValue('dropDownTierOneDateTypes', datetimeFilter.tierOneDateTypes);
    else
        setElementValue('dropDownTierOneDateTypes', '0');  // Asignar valor predeterminado

    var daysOfWeekMapping = {
        "Sunday": 'ctl00_c1_DateTimeCriteriaDetails_dropDownDaysOfWeek_i0_checkBox',
        "Monday": 'ctl00_c1_DateTimeCriteriaDetails_dropDownDaysOfWeek_i1_checkBox',
        "Tuesday": 'ctl00_c1_DateTimeCriteriaDetails_dropDownDaysOfWeek_i2_checkBox',
        "Wednesday": 'ctl00_c1_DateTimeCriteriaDetails_dropDownDaysOfWeek_i3_checkBox',
        "Thursday": 'ctl00_c1_DateTimeCriteriaDetails_dropDownDaysOfWeek_i4_checkBox',
        "Friday": 'ctl00_c1_DateTimeCriteriaDetails_dropDownDaysOfWeek_i5_checkBox',
        "Saturday": 'ctl00_c1_DateTimeCriteriaDetails_dropDownDaysOfWeek_i6_checkBox'
    };

    var weeksMapping = {};
    for (var i = 1; i <= 52; i++) {
        weeksMapping[i.toString()] = 'ctl00_c1_DateTimeCriteriaDetails_dropDownWeeks_i' + (i - 1) + '_checkBox';
    }

    var monthsMapping = {
        "January": 'ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i0_checkBox',
        "February": 'ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i1_checkBox',
        "March": 'ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i2_checkBox',
        "April": 'ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i3_checkBox',
        "May": 'ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i4_checkBox',
        "June": 'ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i5_checkBox',
        "July": 'ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i6_checkBox',
        "August": 'ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i7_checkBox',
        "September": 'ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i8_checkBox',
        "October": 'ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i9_checkBox',
        "November": 'ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i10_checkBox',
        "December": 'ctl00_c1_DateTimeCriteriaDetails_dropDownMonths_i11_checkBox'
    };

    var yearsMapping = {};
    for (var j = 0; j <= 24; j++) {
        yearsMapping[(2023 - j).toString()] = 'ctl00_c1_DateTimeCriteriaDetails_dropDownYears_i' + j + '_checkBox';
    }

    var timesMapping = {
        "8:00 AM": 'ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_i0_checkBox',
        "8:40 AM": 'ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_i1_checkBox',
        "10:00 AM": 'ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_i2_checkBox',
        "11:30 AM": 'ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_i3_checkBox',
        "12:00 PM": 'ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_i4_checkBox',
        "1:00 PM": 'ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_i5_checkBox',
        "2:00 PM": 'ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_i6_checkBox',
        "3:45 PM": 'ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_i7_checkBox',
        "4:00 PM": 'ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_i8_checkBox',
        "6:00 PM": 'ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_i9_checkBox',
        "8:00 PM": 'ctl00_c1_DateTimeCriteriaDetails_dropDownTourTimes_i10_checkBox'
    };

    // Manejar checkboxes para daysOfWeek, weeks, months, years y times
    if (datetimeFilter.daysOfWeek != 'All')
        setCheckboxes(daysOfWeekMapping, datetimeFilter.daysOfWeek);

    if (datetimeFilter.weeks != 'All')
        setCheckboxes(weeksMapping, datetimeFilter.weeks);

    if (datetimeFilter.months != 'All')
        setCheckboxes(monthsMapping, datetimeFilter.months);

    if (datetimeFilter.years != 'All')
        setCheckboxes(yearsMapping, datetimeFilter.years);

    if (datetimeFilter.times != 'All')
        setCheckboxes(timesMapping, datetimeFilter.times);
}

function setBasicCriteriaFilters(basicFilter) {
    if (basicFilter.customerId != null && basicFilter.customerId != 'null') {
        setElementValue('customerIDTextBox', basicFilter.customerId);
    }

    if (basicFilter.extId != null && basicFilter.extId != 'null') {
        setElementValue('externalCustomerIDTextBox', basicFilter.extId);
    }

    if (basicFilter.extLeadId != null && basicFilter.extLeadId != 'null') {
        setElementValue('externalLeadIDTextBox', basicFilter.extLeadId);
    }

    if (basicFilter.tourId != null && basicFilter.tourId != 'null') {
        setElementValue('tourIDTextBox', basicFilter.tourId);
    }

    if (basicFilter.extTourId != null && basicFilter.extTourId != 'null') {
        setElementValue('externalTourIDTextBox', basicFilter.extTourId);
    }

    if (basicFilter.purchaseId != null && basicFilter.purchaseId != 'null') {
        setElementValue('purchaseIDTextBox', basicFilter.purchaseId);
    }

    if (basicFilter.extPurchaseId != null && basicFilter.extPurchaseId != 'null') {
        setElementValue('externalPurchaseIDTextBox', basicFilter.extPurchaseId);
    }

    if (basicFilter.notes != null && basicFilter.notes != 'null') {
        setElementValue('textBoxNotes', basicFilter.notes);
    }

    if (basicFilter.user != null && basicFilter.user != 'null') {
        setParsedElementValue('ctl00_c1_BasicCriteria1_dropDownUser_ClientState', basicFilter.user);
        $("input[id$='ctl00_c1_BasicCriteria1_dropDownUser_Input']").val(JSON.parse(basicFilter.user).text);
    }

    if (basicFilter.importNmbr != null && basicFilter.importNmbr != 'null') {
        setElementValue('textBoxImportNumber', basicFilter.importNmbr);
    }

    if (basicFilter.purchase != null && basicFilter.purchase != 'null') {
        setElementValue('dropDownPurchasesCount', basicFilter.purchase);
    }
}

function setCustomersCriteriaFilters(customersFilter) {
    if (customersFilter.customerDisposition != null && customersFilter.customerDisposition != 'null' && customersFilter.customerDisposition.trim() !== '') {
        setParsedElementValue('ctl00_c1_CustomersCriteriaDetails_customerDispositionIDManyPickLists_manyPickListsDropDown_ClientState', customersFilter.customerDisposition);
        $("input[id$='ctl00_c1_CustomersCriteriaDetails_customerDispositionIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(customersFilter.customerDisposition).text);
    }

    if (customersFilter.name != null && customersFilter.name != 'null' && customersFilter.name.trim() !== '') {
        setElementValue('textBoxName', customersFilter.name);
    }

    if (customersFilter.sex != null && customersFilter.sex != 'null' && customersFilter.sex.trim() !== '') {
        setElementValue('dropDownSex', customersFilter.sex);
    }

    if (customersFilter.customersPickList1Item != null && customersFilter.customersPickList1Item != 'null' && customersFilter.customersPickList1Item.trim() !== '') {
        setParsedElementValue('ctl00_c1_CustomersCriteriaDetails_customersPickList1ItemIDManyPickLists_manyPickListsDropDown_ClientState', customersFilter.customersPickList1Item);
        $("input[id$='ctl00_c1_CustomersCriteriaDetails_customersPickList1ItemIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(customersFilter.customersPickList1Item).text);
    }

    if (customersFilter.guestType != null && customersFilter.guestType != 'null' && customersFilter.guestType.trim() !== '') {
        setParsedElementValue('ctl00_c1_CustomersCriteriaDetails_guestTypeIDManyPickLists_manyPickListsDropDown_ClientState', customersFilter.guestType);
        $("input[id$='ctl00_c1_CustomersCriteriaDetails_guestTypeIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(customersFilter.guestType).text);
    }

    if (customersFilter.guestSex != null && customersFilter.guestSex != 'null' && customersFilter.guestSex.trim() !== '') {
        setElementValue('dropDownGuestSex', customersFilter.guestSex);
    }

    if (customersFilter.customersPickList2Item != null && customersFilter.customersPickList2Item != 'null' && customersFilter.customersPickList2Item.trim() !== '') {
        setParsedElementValue('ctl00_c1_CustomersCriteriaDetails_customersPickList2ItemIDManyPickLists_manyPickListsDropDown_ClientState', customersFilter.customersPickList2Item);
        $("input[id$='ctl00_c1_CustomersCriteriaDetails_customersPickList2ItemIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(customersFilter.customersPickList2Item).text);
    }

    if (customersFilter.incomeType != null && customersFilter.incomeType != 'null' && customersFilter.incomeType.trim() !== '') {
        setParsedElementValue('ctl00_c1_CustomersCriteriaDetails_incomeTypeIDManyPickLists_manyPickListsDropDown_ClientState', customersFilter.incomeType);
        $("input[id$='ctl00_c1_CustomersCriteriaDetails_incomeTypeIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(customersFilter.incomeType).text);
    }

    if (customersFilter.customersPickList3Item != null && customersFilter.customersPickList3Item != 'null' && customersFilter.customersPickList3Item.trim() !== '') {
        setParsedElementValue('ctl00_c1_CustomersCriteriaDetails_customersPickList3ItemIDManyPickLists_manyPickListsDropDown_ClientState', customersFilter.customersPickList3Item);
        $("input[id$='ctl00_c1_CustomersCriteriaDetails_customersPickList3ItemIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(customersFilter.customersPickList3Item).text);
    }

    if (customersFilter.customersDecimal1 != null && customersFilter.customersDecimal1 != 'null' && customersFilter.customersDecimal1.trim() !== '') {
        setElementValue('dropDownCustomersDecimal1', customersFilter.customersDecimal1);
    }

    if (customersFilter.customersBool1 != null && customersFilter.customersBool1 != 'null' && customersFilter.customersBool1.trim() !== '') {
        setElementValue('dropDownCustomersBool1', customersFilter.customersBool1);
    }

    if (customersFilter.phone != null && customersFilter.phone != 'null' && customersFilter.phone.trim() !== '') {
        setElementValue('textBoxPhone', customersFilter.phone);
    }

    if (customersFilter.streetAddress != null && customersFilter.streetAddress != 'null' && customersFilter.streetAddress.trim() !== '') {
        setElementValue('TextBoxStreetAddress', customersFilter.streetAddress);
    }

    if (customersFilter.streetAddress2 != null && customersFilter.streetAddress2 != 'null' && customersFilter.streetAddress2.trim() !== '') {
        setElementValue('TextBoxStreetAddress2', customersFilter.streetAddress2);
    }

    if (customersFilter.city != null && customersFilter.city != 'null' && customersFilter.city.trim() !== '') {
        setElementValue('textBoxCity', customersFilter.city);
    }

    if (customersFilter.state != null && customersFilter.state != 'null' && customersFilter.state.trim() !== '') {
        setParsedElementValue('ctl00_c1_CustomersCriteriaDetails_stateIDManyPickLists_manyPickListsDropDown_ClientState', customersFilter.state);
        $("input[id$='ctl00_c1_CustomersCriteriaDetails_stateIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(customersFilter.state).text);
    }

    if (customersFilter.zipcode != null && customersFilter.zipcode != 'null' && customersFilter.zipcode.trim() !== '') {
        setElementValue('textBoxZipcode', customersFilter.zipcode);
    }

    if (customersFilter.country != null && customersFilter.country != 'null' && customersFilter.country.trim() !== '') {
        setParsedElementValue('ctl00_c1_CustomersCriteriaDetails_countryIDManyPickLists_manyPickListsDropDown_ClientState', customersFilter.country);
        $("input[id$='ctl00_c1_CustomersCriteriaDetails_countryIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(customersFilter.country).text);
    }

    if (customersFilter.businessName != null && customersFilter.businessName != 'null' && customersFilter.businessName.trim() !== '') {
        setElementValue('TextBoxBusiness', customersFilter.businessName);
    }

    if (customersFilter.email != null && customersFilter.email != 'null' && customersFilter.email.trim() !== '') {
        setElementValue('TextBoxEmail', customersFilter.email);
    }

    if (customersFilter.customersPickList4Item != null && customersFilter.customersPickList4Item != 'null' && customersFilter.customersPickList4Item.trim() !== '') {
        setParsedElementValue('ctl00_c1_CustomersCriteriaDetails_customersPickList4ItemIDManyPickLists_manyPickListsDropDown_ClientState', customersFilter.customersPickList4Item);
        $("input[id$='ctl00_c1_CustomersCriteriaDetails_customersPickList4ItemIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(customersFilter.customersPickList4Item).text);
    }

    if (customersFilter.customersBool2 != null && customersFilter.customersBool2 != 'null' && customersFilter.customersBool2.trim() !== '') {
        setElementValue('dropDownCustomersBool2', customersFilter.customersBool2);
    }

    if (customersFilter.customersText != null && customersFilter.customersText != 'null' && customersFilter.customersText.trim() !== '') {
        setElementValue('TextBoxCustomersText1', customersFilter.customersText);
    }

    if (customersFilter.customersText2 != null && customersFilter.customersText2 != 'null' && customersFilter.customersText2.trim() !== '') {
        setElementValue('TextBoxCustomersText2', customersFilter.customersText2);
    }

    if (customersFilter.customersText3 != null && customersFilter.customersText3 != 'null' && customersFilter.customersText3.trim() !== '') {
        setElementValue('TextBoxCustomersText3', customersFilter.customersText3);
    }
}

function setCancellationCriteriaFilters(cancellationFilter) {
    if (cancellationFilter.cxlRequestReason != null && cancellationFilter.cxlRequestReason != 'null') {
        setParsedElementValue('ctl00_c1_CancellationCriteriaDetails_dropDownCancellationReasonTypes_ClientState', cancellationFilter.cxlRequestReason);
        $("input[id$='ctl00_c1_CancellationCriteriaDetails_dropDownCancellationReasonTypes_Input']").val(JSON.parse(cancellationFilter.cxlRequestReason).text);
    }

    if (cancellationFilter.cxlRequestReceived != null && cancellationFilter.cxlRequestReceived != 'null') {
        setParsedElementValue('ctl00_c1_CancellationCriteriaDetails_dropDownCancellationReceivedTypes_ClientState', cancellationFilter.cxlRequestReceived);
        $("input[id$='ctl00_c1_CancellationCriteriaDetails_dropDownCancellationReceivedTypes_Input']").val(JSON.parse(cancellationFilter.cxlRequestReceived).text);
    }

    if (cancellationFilter.withinRescission != null && cancellationFilter.withinRescission != 'null') {
        setElementValue('dropDownWithinRescission', cancellationFilter.withinRescission);
    }

    if (cancellationFilter.cxlRequestDisposition != null && cancellationFilter.cxlRequestDisposition != 'null') {
        setParsedElementValue('ctl00_c1_CancellationCriteriaDetails_dropDownCancellationDispositionTypes_ClientState', cancellationFilter.cxlRequestDisposition);
        $("input[id$='ctl00_c1_CancellationCriteriaDetails_dropDownCancellationDispositionTypes_Input']").val(JSON.parse(cancellationFilter.cxlRequestDisposition).text);
    }

    if (cancellationFilter.cxlRequestStatus != null && cancellationFilter.cxlRequestStatus != 'null') {
        setParsedElementValue('ctl00_c1_CancellationCriteriaDetails_dropDownCancellationStatusTypes_ClientState', cancellationFilter.cxlRequestStatus);
        $("input[id$='ctl00_c1_CancellationCriteriaDetails_dropDownCancellationStatusTypes_Input']").val(JSON.parse(cancellationFilter.cxlRequestStatus).text);
    }

    if (cancellationFilter.cxlRequestUser != null && cancellationFilter.cxlRequestUser != 'null') {
        setParsedElementValue('ctl00_c1_CancellationCriteriaDetails_dropDownCancellationUser_manyPickListsDropDown_ClientState', cancellationFilter.cxlRequestUser);
        $("input[id$='ctl00_c1_CancellationCriteriaDetails_dropDownCancellationUser_manyPickListsDropDown_Input']").val(JSON.parse(cancellationFilter.cxlRequestUser).text);
    }
}

function setPurchaseCriteriaFilters(purchaseFilter) {
    if (purchaseFilter.productCategory != null && purchaseFilter.productCategory != 'null' && purchaseFilter.productCategory.trim() !== '') {
        setParsedElementValue('ctl00_c1_PurchasessCriteriaDetails_productCategoryIDManyPickLists_manyPickListsDropDown_ClientState', purchaseFilter.productCategory);
        $("input[id$='ctl00_c1_PurchasessCriteriaDetails_productCategoryIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(purchaseFilter.productCategory).text);
    }

    if (purchaseFilter.product != null && purchaseFilter.product != 'null' && purchaseFilter.product.trim() !== '') {
        setParsedElementValue('ctl00_c1_PurchasessCriteriaDetails_productIDManyPickLists_manyPickListsDropDown_ClientState', purchaseFilter.product);
        $("input[id$='ctl00_c1_PurchasessCriteriaDetails_productIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(purchaseFilter.product).text);
    }

    if (purchaseFilter.subProduct != null && purchaseFilter.subProduct != 'null' && purchaseFilter.subProduct.trim() !== '') {
        setParsedElementValue('ctl00_c1_PurchasessCriteriaDetails_subProductIDManyPickLists_manyPickListsDropDown_ClientState', purchaseFilter.subProduct);
        $("input[id$='ctl00_c1_PurchasessCriteriaDetails_subProductIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(purchaseFilter.subProduct).text);
    }

    if (purchaseFilter.saleAmount != null && purchaseFilter.saleAmount != 'null' && purchaseFilter.saleAmount.trim() !== '') {
        setElementValue('dropDownSaleAmount', purchaseFilter.saleAmount);
    }

    if (purchaseFilter.downPaymentAmount != null && purchaseFilter.downPaymentAmount != 'null' && purchaseFilter.downPaymentAmount.trim() !== '') {
        setElementValue('dropDownDownPaymentAmount', purchaseFilter.downPaymentAmount);
    }

    if (purchaseFilter.fees1 != null && purchaseFilter.fees1 != 'null' && purchaseFilter.fees1.trim() !== '') {
        setElementValue('dropDownDownFees1Amount', purchaseFilter.fees1);
    }

    if (purchaseFilter.fees2 != null && purchaseFilter.fees2 != 'null' && purchaseFilter.fees2.trim() !== '') {
        setElementValue('dropDownDownFees2Amount', purchaseFilter.fees2);
    }

    if (purchaseFilter.fees3 != null && purchaseFilter.fees3 != 'null' && purchaseFilter.fees3.trim() !== '') {
        setElementValue('dropDownDownFees3Amount', purchaseFilter.fees3);
    }

    if (purchaseFilter.fees4 != null && purchaseFilter.fees4 != 'null' && purchaseFilter.fees4.trim() !== '') {
        setElementValue('dropDownDownFees4Amount', purchaseFilter.fees4);
    }

    if (purchaseFilter.proposalValue != null && purchaseFilter.proposalValue != 'null' && purchaseFilter.proposalValue.trim() !== '') {
        setElementValue('dropDownDownFees5Amount', purchaseFilter.proposalValue);
    }

    if (purchaseFilter.saleTypes != null && purchaseFilter.saleTypes != 'null' && purchaseFilter.saleTypes.trim() !== '') {
        setParsedElementValue('ctl00_c1_PurchasessCriteriaDetails_dropDownSaleTypes_ClientState', purchaseFilter.saleTypes);
        $("input[id$='ctl00_c1_PurchasessCriteriaDetails_dropDownSaleTypes_Input']").val(JSON.parse(purchaseFilter.saleTypes).text);
    }

    if (purchaseFilter.saleStatus != null && purchaseFilter.saleStatus != 'null' && purchaseFilter.saleStatus.trim() !== '') {
        setParsedElementValue('ctl00_c1_PurchasessCriteriaDetails_dropDownSaleStatusTypes_ClientState', purchaseFilter.saleStatus);
        $("input[id$='ctl00_c1_PurchasessCriteriaDetails_dropDownSaleStatusTypes_Input']").val(JSON.parse(purchaseFilter.saleStatus).text);
    }

    if (purchaseFilter.milestones != null && purchaseFilter.milestones != 'null' && purchaseFilter.milestones.trim() !== '') {
        setElementValue('milestoneStatesDropDown', purchaseFilter.milestones);
    }

    if (purchaseFilter.saleDisposition != null && purchaseFilter.saleDisposition != 'null' && purchaseFilter.saleDisposition.trim() !== '') {
        setParsedElementValue('ctl00_c1_PurchasessCriteriaDetails_saleDispositionIDManyPickLists_manyPickListsDropDown_ClientState', purchaseFilter.saleDisposition);
        $("input[id$='ctl00_c1_PurchasessCriteriaDetails_saleDispositionIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(purchaseFilter.saleDisposition).text);
    }

    if (purchaseFilter.purchasesPickList1Item != null && purchaseFilter.purchasesPickList1Item != 'null' && purchaseFilter.purchasesPickList1Item.trim() !== '') {
        setParsedElementValue('ctl00_c1_PurchasessCriteriaDetails_purchasesPickList1ItemIDManyPickLists_manyPickListsDropDown_ClientState', purchaseFilter.purchasesPickList1Item);
        $("input[id$='ctl00_c1_PurchasessCriteriaDetails_purchasesPickList1ItemIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(purchaseFilter.purchasesPickList1Item).text);
    }

    if (purchaseFilter.purchasesPickList2Item != null && purchaseFilter.purchasesPickList2Item != 'null' && purchaseFilter.purchasesPickList2Item.trim() !== '') {
        setParsedElementValue('ctl00_c1_PurchasessCriteriaDetails_purchasesPickList2ItemIDManyPickLists_manyPickListsDropDown_ClientState', purchaseFilter.purchasesPickList2Item);
        $("input[id$='ctl00_c1_PurchasessCriteriaDetails_purchasesPickList2ItemIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(purchaseFilter.purchasesPickList2Item).text);
    }

    if (purchaseFilter.purchasesPickList3Item != null && purchaseFilter.purchasesPickList3Item != 'null' && purchaseFilter.purchasesPickList3Item.trim() !== '') {
        setParsedElementValue('ctl00_c1_PurchasessCriteriaDetails_purchasesPickList3ItemIDManyPickLists_manyPickListsDropDown_ClientState', purchaseFilter.purchasesPickList3Item);
        $("input[id$='ctl00_c1_PurchasessCriteriaDetails_purchasesPickList3ItemIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(purchaseFilter.purchasesPickList3Item).text);
    }

    if (purchaseFilter.purchasesText1 != null && purchaseFilter.purchasesText1 != 'null' && purchaseFilter.purchasesText1.trim() !== '') {
        setElementValue('textBoxPurchasesText1', purchaseFilter.purchasesText1);
    }

    if (purchaseFilter.purchasesText2 != null && purchaseFilter.purchasesText2 != 'null' && purchaseFilter.purchasesText2.trim() !== '') {
        setElementValue('textBoxPurchasesText2', purchaseFilter.purchasesText2);
    }

    if (purchaseFilter.purchasesBool1 != null && purchaseFilter.purchasesBool1 != 'null' && purchaseFilter.purchasesBool1.trim() !== '') {
        setElementValue('dropDownPurchasesBool1', purchaseFilter.purchasesBool1);
    }

    if (purchaseFilter.purchasesBool2 != null && purchaseFilter.purchasesBool2 != 'null' && purchaseFilter.purchasesBool2.trim() !== '') {
        setElementValue('dropDownPurchasesBool2', purchaseFilter.purchasesBool2);
    }
}

function setSalesCriteriaFilters(salesFilter) {
    if (salesFilter.salesTeam != null && salesFilter.salesTeam != 'null' && salesFilter.salesTeam.trim() !== '') {
        setParsedElementValue('ctl00_c1_SalesCriteriaDetails_salesTeamIDManyPickLists_manyPickListsDropDown_ClientState', salesFilter.salesTeam);
        $("input[id$='ctl00_c1_SalesCriteriaDetails_salesTeamIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(salesFilter.salesTeam).text);
    }

    if (salesFilter.podium != null && salesFilter.podium != 'null' && salesFilter.podium.trim() !== '') {
        setParsedElementValue('ctl00_c1_SalesCriteriaDetails_podiumIDManyPickLists_manyPickListsDropDown_ClientState', salesFilter.podium);
        $("input[id$='ctl00_c1_SalesCriteriaDetails_podiumIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(salesFilter.podium).text);
    }

    if (salesFilter.salesAgent != null && salesFilter.salesAgent != 'null' && salesFilter.salesAgent.trim() !== '') {
        setParsedElementValue('ctl00_c1_SalesCriteriaDetails_salesRepIDManyPickLists_manyPickListsDropDown_ClientState', salesFilter.salesAgent);
        $("input[id$='ctl00_c1_SalesCriteriaDetails_salesRepIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(salesFilter.salesAgent).text);
    }

    if (salesFilter.salesCloser1 != null && salesFilter.salesCloser1 != 'null' && salesFilter.salesCloser1.trim() !== '') {
        setParsedElementValue('ctl00_c1_SalesCriteriaDetails_salesCloser1IDManyPickLists_manyPickListsDropDown_ClientState', salesFilter.salesCloser1);
        $("input[id$='ctl00_c1_SalesCriteriaDetails_salesCloser1IDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(salesFilter.salesCloser1).text);
    }

    if (salesFilter.salesCloser2 != null && salesFilter.salesCloser2 != 'null' && salesFilter.salesCloser2.trim() !== '') {
        setParsedElementValue('ctl00_c1_SalesCriteriaDetails_salesCloser2IDManyPickLists_manyPickListsDropDown_ClientState', salesFilter.salesCloser2);
        $("input[id$='ctl00_c1_SalesCriteriaDetails_salesCloser2IDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(salesFilter.salesCloser2).text);
    }

    if (salesFilter.extRep != null && salesFilter.extRep != 'null' && salesFilter.extRep.trim() !== '') {
        setParsedElementValue('ctl00_c1_SalesCriteriaDetails_exitIDManyPickLists_manyPickListsDropDown_ClientState', salesFilter.extRep);
        $("input[id$='ctl00_c1_SalesCriteriaDetails_exitIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(salesFilter.extRep).text);
    }

    if (salesFilter.verificationOfficer != null && salesFilter.verificationOfficer != 'null' && salesFilter.verificationOfficer.trim() !== '') {
        setParsedElementValue('ctl00_c1_SalesCriteriaDetails_verificationOfficerIDManyPickLists_manyPickListsDropDown_ClientState', salesFilter.verificationOfficer);
        $("input[id$='ctl00_c1_SalesCriteriaDetails_verificationOfficerIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(salesFilter.verificationOfficer).text);
    }

    if (salesFilter.salesPickList1Item != null && salesFilter.salesPickList1Item != 'null' && salesFilter.salesPickList1Item.trim() !== '') {
        setParsedElementValue('ctl00_c1_SalesCriteriaDetails_salesPickList1ItemIDManyPickLists_manyPickListsDropDown_ClientState', salesFilter.salesPickList1Item);
        $("input[id$='ctl00_c1_SalesCriteriaDetails_salesPickList1ItemIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(salesFilter.salesPickList1Item).text);
    }

    if (salesFilter.salesExitTeam != null && salesFilter.salesExitTeam != 'null' && salesFilter.salesExitTeam.trim() !== '') {
        setParsedElementValue('ctl00_c1_SalesCriteriaDetails_exitTeamIDManyPickLists_manyPickListsDropDown_ClientState', salesFilter.salesExitTeam);
        $("input[id$='ctl00_c1_SalesCriteriaDetails_exitTeamIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(salesFilter.salesExitTeam).text);
    }

    if (salesFilter.extRep1 != null && salesFilter.extRep1 != 'null' && salesFilter.extRep1.trim() !== '') {
        setParsedElementValue('ctl00_c1_SalesCriteriaDetails_exitRep1IDManyPickLists_manyPickListsDropDown_ClientState', salesFilter.extRep1);
        $("input[id$='ctl00_c1_SalesCriteriaDetails_exitRep1IDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(salesFilter.extRep1).text);
    }

    if (salesFilter.extRep2 != null && salesFilter.extRep2 != 'null' && salesFilter.extRep2.trim() !== '') {
        setParsedElementValue('ctl00_c1_SalesCriteriaDetails_exitRep2IDManyPickLists_manyPickListsDropDown_ClientState', salesFilter.extRep2);
        $("input[id$='ctl00_c1_SalesCriteriaDetails_exitRep2IDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(salesFilter.extRep2).text);
    }

    if (salesFilter.extRep3 != null && salesFilter.extRep3 != 'null' && salesFilter.extRep3.trim() !== '') {
        setParsedElementValue('ctl00_c1_SalesCriteriaDetails_exitRep3IDManyPickLists_manyPickListsDropDown_ClientState', salesFilter.extRep3);
        $("input[id$='ctl00_c1_SalesCriteriaDetails_exitRep3IDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(salesFilter.extRep3).text);
    }

    if (salesFilter.salesText != null && salesFilter.salesText != 'null' && salesFilter.salesText.trim() !== '') {
        setElementValue('textBoxSalesText1', salesFilter.salesText);
    }

    if (salesFilter.salesDecimal != null && salesFilter.salesDecimal != 'null' && salesFilter.salesDecimal.trim() !== '') {
        setElementValue('dropDownSalesDecimal1', salesFilter.salesDecimal);
    }

    if (salesFilter.salesBool != null && salesFilter.salesBool != 'null' && salesFilter.salesBool.trim() !== '') {
        setElementValue('dropDownSalesBool1', salesFilter.salesBool);
    }
}

function setMarketingCriteriaFilters(marketingFilter) {
    if (marketingFilter.marketingTeam != null && marketingFilter.marketingTeam != 'null' && marketingFilter.marketingTeam.trim() !== '') {
        setParsedElementValue('ctl00_c1_MarketingCriteriaDetails_marketingTeamIDManyPickLists_manyPickListsDropDown_ClientState', marketingFilter.marketingTeam);
        $("input[id$='ctl00_c1_MarketingCriteriaDetails_marketingTeamIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(marketingFilter.marketingTeam).text);
    }

    if (marketingFilter.marketingAgent != null && marketingFilter.marketingAgent != 'null' && marketingFilter.marketingAgent.trim() !== '') {
        setParsedElementValue('ctl00_c1_MarketingCriteriaDetails_marketingAgentIDManyPickLists_manyPickListsDropDown_ClientState', marketingFilter.marketingAgent);
        $("input[id$='ctl00_c1_MarketingCriteriaDetails_marketingAgentIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(marketingFilter.marketingAgent).text);
    }

    if (marketingFilter.marketingCloser != null && marketingFilter.marketingCloser != 'null' && marketingFilter.marketingCloser.trim() !== '') {
        setParsedElementValue('ctl00_c1_MarketingCriteriaDetails_marketingCloserIDManyPickLists_manyPickListsDropDown_ClientState', marketingFilter.marketingCloser);
        $("input[id$='ctl00_c1_MarketingCriteriaDetails_marketingCloserIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(marketingFilter.marketingCloser).text);
    }

    if (marketingFilter.confirmer != null && marketingFilter.confirmer != 'null' && marketingFilter.confirmer.trim() !== '') {
        setParsedElementValue('ctl00_c1_MarketingCriteriaDetails_confirmerIDManyPickLists_manyPickListsDropDown_ClientState', marketingFilter.confirmer);
        $("input[id$='ctl00_c1_MarketingCriteriaDetails_confirmerIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(marketingFilter.confirmer).text);
    }

    if (marketingFilter.resetter != null && marketingFilter.resetter != 'null' && marketingFilter.resetter.trim() !== '') {
        setParsedElementValue('ctl00_c1_MarketingCriteriaDetails_resetterIDManyPickLists_manyPickListsDropDown_ClientState', marketingFilter.resetter);
        $("input[id$='ctl00_c1_MarketingCriteriaDetails_resetterIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(marketingFilter.resetter).text);
    }

    if (marketingFilter.venue != null && marketingFilter.venue != 'null' && marketingFilter.venue.trim() !== '') {
        setParsedElementValue('ctl00_c1_MarketingCriteriaDetails_venueIDManyPickLists_manyPickListsDropDown_ClientState', marketingFilter.venue);
        $("input[id$='ctl00_c1_MarketingCriteriaDetails_venueIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(marketingFilter.venue).text);
    }

    if (marketingFilter.campaign != null && marketingFilter.campaign != 'null' && marketingFilter.campaign.trim() !== '') {
        setParsedElementValue('ctl00_c1_MarketingCriteriaDetails_campaignIDManyPickLists_manyPickListsDropDown_ClientState', marketingFilter.campaign);
        $("input[id$='ctl00_c1_MarketingCriteriaDetails_campaignIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(marketingFilter.campaign).text);
    }

    if (marketingFilter.channel != null && marketingFilter.channel != 'null' && marketingFilter.channel.trim() !== '') {
        setParsedElementValue('ctl00_c1_MarketingCriteriaDetails_channelIDManyPickLists_manyPickListsDropDown_ClientState', marketingFilter.channel);
        $("input[id$='ctl00_c1_MarketingCriteriaDetails_channelIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(marketingFilter.channel).text);
    }

    if (marketingFilter.hotel != null && marketingFilter.hotel != 'null' && marketingFilter.hotel.trim() !== '') {
        setParsedElementValue('ctl00_c1_MarketingCriteriaDetails_hotelIDManyPickLists_manyPickListsDropDown_ClientState', marketingFilter.hotel);
        $("input[id$='ctl00_c1_MarketingCriteriaDetails_hotelIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(marketingFilter.hotel).text);
    }

    if (marketingFilter.giftIDs != null && marketingFilter.giftIDs != 'null' && marketingFilter.giftIDs.trim() !== '') {
        setParsedElementValue('ctl00_c1_MarketingCriteriaDetails_giftIDsManyPickLists_manyPickListsDropDown_ClientState', marketingFilter.giftIDs);
        $("input[id$='ctl00_c1_MarketingCriteriaDetails_giftIDsManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(marketingFilter.giftIDs).text);
    }

    if (marketingFilter.marketingPickList1Item1 != null && marketingFilter.marketingPickList1Item1 != 'null' && marketingFilter.marketingPickList1Item1.trim() !== '') {
        setParsedElementValue('ctl00_c1_MarketingCriteriaDetails_marketingPickList1ItemIDManyPickLists_manyPickListsDropDown_ClientState', marketingFilter.marketingPickList1Item1);
        $("input[id$='ctl00_c1_MarketingCriteriaDetails_marketingPickList1ItemIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(marketingFilter.marketingPickList1Item1).text);
    }

    if (marketingFilter.marketingPickList1Item2 != null && marketingFilter.marketingPickList1Item2 != 'null' && marketingFilter.marketingPickList1Item2.trim() !== '') {
        setParsedElementValue('ctl00_c1_MarketingCriteriaDetails_marketingPickList2ItemIDManyPickLists_manyPickListsDropDown_ClientState', marketingFilter.marketingPickList1Item2);
        $("input[id$='ctl00_c1_MarketingCriteriaDetails_marketingPickList2ItemIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(marketingFilter.marketingPickList1Item2).text);
    }

    if (marketingFilter.marketingText != null && marketingFilter.marketingText != 'null' && marketingFilter.marketingText.trim() !== '') {
        setElementValue('textBoxMarketingText1', marketingFilter.marketingText);
    }

    if (marketingFilter.marketingBool != null && marketingFilter.marketingBool != 'null' && marketingFilter.marketingBool.trim() !== '') {
        setElementValue('dropDownMarketingBool1', marketingFilter.marketingBool);
    }

    if (marketingFilter.depositAmount != null && marketingFilter.depositAmount != 'null' && marketingFilter.depositAmount.trim() !== '') {
        setElementValue('dropDownDepositAmount', marketingFilter.depositAmount);
    }

    if (marketingFilter.depositRefundable != null && marketingFilter.depositRefundable != 'null' && marketingFilter.depositRefundable.trim() !== '') {
        setElementValue('dropDownDepositRefundable', marketingFilter.depositRefundable);
    }
}

function setTourCriteriaFilters(tourFilter) {
    if (tourFilter.tourSource != null && tourFilter.tourSource != 'null' && tourFilter.tourSource.trim() !== '') {
        setParsedElementValue('ctl00_c1_TourCriteriaDetails_tourSourceIDManyPickLists_manyPickListsDropDown_ClientState', tourFilter.tourSource);
        $("input[id$='ctl00_c1_TourCriteriaDetails_tourSourceIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(tourFilter.tourSource).text);
    }

    if (tourFilter.office != null && tourFilter.office != 'null' && tourFilter.office.trim() !== '') {
        setParsedElementValue('ctl00_c1_TourCriteriaDetails_tourTypeIDManyPickLists_manyPickListsDropDown_ClientState', tourFilter.office);
        $("input[id$='ctl00_c1_TourCriteriaDetails_tourTypeIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(tourFilter.office).text);
    }

    var tourStatusMapping = {
        "-Assigned": 'ctl00_c1_TourCriteriaDetails_dropDownTourStatusTypes_i0_checkBox',
        "-Unassigned": 'ctl00_c1_TourCriteriaDetails_dropDownTourStatusTypes_i1_checkBox',
        "-Exclude": 'ctl00_c1_TourCriteriaDetails_dropDownTourStatusTypes_i2_checkBox',
        "Booked": 'ctl00_c1_TourCriteriaDetails_dropDownTourStatusTypes_i3_checkBox',
        "Cancelled": 'ctl00_c1_TourCriteriaDetails_dropDownTourStatusTypes_i4_checkBox',
        "No Show": 'ctl00_c1_TourCriteriaDetails_dropDownTourStatusTypes_i5_checkBox',
        "Show Qualified": 'ctl00_c1_TourCriteriaDetails_dropDownTourStatusTypes_i6_checkBox',
        "Not Qualified": 'ctl00_c1_TourCriteriaDetails_dropDownTourStatusTypes_i7_checkBox',
        "LO-NotContacted": 'ctl00_c1_TourCriteriaDetails_dropDownTourStatusTypes_i8_checkBox',
        "LO-Unscheduled": 'ctl00_c1_TourCriteriaDetails_dropDownTourStatusTypes_i9_checkBox',
        "LC-NotInterested": 'ctl00_c1_TourCriteriaDetails_dropDownTourStatusTypes_i10_checkBox',
        "LC-Ineligible": 'ctl00_c1_TourCriteriaDetails_dropDownTourStatusTypes_i11_checkBox',
        "BO-Confirmed": 'ctl00_c1_TourCriteriaDetails_dropDownTourStatusTypes_i12_checkBox',
        "BC-CustomMarketing": 'ctl00_c1_TourCriteriaDetails_dropDownTourStatusTypes_i13_checkBox',
        "TC-CourtesyTour": 'ctl00_c1_TourCriteriaDetails_dropDownTourStatusTypes_i14_checkBox',
        "TC-CustomSales": 'ctl00_c1_TourCriteriaDetails_dropDownTourStatusTypes_i15_checkBox',
        "Ignore": 'ctl00_c1_TourCriteriaDetails_dropDownTourStatusTypes_i16_checkBox'
    };

    if (tourFilter.tourStatus != 'All' && tourFilter.tourStatus.trim() !== '') {
        setCheckboxes(tourStatusMapping, tourFilter.tourStatus);
        $("input[id$='ctl00_c1_TourCriteriaDetails_dropDownTourStatusTypes_Input']").val(
            JSON.parse('{"logEntries": [], "value": "' + tourFilter.tourStatus + '", "text": "' + tourFilter.tourStatus + '", "enabled": true, "checkedIndices": [], "checkedItemsTextOverflows": false }').text
        );
    }

    if (tourFilter.tourDisposition != null && tourFilter.tourDisposition != 'null' && tourFilter.tourDisposition.trim() !== '') {
        setParsedElementValue('ctl00_c1_TourCriteriaDetails_tourConcernTypeIDManyPickLists_manyPickListsDropDown_ClientState', tourFilter.tourDisposition);
        $("input[id$='ctl00_c1_TourCriteriaDetails_tourConcernTypeIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(tourFilter.tourDisposition).text);
    }

    if (tourFilter.importMethod != null && tourFilter.importMethod != 'null' && tourFilter.importMethod.trim() !== '') {
        setParsedElementValue('ctl00_c1_TourCriteriaDetails_tourTypePickList1ItemIDManyPickLists_manyPickListsDropDown_ClientState', tourFilter.importMethod);
        $("input[id$='ctl00_c1_TourCriteriaDetails_tourTypePickList1ItemIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(tourFilter.importMethod).text);
    }

    if (tourFilter.businessPartner != null && tourFilter.businessPartner != 'null' && tourFilter.businessPartner.trim() !== '') {
        setParsedElementValue('ctl00_c1_TourCriteriaDetails_locationIDManyPickLists_manyPickListsDropDown_ClientState', tourFilter.businessPartner);
        $("input[id$='ctl00_c1_TourCriteriaDetails_locationIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(tourFilter.businessPartner).text);
    }

    if (tourFilter.businessGroup != null && tourFilter.businessGroup != 'null' && tourFilter.businessGroup.trim() !== '') {
        setParsedElementValue('ctl00_c1_TourCriteriaDetails_regionIDManyPickLists_manyPickListsDropDown_ClientState', tourFilter.businessGroup);
        $("input[id$='ctl00_c1_TourCriteriaDetails_regionIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(tourFilter.businessGroup).text);
    }

    if (tourFilter.isShow != null && tourFilter.isShow.trim() !== '') {
        var dropDownTourBool1 = document.getElementById('dropDownTourBool1');
        if (dropDownTourBool1 != null)
            setElementValue('dropDownTourBool1', tourFilter.isShow);
    }

    if (tourFilter.saleBonusGifts != null && tourFilter.saleBonusGifts != 'null' && tourFilter.saleBonusGifts.trim() !== '') {
        setParsedElementValue('ctl00_c1_TourCriteriaDetails_toursManyPickList1ItemIDsManyPickLists_manyPickListsDropDown_ClientState', tourFilter.saleBonusGifts);
        $("input[id$='ctl00_c1_TourCriteriaDetails_toursManyPickList1ItemIDsManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(tourFilter.saleBonusGifts).text);
    }
}

function setLeadCriteriaFilters(leadFilter) {
    if (leadFilter.leadSourceFile != null && leadFilter.leadSourceFile != 'null' && leadFilter.leadSourceFile.trim() !== '') {
        setParsedElementValue('ctl00_c1_LeadCriteria_leadSourceIDManyPickLists_manyPickListsDropDown_ClientState', leadFilter.leadSourceFile);
        $("input[id$='ctl00_c1_LeadCriteria_leadSourceIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(leadFilter.leadSourceFile).text);
    }

    if (leadFilter.leadDisposition != null && leadFilter.leadDisposition != 'null' && leadFilter.leadDisposition.trim() !== '') {
        setParsedElementValue('ctl00_c1_LeadCriteria_leadDispositionIDManyPickLists_manyPickListsDropDown_ClientState', leadFilter.leadDisposition);
        $("input[id$='ctl00_c1_LeadCriteria_leadDispositionIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(leadFilter.leadDisposition).text);
    }

    if (leadFilter.calendarName != null && leadFilter.calendarName != 'null' && leadFilter.calendarName.trim() !== '') {
        setParsedElementValue('ctl00_c1_LeadCriteria_leadPickList1ItemIDManyPickLists_manyPickListsDropDown_ClientState', leadFilter.calendarName);
        $("input[id$='ctl00_c1_LeadCriteria_leadPickList1ItemIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(leadFilter.calendarName).text);
    }

    if (leadFilter.leadSource != null && leadFilter.leadSource != 'null' && leadFilter.leadSource.trim() !== '') {
        setParsedElementValue('ctl00_c1_LeadCriteria_leadPickList2ItemIDManyPickLists_manyPickListsDropDown_ClientState', leadFilter.leadSource);
        $("input[id$='ctl00_c1_LeadCriteria_leadPickList2ItemIDManyPickLists_manyPickListsDropDown_Input']").val(JSON.parse(leadFilter.leadSource).text);
    }
}

/* =========================== HELPER FUNCTIONS =========================== */

function escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

function displayRowsJoin(joinData) {

    for (var i = 0; i < joinData.length; i++) {

        var data = joinData[i];
        var parentElement = document.querySelectorAll(".join-inputs-container");
        var cont = i + 1;
        parentElement.forEach(function (div) {
            if (div.getAttribute('id') === "joinContainer" + cont) {
                div.setAttribute('data-id', data.reportJoinId);
                var firstJoin = div.querySelector('.firstJoinKey');
                var secondJoin = div.querySelector('.secondJoinKey');
                var uniqueKey = div.querySelector('.uniqueKey');
                var datalist_1 = div.querySelector('.joinSelect');

                firstJoin.value = data.leftKey;
                secondJoin.value = data.rightKey;
                uniqueKey.value = data.uniqueKey;

                if (datalist_1) {
                    datalist_1.setAttribute('data-id', data.reportJoinId);
                    for (var i = 0; i <= datalist_1.options.length; i++) {
                        var option = datalist_1.options[i];
                        datalist_1
                        if (option.value == data.type) {
                            option.selected = true;
                            break;
                        }
                    }
                }
            }
        });
    }
}

function displayRowsKpi(kpiData) {
    var accordion = document.getElementById('accordion');


    for (var i = 0; i < kpiData.length; i++) {

        if (accordion) {

            var divs_header = accordion.querySelectorAll('.header-container');
            var kpi = kpiData[i];
            divs_header.forEach(function (div) {
                kpi.name = sanitizeColumnName(kpi.name);
                if (kpi && div.getAttribute('data-column-name') === kpi.name) {
                    var nextDiv = div.nextElementSibling;
                    var operationElement = div.querySelector('[id^="operation"]');
                    var conditionElement = div.querySelector('[id^="condition"]');
                    var format = nextDiv.querySelector('#mathFormat' + kpi.name + 1);

                    if (kpi.typeFormat != "" && format) {
                        for (var a = 0; a <= format.options.length; a++) {
                            var option = format.options[a];
                            if (option) {
                                if (option.value == kpi.typeFormat) {
                                    option.selected = true;
                                }
                            }
                        }
                    }

                    if (operationElement)
                        operationElement.value = kpi.operation;

                    if (conditionElement)
                        conditionElement.value = kpi.condition;

                    if (kpi.condition != "") {

                        // Split the condition into components based on logical operators (&&, ||)
                        var condition_value = kpi.condition.split(/\s*(&&|\|\|)\s*/);

                        // Regular expression to match "IF ... THEN ... ELSE THEN ..." structure
                        const regex = /IF\s+(.*?)\s+THEN\s+(.*?)\s+ELSE\s+THEN\s+((?:\S+\s+\S+|\S+))/;

                        // Match the condition string using the regex
                        const matches = kpi.condition.match(regex);
                        var cont = 1;

                        if (matches) {
                            // Extract parts of the condition
                            const condition = matches[1]; // The condition after "IF"
                            const afterThen = matches[2]; // Expression after "THEN" before "ELSE THEN"
                            const splitResult = matches[3] ? matches[3].split(' ') : [];
                            const prefix = splitResult[0] || ''; // Prefix part before "row." (can be anything or empty)
                            const rowPart = splitResult[1] || ''; // The "row.CustomersText1" part

                            // Get input fields for condition parts
                            var inputValue11 = nextDiv.querySelector('#ffirstConditionContainerInput' + kpi.name + cont);
                            var inputValue22 = nextDiv.querySelector('#ssecondConditionContainerInput' + kpi.name + cont);
                            var inputValue3 = nextDiv.querySelector('#fourConditionContainerInput' + kpi.name + cont);
                            var ifresult = nextDiv.querySelector('#ifresultConditionContainerInput' + kpi.name + cont);
                            var elseresult = nextDiv.querySelector('#elseresultConditionContainerInput' + kpi.name + cont);
                            var condition_operator = nextDiv.querySelector('#operators' + kpi.name + cont);

                            // Split the condition into its components: left side, operator, and right side
                            let data = condition.split(/\s*(==|!=|>=|<=|>|<)\s*/);

                            // Assign values to the respective input fields
                            if (inputValue11)
                                inputValue11.value = data[0]; // Left side of the condition
                            if (inputValue22)
                                inputValue22.value = data[2]; // Right side of the condition
                            if (inputValue3)
                                inputValue3.value = rowPart; // Row part (e.g., row.CustomersText1)
                            if (ifresult)
                                ifresult.value = afterThen; // Result after "THEN"
                            if (elseresult)
                                elseresult.value = prefix; // Result after "ELSE THEN"

                            // Set the condition operator in the dropdown
                            if (condition_operator && data[1] != '') {
                                for (var a = 0; a <= condition_operator.options.length; a++) {
                                    var option = condition_operator.options[a];
                                    if (option) {
                                        if (option.value == data[1]) {
                                            option.selected = true; // Select the operator
                                        }
                                    }
                                }
                            }
                        } else {
                            // Handle conditions with logical operators (&&, ||)
                            for (var i = 0; i < condition_value.length; i++) {
                                if (i > 0 && (condition_value[i] == "&&" || condition_value[i] == '||')) {
                                    // Add new condition set for logical operator
                                    addCondition('conditionContainerLabel' + kpi.name, kpi.name, cont + 1);
                                    var logical_operator = nextDiv.querySelector('#logicalOperators' + kpi.name + cont);
                                    if (logical_operator) {
                                        for (var a = 0; a <= logical_operator.options.length; a++) {
                                            var option = logical_operator.options[a];
                                            if (option) {
                                                if (option.value == condition_value[i]) {
                                                    option.selected = true; // Select logical operator
                                                }
                                            }
                                        }
                                    }
                                    cont++;
                                } else {
                                    // Handle individual conditions without logical operators
                                    if (condition_value[i] != "&&" || condition_value[i] != '||') {
                                        var firtscondition = nextDiv.querySelector('#firstConditionContainerInput' + kpi.name + cont);
                                        var seconcondition = nextDiv.querySelector('#secondConditionContainerInput' + kpi.name + cont);
                                        var condition_operator = nextDiv.querySelector('#operators' + kpi.name + cont);

                                        // Split condition into parts
                                        let data = condition_value[i].split(/\s*(==|!=|>=|<=|>|<)\s*/);

                                        // Assign values to the input fields
                                        if (firtscondition)
                                            firtscondition.value = data[0];
                                        if (seconcondition)
                                            seconcondition.value = data[2];

                                        // Set the operator in the dropdown
                                        if (condition_operator && data[1] != '') {
                                            for (var a = 0; a <= condition_operator.options.length; a++) {
                                                var option = condition_operator.options[a];
                                                if (option) {
                                                    if (option.value == data[1]) {
                                                        option.selected = true;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }

                    if (kpi.operation) {
                        // Regex to detect decimal numbers, integers, columns, operators, and parentheses
                        var operation_value = kpi.operation
                            .match(/(?:\d+\.\d+|\d+|[a-zA-Z0-9%_$().]+|[+\-*\/%()])/g) // Captures numbers, columns, symbols like %, $, _, and operators
                            .filter(item => item.trim() !== ""); // Remove empty or whitespace values

                        var cont = 1;
                        var operators_regex = /^[+\-*\/%]$/; // Valid arithmetic operators

                        for (var i = 0; i < operation_value.length; i++) {
                            if (operators_regex.test(operation_value[i])) {
                                // If it's an operator, process the operator selector
                                addSet('operationsContainerLabel' + kpi.name, kpi.name, cont + 1);
                                var logical_operations = nextDiv.querySelector('#mathOperators' + kpi.name + cont);
                                if (logical_operations) {
                                    for (var a = 0; a <= logical_operations.options.length; a++) {
                                        var option = logical_operations.options[a];
                                        if (option) {
                                            if (option.value == operation_value[i]) {
                                                option.selected = true; // Select the operator
                                            }
                                        }
                                    }
                                }
                                cont++;
                            } else {
                                // Handle columns, values, and parentheses as part of the input
                                if (operation_value[i] != "&&" && operation_value[i] != "||") {
                                    var input_container = nextDiv.querySelector('#listContainerInput' + kpi.name + cont);
                                    if (input_container) {
                                        input_container.value = (input_container.value || "") + operation_value[i]; // Append the value
                                    }
                                }
                            }
                        }
                    }



                    return;
                }
            });
        }
    }
}

function findKpiByCode(code) {
    var matchingKpi = customKpis.find(function (kpi) {
        return kpi.code === code;
    });

    return matchingKpi ? ('rowData.' + matchingKpi.columkey) : code;
}

function getSelectedUserIds() {
    // Select all checked checkboxes with the class 'user-checkbox'
    const selectedCheckboxes = document.querySelectorAll('.user-checkbox:checked');
    // Convert the NodeList to an array and map each checkbox to its value
    const selectedUserIds = Array.from(selectedCheckboxes).map(checkbox => checkbox.value);
    // Return the array of selected user IDs
    return selectedUserIds;
}

function retrieveJoinData(reportId) {
    let joinContainers = document.querySelectorAll('.join-inputs-container');
    let joinData = [];

    for (let joinContainer of joinContainers) {
        let joinSelect = joinContainer.querySelector('.joinSelect');
        let firstJoinKey = joinContainer.querySelector(`input[id^="firstJoinKey_"]`);
        let secondJoinKey = joinContainer.querySelector(`input[id^="secondJoinKey_"]`);
        let uniqueKey = joinContainer.querySelector(`input[id^="uniqueKey_"]`);
        let reportJoinId = parseInt(joinSelect.getAttribute('data-id'));

        let joinType = joinSelect.value;
        let leftKey = firstJoinKey.value.trim();
        let rightKey = secondJoinKey.value.trim();
        let uniqueKeyValue = uniqueKey.value.trim();

        // Ensure both join keys and the join type are not empty
        if (leftKey && rightKey && joinType) {
            let joinItem = {
                joinType,
                leftKey,
                rightKey,
                uniqueKey: uniqueKeyValue,
                reportJoinId,
                reportId,
            };

            joinData.push(joinItem);
        }
    }

    return joinData;
}

function getElementValue(id) {
    return document.getElementById(id)?.value || null;
}

function getParsedElementValue(id) {
    try {
        const elementValue = document.getElementById(id)?.value || 'null';
        const parsedValue = JSON.parse(elementValue); // Intenta analizar como JSON
        return parsedValue?.value || null; // Devuelve el campo `value` si existe, o null
    } catch (e) {
        return null;
    }
}

function getParsedElementJson(id) {
    try {
        const elementValue = document.getElementById(id)?.value || 'null';

        // Verifica si el valor coincide con la cadena vacía de JSON
        const emptyJson = '{"logEntries":[],"value":"","text":"","enabled":true,"checkedIndices":[],"checkedItemsTextOverflows":false}';

        if (elementValue === emptyJson) {
            return null;
        }

        return elementValue;
    } catch (e) {
        return null;
    }
}

function setElementValue(elementId, value) {
    var element = document.getElementById(elementId);

    if (!element) {
        /*//console.error("El elemento con ID " + elementId + " no se encontró.");*/
        return;
    }

    // Verificar el tipo de elemento y establecer el valor apropiadamente
    switch (element.tagName.toLowerCase()) {
        case 'input':
            if (element.type === 'checkbox' || element.type === 'radio') {
                element.checked = value; // Para checkboxes y radios, usamos 'checked'
            } else {
                element.value = value; // Para otros inputs como 'text', 'number', etc.
            }
            break;
        case 'select':
            element.value = value; // Para selects
            break;
        case 'textarea':
            element.value = value; // Para textareas
            break;
        //default:
        //    //console.warn("El elemento con ID " + elementId + " no es compatible con setElementValue.");
    }
}

function clearCheckboxes(mapping) {
    for (var key in mapping) {
        if (mapping.hasOwnProperty(key)) {
            var checkboxId = mapping[key];

            // Encuentra el checkbox por su ID
            var checkbox = document.getElementById(checkboxId);

            // Si el checkbox existe, desmarcarlo
            if (checkbox) {
                checkbox.checked = false;
            } else {
                //console.error("Checkbox con id " + checkboxId + " no encontrado.");
            }
        }
    }
}


function setParsedElementValue(id, data) {
    try {
        document.getElementById(id).value = data;
        return;
    } catch (e) {
        return;
    }
}

function updateOperationsList(operationsList) {
    return operationsList.map(operation => replaceCondition(operation));
}

function replaceCondition(data) {
    let condition = data.condition;
    const regex = /(\w+\.\w+)\s*>=\s*(\d+)(Days|Weeks|Months|Years)\(\)/; ///(\w+\.\w+)\s*>=\s*'(\d+)(Days|Weeks|Months|Years)\(\)'/;

    let match = condition.match(regex);
    if (match) {
        let dateField = match[1];
        let amount = parseInt(match[2]);
        let unit = match[3];

        let currentDate = new Date();
        let pastDate = new Date(currentDate);

        switch (unit) {
            case 'Days':
                pastDate.setDate(currentDate.getDate() - amount);
                break;
            case 'Weeks':
                pastDate.setDate(currentDate.getDate() - (amount * 7));
                break;
            case 'Months':
                pastDate.setMonth(currentDate.getMonth() - amount);
                break;
            case 'Years':
                pastDate.setFullYear(currentDate.getFullYear() - amount);
                break;
        }

        let currentDateStr = currentDate.toISOString().split('T')[0] + ' 00:00:00.000';
        let pastDateStr = pastDate.toISOString().split('T')[0] + ' 00:00:00.000';

        let newCondition = condition.replace(
            regex,
            `${dateField} >= '${pastDateStr}' && ${dateField} <= '${currentDateStr}'`
        );

        data.condition = newCondition;
    }

    return data;
}

function processJoinsBySavedData(savedJoinData, currentData) {
    for (let joinContainer of savedJoinData) {
        let joinSelect = joinContainer.type.toLowerCase(); // Ensure lowercase join type
        let firstJoinKey = joinContainer.leftKey.trim();
        let secondJoinKey = joinContainer.rightKey.trim();
        let uniqueKey = joinContainer.uniqueKey.trim();

        if (firstJoinKey && secondJoinKey) {
            let joinKey1Dataset = firstJoinKey.split('.')[0];
            let joinKey2Dataset = secondJoinKey.split('.')[0];
            let joinKey1Attr = firstJoinKey.split('.')[1];
            let joinKey2Attr = secondJoinKey.split('.')[1];

            let dataSet1 = currentData[joinKey1Dataset];
            let dataSet2 = currentData[joinKey2Dataset];

            if (!dataSet1 || !dataSet2) {
                return currentData;
            }

            let joinedData;
            if (joinSelect === 'left') {
                joinedData = performLeftJoin(dataSet1, dataSet2, joinKey1Attr, joinKey2Attr);
            } else if (joinSelect === 'right') {
                joinedData = performRightJoin(dataSet1, dataSet2, joinKey1Attr, joinKey2Attr);
            } else if (joinSelect === 'inner') {
                joinedData = performInnerJoin(dataSet1, dataSet2, joinKey1Attr, joinKey2Attr);
            }

            let uniqueJoinedData = makeDataUnique(joinedData, uniqueKey);
            currentData[joinKey1Dataset] = uniqueJoinedData;
        }
    }

    return currentData;
}

function performLeftJoin(dataSet1, dataSet2, joinKey1Attr, joinKey2Attr) {
    return lodash.flatMap(dataSet1, appt => {
        let matchingRows = lodash.filter(dataSet2, row => row[joinKey2Attr].toLowerCase() === appt[joinKey1Attr].toLowerCase());
        if (matchingRows.length === 0) {
            return [lodash.merge({}, appt)]; // Include dataSet1 even without a match
        } else {
            return lodash.map(matchingRows, row => lodash.merge({}, appt, row));
        }
    });
}

function performRightJoin(dataSet1, dataSet2, joinKey1Attr, joinKey2Attr) {
    return lodash.flatMap(dataSet2, row => {
        let matchingRows = lodash.filter(dataSet1, appt => appt[joinKey1Attr].toLowerCase() === row[joinKey2Attr].toLowerCase());
        if (matchingRows.length === 0) {
            return [lodash.merge({}, row)]; // Include dataSet2 even without a match
        } else {
            return lodash.map(matchingRows, appt => lodash.merge({}, appt, row));
        }
    });
}

function performInnerJoin(dataSet1, dataSet2, joinKey1Attr, joinKey2Attr) {
    return lodash.flatMap(dataSet1, appt => {
        let matchingRows = lodash.filter(dataSet2, row => row[joinKey2Attr].toLowerCase() === appt[joinKey1Attr].toLowerCase());
        return lodash.map(matchingRows, row => lodash.merge({}, appt, row));
    });
}

function makeDataUnique(data, uniqueKey) {
    return lodash.uniqBy(data, uniqueKey);
}

function processJoins(result) {
    let joinContainers = document.querySelectorAll('.join-inputs-container');

    let currentData = result; // Initialize with the base data

    for (let joinContainer of joinContainers) {
        let joinSelect = joinContainer.querySelector('.joinSelect');
        let firstJoinKey = joinContainer.querySelector(`input[id^="firstJoinKey_"]`);
        let secondJoinKey = joinContainer.querySelector(`input[id^="secondJoinKey_"]`);
        let uniqueKey = joinContainer.querySelector(`input[id^="uniqueKey_"]`);

        let joinType = joinSelect.value;
        let joinKey1 = firstJoinKey.value.trim();
        let joinKey2 = secondJoinKey.value.trim();

        if (joinKey1 && joinKey2) {
            let joinKey1Dataset = joinKey1.split('.')[0];
            let joinKey2Dataset = joinKey2.split('.')[0];
            let joinKey1Attr = joinKey1.split('.')[1];
            let joinKey2Attr = joinKey2.split('.')[1];

            let dataSet1 = currentData[joinKey1Dataset];
            let dataSet2 = currentData[joinKey2Dataset];

            if (!dataSet1 || !dataSet2) {
                ////console.error("One of the datasets could not be resolved.");
                return currentData;
            }

            let joinedData;
            if (joinType === 'left') {
                joinedData = performLeftJoin(dataSet1, dataSet2, joinKey1Attr, joinKey2Attr);
            } else if (joinType === 'right') {
                joinedData = performRightJoin(dataSet1, dataSet2, joinKey1Attr, joinKey2Attr);
            } else if (joinType === 'inner') {
                joinedData = performInnerJoin(dataSet1, dataSet2, joinKey1Attr, joinKey2Attr);
            }

            let uniqueJoinedData = makeDataUnique(joinedData, uniqueKey); //'appointmentId');
            currentData[joinKey1Dataset] = uniqueJoinedData;
            //currentData = uniqueJoinedData;
        }
    }

    return currentData;
}

function getCustomAggFuncs(operationsList, rowData, groupColumn) {
    let customAggFuncs = {};

    for (let operation of operationsList) {
        customAggFuncs[operation.columnName] = function (values) {
            let validValues = values.filter(value => value !== undefined && value !== null);

            let conditionFunc;
            if (operation.condition) {
                let conditionCode = 'return ' + operation.condition.replace(operation.condition.split(' ')[0], 'value');
                conditionFunc = new Function('value', conditionCode);
                validValues = validValues.filter(conditionFunc);
            }

            let result;
            switch (operation.aggFunc) {
                case 'sum':
                case 'derived': // 'derived' aggregation function is treated as 'sum'
                    result = validValues.reduce((a, b) => a + b, 0);
                    break;
                case 'count':
                    result = validValues.reduce((sum, value) => sum + value, 0);
                    break;
                default:
                    result = null;
            }

            return result;
        };
    }

    return customAggFuncs;
}

function evalCondition(condition, row, operation) {
    if (row == null) {
        return false;
    }
    if (!condition) return true;

    // Reemplazar lo que está antes del punto por "row", pero preservar los espacios
    if (operation.aggFunc === 'ifelse') {
        const regex = /IF\s+(.*?)\s+THEN/;
        condition = condition.replace(/\b\w+\.(\s*)/g, "row$1.");
        const matches = condition.match(regex);
        condition = matches[1];
    } else {
        condition = condition.replace(/\b\w+\.(\s*)/g, "row$1.");
    }

    // Poner la primera letra después del punto en minúscula, y preservar los espacios
    conditionCode = condition.replace(/\.(\s*)(\w)/g, (_, spaces, letter) => "." + spaces + letter.toLowerCase());

    const patron = /Today\(\)\s*([\+\-])\s*(\d+)(Days|Weeks|Months|Years)|Today\(\)/;

    if (patron.test(conditionCode)) {
        conditionCode = replaceDateConditions(conditionCode);
    }

    try {
        if (operation.aggFunc === 'count') {
            var r = eval(conditionCode);
            r = r ? 1 : 0;
            return r;
        } else if (operation.aggFunc === 'ifelse') {
            if (conditionCode.indexOf('.') !== -1) {
                var r = eval(conditionCode);
                r = r ? 1 : 0;
                return r;
            } else {
                let data = conditionCode.split(/\s*(==|!=|>=|<=|>|<)\s*/);
                var r = eval('row.' + data[0] + ' ' + data[1] + ' ' + data[2]);
                r = r ? 1 : 0;
                return r;
            }
        }
        return eval(conditionCode);
    } catch (e) {
        $('#loader').hide();
        console.error("Error evaluating condition:", e);
        return 0;  // Return a default value of 0 if any error occurs
    }
}


function regexConditionValue(condition, row, evalResult) {
    try {
        // Reemplaza las instancias de 'word.' por 'row.' en la condición
        condition = condition.replace(/\b\w+\.(\s*)/g, "row$1.");

        const regex = /IF\s+(.*?)\s+THEN\s+(.*?)\s+ELSE\s+THEN\s+((?:\S+\s+\S+|\S+))/;

        const matches = condition.match(regex);

        if (matches) {
            const afterThen = matches[2]; // Expresión después de THEN antes de ELSE THEN
            const splitResult = matches[3] ? matches[3].split(' ') : [];
            const prefix = splitResult[0] || ''; // Parte antes de "row." (puede ser cualquier cosa o vacío)
            let rowPart = splitResult[1] || ''; // Parte "row.CustomersText1"

            // Extraemos el nombre de la propiedad de rowPart
            rowPart = rowPart.replace(/\.(\s*)(\w)/g, (_, spaces, letter) => "." + spaces + letter.toLowerCase());

            const rowProperty = rowPart.split('row.')[1]; // Eliminar 'row.' para obtener el nombre de la propiedad

            const rowValue = row[rowProperty];

            // Realizar las acciones dependiendo de evalResult
            if (evalResult == 1) {
                return afterThen;
            } else if (evalResult == 0) {
                return prefix + (rowValue !== undefined ? rowValue : ""); // Concatenar con el valor de row
            }
        }

        return null;  // Si no hay coincidencias o algo no está bien
    } catch (error) {
        $('#loader').hide();
        // Handle any errors that might occur during execution
        console.error("Error evaluating condition:", error);
        return null;  // Return null in case of an error to avoid breaking the program
    }
}


function evalConditionValue(condition, params) {
    // Reemplaza las instancias de 'word.' por 'row.' en la condición
    condition = condition.replace(/\b\w+\.(\s*)/g, "row$1.");

    // Combine both data sources into one object
    const data = { ...params.node.aggData, ...params.data };

    const regex = /IF\s+(.*?)\s+THEN\s+(.*?)\s+ELSE\s+THEN\s+((?:\S+\s+\S+|\S+))/;

    try {
        // Match condition using regex
        const matches = condition.match(regex);

        if (matches) {
            const condition = matches[1];
            const afterThen = matches[2]; // Expression after THEN and before ELSE THEN
            const splitResult = matches[3] ? matches[3].split(' ') : [];
            const prefix = splitResult[0] || ''; // Part before "row." (may be anything or empty)
            let rowPart = splitResult[1] || ''; // Part "row.CustomersText1"

            // Extract the property name from rowPart
            rowPart = rowPart.replace(/\.(\s*)(\w)/g, (_, spaces, letter) => "." + spaces + letter.toLowerCase());

            // Get the row value from the data
            const rowValue = data[rowPart];

            // Split the condition around the operator (==, !=, etc.)
            let datas = condition.split(/\s*(==|!=|>=|<=|>|<)\s*/);

            let value = data[datas[0]];

            // Clean the value by removing any unwanted characters
            if (/[%]/.test(value))
                value = value.replace('%', '');
            if (/[$]/.test(value))
                value = value.replace('$', '');
            if (/[,]/.test(value))
                value = value.replace(',', '');

            // Evaluate the condition with eval
            var r = eval(value + ' ' + datas[1] + ' ' + datas[2]);

            // Return the appropriate result based on the evaluation
            if (r) {
                return afterThen;
            } else {
                return prefix + (rowValue !== undefined ? rowValue : ""); // Concatenate with the value from row
            }
        }

        return null;  // Return null if no match or condition is incorrect
    }
    catch (error) {
        $('#loader').hide();
        // Log the error for debugging purposes
        console.error("Error evaluating condition:", error);
        return null;  // Return null in case of an error
    }
}


function replaceDateConditions(code) {
    const regex = /(\b\w+)\s*(==|!=|>|>=|<|<=)\s*Today\(\)\s*([\+\-]\s*\d+)(Days|Weeks|Months|Years)|(\b\w+)\s*(==|!=|>|>=|<|<=)\s*Today\(\)/g;

    try {
        return code.replace(regex, (match, dateFieldWithOffset, operatorWithOffset, offset, unitWithOffset, dateFieldToday, operatorToday) => {
            if (dateFieldToday) {
                // Para la condición Today()
                const dateValue = convertToISO({ unit: 'Today' });
                return `${dateFieldToday} ${operatorToday} '${dateValue.toString()}'`;
            } else if (dateFieldWithOffset) {
                // Para las condiciones Today() + N y Today() - N
                const operator = offset.trim().charAt(0);
                const value = parseInt(offset.trim().slice(1).trim(), 10);

                // Check for invalid offset value
                if (isNaN(value)) {
                    throw new Error(`Invalid offset value: ${offset}`);
                }

                const dateValue = convertToISO({ operator, unit: unitWithOffset, value });
                return `${dateFieldWithOffset} ${operatorWithOffset} '${dateValue.toString()}'`;
            }
        });
    } catch (error) {
        $('#loader').hide();
        console.error("Error in replaceDateConditions:", error);
        return code;  // Return the original code if an error occurs
    }
}


function convertToISO(condition) {
    // Usar la fecha actual como base si extractTimeCondition no devuelve una fecha válida
    let dateValue = extractTimeCondition();

    // Validar que dateValue es una instancia válida de Date
    if (!(dateValue instanceof Date) || isNaN(dateValue)) {
        // Si dateValue no es una fecha válida, asignar la fecha actual
        dateValue = new Date();
    }

    if (condition.unit === 'Today') {
        return dateValue.toISOString(); // Devolver directamente si es "Today"
    }

    switch (condition.unit) {
        case 'Days':
            dateValue.setDate(dateValue.getDate() + (condition.operator === '+' ? condition.value : -condition.value));
            break;
        case 'Weeks':
            dateValue.setDate(dateValue.getDate() + (condition.operator === '+' ? condition.value * 7 : -condition.value * 7));
            break;
        case 'Months':
            dateValue.setMonth(dateValue.getMonth() + (condition.operator === '+' ? condition.value : -condition.value));
            break;
        case 'Years':
            dateValue.setFullYear(dateValue.getFullYear() + (condition.operator === '+' ? condition.value : -condition.value));
            break;
        default:
            throw new Error(`Unknown time unit: ${condition.unit}`); // Manejo de unidades desconocidas
    }

    return dateValue.toISOString();
}


function extractTimeCondition() {
    let select = document.getElementById("dropDownYearRanges");
    let selectedValue = select.value;
    let currentDate = new Date();
    let resultDate;

    switch (selectedValue) {
        case "20": // Current Year
            resultDate = new Date(currentDate.getFullYear(), currentDate.getMonth(), currentDate.getDate());
            break;
        case "22": // Last Year
            resultDate = new Date(currentDate.getFullYear() - 1, currentDate.getMonth(), currentDate.getDate());
            break;
        case "50": // Two Years Ago
            resultDate = new Date(currentDate.getFullYear() + 2, currentDate.getMonth(), currentDate.getDate());
            break;
        case "51": // Three Years Ago
            resultDate = new Date(currentDate.getFullYear() + 3, currentDate.getMonth(), currentDate.getDate());
            break;
        case "52": // Four Years Ago
            resultDate = new Date(currentDate.getFullYear() + 4, currentDate.getMonth(), currentDate.getDate());
            break;
        case "53": // Five Years Ago
            resultDate = new Date(currentDate.getFullYear() + 5, currentDate.getMonth(), currentDate.getDate());
            break;
        default:
            resultDate = "Invalid selection";
    }
    return resultDate;
}

function evalDerivedOperation(operationString, row) {
    let codeForDerived = 'let result = ';

    try {
        // Build the dynamic operation code
        codeForDerived += operationString.replace(/(?:\b\w+|\$\w+|\w+\$\w+|\w+\$)+/g, match => {
            // Detect if the match is a number (literal value)
            if (!isNaN(match)) {
                return match; // It's a number, leave it as is
            }

            // Detect and strip prefix if present
            let prefix = match.includes('.') ? match.split('.')[0] + '.' : '';
            let strippedMatch = match.replace(prefix, "");

            if (strippedMatch.includes('.')) {
                let nestedFields = strippedMatch.split('.');
                let dataPath = 'row';
                nestedFields.forEach(subPart => {
                    dataPath += `["${subPart}"]`;
                });
                return dataPath + ' || 0'; // Return value from row or 0 if it doesn't exist
            }

            return `(row["${strippedMatch}"] || 0)`; // Lookup value in row
        }) + '; return isNaN(result) || !isFinite(result) ? 0 : result;';

        // Remove special characters like $ or %
        codeForDerived = codeForDerived.replace(/\$|%/g, '');

        // Execute the dynamic function with the provided row data
        return new Function('row', codeForDerived)(row);
    } catch (error) {
        $('#loader').hide();
        // Handle any errors in code generation or execution
        console.error("Error during derived operation evaluation:", error);
        return -1; // Return 0 in case of an error
    }
}

function sortOperations(operations) {
    try {
        let sortedOperations = [];
        let addedOperationNames = new Set();
        let operationsCopy = operations.slice(); // Hacemos una copia de las operaciones originales
        let maxIterations = operationsCopy.length; // Evitar bucles infinitos

        // Primero agregar todas las operaciones que no son derivadas
        for (let i = 0; i < operationsCopy.length; i++) {
            let operation = operationsCopy[i];
            if (operation.aggFunc !== 'derived') {
                addedOperationNames.add(operation.columnName);
                sortedOperations.push(operation);
                operationsCopy.splice(i, 1); // Eliminar la operación no derivada
                i--; // Ajustar el índice porque removimos un elemento
            }
        }

        // Luego agregar las operaciones derivadas, pero solo si sus dependencias están satisfechas
        let iterationCount = 0;
        while (operationsCopy.length > 0) {
            let operationAdded = false;

            for (let i = 0; i < operationsCopy.length; i++) {
                let operation = operationsCopy[i];

                // Verificar si es derivada y si sus partes ya han sido calculadas
                if (operation.aggFunc === 'derived' && canAddDerivedOperation(operation, addedOperationNames)) {
                    addedOperationNames.add(operation.columnName);
                    sortedOperations.push(operation);
                    operationsCopy.splice(i, 1); // Eliminar la operación derivada
                    operationAdded = true;
                    break; // Reiniciar el bucle para seguir agregando
                }
            }

            iterationCount++;

            // Si no se agregó ninguna operación derivada y alcanzamos el máximo número de iteraciones, hay un problema
            if (!operationAdded) {
                $('#loader').hide();
                console.error("Circular dependency detected in derived operations. The problematic operations are:");
                console.error(operationsCopy); // Imprimir operaciones que no pudieron resolverse
                return []; // Salir inmediatamente
            }

            // Salida de seguridad para evitar un bucle infinito en casos extremos
            if (iterationCount > maxIterations) {
                $('#loader').hide();
                console.error("Maximum iterations reached. Aborting. Remaining operations are:");
                console.error(operationsCopy); // Imprimir operaciones restantes
                return [];
            }
        }

        return sortedOperations;
    }
    catch (e) {
        $('#loader').hide();
        showSwal('error', 'Error sort operations');
        return null;
    }
}

function canAddDerivedOperation(operation, addedOperationNames) {
    // Dividimos la operación en partes (tratando de separar operadores y nombres de columnas, incluyendo paréntesis)
    let operationParts = operation.operation.split(/([+\-*/()\s])/g); // Separa por operadores +, -, *, /, paréntesis y espacios

    // Recorremos cada parte de la operación
    for (let part of operationParts) {
        part = part.trim(); // Quitamos espacios en blanco

        // Si la parte no es un operador, paréntesis, ni está vacía
        if (part !== "" && !['+', '-', '*', '/', '(', ')'].includes(part)) {
            // Verifica si la parte es un número
            if (!isNaN(part)) {
                continue; // Si es un número, no es un nombre de columna, continúa con la siguiente parte
            }

            // Si no es un número ni un operador/paréntesis, debe ser un nombre de columna
            if (!addedOperationNames.has(part)) {
                return 0; // Si alguna parte no ha sido añadida aún, no se puede agregar la operación derivada
            }
        }
    }

    // Si todas las partes ya están añadidas o son válidas, se puede añadir la operación derivada
    return 1;
}



function populateDropdown(newColumnDefs, rawData) {
    var ddlColumns = document.getElementById('ddlColumns');
    ddlColumns.innerHTML = '';

    // Getting column names from rawData
    if (rawData && rawData.length > 0) {
        var rawColumnNames = Object.keys(rawData[0]);
        for (let columnName of rawColumnNames) {
            var opt = document.createElement('option');
            opt.value = columnName;
            opt.innerHTML = columnName;
            ddlColumns.appendChild(opt);
        }
    }

    // Adding newColumnDefs
    for (let columnDef of newColumnDefs) {
        var opt = document.createElement('option');
        opt.value = columnDef.field;
        opt.innerHTML = columnDef.headerName;
        ddlColumns.appendChild(opt);
    }
}

function checkUsersInList(userIds) {
    const checkboxes = document.querySelectorAll('.user-checkbox');

    // If userIds array is empty or null, uncheck all checkboxes
    if (!userIds || userIds.length === 0) {
        checkboxes.forEach(checkbox => {
            checkbox.checked = false;
        });
        return;
    }

    checkboxes.forEach(checkbox => {
        // Check if the checkbox value (user ID) is in the provided list of user IDs
        if (userIds.includes(checkbox.value)) {
            checkbox.checked = true; // Check the checkbox if the user ID is in the list
        } else {
            checkbox.checked = false; // Uncheck the checkbox if the user ID is not in the list
        }
    });
}

function getFileType(file) {
    const extension = file.name.split('.').pop().toLowerCase();
    if (extension === 'json') {
        return 'json';
    } else if (extension === 'csv') {
        return 'csv';
    } else {
        throw new Error('Unsupported file type');
    }
}

function csvJSON(csv) {
    const lines = csv.split('\n');
    const result = [];
    const headers = lines[0].split(',');

    for (let i = 1; i < lines.length; i++) {
        const currentLine = lines[i].split(',');

        // Verificar si la línea tiene la misma cantidad de columnas que los encabezados
        if (currentLine.length !== headers.length) {
            //console.error(`La línea ${i + 1} no tiene la misma cantidad de columnas que los encabezados.`);
            continue; // Saltar esta línea y pasar a la siguiente
        }

        const obj = {};

        for (let j = 0; j < headers.length; j++) {
            obj[headers[j].trim().replace('"', '').replace('"', '')] = currentLine[j].trim().replace('"', '').replace('"', '');
        }

        result.push(obj);
    }

    return result;
}

function updateDateRange(value) {
    let dateRange;
    if (value === null)
        dateRange = document.getElementById('dropDownDateRanges').value;
    else
        dateRange = value;
    const yearRangeSelect = document.getElementById('dropDownYearRanges');
    const startDateInput = document.getElementById('ctl00_c1_DateTimeCriteriaDetails_startDateRadDatePicker_dateInput');
    const endDateInput = document.getElementById('ctl00_c1_DateTimeCriteriaDetails_endDateRadDatePicker_dateInput');

    const today = new Date();
    let startDate = '';
    let endDate = '';

    switch (dateRange) {
        case '':
            yearRangeSelect.value = ''; // Set to Custom
            break;
        case '-1':
        case '2':
            yearRangeSelect.value = '2'; // Set to Custom
            break;
        case '3': // Today
            startDate = endDate = today.toISOString().split('T')[0];
            break;
        case '4': // Tomorrow
            const tomorrow = new Date(today);
            tomorrow.setDate(tomorrow.getDate() + 1);
            startDate = endDate = tomorrow.toISOString().split('T')[0];
            break;
        case '6': // Yesterday
            const yesterday = new Date(today);
            yesterday.setDate(yesterday.getDate() - 1);
            startDate = endDate = yesterday.toISOString().split('T')[0];
            break;
        case '23': // Last 7 Days
            const last7Days = new Date(today);
            last7Days.setDate(last7Days.getDate() - 7);
            startDate = last7Days.toISOString().split('T')[0];
            endDate = today.toISOString().split('T')[0];
            break;
        case '28': // Next 7 Days
            const next7Days = new Date(today);
            next7Days.setDate(next7Days.getDate() + 7);
            startDate = today.toISOString().split('T')[0];
            endDate = next7Days.toISOString().split('T')[0];
            break;
        case '7': // Week To Date
            const weekStart = new Date(today);
            weekStart.setDate(today.getDate() - today.getDay());
            startDate = weekStart.toISOString().split('T')[0];
            endDate = today.toISOString().split('T')[0];
            break;
        case '8': // Current Week
            const currWeekStart = new Date(today);
            const currWeekEnd = new Date(today);
            currWeekStart.setDate(today.getDate() - today.getDay());
            currWeekEnd.setDate(today.getDate() + (6 - today.getDay()));
            startDate = currWeekStart.toISOString().split('T')[0];
            endDate = currWeekEnd.toISOString().split('T')[0];
            break;
        case '9': // Next Week
            const nextWeekStart = new Date(today);
            const nextWeekEnd = new Date(today);
            nextWeekStart.setDate(today.getDate() + (7 - today.getDay()));
            nextWeekEnd.setDate(nextWeekStart.getDate() + 6);
            startDate = nextWeekStart.toISOString().split('T')[0];
            endDate = nextWeekEnd.toISOString().split('T')[0];
            break;
        case '10': // Last Week
            const lastWeekStart = new Date(today);
            const lastWeekEnd = new Date(today);
            lastWeekStart.setDate(today.getDate() - today.getDay() - 7);
            lastWeekEnd.setDate(lastWeekStart.getDate() + 6);
            startDate = lastWeekStart.toISOString().split('T')[0];
            endDate = lastWeekEnd.toISOString().split('T')[0];
            break;
        case '24': // Last 30 Days
            const last30Days = new Date(today);
            last30Days.setDate(today.getDate() - 30);
            startDate = last30Days.toISOString().split('T')[0];
            endDate = today.toISOString().split('T')[0];
            break;
        case '25': // Last 60 Days
            const last60Days = new Date(today);
            last60Days.setDate(today.getDate() - 60);
            startDate = last60Days.toISOString().split('T')[0];
            endDate = today.toISOString().split('T')[0];
            break;
        case '26': // Last 90 Days
            const last90Days = new Date(today);
            last90Days.setDate(today.getDate() - 90);
            startDate = last90Days.toISOString().split('T')[0];
            endDate = today.toISOString().split('T')[0];
            break;
        case '11': // Month To Date
            const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
            startDate = monthStart.toISOString().split('T')[0];
            endDate = today.toISOString().split('T')[0];
            break;
        case '12': // Current Month
            const currMonthStart = new Date(today.getFullYear(), today.getMonth(), 1);
            const currMonthEnd = new Date(today.getFullYear(), today.getMonth() + 1, 0);
            startDate = currMonthStart.toISOString().split('T')[0];
            endDate = currMonthEnd.toISOString().split('T')[0];
            break;
        case '13': // Next Month
            const nextMonthStart = new Date(today.getFullYear(), today.getMonth() + 1, 1);
            const nextMonthEnd = new Date(today.getFullYear(), today.getMonth() + 2, 0);
            startDate = nextMonthStart.toISOString().split('T')[0];
            endDate = nextMonthEnd.toISOString().split('T')[0];
            break;
        case '14': // Last Month
            const lastMonthStart = new Date(today.getFullYear(), today.getMonth() - 1, 1);
            const lastMonthEnd = new Date(today.getFullYear(), today.getMonth(), 0);
            startDate = lastMonthStart.toISOString().split('T')[0];
            endDate = lastMonthEnd.toISOString().split('T')[0];
            break;
        case '15': // Quarter To Date
            const quarterStart = new Date(today.getFullYear(), Math.floor(today.getMonth() / 3) * 3, 1);
            startDate = quarterStart.toISOString().split('T')[0];
            endDate = today.toISOString().split('T')[0];
            break;
        case '18': // Last Quarter
            const lastQuarterStart = new Date(today.getFullYear(), Math.floor((today.getMonth() - 3) / 3) * 3, 1);
            const lastQuarterEnd = new Date(today.getFullYear(), Math.floor(today.getMonth() / 3) * 3, 0);
            startDate = lastQuarterStart.toISOString().split('T')[0];
            endDate = lastQuarterEnd.toISOString().split('T')[0];
            break;
        case '19': // Year To Date
            const yearStart = new Date(today.getFullYear(), 0, 1);
            startDate = yearStart.toISOString().split('T')[0];
            endDate = today.toISOString().split('T')[0];
            break;
        case '20': // All Year
            const allYearStart = new Date(today.getFullYear(), 0, 1);
            const allYearEnd = new Date(today.getFullYear(), 11, 31);

            const padToTwoDigits = (num) => num.toString().padStart(2, '0');

            startDate = `${padToTwoDigits(allYearStart.getDate())}/${padToTwoDigits(allYearStart.getMonth() + 1)}/${allYearStart.getFullYear()}`;
            endDate = `${padToTwoDigits(allYearEnd.getDate())}/${padToTwoDigits(allYearEnd.getMonth() + 1)}/${allYearEnd.getFullYear()}`;

            break;
        default:
            break;
    }

    startDateInput.value = startDate;
    endDateInput.value = endDate;
    initial_date = startDate;
    last_date = endDate;
}

function updateYearRange(value) {
    let yearRange;
    if (value === null)
        dateRange = document.getElementById('dropDownYearRanges').value;
    else
        dateRange = value;
    const startDateInput = document.getElementById('ctl00_c1_DateTimeCriteriaDetails_startDateRadDatePicker_dateInput');
    const endDateInput = document.getElementById('ctl00_c1_DateTimeCriteriaDetails_endDateRadDatePicker_dateInput');

    const currentYear = new Date().getFullYear();

    switch (yearRange) {
        case '20': // Current Year
            startDateInput.value = startDateInput.value.replace(/^(\d{2}\/\d{2}\/)\d{4}/, `$1${currentYear}`);
            endDateInput.value = endDateInput.value.replace(/^(\d{2}\/\d{2}\/)\d{4}/, `$1${currentYear}`);
            break;
        case '22': // Last Year
            startDateInput.value = startDateInput.value.replace(/^(\d{2}\/\d{2}\/)\d{4}/, `$1${currentYear - 1}`);
            endDateInput.value = endDateInput.value.replace(/^(\d{2}\/\d{2}\/)\d{4}/, `$1${currentYear - 1}`);
            break;
        case '50': // Two Years Ago
            startDateInput.value = startDateInput.value.replace(/^(\d{2}\/\d{2}\/)\d{4}/, `$1${currentYear - 2}`);
            endDateInput.value = endDateInput.value.replace(/^(\d{2}\/\d{2}\/)\d{4}/, `$1${currentYear - 2}`);
            break;
        case '51': // Three Years Ago
            startDateInput.value = startDateInput.value.replace(/^(\d{2}\/\d{2}\/)\d{4}/, `$1${currentYear - 3}`);
            endDateInput.value = endDateInput.value.replace(/^(\d{2}\/\d{2}\/)\d{4}/, `$1${currentYear - 3}`);
            break;
        case '52': // Four Years Ago
            startDateInput.value = startDateInput.value.replace(/^(\d{2}\/\d{2}\/)\d{4}/, `$1${currentYear - 4}`);
            endDateInput.value = endDateInput.value.replace(/^(\d{2}\/\d{2}\/)\d{4}/, `$1${currentYear - 4}`);
            break;
        case '53': // Five Years Ago
            startDateInput.value = startDateInput.value.replace(/^(\d{2}\/\d{2}\/)\d{4}/, `$1${currentYear - 5}`);
            endDateInput.value = endDateInput.value.replace(/^(\d{2}\/\d{2}\/)\d{4}/, `$1${currentYear - 5}`);
            break;
        default:
            break;
    }

}

function updateDropdownOptions() {
    const dropdowns = document.querySelectorAll('#dropDownDateRanges, #dropDownYearRanges');

    // Loop through each dropdown
    dropdowns.forEach(dropdown => {
        // Check if dropdown element exists
        if (!dropdown) {
            ////console.log(`Dropdown element ${dropdown.id} not found. Waiting for it to be available...`);
            return; // Exit function if dropdown is not found
        }

        const optionValuesBefore = [];

        // Log values before updating
        dropdown.querySelectorAll('option').forEach(option => {
            optionValuesBefore.push({
                value: option.value,
                text: option.textContent
            });
        });

        ////console.log(`Values before update (${dropdown.id}):`, optionValuesBefore);

        const optionValues = new Set();
        const optionMap = new Map(); // Map to store original text for each unique value

        // Loop through each option and add unique values to Set
        dropdown.querySelectorAll('option').forEach(option => {
            const value = option.value;
            if (!optionValues.has(value)) {
                optionValues.add(value);
                optionMap.set(value, option.textContent); // Store original text for value
            }
        });

        // Clear existing options from dropdown
        dropdown.innerHTML = '';

        // Add unique options back to dropdown with original text
        optionValues.forEach(value => {
            const option = document.createElement('option');
            option.value = value;
            option.textContent = optionMap.get(value); // Set original text from map
            dropdown.appendChild(option);
        });

        ////console.log(`Values after update (${dropdown.id}):`, Array.from(optionValues));
    });

    runCount++; // Increment the run count

    // Check if maximum run count (3 times) is reached
    if (runCount >= 3) {
        ////console.log('Maximum run count reached. Stopping further updates.');
        clearInterval(intervalId); // Stop the interval
    }
}

function runAfterDocumentReady() {
    if (document.readyState === 'complete') {
        updateDropdownOptions(); // Call the function when document is ready
    } else {
        //console.log('Waiting for document to be fully loaded...');
        document.addEventListener('DOMContentLoaded', updateDropdownOptions);
    }
}

function setOverflowAutoForDropDowns() {
    var divs = document.querySelectorAll('div[id$="_DropDown"]');
    divs.forEach(function (div) {
        div.style.overflow = 'auto';
    });
}

function showFilters() {
    /* initialFilters = collectFiltersData();*/
    //updateDropdownOptions();
    setOverflowAutoForDropDowns();

    var btn = document.getElementById('btnAddFilters');
    var filterPanel = document.getElementById("filterPanel");

    // Disable the button
    btn.disabled = true;

    // Change the button text to "Wait..."
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

    // Toggle the filter panel visibility after a delay
    setTimeout(function () {
        filterPanel.classList.toggle("active");

        // Enable the button and restore original text
        btn.disabled = false;
        btn.innerHTML = '<i class="fas fa-filter"></i> KPI Filters';
    }, 3000);

}
function closeFilters() {
    var filterPanel = document.getElementById("filterPanel");
    filterPanel.classList.toggle("active");

    //var newFilters = collectFiltersData();

    // Compare new filters with initial filters
    saveReport();
}

function deleteReport() {
    unAssignReport(selectedReportId);
}

function setCheckboxes(mapping, selectedItems) {
    // Convierte la cadena de días en un array
    var selectedArray = selectedItems.split(',');

    // Itera sobre cada propiedad del objeto de mapeo
    for (var key in mapping) {
        if (mapping.hasOwnProperty(key)) {
            var checkboxId = mapping[key];

            // Encuentra el checkbox por su ID
            var checkbox = document.getElementById(checkboxId);

            // Si el checkbox existe, se marca o desmarca según esté en el array
            if (checkbox) {
                if (selectedArray.includes(key)) {
                    checkbox.checked = true;  // Marca el checkbox si está en el array de seleccionados
                } else {
                    checkbox.checked = false; // Desmarca si no está en el array
                }
            } else {
                //console.error("Checkbox con id " + checkboxId + " no encontrado.");
            }
        }
    }
}

function processValueGetterExpression(colDef, params) {
    if (typeof colDef.valueGetter === 'string') {

        try {
            // Usamos new Function para evaluar la expresión de manera controlada
            const combinedData = { ...params, ...params.data };
            let value = combinedData[colDef.headerName] || 0;  // Devuelve 0 si no existe el valor

            // Eliminar símbolos % y $ si es una cadena
            if (typeof value === 'string') {
                value = value.replace(/[%$,]/g, '').trim();
            }

            value = Number(value) || 0;
            return value;
        } catch (e) {
            console.error("Error evaluando valueGetter: ", e);
            return 0;  // Devuelve un valor por defecto si hay un error
        }
    }

    return 0;  // Devuelve 0 si no es un string el valueGetter
}

async function clearReportTool() {
    await clearDateTimeCriterial();
    await clearCancellationCriteriaFilters();
    await clearCustomersCriteriaFilters();
    await clearBasicCriterial();
    await clearMarketingCriteriaFilters();
    await clearPurchaseCriteriaFilters();
    await clearSalesCriteriaFilters();
    await clearTourCriteriaFilters();

    var containers = document.querySelectorAll('.join-inputs-container');
    joinCounter = 1;
    // Iterar sobre los contenedores seleccionados
    containers.forEach(function (container) {
        // Verificar si el contenedor tiene el id "joinContainer1"
        if (container.id !== 'joinContainer1') {
            // Eliminar el contenedor si no tiene el id "joinContainer1"
            container.parentNode.removeChild(container);
        }
        else {
            document.getElementById('joinSelect1').selectedIndex = 0;
            document.getElementById('firstJoinKey_1').value = "";
            document.getElementById('secondJoinKey_1').value = "";
            document.getElementById('uniqueKey_1').value = "";
        }
    });

    const accordionDiv = document.getElementById('accordion');
    accordionDiv.innerHTML = '';
    $("#columnName").val("");
    const reportTypeDropdown = document.getElementById('reportTypeOnDropdown');
    reportTypeDropdown.selectedIndex = 0;
    $("#reportName").val("");
    const preJoinsDropdown = document.getElementById('preJoinsDropdown');
    preJoinsDropdown.selectedIndex = 0;
}

