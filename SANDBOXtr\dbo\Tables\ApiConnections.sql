﻿CREATE TABLE [dbo].[ApiConnections] (
    [apiConnectionID]                          INT             IDENTITY (1, 1) NOT NULL,
    [apiExternalConnectionID]                  INT             NULL,
    [apiConnectionKey]                         NVARCHAR (64)   NULL,
    [apiConnectionName]                        NVARCHAR (128)  NULL,
    [apiConnectorID]                           INT             NOT NULL,
    [endpointAddress]                          NVARCHAR (MAX)  NULL,
    [endpointArgument1]                        NVARCHAR (MAX)  NULL,
    [endpointArgument2]                        NVARCHAR (MAX)  NULL,
    [endpointLastUpdatedTime]                  DATETIME        NULL,
    [hasFileUpload]                            BIT             CONSTRAINT [DF_ApiConnections_hasFileUpload] DEFAULT ((0)) NOT NULL,
    [hasImportTour]                            BIT             NOT NULL,
    [userName]                                 NVARCHAR (64)   NULL,
    [password]                                 VARBINARY (256) NULL,
    [userID]                                   INT             NOT NULL,
    [middlewareDelimitedPreActions]            NVARCHAR (256)  NULL,
    [middlewareAction]                         NVARCHAR (64)   NOT NULL,
    [middlewareActionDelimitedValidationRules] NVARCHAR (MAX)  NULL,
    [middlewareDelimitedPostActions]           NVARCHAR (256)  NULL,
    [pagedCount]                               INT             NOT NULL,
    [useTypeIDs]                               BIT             CONSTRAINT [DF_ApiConnections_useTypeIDs] DEFAULT ((1)) NOT NULL,
    [disableImportTourReference]               BIT             CONSTRAINT [DF_ApiConnections_disableImportTourReference] DEFAULT ((0)) NOT NULL,
    [importTourReferenceIgnoreUpdated]         BIT             CONSTRAINT [DF_ApiConnections_importTourReferenceIgnoreUpdated] DEFAULT ((1)) NOT NULL,
    [enableSchedule]                           BIT             CONSTRAINT [DF_ApiConnections_enableSchedule] DEFAULT ((0)) NOT NULL,
    [scheduleIntervalInMinutes]                INT             CONSTRAINT [DF_ApiConnections_scheduleIntervalInMinutes] DEFAULT ((0)) NULL,
    [scheduleStartUtcTime]                     DATETIME        NULL,
    [isProcessing]                             BIT             CONSTRAINT [DF_ApiConnections_isProcessing] DEFAULT ((0)) NOT NULL,
    [shouldContinueProcessing]                 BIT             CONSTRAINT [DF_ApiConnections_shouldContinueProcessing] DEFAULT ((0)) NOT NULL,
    [processingStartTime]                      DATETIME        NULL,
    [processingTotalCount]                     INT             NULL,
    [processingRemainingCount]                 INT             NULL,
    [processingPerformance]                    NVARCHAR (64)   NULL,
    [lastMiddlewareActionTime]                 DATETIME        NULL,
    [lastMiddlewareActionTotalCount]           INT             NULL,
    [lastMiddlewareActionPerformance]          NVARCHAR (64)   NULL,
    [lastMiddlewareImportNumber]               NVARCHAR (64)   NULL,
    [filterStartDate]                          DATETIME        NULL,
    [filterEndDate]                            DATETIME        NULL,
    [argumentExternalApiConnectionID]          NVARCHAR (64)   NULL,
    [argumentDelimitedPropertyKeys]            NVARCHAR (MAX)  NULL,
    [argument1]                                NVARCHAR (MAX)  NULL,
    [argument2]                                NVARCHAR (MAX)  NULL,
    [argument3]                                NVARCHAR (MAX)  NULL,
    [argument4]                                NVARCHAR (MAX)  NULL,
    [argumentStartDate]                        DATETIME        NULL,
    [argumentEndDate]                          DATETIME        NULL,
    [argumentDelimitedTourStatusTypeIDs]       NVARCHAR (MAX)  NULL,
    [argumentDelimitedTourStatusTypeNames]     NVARCHAR (MAX)  NULL,
    [argumentDelimitedLocationIDs]             NVARCHAR (MAX)  NULL,
    [argumentDelimitedLocationNames]           NVARCHAR (MAX)  NULL,
    [authorizations]                           TEXT            NULL,
    [middlewareCondition]                      NVARCHAR (500)  NULL,
    [enableCondition]                          BIT             NULL,
    CONSTRAINT [PK_ApiConnections] PRIMARY KEY CLUSTERED ([apiConnectionID] ASC),
    CONSTRAINT [FK_ApiConnections_apiConnectorID] FOREIGN KEY ([apiConnectorID]) REFERENCES [dbo].[ApiConnectors] ([apiConnectorID]),
    CONSTRAINT [FK_ApiConnections_userID] FOREIGN KEY ([userID]) REFERENCES [dbo].[Users] ([userID])
);

