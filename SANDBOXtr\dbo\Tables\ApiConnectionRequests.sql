﻿CREATE TABLE [dbo].[ApiConnectionRequests] (
    [apiConnectionRequestId] INT  IDENTITY (1, 1) NOT NULL,
    [apiConnectionID]        INT  NOT NULL,
    [apiTriggerId]           INT  NOT NULL,
    [requestEndpoint]        TEXT NOT NULL,
    [paramsHeaders]          TEXT NULL,
    [paramsBody]             TEXT NULL,
    [paramsUrl]              TEXT NULL,
    [apiHttpRequestMethodId] INT  NOT NULL,
    [requestBody]            TEXT NULL,
    [startDateOffset]        INT  NULL,
    [endDateOffset]          INT  NULL,
    [sendingParams]          BIT  NULL,
    CONSTRAINT [PK_ApiConnectionsMappingProperties] PRIMARY KEY CLUSTERED ([apiConnectionRequestId] ASC)
);

